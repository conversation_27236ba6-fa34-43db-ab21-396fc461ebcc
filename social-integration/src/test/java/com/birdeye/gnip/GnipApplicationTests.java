package com.birdeye.gnip;

import java.net.URI;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import com.zaubersoftware.gnip4j.api.GnipFacade;
import com.zaubersoftware.gnip4j.api.GnipStream;
import com.zaubersoftware.gnip4j.api.StreamNotificationAdapter;
import com.zaubersoftware.gnip4j.api.UriStrategy;
import com.zaubersoftware.gnip4j.api.impl.DefaultGnipFacade;
import com.zaubersoftware.gnip4j.api.impl.ImmutableGnipAuthentication;
import com.zaubersoftware.gnip4j.api.model.Activity;
import com.zaubersoftware.gnip4j.api.support.http.JRERemoteResourceProvider;

@RunWith(SpringRunner.class)
@ContextConfiguration//(classes = {ThreadPoolExecutor.class})
public class GnipApplicationTests {
	
//	@Autowired
//	ThreadPoolExecutor executor;
	ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.MILLISECONDS, 
			new ArrayBlockingQueue<>(10000));;
	
	 private final Logger logger = LoggerFactory.getLogger(GnipApplicationTests.class);
	    
	    final UriStrategy uriStrategy = new UriStrategy() {

			@Override
			public URI createStreamUri(String account, String streamName) {
				return URI.create("http://localhost:9898"); 
			}

			@Override
			public URI createRulesUri(String account, String streamName) {
				// TODO Auto-generated method stub
				return null;
			}

			@Override
			public URI createRulesDeleteUri(String account, String streamName) {
				// TODO Auto-generated method stub
				return null;
			}

			@Override
			public String getHttpMethodForRulesDelete() {
				// TODO Auto-generated method stub
				return null;
			}

			@Override
			public URI createStreamUri(String account, String streamName, Integer backFillMinutes) {
				// TODO Auto-generated method stub
				return null;
			}

			@Override
			public URI createRulesValidationUri(String account, String streamName) {
				// TODO Auto-generated method stub
				return null;
			}
			
		};
	  
	@Test
	public void testApplication() {
		
		final JRERemoteResourceProvider resourceProvider = new JRERemoteResourceProvider( 
                new ImmutableGnipAuthentication("foo", "bar")); 
        final GnipFacade gnip = new DefaultGnipFacade(resourceProvider, uriStrategy);
        
        final StreamNotificationAdapter<Activity> observer = new StreamNotificationAdapter<Activity>() {

			@Override
			public void notify(Activity activity, GnipStream stream) {
				logger.info("activity is : ", activity.getId());
				
			} 
	    };
		
        final GnipStream stream = gnip.createPowertrackStream(Activity.class).withAccount("test-account") 
                .withType("test-stream").withExecutorService(executor)
                .withObserver(observer) 
                .build(); 
	}

}
