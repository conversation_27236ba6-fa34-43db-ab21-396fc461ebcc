/**
 * 
 */
package com.birdeye.gnip.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Rules {
    private List<Rule> matching_rules;

    public List<Rule> getMatching_rules() {
        return matching_rules;
    }

    public void setMatching_rules(List<Rule> matching_rules) {
        this.matching_rules = matching_rules;
    }

    @Override
    public String toString() {
        return "Rules{" + "matching_rules=" + matching_rules + '}';
    }
}
