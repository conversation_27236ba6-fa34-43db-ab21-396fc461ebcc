package com.birdeye.gnip.util;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import com.birdeye.gnip.model.Entities;
import com.birdeye.gnip.model.Generator;
import com.birdeye.gnip.model.Location;
import com.birdeye.gnip.model.MediaUrls;
import com.birdeye.gnip.model.Rule;
import com.birdeye.gnip.model.Rules;
import com.birdeye.gnip.model.Tweet;
import com.birdeye.gnip.model.TweetActor;
import com.zaubersoftware.gnip4j.api.model.Activity;
import com.zaubersoftware.gnip4j.api.model.MatchingRules;
import com.zaubersoftware.gnip4j.api.model.TwitterEntities;

public class UtilsServiceImpl{
	
	private static final Logger		logger	= LoggerFactory.getLogger(UtilsServiceImpl.class);
	
	public static Tweet getTweetFromActivity(Activity activity) {
		Tweet tweet = new Tweet();
		tweet.setActor(getActor(activity.getActor()));
		tweet.setBody(activity.getBody());
		tweet.setGenerator(getGenerator(activity.getGenerator()));
		tweet.setGnip(getGnip(activity.getGnip()));
		tweet.setId(activity.getId());
		tweet.setLink(activity.getLink());
		tweet.setLocation(getActivityLocation(activity.getLocation()));
		tweet.setPostedTime(activity.getPostedTime());
		if(activity.getLongObject() != null) {
			logger.info("getTweetFromActivity: longObject.body {}", activity.getLongObject().getBody());
			logger.info("processing tweet id :: {} with long Object",tweet.getId());
			// BIRDEYE-76036: Fix for text getting clipped in case no. of chars >140
			if(StringUtils.isNotEmpty(activity.getLongObject().getBody())) {
				tweet.setBody(activity.getLongObject().getBody());
			}
			tweet.setTwitter_entities(getMediaUrls(activity.getLongObject().getTwitterExtendedEntities()));
		}else {
			logger.info("processing tweet id :: {} with extended entity",tweet.getId());
			tweet.setTwitter_entities(getMediaUrls(activity.getTwitterExtendedEntities()));
		}
		return tweet;
	}
	
	private static TweetActor getActor(com.zaubersoftware.gnip4j.api.model.Actor actor) {
		TweetActor act = new TweetActor();
		BeanUtils.copyProperties(actor, act);
		return act;
	}
	
	private static Location getActivityLocation(Activity.Location location) {
		Location loc = new Location();
		if(location != null) {
			BeanUtils.copyProperties(location, loc);
			return loc;
		}
		return null;
	}
	private static Generator getGenerator(com.zaubersoftware.gnip4j.api.model.Generator generator) {
		Generator gen = new Generator();
		BeanUtils.copyProperties(generator, gen);
		return gen;
	}
	
	private static Rules getGnip(com.zaubersoftware.gnip4j.api.model.Gnip activityGnip) {
		Rules gnip = new Rules();
		gnip.setMatching_rules(getMatchingRule(activityGnip.getMatchingRules()));
		return gnip;
	}
	
	private static List<Rule> getMatchingRule(List<MatchingRules> gnipRules){
		List<Rule> rules = new ArrayList<>();
		gnipRules.stream().forEach(gnipRule -> rules.add(getMatchingRule(gnipRule)));
		return rules;
	}
	
	private static Rule getMatchingRule(MatchingRules gnipRule){
		Rule rule = new Rule();
		BeanUtils.copyProperties(gnipRule, rule);
		return rule;
	}
	
	private static Entities getMediaUrls(TwitterEntities entity) {
		Entities entities = new Entities();
		if(entity != null) {
			entities.setMedia(getMedia(entity.getMediaUrls()));
			BeanUtils.copyProperties(entity, entities);
		}
		return entities;
	}
	
	private static List<MediaUrls> getMedia(List<com.zaubersoftware.gnip4j.api.model.MediaUrls> mediaUrls) {
		List<MediaUrls> urls = new ArrayList<>();
		mediaUrls.stream().forEach(media ->urls.add(getUrl(media)));
		return urls;
	}
	
	private static MediaUrls getUrl(com.zaubersoftware.gnip4j.api.model.MediaUrls media) {
		MediaUrls mediaUrl = new MediaUrls();
		mediaUrl.setId_str(media.getId());
		mediaUrl.setMedia_url(media.getMediaURL());
		mediaUrl.setMedia_url_https(media.getMediaUrlHttps());
		mediaUrl.setSource_status_id_str(media.getSourceStatusIdStr());
		mediaUrl.setType(media.getType());
		return mediaUrl;
	}
}
