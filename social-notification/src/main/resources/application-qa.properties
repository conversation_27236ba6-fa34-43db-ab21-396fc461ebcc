
# AWS-SM
aws.secret.enabled = true
aws.secret.region=us-west-1
aws.sm.bazaarify.secret.id=qa/mysql/bazaarify/master
aws.sm.social.secret.id=qa/mysql/app_social/master
aws.sm.gnip.secret.id=qa/social/secrets

#Kafka
kafka.server=qa-kafka.birdeye.internal:9092

#kafka EU
kafka.eu.server=qa-kafka.birdeye.internal:9092
kafka.eu.linger.ms.config=5
kafka.eu.max.in.flight.requests.per.connection=1
kafka.eu.retries.config=3
kafka.eu.request.timeout.ms.config=15000
kafka.eu.retry.backoff.ms.config=1000

#Kafka new
kafka.server.new=qa-kafka.birdeye.internal:9092
kafka.server.new.us=qa-kafka.birdeye.internal:9092


gmb.notification.topic=gmb-notification
fb.ratings.notification.topic= fb-ratings-notification
fb.feed.notification.topic=fb-feed-notification
fb.data.deletion.callback=fb-data-deletion-callback
fb.picture.notification.topic=fb-picture-notification
fb.engage.notification.topic = social-fb-engage-notification
receive.facebook= receive-facebook
receive.apple=receive-apple
#Kafka properties
kafka.linger.ms.config=5
kafka.max.in.flight.requests.per.connection=1
kafka.retries.config=3
kafka.request.timeout.ms.config=10000
kafka.retry.backoff.ms.config=1000
server.port=8080
gbm.notification.topic=gbm-notification-webhook
instagram.notification.topic=instagram-notification
instagram.story.insight.topic=instagram-story-insight
social.apple.msp.id = aacbd1e1-3c86-4aa0-b640-108acc9c5c95
social.apple.msp.secret=64hdQdkZBcDMThZfGRBCwMoZCcLIBf1ww4k7l5MdrxI=
brightdata.review.notification = brightdata-review-notification
instagram.comment.topic=social-instagram-comment
instagram.direct.message.topic=social-instagram-engage-direct-message
instagram.mention.topic=social-instagram-mention

linkedin.notification.topic=linkedin-notification
linkedin.secret.key=v7JFgLQhQmpRkgs2
linkedin-comment-save-topic=social-linkedin-create-comment-events
linkedin-mention-save-topic=social-linkedin-share-mention-events
linkedin-comment-edit-topic=social-linkedin-comment-edit-event
linkedin-comment-delete-topic=social-linkedin-comment-delete-event
linkedin-like-event-topic=social-linkedin-like-event

twitter-create-events-topic=social-twitter-create-events
twitter-favorite-events-topic=social-twitter-favorite-events
twitter-follow-events-topic=social-twitter-follow-events
twitter-block-events-topic=social-twitter-block-events
twitter-mute-events-topic=social-twitter-mute-events
twitter-user-events-topic=social-twitter-user-events
twitter-message-events-topic=social-twitter-direct-message-events
twitter-delete-events-topic=social-twitter-delete-events

yelp.notification.topic=social-reviews-yelp-integration

tiktok.publish.notification.topic=tiktok-publish-notification
tiktok.comment.notification.topic=tiktok-comment-notification


whatsapp.secret.key=birdeye-whatsapp-secret-token

## social socialapp/@admin99008
social.datasource.jdbcUrl=**********************************************************************************************
social.datasource.driver-class-name=com.mysql.jdbc.Driver
social.datasource.username=app_social
social.datasource.password=Admin##764211342111
social.datasource.poolName=social-Hikari-Pool
social.datasource.maximumPoolSize=50
social.datasource.minimumIdle=5
social.datasource.maxLifetime=2000000
social.datasource.connectionTimeout=60000
social.datasource.idleTimeout=30000
social.datasource.test-while-idle = true
social.datasource.test-on-borrow = true
social.datasource.time-between-eviction-runs-millis = 60000
social.datasource.validation-query = SELECT 1
social.datasource.validation-query-timeout = 3
social.datasource.pool-prepared-statements=true
social.datasource.max-open-prepared-statements=250
social.datasource.max-sql-prepared-statements=2048
social.datasource.use-server-prepared-statements=true
social.datasource.remove-abandoned = true
social.datasource.remove-abandoned-timeout = 120

#Redis Configs
redis.hostname=***********
redis.port=6379
redis.password=foobird
redis.database=10
redis.timeout=3000
redis.ssl.enabled=false