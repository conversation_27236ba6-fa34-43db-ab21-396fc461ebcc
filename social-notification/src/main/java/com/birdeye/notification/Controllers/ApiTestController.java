package com.birdeye.notification.Controllers;


import com.birdeye.notification.service.RoutingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.ForkJoinPool;

@RestController
@RequestMapping("/social/notification/test")
public class ApiTestController {

    private static final Logger logger = LoggerFactory.getLogger(ApiTestController.class);

    @Autowired
    private RoutingService routingService;

    @GetMapping("/db/test")
    public ResponseEntity<Void> checkDBConnection(@RequestParam("pageId") String pageId, @RequestParam("srcId") Integer srcId){
        logger.info("controller key: {}", pageId);
        String value = routingService.getRegionForPage(pageId, srcId);
        logger.info("controller value: {}", value);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/parallel/count")
    public ResponseEntity<Void> checkParallelCount(){
        int parallelism = ForkJoinPool.getCommonPoolParallelism();
        logger.info("Parallelism level: {}", parallelism);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
