package com.birdeye.notification.Controllers;

import com.birdeye.notification.model.WhatsappEventRequest;
import com.birdeye.notification.service.IWhatsappNotificationService;
import com.birdeye.notification.utils.JSONUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(path = "/whatsapp/notification")
public class WhatsappNotificationController {

    private static final Logger LOG = LoggerFactory.getLogger(FbNotificationController.class);

    //@Value("${whatsapp.secret.key}")
    private static final String key="birdeye-whatsapp-secret-token";

    @Autowired
    private IWhatsappNotificationService whatsappNotificationService;

    @PostMapping(path = "/whatsapp",consumes = "application/json",produces = "application/json")
    public ResponseEntity<String> receiveWhatsappMessage(@RequestBody Object request){
        LOG.info("Whatsapp raw notification received with notification payload {}", request);
        whatsappNotificationService.processWhatsappEvent(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/whatsapp",produces = {"application/x-www-form-urlencoded","application/xml", "application/json", "text/html"})
    public ResponseEntity<String> whatsappHandshake(@RequestParam("hub.mode") String mode, @RequestParam("hub.challenge") String challenge,
                                                    @RequestParam("hub.verify_token") String verify_token) {
        LOG.info("whatsapp handshake request received mode {} , challenge {} , verify_token {}", mode, challenge, verify_token);
        try {
            if ("subscribe".equalsIgnoreCase(mode) && key.equalsIgnoreCase(verify_token)) {
                return new ResponseEntity<>(challenge, HttpStatus.OK);
            }
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception ex) {
            LOG.error("Exception occurred in receiveWhatsappEvent {}", ex);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }


}