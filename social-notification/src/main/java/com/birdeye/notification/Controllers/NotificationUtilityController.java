package com.birdeye.notification.Controllers;


import com.birdeye.notification.model.PageSyncEventRequest;
import com.birdeye.notification.service.RoutingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/notification")
public class NotificationUtilityController {

    private static final Logger logger = LoggerFactory.getLogger(ApiTestController.class);

    @Autowired
    private RoutingService routingService;

    @PostMapping("/sync/page-region")
    public ResponseEntity<Void> syncPageRegion(@RequestBody PageSyncEventRequest pageSyncEventRequest){
        logger.info("Received request to sync page for payload: {}", pageSyncEventRequest);
        routingService.saveOrUpdate(pageSyncEventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
