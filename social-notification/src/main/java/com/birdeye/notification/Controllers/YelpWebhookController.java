package com.birdeye.notification.Controllers;

import com.birdeye.notification.model.FacebookEventRequest;
import com.birdeye.notification.model.YelpPayloadRequest;
import com.birdeye.notification.service.YelpEventHandler;
import com.birdeye.notification.service.YelpEventHandlerImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/yelp")
public class YelpWebhookController {

    private final Logger logger = LoggerFactory.getLogger(YelpEventHandlerImpl.class);

    @Autowired
    private YelpEventHandler yelpEventHandler;

    // Verification Endpoint (GET Request)
    @GetMapping("/notification")
    public ResponseEntity<Map<String, String>> verifyWebhook(@RequestParam("verification") String verificationToken) {
        // Respond with the verification token in JSON format
        Map<String, String> response = new HashMap<>();
        response.put("verification", verificationToken);
        return ResponseEntity.ok(response);
    }

    @PostMapping(path = "/notification", consumes = "application/json", produces = "application/json")
    public ResponseEntity<String> receiveYelpEvent(@RequestBody Object payload) {
        try {
            logger.info("Request received to process yelp event for payload id {}", payload);

            ObjectMapper mapper = new ObjectMapper();
            // Convert the payload to a JSON string
            String jsonString = mapper.writeValueAsString(payload);

            // Deserialize the JSON string into YelpPayloadRequest
            YelpPayloadRequest request = mapper.readValue(jsonString, YelpPayloadRequest.class);

            yelpEventHandler.processYelpPayload(request);
            return ResponseEntity.ok("Event processed successfully");
        } catch (Exception ex) {
            logger.error("Exception occurred in receiveYelpEvent {} {}", payload, ex);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
