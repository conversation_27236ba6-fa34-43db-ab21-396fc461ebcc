package com.birdeye.notification.Controllers;

import com.birdeye.notification.service.ILinkedInNotificationService;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import org.json.JSONObject;
import org.apache.commons.codec.binary.Hex;
import java.io.UnsupportedEncodingException;

@RestController
@RequestMapping(path = "/notification")
public class LinkedinNotificationController {

    private static final Logger LOG = LoggerFactory.getLogger(LinkedinNotificationController.class);

    @Value("${linkedin.secret.key}")
    private String key;

    @Autowired
    private ILinkedInNotificationService linkedInNotificationService;

    @PostMapping(path = "/linkedin",consumes = "application/json",produces = "application/json")
    public ResponseEntity<String> receiveLinkedinMessage(@RequestHeader("X-LI-Signature") String signature, @RequestBody byte[] linkedinEventRequest) throws UnsupportedEncodingException, JsonProcessingException {

        LOG.info("linkedin event received for object in byte array {} and signature {} ",linkedinEventRequest,signature);
       linkedInNotificationService.processLinkedinEvent(signature,linkedinEventRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(path = "/linkedin",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> linkedinHandshake(@RequestParam("challengeCode") String challengeCode,@RequestParam("applicationId") String applicationId){
        LOG.info("linkedin handshake request received challenge {} and applicationId {}",challengeCode,applicationId);
        JSONObject json = new JSONObject();
        try {
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");
            sha256_HMAC.init(secret_key);

            String encodedData=  Hex.encodeHexString(sha256_HMAC.doFinal(challengeCode.getBytes("UTF-8")));
            LOG.info("linkedin encodedData data {}",encodedData);

				json.put("challengeCode", challengeCode);
				json.put("challengeResponse", encodedData);
            LOG.info("linkedin handshake json {}",json);
        } catch (Exception e) {
        	LOG.error("Error while subscribing to the linkedin webhook event ", e);
        }
        return new ResponseEntity<>(json.toString(),HttpStatus.OK);
    }

}
