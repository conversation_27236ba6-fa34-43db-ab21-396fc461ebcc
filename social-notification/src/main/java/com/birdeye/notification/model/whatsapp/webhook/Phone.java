package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Phone.
 */
public class Phone {

        @JsonProperty("phone") String phone;

        @JsonProperty("wa_id") String waId;

        @JsonProperty("type") String type;

        public String getPhone() {
                return phone;
        }

        public void setPhone(String phone) {
                this.phone = phone;
        }

        public String getWaId() {
                return waId;
        }

        public void setWaId(String waId) {
                this.waId = waId;
        }

        public String getType() {
                return type;
        }

        public void setType(String type) {
                this.type = type;
        }
}