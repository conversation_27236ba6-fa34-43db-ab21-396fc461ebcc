package com.birdeye.notification.model.whatsapp.webhook;

import com.birdeye.notification.model.whatsapp.webhook.type.EventType;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * The type Value.
 *
 * @param metadata                The metadata about your phone number.
 * @param messagingProduct        The messaging service used for Webhooks. For WhatsApp messages, this value needs to be set to “whatsapp”.
 * @param messages                An array of message objects. Added to Webhooks for incoming message notifications.
 * @param contacts                An array of contacts
 * @param statuses                An array of message status objects. Added to Webhooks for message status update.
 * @param decision                Used if a decision about accounts or phone numbers has been made. (APPROVED or REJECTED)
 * @param displayPhoneNumber      Includes the display phone number of the business  account that triggered the notification.
 * @param event                   Used when an event happened in a specific WABA. See {@link EventType}
 * @param messageTemplateId       The message template ID
 * @param messageTemplateLanguage The message template language
 * @param messageTemplateName     The message template name
 * @param phoneNumber             the phone number
 * @param reason                  reason
 * @param rejectionReason         If a request was rejected, this field displays the reason for that rejection.
 * @param requestedVerifiedName   This field displays the name that was sent to be verified.
 * @param messageTemplateElement   This field displays the text of the template edited.
 * @param messageTemplateFooter   This field displays the footer of the template edited.
 *
 */
public class Value{

        @JsonProperty("metadata") Metadata metadata;

        @JsonProperty("messaging_product") String messagingProduct;

        @JsonProperty("messages") List<Message> messages;

        @JsonProperty("contacts") List<Contact> contacts;

        @JsonProperty("statuses") List<Status> statuses;

        @JsonProperty("event")
        EventType event;

        @JsonProperty("phone_number") String phoneNumber;

        @JsonProperty("message_template_id") String messageTemplateId;

        @JsonProperty("message_template_name") String messageTemplateName;

        @JsonProperty("message_template_language") String messageTemplateLanguage;
        @JsonProperty("message_template_element") String messageTemplateElement;
        @JsonProperty("message_template_footer") String messageTemplateFooter;

        @JsonProperty("reason") String reason;

        @JsonProperty("display_phone_number") String displayPhoneNumber;
        @JsonProperty("decision") String decision;

        @JsonProperty("requested_verified_name") String requestedVerifiedName;

        @JsonProperty("rejection_reason") Object rejectionReason;

        @JsonProperty("disable_info") DisableInfo disableInfo;

        @JsonProperty("current_limit") String currentLimit;

        @JsonProperty("ban_info") BanInfo banInfo;

        @JsonProperty("restriction_info") List<RestrictionInfo> restrictionInfo;

        public Metadata getMetadata() {
                return metadata;
        }

        public void setMetadata(Metadata metadata) {
                this.metadata = metadata;
        }

        public String getMessagingProduct() {
                return messagingProduct;
        }

        public void setMessagingProduct(String messagingProduct) {
                this.messagingProduct = messagingProduct;
        }

        public List<Message> getMessages() {
                return messages;
        }

        public void setMessages(List<Message> messages) {
                this.messages = messages;
        }

        public List<Contact> getContacts() {
                return contacts;
        }

        public void setContacts(List<Contact> contacts) {
                this.contacts = contacts;
        }

        public List<Status> getStatuses() {
                return statuses;
        }

        public void setStatuses(List<Status> statuses) {
                this.statuses = statuses;
        }

        public EventType getEvent() {
                return event;
        }

        public void setEvent(EventType event) {
                this.event = event;
        }

        public String getPhoneNumber() {
                return phoneNumber;
        }

        public void setPhoneNumber(String phoneNumber) {
                this.phoneNumber = phoneNumber;
        }

        public String getMessageTemplateId() {
                return messageTemplateId;
        }

        public void setMessageTemplateId(String messageTemplateId) {
                this.messageTemplateId = messageTemplateId;
        }

        public String getMessageTemplateName() {
                return messageTemplateName;
        }

        public void setMessageTemplateName(String messageTemplateName) {
                this.messageTemplateName = messageTemplateName;
        }

        public String getMessageTemplateLanguage() {
                return messageTemplateLanguage;
        }

        public void setMessageTemplateLanguage(String messageTemplateLanguage) {
                this.messageTemplateLanguage = messageTemplateLanguage;
        }

        public String getReason() {
                return reason;
        }

        public void setReason(String reason) {
                this.reason = reason;
        }

        public String getDisplayPhoneNumber() {
                return displayPhoneNumber;
        }

        public void setDisplayPhoneNumber(String displayPhoneNumber) {
                this.displayPhoneNumber = displayPhoneNumber;
        }

        public String getDecision() {
                return decision;
        }

        public void setDecision(String decision) {
                this.decision = decision;
        }

        public String getRequestedVerifiedName() {
                return requestedVerifiedName;
        }

        public void setRequestedVerifiedName(String requestedVerifiedName) {
                this.requestedVerifiedName = requestedVerifiedName;
        }

        public Object getRejectionReason() {
                return rejectionReason;
        }

        public void setRejectionReason(Object rejectionReason) {
                this.rejectionReason = rejectionReason;
        }

        public DisableInfo getDisableInfo() {
                return disableInfo;
        }

        public void setDisableInfo(DisableInfo disableInfo) {
                this.disableInfo = disableInfo;
        }

        public String getCurrentLimit() {
                return currentLimit;
        }

        public void setCurrentLimit(String currentLimit) {
                this.currentLimit = currentLimit;
        }

        public BanInfo getBanInfo() {
                return banInfo;
        }

        public void setBanInfo(BanInfo banInfo) {
                this.banInfo = banInfo;
        }

        public List<RestrictionInfo> getRestrictionInfo() {
                return restrictionInfo;
        }

        public void setRestrictionInfo(List<RestrictionInfo> restrictionInfo) {
                this.restrictionInfo = restrictionInfo;
        }

        public String getMessageTemplateElement() {
                return messageTemplateElement;
        }

        public void setMessageTemplateElement(String messageTemplateElement) {
                this.messageTemplateElement = messageTemplateElement;
        }

        public String getMessageTemplateFooter() {
                return messageTemplateFooter;
        }

        public void setMessageTemplateFooter(String messageTemplateFooter) {
                this.messageTemplateFooter = messageTemplateFooter;
        }
}