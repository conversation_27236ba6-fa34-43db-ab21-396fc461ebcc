package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbPost {
    private String status_type;
    private Boolean is_published;
    private String updated_time;
    private String permalink_url;
    private String promotion_status;
    private String id;

    public String getStatus_type() {
        return status_type;
    }

    public void setStatus_type(String status_type) {
        this.status_type = status_type;
    }

    public Boolean getIs_published() {
        return is_published;
    }

    public void setIs_published(Boolean is_published) {
        this.is_published = is_published;
    }

    public String getUpdated_time() {
        return updated_time;
    }

    public void setUpdated_time(String updated_time) {
        this.updated_time = updated_time;
    }

    public String getPermalink_url() {
        return permalink_url;
    }

    public void setPermalink_url(String permalink_url) {
        this.permalink_url = permalink_url;
    }

    public String getPromotion_status() {
        return promotion_status;
    }

    public void setPromotion_status(String promotion_status) {
        this.promotion_status = promotion_status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "FbPost{" +
                "status_type='" + status_type + '\'' +
                ", is_published=" + is_published +
                ", updated_time='" + updated_time + '\'' +
                ", permalink_url='" + permalink_url + '\'' +
                ", promotion_status='" + promotion_status + '\'' +
                ", id='" + id + '\'' +
                '}';
    }
}
