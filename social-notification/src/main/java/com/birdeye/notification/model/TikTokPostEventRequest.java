package com.birdeye.notification.model;

import com.birdeye.notification.utils.TikTokPublishContentDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TikTokPostEventRequest {

    @JsonProperty("client_key")
    private String clientKey;

    private String event;

    @JsonProperty("create_time")
    private Long createTime;

    @JsonProperty("user_openid")
    private String userOpenid;

    @JsonProperty("content")
    @JsonDeserialize(using = TikTokPublishContentDeserializer.class)
    private TikTokPublishContent content;

    public String getClientKey() {
        return clientKey;
    }

    public void setClientKey(String clientKey) {
        this.clientKey = clientKey;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUserOpenid() {
        return userOpenid;
    }

    public void setUserOpenid(String userOpenid) {
        this.userOpenid = userOpenid;
    }

    public TikTokPublishContent getContent() {
        return content;
    }

    public void setContent(TikTokPublishContent content) {
        this.content = content;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TikTokPublishContent {

        @JsonProperty("publish_id")
        private String publishId;

        @JsonProperty("publish_type")
        private String publishType;

        @JsonProperty("post_id")
        private String postId;

        public String getPublishId() {
            return publishId;
        }

        public void setPublishId(String publishId) {
            this.publishId = publishId;
        }

        public String getPublishType() {
            return publishType;
        }

        public void setPublishType(String publishType) {
            this.publishType = publishType;
        }

        public String getPostId() {
            return postId;
        }

        public void setPostId(String postId) {
            this.postId = postId;
        }
    }
}