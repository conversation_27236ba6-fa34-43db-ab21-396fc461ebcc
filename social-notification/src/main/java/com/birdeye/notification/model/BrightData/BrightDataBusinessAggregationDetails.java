package com.birdeye.notification.model.BrightData;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BrightDataBusinessAggregationDetails {
    private long requestDate;
    private String aggregationType;
    private String businessAggregationId;
    private String url;
    private String sourceId;
    private String reviewMinDate;
    private long businessInfoId;
    private long aggInfoId;
    private long businessId;
    private long reviewTillDate;
    private long batchSize;
    private boolean proxy;
    private boolean freeProdEnv;
    private String aggregationFrequencyType;
    private boolean fbCrawlEnabled;
    private String timeout;

    public String getAggregationType() {
        return aggregationType;
    }

    public void setAggregationType(String aggregationType) {
        this.aggregationType = aggregationType;
    }


    public long getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(long requestDate) {
        this.requestDate = requestDate;
    }

    @JsonProperty("business_aggregation_id")
    public String getBusinessAggregationId() {
        return businessAggregationId;
    }

    public void setBusinessAggregationId(String businessAggregationId) {
        this.businessAggregationId = businessAggregationId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    @JsonProperty("review_min_date")
    public String getReviewMinDate() {
        return reviewMinDate;
    }

    public void setReviewMinDate(String reviewMinDate) {
        this.reviewMinDate = reviewMinDate;
    }

    public long getBusinessInfoId() {
        return businessInfoId;
    }

    public void setBusinessInfoId(long businessInfoId) {
        this.businessInfoId = businessInfoId;
    }

    public long getAggInfoId() {
        return aggInfoId;
    }

    public void setAggInfoId(long aggInfoId) {
        this.aggInfoId = aggInfoId;
    }

    public long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(long businessId) {
        this.businessId = businessId;
    }

    public long getReviewTillDate() {
        return reviewTillDate;
    }

    public void setReviewTillDate(long reviewTillDate) {
        this.reviewTillDate = reviewTillDate;
    }

    public long getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(long batchSize) {
        this.batchSize = batchSize;
    }

    public boolean isProxy() {
        return proxy;
    }

    public void setProxy(boolean proxy) {
        this.proxy = proxy;
    }

    public boolean isFreeProdEnv() {
        return freeProdEnv;
    }

    public void setFreeProdEnv(boolean freeProdEnv) {
        this.freeProdEnv = freeProdEnv;
    }

    public String getAggregationFrequencyType() {
        return aggregationFrequencyType;
    }

    public void setAggregationFrequencyType(String aggregationFrequencyType) {
        this.aggregationFrequencyType = aggregationFrequencyType;
    }

    public boolean isFbCrawlEnabled() {
        return fbCrawlEnabled;
    }

    public void setFbCrawlEnabled(boolean fbCrawlEnabled) {
        this.fbCrawlEnabled = fbCrawlEnabled;
    }

    public String getTimeout() { return timeout; }

    public void setTimeout(String timeout) { this.timeout = timeout; }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("BrightDataBusinessAggregationDetails{");
        sb.append("requestDate=").append(requestDate);
        sb.append(", aggregationType='").append(aggregationType).append('\'');
        sb.append(", businessAggregationId='").append(businessAggregationId).append('\'');
        sb.append(", url='").append(url).append('\'');
        sb.append(", sourceId='").append(sourceId).append('\'');
        sb.append(", reviewMinDate='").append(reviewMinDate).append('\'');
        sb.append(", businessInfoId=").append(businessInfoId);
        sb.append(", aggInfoId=").append(aggInfoId);
        sb.append(", businessId=").append(businessId);
        sb.append(", reviewTillDate=").append(reviewTillDate);
        sb.append(", batchSize=").append(batchSize);
        sb.append(", proxy=").append(proxy);
        sb.append(", freeProdEnv=").append(freeProdEnv);
        sb.append(", aggregationFrequencyType='").append(aggregationFrequencyType).append('\'');
        sb.append(", fbCrawlEnabled=").append(fbCrawlEnabled);
        sb.append(", timeout='").append(timeout).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
