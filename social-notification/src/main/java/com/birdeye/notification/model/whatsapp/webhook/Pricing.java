package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Pricing.
 *
 * @param pricingModel Type of pricing model being used. Current supported values are:<ul>                     <li>"CBP" (conversation-based pricing): See Conversation-Based Pricing for rates based on recipient country.</li>                     <li>"NBP" (notification-based pricing): Notifications are also known as Template Messages (click here for details on pricing).</li>                     </ul>                     This pricing model will be deprecated in a future release early 2022.
 */
public class Pricing {

        @JsonProperty("pricing_model") String pricingModel;

        @JsonProperty("category") String category;

        @JsonProperty("billable") boolean billable;

        public String getPricingModel() {
                return pricingModel;
        }

        public void setPricingModel(String pricingModel) {
                this.pricingModel = pricingModel;
        }

        public String getCategory() {
                return category;
        }

        public void setCategory(String category) {
                this.category = category;
        }

        public boolean isBillable() {
                return billable;
        }

        public void setBillable(boolean billable) {
                this.billable = billable;
        }
}