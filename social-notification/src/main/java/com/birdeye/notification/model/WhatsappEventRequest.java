package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WhatsappEventRequest implements Serializable {

    private String object;
    private List<com.birdeye.notification.model.whatsapp.webhook.Entry> entry;
    private String event;


    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public List<com.birdeye.notification.model.whatsapp.webhook.Entry> getEntry() {
        return entry;
    }

    public void setEntry(List<com.birdeye.notification.model.whatsapp.webhook.Entry> entry) {
        this.entry = entry;
    }

}