package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Conversation.
 *
 * @param expirationTimestamp The timestamp when the current ongoing conversation expires. This field is not present in all Webhook types.
 * @param origin              Describes where the conversation originated from. See {@link Origin} object for more information.
 * @param id                  The ID of the conversation the given status notification belongs to.
 */
public class Conversation {

        @JsonProperty("expiration_timestamp") String expirationTimestamp;

        @JsonProperty("origin") Origin origin;

        @JsonProperty("id") String id;

        public String getExpirationTimestamp() {
                return expirationTimestamp;
        }

        public void setExpirationTimestamp(String expirationTimestamp) {
                this.expirationTimestamp = expirationTimestamp;
        }

        public Origin getOrigin() {
                return origin;
        }

        public void setOrigin(Origin origin) {
                this.origin = origin;
        }

        public String getId() {
                return id;
        }

        public void setId(String id) {
                this.id = id;
        }
}