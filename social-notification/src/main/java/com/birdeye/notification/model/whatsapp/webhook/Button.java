package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Button.
 *
 * @param payload The developer-defined payload for the button when a business account sends interactive messages.
 * @param text    The button text
 */
public class Button{

        @JsonProperty("payload") String payload;

        @JsonProperty("text") String text;

        public String getPayload() {
                return payload;
        }

        public void setPayload(String payload) {
                this.payload = payload;
        }

        public String getText() {
                return text;
        }

        public void setText(String text) {
                this.text = text;
        }
}