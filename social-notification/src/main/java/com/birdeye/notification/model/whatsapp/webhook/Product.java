package com.birdeye.notification.model.whatsapp.webhook;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Product.
 */
public class Product{

        @JsonProperty("quantity") String quantity;

        @JsonProperty("product_retailer_id") String productRetailerId;

        @JsonProperty("item_price") String itemPrice;

        @JsonProperty("currency") String currency;

        public String getQuantity() {
                return quantity;
        }

        public void setQuantity(String quantity) {
                this.quantity = quantity;
        }

        public String getProductRetailerId() {
                return productRetailerId;
        }

        public void setProductRetailerId(String productRetailerId) {
                this.productRetailerId = productRetailerId;
        }

        public String getItemPrice() {
                return itemPrice;
        }

        public void setItemPrice(String itemPrice) {
                this.itemPrice = itemPrice;
        }

        public String getCurrency() {
                return currency;
        }

        public void setCurrency(String currency) {
                this.currency = currency;
        }
}

