package com.birdeye.notification.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramEntry implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private Long id; // ig id of business
    private Long time;
    private List<InstagramMessaging> messaging;
	private List<InstagramChanges> changes;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

	public List<InstagramMessaging> getMessaging() {
		return messaging;
	}

	public void setMessaging(List<InstagramMessaging> messaging) {
		this.messaging = messaging;
	}

	public List<InstagramChanges> getChanges() {
		return changes;
	}

	public void setChanges(List<InstagramChanges> changes) {
		this.changes = changes;
	}

	@java.lang.Override
	public java.lang.String toString() {
		return "InstagramEntry{" +
				"id=" + id +
				", time=" + time +
				", messaging=" + messaging +
				", changes=" + changes +
				'}';
	}

}
