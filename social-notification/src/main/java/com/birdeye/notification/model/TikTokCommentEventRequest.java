package com.birdeye.notification.model;

import com.birdeye.notification.utils.TikTokCommentContentDeserializer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TikTokCommentEventRequest {

    @JsonProperty("client_key")
    private String clientKey;

    private String event;

    @JsonProperty("create_time")
    private Long createTime;

    @JsonProperty("user_openid")
    private String userOpenid;

    @JsonProperty("content")
    @JsonDeserialize(using = TikTokCommentContentDeserializer.class)
    private TikTokCommentContent content;

    public String getClientKey() {
        return clientKey;
    }

    public void setClientKey(String clientKey) {
        this.clientKey = clientKey;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public String getUserOpenid() {
        return userOpenid;
    }

    public void setUserOpenid(String userOpenid) {
        this.userOpenid = userOpenid;
    }

    public TikTokCommentContent getContent() {
        return content;
    }

    public void setContent(TikTokCommentContent content) {
        this.content = content;
    }

    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TikTokCommentContent {

        @JsonProperty("comment_id")
        private String commentId;

        @JsonProperty("video_id")
        private String videoId;

        @JsonProperty("parent_comment_id")
        private String parentCommentId;

        @JsonProperty("comment_type")
        private String commentType;

        @JsonProperty("comment_action")
        private String commentAction;

        @JsonProperty("unique_identifier")
        private String uniqueIdentifier;

        @JsonProperty("timestamp")
        private Long timestamp;

        public String getCommentId() {
            return commentId;
        }

        public void setCommentId(String commentId) {
            this.commentId = commentId;
        }

        public String getVideoId() {
            return videoId;
        }

        public void setVideoId(String videoId) {
            this.videoId = videoId;
        }

        public String getParentCommentId() {
            return parentCommentId;
        }

        public void setParentCommentId(String parentCommentId) {
            this.parentCommentId = parentCommentId;
        }

        public String getCommentType() {
            return commentType;
        }

        public void setCommentType(String commentType) {
            this.commentType = commentType;
        }

        public String getCommentAction() {
            return commentAction;
        }

        public void setCommentAction(String commentAction) {
            this.commentAction = commentAction;
        }

        public String getUniqueIdentifier() {
            return uniqueIdentifier;
        }

        public void setUniqueIdentifier(String uniqueIdentifier) {
            this.uniqueIdentifier = uniqueIdentifier;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }
    }

    @Override
    public String toString() {
        return "TikTokCommentEventRequest{" +
                "clientKey='" + clientKey + '\'' +
                ", event='" + event + '\'' +
                ", createTime=" + createTime +
                ", userOpenid='" + userOpenid + '\'' +
                ", content=" + content +
                '}';
    }
}