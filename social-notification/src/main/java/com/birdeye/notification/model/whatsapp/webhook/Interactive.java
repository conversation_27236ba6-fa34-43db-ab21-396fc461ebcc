package com.birdeye.notification.model.whatsapp.webhook;

import com.birdeye.notification.model.whatsapp.webhook.ButtonReply;
import com.birdeye.notification.model.whatsapp.webhook.ListReply;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * The type Interactive.
 *
 * @param listReply   Used on Webhooks related to List Messages                    Contains a list {@link ListReply} object.
 * @param type        Contains the type of interactive object. Supported options are:<ul>                    <li>button_reply: for responses of Reply Buttons.</li>                    <li>list_reply: for responses to List Messages and other interactive objects.</li></ul>
 * @param buttonReply Used on Webhooks related to Reply Buttons.                    Contains a {@link ButtonReply} reply object.
 */
public class Interactive {

        @JsonProperty("list_reply")
        ListReply listReply;

        @JsonProperty("type") String type;

        @JsonProperty("button_reply")
        ButtonReply buttonReply;

        public ListReply getListReply() {
                return listReply;
        }

        public void setListReply(ListReply listReply) {
                this.listReply = listReply;
        }

        public String getType() {
                return type;
        }

        public void setType(String type) {
                this.type = type;
        }

        public ButtonReply getButtonReply() {
                return buttonReply;
        }

        public void setButtonReply(ButtonReply buttonReply) {
                this.buttonReply = buttonReply;
        }
}