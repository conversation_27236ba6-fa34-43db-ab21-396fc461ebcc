package com.birdeye.notification.model.BrightData;

import java.io.Serializable;
import java.util.List;

/*
* The class used for handling error response from Brightdata
* */
public class BrightDataErrorResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    private BrightDataBusinessAggregationDetails input;

    private List<BrightDataReviewErrorData> lines;

    public List<BrightDataReviewErrorData> getLines() {
        return lines;
    }

    public void setLines(List<BrightDataReviewErrorData> lines) {
        this.lines = lines;
    }

    public BrightDataBusinessAggregationDetails getInput() {
        return input;
    }

    public void setInput(BrightDataBusinessAggregationDetails input) {
        this.input = input;
    }
}