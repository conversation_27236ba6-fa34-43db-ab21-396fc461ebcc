package com.birdeye.notification.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramMessage implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String mid; // <MESSAGE_ID>
    private String text; // MESSAGE_CONTENT
    private List<InstagramAttachments> attachments;
    private Boolean is_echo=false;
    private InstagramQuickReply quick_reply;
    private String is_unsupported; //not included if message is supported
    private String is_deleted; // if true, mid of deleted review will be sent
    private InstagramReplyTo reply_to; // will be received in case of story reply or inline reply
    
    
    private InstagramReferral referral;

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<InstagramAttachments> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<InstagramAttachments> attachments) {
        this.attachments = attachments;
    }

    public Boolean getIs_echo() {
        return is_echo;
    }

    public void setIs_echo(Boolean is_echo) {
        this.is_echo = is_echo;
    }

	public InstagramReferral getReferral() {
		return referral;
	}

	public void setReferral(InstagramReferral referral) {
		this.referral = referral;
	}

	public InstagramQuickReply getQuick_reply() {
		return quick_reply;
	}

	public void setQuick_reply(InstagramQuickReply quick_reply) {
		this.quick_reply = quick_reply;
	}

	public String getIs_unsupported() {
		return is_unsupported;
	}

	public void setIs_unsupported(String is_unsupported) {
		this.is_unsupported = is_unsupported;
	}

	public String getIs_deleted() {
		return is_deleted;
	}

	public void setIs_deleted(String is_deleted) {
		this.is_deleted = is_deleted;
	}
	
	public InstagramReplyTo getReply_to() {
		return reply_to;
	}

	public void setReply_to(InstagramReplyTo reply_to) {
		this.reply_to = reply_to;
	}

	@Override
	public String toString() {
		return "InstagramMessage [mid=" + mid + ", text=" + text + ", attachments=" + attachments + ", is_echo="
				+ is_echo + ", quick_reply=" + quick_reply + ", is_unsupported=" + is_unsupported + ", is_deleted="
				+ is_deleted + ", reply_to=" + reply_to + ", referral=" + referral + "]";
	}

}
