package com.birdeye.notification.model.BrightData;

import java.util.List;

/* This class hold data in case error is thrown from BrightData. Require different class because BrightDataReviewData
and this class both have same key error in their field.*/
public class BrightDataReviewErrorData {

        private long inorganicReviewCount;
        private long invalidReviewCount;
        private long duplicateReviewCount;
        private String aggregationStatus;
        private String servedByIp;
        private String servedByPort;
        private long timeTaken;
        private long originalReviewCount;
        private long reviewReturnedCount;
        private String startDate;
        private String endDate;
        private double overallRatingOnSource;
        private long reviewCountOnSource;
        private String reviewAggregationId;
        private long businessInfoId;
        private long aggInfoId;
        private long businessId;
        private boolean freeProdEnv;
        private String aggregationFrequencyType;
        private List<BrightDataReviewsList> data;
        private Error error;

        public long getInorganicReviewCount() {
            return inorganicReviewCount;
        }

        public void setInorganicReviewCount(long inorganicReviewCount) {
            this.inorganicReviewCount = inorganicReviewCount;
        }

        public long getInvalidReviewCount() {
            return invalidReviewCount;
        }

        public void setInvalidReviewCount(long invalidReviewCount) {
            this.invalidReviewCount = invalidReviewCount;
        }

        public long getDuplicateReviewCount() {
            return duplicateReviewCount;
        }

        public void setDuplicateReviewCount(long duplicateReviewCount) {
            this.duplicateReviewCount = duplicateReviewCount;
        }

        public String getAggregationStatus() {
            return aggregationStatus;
        }

        public void setAggregationStatus(String aggregationStatus) {
            this.aggregationStatus = aggregationStatus;
        }

        public String getServedByIp() {
            return servedByIp;
        }

        public void setServedByIp(String servedByIp) {
            this.servedByIp = servedByIp;
        }

        public String getServedByPort() {
            return servedByPort;
        }

        public void setServedByPort(String servedByPort) {
            this.servedByPort = servedByPort;
        }

        public long getTimeTaken() {
            return timeTaken;
        }

        public void setTimeTaken(long timeTaken) {
            this.timeTaken = timeTaken;
        }

        public Error getError() {
        return error;
    }

        public void setError(Error error) {
        this.error = error;
    }

        public long getOriginalReviewCount() {
            return originalReviewCount;
        }

        public void setOriginalReviewCount(long originalReviewCount) {
            this.originalReviewCount = originalReviewCount;
        }

        public long getReviewReturnedCount() {
            return reviewReturnedCount;
        }

        public void setReviewReturnedCount(long reviewReturnedCount) {
            this.reviewReturnedCount = reviewReturnedCount;
        }

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public double getOverallRatingOnSource() {
            return overallRatingOnSource;
        }

        public void setOverallRatingOnSource(double overallRatingOnSource) {
            this.overallRatingOnSource = overallRatingOnSource;
        }

        public long getReviewCountOnSource() {
            return reviewCountOnSource;
        }

        public void setReviewCountOnSource(long reviewCountOnSource) {
            this.reviewCountOnSource = reviewCountOnSource;
        }

        public String getReviewAggregationId() {
            return reviewAggregationId;
        }

        public void setReviewAggregationId(String reviewAggregationId) {
            this.reviewAggregationId = reviewAggregationId;
        }

        public long getBusinessInfoId() {
            return businessInfoId;
        }

        public void setBusinessInfoId(long businessInfoId) {
            this.businessInfoId = businessInfoId;
        }

        public long getAggInfoId() {
            return aggInfoId;
        }

        public void setAggInfoId(long aggInfoId) {
            this.aggInfoId = aggInfoId;
        }

        public long getBusinessId() {
            return businessId;
        }

        public void setBusinessId(long businessId) {
            this.businessId = businessId;
        }

        public boolean isFreeProdEnv() {
            return freeProdEnv;
        }

        public void setFreeProdEnv(boolean freeProdEnv) {
            this.freeProdEnv = freeProdEnv;
        }

        public String getAggregationFrequencyType() {
            return aggregationFrequencyType;
        }

        public void setAggregationFrequencyType(String aggregationFrequencyType) {
            this.aggregationFrequencyType = aggregationFrequencyType;
        }

        public List<BrightDataReviewsList> getData() {
            return data;
        }

        public void setData(List<BrightDataReviewsList> data) {
            this.data = data;
        }
    }

