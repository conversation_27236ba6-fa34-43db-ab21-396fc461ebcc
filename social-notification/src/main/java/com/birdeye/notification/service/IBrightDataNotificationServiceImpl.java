package com.birdeye.notification.service;

import com.birdeye.notification.model.BrightData.Error;
import com.birdeye.notification.model.BrightData.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

@Service
public class IBrightDataNotificationServiceImpl implements IBrightDataNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(IBrightDataNotificationServiceImpl.class);

    private static final String CRAWLER = "Crawler";
    private static final Integer DEFAULT_BRIGHTDATA_ERROR_CODE = 601;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Value("${brightdata.review.notification}")
    private String brightdataReviewNotificationTopic;

    public void processBrightDataReviews(BrightDataResponse payload) {
        try {
            if (Objects.nonNull(payload.getLines())) {
            LOG.info("Pushed BrightData payload to kakfa {}",payload);
            kafkaProducerService.send(brightdataReviewNotificationTopic, payload);
            } else {
                // If payload has error transform it to diff json and send to brightdataReviewNotificationTopic
                LOG.info("Brightdata: no review data recieved {}", payload);
                if (payload.isError()) {
                    BrightDataErrorResponse brightDataErrorResponse = transformPayloadForError(payload);
                    kafkaProducerService.send(brightdataReviewNotificationTopic, brightDataErrorResponse);
                }
            }
        }catch (Exception e){
            LOG.error("Exception occurred while sending message to kafka for BrightData for businessAggregationId id {} , exception {}", payload.getInput().getBusinessAggregationId(),e);
        }
    }

    // Transform the payload in case error is sent from BrightData for daily and fresh agg BIRDEYE-95508
    private BrightDataErrorResponse transformPayloadForError(BrightDataResponse payload) {

        String errorMessage = payload.getMessage();

        String[] errorMessages = errorMessage.split(CRAWLER);
        String msg;
        BrightDataErrorResponse brightDataErrorResponse = null;

        // If Crawler is not found in errorMsg then pick the entire message
        if(Objects.nonNull(errorMessages) && errorMessages.length >= 1) {
            msg = errorMessages.length == 1 ? errorMessages[0] : errorMessages[1];

            Integer errorCode = findErrorCode(msg);
            String errorMsg = findErrorMsg(msg);

            if (!errorMsg.isEmpty()) {
                brightDataErrorResponse = new BrightDataErrorResponse();
                BrightDataReviewErrorData brightDataReviewErrorData = populateBrightDataReviewForError(payload.getInput());
                Error error = new Error();
                error.setCode(errorCode);
                error.setMessage(errorMessage);
                brightDataReviewErrorData.setError(error);
                brightDataReviewErrorData.setData(Collections.EMPTY_LIST);
                brightDataErrorResponse.setInput(payload.getInput());
                brightDataErrorResponse.setLines(Collections.singletonList(brightDataReviewErrorData));

            }
        }
        return brightDataErrorResponse;
    }

    private BrightDataReviewErrorData populateBrightDataReviewForError(BrightDataBusinessAggregationDetails input) {
        BrightDataReviewErrorData brightDataReviewErrorData = new BrightDataReviewErrorData();
        BeanUtils.copyProperties(input,brightDataReviewErrorData);
        brightDataReviewErrorData.setReviewAggregationId(input.getBusinessAggregationId());
        brightDataReviewErrorData.setAggregationStatus("FAILED");
        brightDataReviewErrorData.setServedByIp("data-collector");
        return brightDataReviewErrorData;
    }

    // Parse error message and get all things under message except numeric errorCode
    private String findErrorMsg(String msg) {
        StringBuffer alpha = new StringBuffer();
        for (int i=0; i<msg.length(); i++)
        {
            if(!Character.isDigit(msg.charAt(i)))
                alpha.append(msg.charAt(i));
        }
        return alpha.toString();
    }

    // Replace all things with space except digits which would be errorCode.
    private Integer findErrorCode(String msg) {
        Integer errorCode = DEFAULT_BRIGHTDATA_ERROR_CODE;
        String errorCodes;
        if(msg.contains("|")) {
            errorCodes = msg.split("\\|")[0];
            errorCodes = errorCodes.replaceAll("[^\\d]", " ").trim().replaceAll(" +", " ");
            try {
                errorCode = Integer.parseInt(errorCodes);
            } catch (NumberFormatException numberFormatException) {
                LOG.error("Exception occurred while fetching error code from message {}", errorCodes);
                return errorCode;
            }
        }

        return errorCode;
    }
}
