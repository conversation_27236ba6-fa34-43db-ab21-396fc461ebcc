package com.birdeye.notification.service;

import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.birdeye.notification.model.InstagramEventRequest;

@Service
public class InstagramNotificationServiceImpl implements IInstagramNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(InstagramNotificationServiceImpl.class);

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Value("${instagram.notification.topic}")
    private String notificationTopic;

    @Value("${instagram.comment.topic}")
    private String commentTopic;

    @Value("${instagram.mention.topic}")
    private String mentionTopic;

    @Value("${instagram.story.insight.topic}")
    private String storyInsightTopic;

    private static final String MENTIONS = "mentions";

    private static final String COMMENTS = "comments";

    private static final String INSTAGRAM = "instagram";

    private static final String INSTAGRAM_MSG_RECEIVE = "INSTAGRAM_MSG_RECEIVE";

    private static final String STORY_INSIGHTS = "story_insights";

    private static final String INSTAGRAM_STORY_INSIGHT_RECEIVE = "INSTAGRAM_STORY_INSIGHT_RECEIVE";

    private static final Integer SOURCE_ID = 195;
    @Autowired
    private RoutingService routingService;

    @Override
    public void processInstagramEvent(InstagramEventRequest instagramEventRequest) {
        if (Objects.nonNull(instagramEventRequest) && Objects.equals(instagramEventRequest.getObject(), INSTAGRAM) && !CollectionUtils.isEmpty(instagramEventRequest.getEntry())) {
            instagramEventRequest.getEntry().forEach(entry -> {
                LOG.info("Processing instagram event for ig id {}", entry.getId());
                String pageId = String.valueOf(entry.getId());

                // check if message entity present
                if (!CollectionUtils.isEmpty(entry.getMessaging())) {
                    LOG.info("Pushing instagram message to kafka topic for ig id {}", entry.getId());
                    instagramEventRequest.setEvent(INSTAGRAM_MSG_RECEIVE);
                    try {
                        routingService.sendEventByRegion(instagramEventRequest,notificationTopic, pageId, SOURCE_ID);
                        LOG.info("Instagram message pushed to kafka topic for pageId {}", entry.getId());
                    } catch (Exception ex) {
                        LOG.error("Exception occurred while sending message to kafka for Instagram for ig id {} , exception ", entry.getId(), ex);
                    }
                } else if (!CollectionUtils.isEmpty(entry.getChanges())) {
                    LOG.info("Pushing instagram event to kafka topic for ig id {}", entry.getId());
                    entry.getChanges().forEach(change -> {
                        try {
                            if (STORY_INSIGHTS.equalsIgnoreCase(change.getField())) {
                                instagramEventRequest.setEvent(INSTAGRAM_STORY_INSIGHT_RECEIVE);
                                routingService.sendEventByRegion(instagramEventRequest,storyInsightTopic, pageId, SOURCE_ID);
                                LOG.info("Instagram story insight pushed to kafka topic for pageId {}", entry.getId());
                            } else if (MENTIONS.equalsIgnoreCase(change.getField())) {
                                routingService.sendEventByRegion(instagramEventRequest,mentionTopic, pageId, SOURCE_ID);
                                LOG.info("Instagram mention pushed to kafka topic for pageId {}", entry.getId());
                            } else if (COMMENTS.equalsIgnoreCase(change.getField())) {
                                routingService.sendEventByRegion(instagramEventRequest,commentTopic, pageId, SOURCE_ID);
                                LOG.info("Instagram comment pushed to kafka topic for pageId {}", entry.getId());
                            }
                        } catch (Exception ex) {
                            LOG.error("Exception occurred while sending mention to kafka for Instagram for ig id {} , exception ", entry.getId(), ex);
                        }
                    });
                }
            });
        }
    }
}
