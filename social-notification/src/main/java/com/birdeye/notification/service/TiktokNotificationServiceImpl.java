package com.birdeye.notification.service;

import com.birdeye.notification.model.TikTokCommentEventRequest;
import com.birdeye.notification.model.TikTokPostEventRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

@Service
public class TiktokNotificationServiceImpl implements TiktokNotificationService{

    private static final Logger log = LoggerFactory.getLogger(TiktokNotificationServiceImpl.class);

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private Environment env;
    private static final Integer SOURCE_ID = 420;
    @Autowired
    private RoutingService routingService;

    @Override
    public void processTiktokPostEvent(TikTokPostEventRequest request) {
        try {
            if ("post.publish.publicly_available".equalsIgnoreCase(request.getEvent())) {
                String postId = request.getContent().getPostId();
                String pageId = request.getUserOpenid();
                log.info("Received TikTok publish complete event for postId: {}", postId);

                try {
                    routingService.sendEventByRegion(request,env.getProperty("tiktok.publish.notification.topic"), pageId, SOURCE_ID);
                } catch (Exception ex) {
                    log.error("Exception occurred while sending message to Kafka for TikTok publish event, postId: {}, exception: {}",
                            postId, ex.getMessage(), ex);
                }
            } else {
                log.warn("Ignoring TikTok event: {}", request.getEvent());
            }
        } catch (Exception ex) {
            log.error("Exception while processing TikTok webhook event: {}", ex.getMessage(), ex);
        }
    }


    @Override
    public void processTiktokCommentEvent(TikTokCommentEventRequest request) {
        if ("comment.update".equalsIgnoreCase(request.getEvent())) {
            String commentId = request.getContent().getCommentId();
            String pageId = request.getUserOpenid();
            log.info("Received TikTok comment update event for commentId: {} and comment action: {}",
                    commentId, request.getContent().getCommentAction());
            try {
                routingService.sendEventByRegion(request,env.getProperty("tiktok.comment.notification.topic"), pageId, SOURCE_ID);
            } catch (Exception ex) {
                log.error("Exception occurred while sending message to Kafka for TikTok comment update event, commentId: {}, exception: {}",
                        commentId, ex.getMessage(), ex);
            }
        }
    }
}
