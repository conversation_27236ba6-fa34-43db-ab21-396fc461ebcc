package com.birdeye.notification.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.util.Objects;
import java.util.zip.GZIPInputStream;

import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;

import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.birdeye.notification.model.AppleEventRequest;
import com.birdeye.notification.utils.CommonUtil;
import com.birdeye.notification.utils.JSONUtils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

@Service
public class AppleNotificationServiceImpl implements IAppleNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(AppleNotificationServiceImpl.class);

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private Environment env;

    @Value("${social.apple.msp.id}")
    private String socialAppleMspId;
    
    @Value("${social.apple.msp.secret}")
    private String socialMspSecret;
    private static final Integer SOURCE_ID = 409;
    @Autowired
    private RoutingService routingService;

    @Override
    public void processAppleEvent(byte[] payload, String destinationId, String messageId, String capabilityList, String intent, String authorization, String encoding) throws Exception {

        String authorizationToken = authorization.substring(authorization.indexOf(" ")+1);
        if (!validateMessage(authorizationToken, encoding)) {
            LOG.error("Error occured Invalid Message for destinationId {},intent {} and messageId {}", destinationId, intent, messageId);
            return;
        }
        LOG.info("Valid Message for destinationId {},intent {} and messageId {}", destinationId, intent, messageId);
        if (Objects.nonNull(payload)) {
			String request = decompressPayload(payload, destinationId, messageId, intent, "UTF-8");
            AppleEventRequest appleEventRequest = JSONUtils.fromJSON(request, AppleEventRequest.class);
            //Need to be validated when apple messaging is enabled **
            String pageId = appleEventRequest.getId();
//			if (!CollectionUtils.isEmpty(appleEventRequest.getAttachments())) {
//				appleEventRequest
//						.setBody(JSONUtils
//								.fromJSON(decompressPayload(payload, destinationId, messageId, intent, null),
//								AppleEventRequest.class).getBody());
//
//			}
            appleEventRequest.setCapabilityList(CommonUtil.convertCommaSeparatedStringToList(capabilityList));
            appleEventRequest.setIntent(intent);
            LOG.info("apple event received for object {}", request);
            try {
				if (!StringUtils.isBlank(request)) {
                    routingService.sendEventByRegion(appleEventRequest,env.getProperty("receive.apple"), pageId, SOURCE_ID);
					LOG.info("messenger message pushed to kafka topic for destinationId {},intent {} and messageId {}",
							destinationId, intent, messageId);
				} else {
					LOG.error("empty body sent in payload: {}", request);
            	}
			} catch (Exception ex) {
                LOG.error(
                        "Exception occurred while sending message to kafka for messenger for destinationId {},intent {} and messageId {} , exception {}",
                        destinationId, intent, messageId, ex);
            }
        }

    }

    private Boolean validateMessage(String authorizationToken, String encoding) throws Exception {
        String decodedJWT = decodeJWT(authorizationToken);
        String contentEncoded = "gzip";
        if (socialAppleMspId.equals(decodedJWT) && contentEncoded.equals(encoding)) {
            return true;
        }
        return false;
    }

	private String decompressPayload(byte[] appleEventRequest, String destinationId, String messageId, String intent,
			String type)
			throws UnsupportedEncodingException {
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		try {
			IOUtils.copy(new GZIPInputStream(new ByteArrayInputStream(appleEventRequest)), out);
		} catch (IOException e) {
			LOG.error(
					"Exception occurred while decompressing message for destinationId {},intent {} and messageId {} , exception {}",
					destinationId, intent, messageId);
		}
		return StringUtils.isBlank(type) ? out.toString() : out.toString(type);
    }

	private String decodeJWT(String jwt) throws Exception {
		byte[] apiKeySecretBytes = DatatypeConverter.parseBase64Binary(socialMspSecret);
		Key signingKey = new SecretKeySpec(apiKeySecretBytes, SignatureAlgorithm.HS256.getJcaName());
        Jws<Claims> claim = Jwts.parser()
                .setSigningKey(signingKey.getEncoded())
                .parseClaimsJws(jwt);
        return (claim != null && claim.getBody() != null) ? getMspIdFromToken(claim.getBody()) : null;
    }

    private String getMspIdFromToken(Claims body){
        return body.get("iss") != null ? body.get("iss").toString() : body.get("aud") != null ? body.get("aud").toString() : null ;
    }
}