package com.birdeye.notification.service;

import com.birdeye.notification.model.LinkedinEventRequest;
import com.birdeye.notification.model.LinkedinNotification;
import com.birdeye.notification.utils.JSONUtils;
import com.birdeye.notification.constants.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.springframework.util.StringUtils;

@Service
public class LinkedinNotificationServiceImpl implements ILinkedInNotificationService{

    private static final Logger LOG = LoggerFactory.getLogger(LinkedinNotificationServiceImpl.class);

    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final String SIGNATURE_PREFIX = "hmacsha256=";
    private static final String HMAC_SHA_ALGORITHM = "HmacSHA256";
    public static final String URN_LI_ORG = "urn:li:organization:";

    @Value("${linkedin.notification.topic}")
    private String notificationTopic;

    @Value("${linkedin.secret.key}")
    private String secretKey;
    @Autowired
    private Environment env;
    private static final Integer SOURCE_ID = 109;
    @Autowired
    private RoutingService routingService;

    List<String> actions = Arrays.asList("SHARE_MENTION", "COMMENT", "ADMIN_COMMENT","PHOTO_MENTION", "COMMENT_EDIT", "COMMENT_DELETE", "LIKE");

    @Override
    public void processLinkedinEvent(String signature,byte[] request) throws UnsupportedEncodingException {
        String topicName = "";
        String json= new String(request);
       if (!validateSignature(signature, json, secretKey)) {
            LOG.error("Error occurred while validating signature for payload");
            return;
        }
        LinkedinEventRequest linkedinEventRequest = JSONUtils.fromJSON(json, LinkedinEventRequest.class);
        LOG.info("linkedin event request unfiltered received {} ", JSONUtils.toJSON(linkedinEventRequest));
        //linkedinEventRequest.getNotifications().removeIf(data->!actions.contains(data.getAction().toUpperCase()));
        if (Objects.nonNull(linkedinEventRequest) && Objects.equals(linkedinEventRequest.getType(), "ORGANIZATION_SOCIAL_ACTION_NOTIFICATIONS"))
            if (!CollectionUtils.isEmpty(linkedinEventRequest.getNotifications())) {
                try {
                    for(LinkedinNotification notification : linkedinEventRequest.getNotifications()){
                        if(notification != null && notification.getAction() != null){
                            String action = notification.getAction();
                            String pageId = String.valueOf(notification.getOrganizationalEntity().split(URN_LI_ORG)[1]);
                            if(action != null) {
                                if (action.equalsIgnoreCase(LinkedInNotificationTypeEnum.PHOTO_MENTION_EVENT.getName())
                                        || action.equalsIgnoreCase(LinkedInNotificationTypeEnum.SHARE_MENTION_EVENT.getName())) {
                                    topicName = env.getProperty("linkedin-mention-save-topic");
                                } else if (action.equalsIgnoreCase(LinkedInNotificationTypeEnum.ADMIN_COMMENT_CREATE_EVENT.getName())
                                        || action.equalsIgnoreCase(LinkedInNotificationTypeEnum.COMMENT_CREATE_EVENT.getName())) {
                                    topicName = env.getProperty("linkedin-comment-save-topic");
                                } else if (action.equalsIgnoreCase(LinkedInNotificationTypeEnum.COMMENT_EDIT_EVENT.getName())) {
                                    topicName = env.getProperty("linkedin-comment-edit-topic");
                                } else if (action.equalsIgnoreCase(LinkedInNotificationTypeEnum.COMMENT_DELETE_EVENT.getName())) {
                                    topicName = env.getProperty("linkedin-comment-delete-topic");
                                } else if (action.equalsIgnoreCase(LinkedInNotificationTypeEnum.LIKE_EVENT.getName())) {
                                    topicName = env.getProperty("linkedin-like-event-topic");
                                }
                                if(!StringUtils.isEmpty(topicName)){
                                    routingService.sendEventByRegion(linkedinEventRequest,topicName, pageId, SOURCE_ID);
                                }
                            }
                        }
                    }
                    //kafkaProducerService.send(notificationTopic, linkedinEventRequest); // TODO @mahak move to constants instead
                    LOG.info("Linkedin event pushed to kafka topic {}", topicName);
                } catch (Exception ex) {
                    LOG.error("Exception occurred while sending message to kafka for Linkedin with exception {}", ex);
                }
            }
        }



    private boolean validateSignature(String signatureHeader, String payload, String secretToken)
            throws UnsupportedEncodingException {

        LOG.info("Data received in validateSignature {}", payload);
        if (secretToken == null || secretToken.equals("")) {
            LOG.error("webhookSecret not configured. Skip signature validation");
            return false;
        }

        byte[] signatureHeaderBytes;

        try {
           signatureHeaderBytes = Hex.decodeHex(signatureHeader.toCharArray());
        } catch (DecoderException e) {
            LOG.error("Invalid signature: {}", signatureHeader);
            return false;
        }
        payload=SIGNATURE_PREFIX.concat(payload);
        byte[] expectedSignature = getExpectedSignature(payload.getBytes( "UTF-8" ), secretToken);

        String message=Hex.encodeHexString(expectedSignature);
        LOG.info("generated signature:{} ", message);
        return message.equals(signatureHeader);
    }


    private byte[] getExpectedSignature(byte[] payload, String secretToken) {

        SecretKeySpec key = new SecretKeySpec(secretToken.getBytes(), HMAC_SHA_ALGORITHM);
        Mac hmac;
        try {
            hmac = Mac.getInstance(HMAC_SHA_ALGORITHM);
            hmac.init(key);
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("Hmac SHA must be supported", e);
        } catch (InvalidKeyException e) {
            throw new IllegalStateException("Hmac SHA must be compatible to Hmac SHA Secret Key", e);
        }
        return hmac.doFinal(payload);
    }


}
