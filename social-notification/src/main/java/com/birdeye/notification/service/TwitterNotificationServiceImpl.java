package com.birdeye.notification.service;

import com.birdeye.notification.constants.TwitterNotificationTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

@Service
public class TwitterNotificationServiceImpl implements ITwitterNotificationService{

    private static final Logger LOG = LoggerFactory.getLogger(TwitterNotificationServiceImpl.class);
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private Environment env;
    private static final Integer SOURCE_ID = 108;
    @Autowired
    private RoutingService routingService;

    @Override
    public void publishEvent(Map<String,Object> obj)  {
        LOG.info("Twitter event request unfiltered received {} ", obj);
        String topicName = "";
        try {
            if(obj != null) {
                if (obj.containsKey(TwitterNotificationTypeEnum.CREATE_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-create-events-topic");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.FOLLOW_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-follow-events-topic");
                }else  if (obj.containsKey(TwitterNotificationTypeEnum.FAVORITE_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-favorite-events-topic");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.BLOCK_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-block-events-topic");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.MUTE_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-mute-events-topic");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.USER_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-user-events-topic");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.DIRECT_MESSAGE_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-message-events-topic");
                    obj.put("event","TWITTER_RECEIVE");
                }else if (obj.containsKey(TwitterNotificationTypeEnum.DELETE_EVENTS.getName())) {
                    topicName = env.getProperty("twitter-delete-events-topic");
                }
                String pageId = null;
                if(obj.containsKey("for_user_id")) {
                    pageId = String.valueOf(obj.get("for_user_id"));
                }

                if(!StringUtils.isEmpty(topicName)){
                    LOG.info("Twitter notification pushed to topic : {}", topicName);
                    routingService.sendEventByRegion(obj,topicName, pageId, SOURCE_ID);
                }
            }
        } catch(Exception ex) {
            LOG.error("Exception occurred while sending message to kafka for twitter with exception {}", ex);
        }
    }

}
