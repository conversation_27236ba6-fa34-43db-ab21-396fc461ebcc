package com.birdeye.notification.service;

import com.birdeye.notification.dao.PageRegionInfoRepo;
import com.birdeye.notification.entities.PageRegionInfo;
import com.birdeye.notification.model.PageSyncEventRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class RoutingServiceImpl implements RoutingService {

    @Autowired
    private PageRegionInfoRepo pageRegionInfoRepo;

    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate<String,String> redisTemplate;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final Logger logger = LoggerFactory.getLogger(RoutingServiceImpl.class);


    private final Map<String, String> memoryCache = Collections.synchronizedMap(
            new LinkedHashMap<String, String>(10000, 0.75f, true) {
                @Override
                protected boolean removeEldestEntry(Map.Entry eldest) {
                    return size() > 10000;
                }
            });

    private static final String REDIS_KEY_PREFIX = "pageRegion:";

    @Override
    public String getRegionForPage(String pageId, Integer sourceId) {
        if(StringUtils.isEmpty(pageId) || Objects.isNull(sourceId)) {
            return null;
        }
        String region = null;
        try {
            //in-memory cache
//            region = memoryCache.get(pageId);
//            if (StringUtils.isNotEmpty(region)) {
//                logger.info("--------------[log] found in in-memory cache: {}", region);
//                return region;
//            }

            //redis cache
            region = redisTemplate.opsForValue().get(REDIS_KEY_PREFIX + pageId);
            if (StringUtils.isNotEmpty(region)) {
                logger.info("--------------[log] found in redis cache: {}", region);
                memoryCache.put(pageId, region);
                return region;
            }

            //fallback to db
            PageRegionInfo pageRegionInfo = pageRegionInfoRepo.findByPageIdAndSourceId(pageId, sourceId);
            if (pageRegionInfo != null) {
                region = pageRegionInfo.getRegion();
                logger.info("--------------[log] found in db: {}", region);
                redisTemplate.opsForValue().set(REDIS_KEY_PREFIX + pageId, region, 43200l, TimeUnit.SECONDS);
                memoryCache.put(pageId, region);
                return region;
            }
            logger.info("--------------[log] not found anywhere: {}", region);
        } catch (Exception e) {
            logger.info("Error while getting region for pageId {} and sourceId {}: {}", pageId, sourceId, e.getMessage());
        }
        return region;
    }

    @Override
    public void sendEventByRegion(Object payload, String topic, String pageId, Integer sourceId) {

        try {
            String region = getRegionForPage(pageId, sourceId);
            if ("US".equalsIgnoreCase(region)) {
                kafkaProducerService.send(topic, payload);
            } else if ("EU".equalsIgnoreCase(region)) {
                kafkaProducerService.sendEU(topic, payload);
            } else if (StringUtils.isEmpty(region)) {
                kafkaProducerService.send(topic, payload);
                kafkaProducerService.sendEU(topic, payload);
            }
        } catch (Exception e) {
            logger.error("Error while sending event on region: {}", e.getMessage());
        }
    }

    @Override
    public void invalidateCache(String pageId) {
        memoryCache.remove(pageId);
        redisTemplate.delete(REDIS_KEY_PREFIX + pageId);
    }

    @Override
    public void saveOrUpdate(PageSyncEventRequest pageSyncEventRequest) {
        String pageId = pageSyncEventRequest.getPageId();
        Integer sourceId = pageSyncEventRequest.getSourceId();
        String region = pageSyncEventRequest.getRegion();
        PageRegionInfo pageRegionInfo = new PageRegionInfo(pageId, sourceId, region);
        try {
            pageRegionInfoRepo.saveAndFlush(pageRegionInfo);
        } catch (Exception e) {
            logger.error("Error while saving page region info for pageId: {} : {}", pageId, e.getMessage());
            pageRegionInfo = pageRegionInfoRepo.findByPageId(pageId);
            pageRegionInfo.setRegion(region);
            pageRegionInfo.setSourceId(sourceId);
            pageRegionInfoRepo.saveAndFlush(pageRegionInfo);
        }
        redisTemplate.opsForValue().set(REDIS_KEY_PREFIX + pageId, region,43200l, TimeUnit.SECONDS);
        memoryCache.put(pageId, region);
    }


}
