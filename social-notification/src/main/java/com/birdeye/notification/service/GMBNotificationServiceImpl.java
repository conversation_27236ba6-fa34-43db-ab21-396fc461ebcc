/**
 * 
 */
package com.birdeye.notification.service;

import com.birdeye.notification.model.GMBNotificationReviewRequest;
import com.birdeye.notification.model.GoogleBusinessMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import java.util.Map;

@Service
public class GMBNotificationServiceImpl implements IGMBNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(GMBNotificationServiceImpl.class);

    @Autowired
    KafkaProducerService kafkaProducerService;

    @Autowired
    private Environment env;
    private static final Integer SOURCE_ID = 2;
    @Autowired
    private RoutingService routingService;

    @Override
    public void submitGMBNotification(GMBNotificationReviewRequest gmbNotificationReviewRequest) throws Exception {
        routingService.sendEventByRegion(gmbNotificationReviewRequest,env.getProperty("gmb.notification.topic"), gmbNotificationReviewRequest.getLocationId(), SOURCE_ID);
        LOG.info("[GMB] review pushed to kafka for gmb location Id , event data : {} {}",gmbNotificationReviewRequest.getLocationId(),gmbNotificationReviewRequest.getEventData());
    }

    @Override
    public void process(Map notification) throws Exception {
        LOG.info("[GMB] Review notification received : {}",notification);
        GMBNotificationReviewRequest gmbNotificationReviewRequest = GMBNotificationReviewRequest.convertToGMBNotificationRequest(notification);
        LOG.info("[GMB] review received for gmb location Id {}",gmbNotificationReviewRequest.getLocationId());
        this.submitGMBNotification(gmbNotificationReviewRequest);
    }


    @Deprecated
    public void receiveGoogleMsg(GoogleBusinessMessage notification) throws Exception{
        kafkaProducerService.send(env.getProperty("gbm.notification.topic"),notification);
        LOG.info("[Google Business Message] pushed on kafka for inbox service agent : {}, place id {}", 
        		null != notification ? notification.getAgent() : "null", 
        				null != notification ? notification.getContext() : "null" );
    }
}

