package com.birdeye.notification.service;

import com.birdeye.notification.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
public class FbNotificationServiceImpl implements IFbNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(FbNotificationServiceImpl.class);

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final Integer SOURCE_ID = 110;
    @Autowired
    private RoutingService routingService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private Environment env;

    @Override
    public void processFbEvent(FacebookEventRequest facebookEventRequest) {
        if(Objects.nonNull(facebookEventRequest)){
            if(!CollectionUtils.isEmpty(facebookEventRequest.getEntry())){
                facebookEventRequest.getEntry().forEach(entry -> {
                    String pageId = String.valueOf(entry.getId());
                    LOG.info("Processing fb event for pageId {}",entry.getId());
                    if(!CollectionUtils.isEmpty(entry.getMessaging())){
                        LOG.info("Pushing messenger message to kafka topic for pageId {}",entry.getId());
                        facebookEventRequest.setEvent("FACEBOOK_RECEIVE");
                        try {
                            routingService.sendEventByRegion(facebookEventRequest,env.getProperty("receive.facebook"), pageId, SOURCE_ID);
                            LOG.info("messenger message pushed to kafka topic for pageId {}",entry.getId());
                        }catch (Exception ex){
                            LOG.error("Exception occurred while sending message to kafka for messenger for pageId {} , exception {}",entry.getId(),ex);
                        }
                    }else if(!CollectionUtils.isEmpty(entry.getChanges())){
                        LOG.info("Pushing rating message to kafka topic to social for pageId {}",entry.getId());
                        try{
                                entry.getChanges().stream().forEach(v -> {
                                    if(Objects.isNull(v)) {
                                        LOG.error("Exiting, No data found to proceed with payload {}", facebookEventRequest);
                                        return;
                                    }

                                   Boolean isEngageEvent  = ("feed".equalsIgnoreCase(v.getField()) || "mention".equalsIgnoreCase(v.getField()) );
                                    if(isEngageEvent) {
                                        try {
                                            routingService.sendEventByRegion(facebookEventRequest,env.getProperty("fb.engage.notification.topic"), pageId, SOURCE_ID);
                                        } catch (Exception ex) {
                                            LOG.error("Exception occurred while sending message to kafka for feed for pageId {} , exception {}",entry.getId(),ex);

                                        }
                                    }

                                   Boolean isFeedEvent =  v.getField().equalsIgnoreCase("feed") && v.getValue().getItem().equalsIgnoreCase("comment");
                                   if(isFeedEvent) {
                                       try {
                                           routingService.sendEventByRegion(facebookEventRequest,env.getProperty("fb.feed.notification.topic"), pageId, SOURCE_ID);
                                           return;
                                       } catch (Exception ex) {
                                           LOG.error("Exception occurred while sending message to kafka for feed for pageId {} , exception {}",entry.getId(),ex);

                                       }
                                   } else if(v.getField().equalsIgnoreCase("picture")) {
                                       DpSyncRequest dpSyncRequest = new DpSyncRequest();
                                       dpSyncRequest.setPageId(entry.getId().toString());
                                       try {
                                           routingService.sendEventByRegion(dpSyncRequest,env.getProperty("fb.picture.notification.topic"), pageId, SOURCE_ID);
                                           return;
                                       } catch (Exception e) {
                                           LOG.error("Exception occurred while sending picture update message to kafka for feed for pageId {} , exception {}",entry.getId(),e);
                                       }
                                   }
                                });

                            routingService.sendEventByRegion(facebookEventRequest,env.getProperty("fb.ratings.notification.topic"), pageId, SOURCE_ID);
                        }catch (Exception ex){
                            LOG.error("Exception occurred while sending message to kafka for ratings for pageId {} , exception {}",entry.getId(),ex);
                        }
                    }
                });
            }
        }
    }

    private static <T> T getNestedValue(Map map, String... keys) {
        Object value = map;

        for (String key : keys) {
            value = ((Map) value).get(key);
        }

        return (T) value;
    }

    public String handleDataDeletionCallback(String signedRequest) {
        LOG.info("Request received to delete fb data with signedRequest: {}", signedRequest);
        try {
            String confirmationCode = UUID.randomUUID().toString();
            routingService.sendEventByRegion(new FacebookDataDeletionPayload(signedRequest, confirmationCode),env.getProperty("fb.data.deletion.callback"), null, SOURCE_ID );
            FacebookDataDeletionResponse response = new FacebookDataDeletionResponse();
            response.setUrl("https://birdeye.com/terms?id=" + confirmationCode);
            response.setConfirmation_code(confirmationCode);
            return objectMapper.writeValueAsString(response);
        } catch (Exception e) {
            LOG.error("Error occurred while handling data deletion callback for fb: {}", e.getMessage());
            return createErrorResponse("An error occurred: " + e.getMessage());
        }
    }

    private String createErrorResponse(String errorMessage) {
        try {
            FacebookErrorResponse errorResponse = new FacebookErrorResponse();
            errorResponse.setError(errorMessage);
            return objectMapper.writeValueAsString(errorResponse);
        } catch (Exception e) {
            return "{\"error\":\"Failed to create error response\"}";
        }
    }
}
