package com.birdeye.notification.utils;

import com.birdeye.notification.model.TikTokPostEventRequest;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class TikTokPublishContentDeserializer extends JsonDeserializer<TikTokPostEventRequest.TikTokPublishContent> {
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger LOG = LoggerFactory.getLogger(TikTokPublishContentDeserializer.class);

    @Override
    public TikTokPostEventRequest.TikTokPublishContent deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        LOG.info("deserialize TikTokEventRequest");
        JsonNode node = objectMapper.readTree(p);

        if (node.isTextual()) {
            return objectMapper.readValue(node.asText(), TikTokPostEventRequest.TikTokPublishContent.class);
        } else {
            return objectMapper.treeToValue(node, TikTokPostEventRequest.TikTokPublishContent.class);
        }
    }
}