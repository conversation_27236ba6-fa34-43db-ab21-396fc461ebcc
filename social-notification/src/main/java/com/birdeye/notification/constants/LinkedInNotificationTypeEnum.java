package com.birdeye.notification.constants;

import java.util.Arrays;

public enum LinkedInNotificationTypeEnum {
    COMMENT_CREATE_EVENT("COMMENT", "social-linkedin-create-comment-events"),
    ADMIN_COMMENT_CREATE_EVENT("ADMIN_COMMENT", "social-linkedin-create-comment-events"),
    SHARE_MENTION_EVENT("SHARE_MENTION", "social-linkedin-share-mention-events"),
    PHOTO_MENTION_EVENT("PHOTO_MENTION", "social-linkedin-share-mention-events"),
    COMMENT_EDIT_EVENT("COMMENT_EDIT", "social-linkedin-comment-edit-events"),
    COMMENT_DELETE_EVENT("COMMENT_DELETE", "social-linkedin-comment-delete-events"),
    LIKE_EVENT("LIKE", "social-linkedin-like-events"),

    ;

    private final String name;
    private final String topicName;

    public String getName() {
        return name;
    }

    public String getTopicName() {
        return topicName;
    }

    LinkedInNotificationTypeEnum(String name, String topicName) {
        this.name = name;
        this.topicName = topicName;
    }

    public static LinkedInNotificationTypeEnum getByName(String name) {
        for (LinkedInNotificationTypeEnum status : LinkedInNotificationTypeEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
