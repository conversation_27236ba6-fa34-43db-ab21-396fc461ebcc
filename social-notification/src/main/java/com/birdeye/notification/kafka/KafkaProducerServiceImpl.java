/**
 * 
 */
package com.birdeye.notification.kafka;

import com.birdeye.notification.service.KafkaProducerService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 *
 */
@Service
public class KafkaProducerServiceImpl implements KafkaProducerService {
	private static final Logger LOG = LoggerFactory.getLogger(KafkaProducerServiceImpl.class);
	@Autowired
	@Qualifier("kafkaTemplate")
	private KafkaTemplate<String, Object> kafkaTemplate;

	@Autowired
	@Qualifier("kafkaTemplateEU")
	private KafkaTemplate<String, Object> kafkaTemplateEU;
	
	@Autowired
	private ObjectMapper objectMapper;
	
	@Override
	public void send(String topic, Object payload) throws Exception {
		String payloadString = objectMapper.writeValueAsString(payload);
		LOG.info("Submitting request to kafka topic {} with payload:{}",topic,payloadString);
		kafkaTemplate.send(topic, payloadString);
	}

	@Override
	public void sendEU(String topic, Object payload) throws Exception {
		String payloadString = objectMapper.writeValueAsString(payload);
		LOG.info("Submitting request to kafka EU topic {} with payload:{}",topic,payloadString);
		kafkaTemplateEU.send(topic, payloadString);
	}
}
