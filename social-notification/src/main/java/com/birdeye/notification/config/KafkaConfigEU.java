package com.birdeye.notification.config;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableKafka
public class KafkaConfigEU {

    @Autowired
    private Environment env;

    @Bean("kafkaTemplateEU")
    public KafkaTemplate<String, Object> kafkaTemplateEU() {
        return new KafkaTemplate<>(this.kafkaProducerFactoryEU());
    }

    private ProducerFactory<String, Object> kafkaProducerFactoryEU() {
        return new DefaultKafkaProducerFactory<>(this.kafkaConfigEU());
    }

    private Map<String, Object> kafkaConfigEU() {
        Map<String, Object> config = new HashMap<>();
        config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, env.getProperty("kafka.eu.server"));
        config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        config.put(ProducerConfig.LINGER_MS_CONFIG, env.getProperty("kafka.eu.linger.ms.config"));
        config.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, env.getProperty("kafka.eu.max.in.flight.requests.per.connection"));
        config.put(ProducerConfig.RETRIES_CONFIG, env.getProperty("kafka.eu.retries.config"));
        config.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, env.getProperty("kafka.eu.request.timeout.ms.config"));
        config.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, env.getProperty("kafka.eu.retry.backoff.ms.config"));
        return config;
    }
}