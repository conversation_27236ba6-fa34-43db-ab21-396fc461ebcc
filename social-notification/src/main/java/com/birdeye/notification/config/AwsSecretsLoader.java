package com.birdeye.notification.config;


import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnClass(AWSSecretsManager.class)
public class AwsSecretsLoader {

    private static final Logger logger = LoggerFactory.getLogger(AwsSecretsLoader.class);

    @Value("${aws.sm.social.secret.id}")
    private String socialSecretName;

    @Value("${aws.secret.enabled}")
    private String smEnabled;

    @Autowired(required = false)
    @Qualifier("defaultSecretsManagerClient")
    private AWSSecretsManager awsSecretsManager;

    private Map<String, String> secretsMap = new HashMap<>();

    public Map<String, String> getSecretObject() {
        return secretsMap;
    }

    @PostConstruct
    public void retrieveSecrets() throws Exception {
        if (Boolean.valueOf(smEnabled)) {
            getSecretValue(socialSecretName, "social_master_db");
        }
    }

    private GetSecretValueResult getSecretValue(String secretName, String secretType) throws Exception {
        logger.info("[AWS SM] Retrieving secret value for secret name {}", secretName);
        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest().withSecretId(secretName);
        GetSecretValueResult getSecretValueResult = null;
        try {
            logger.info("[AWS SM] Retrieving secret value for object {}", getSecretValueRequest);
            getSecretValueResult = awsSecretsManager.getSecretValue(getSecretValueRequest);
            logger.info("secret value is {}", getSecretValueResult.toString());
            Map<String, String> awsSecretValMap = getSecretMap(getSecretValueResult.getSecretString());
            buildAwsSecretsMap(awsSecretValMap, secretType);
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to retrive secrets ", e);
            throw new Exception("FAILED_TO_GET_AWS_SM_SECRETS");
        }
        return getSecretValueResult;

    }

    private Map<String, String> getSecretMap(String secretValueResult) throws Exception {
        Map<String, String> awsSecretMap = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            awsSecretMap = mapper.readValue(secretValueResult, Map.class);
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to unmarshal secrets", e);
            throw new Exception("FAILED_TO_PARSE_AWS_SM_SECRETS");
        }
        return awsSecretMap;
    }

    private Map<String, String> buildAwsSecretsMap(Map<String, String> secretVal, String secretType) {
        if ("social_master_db".equals(secretType)) {
            secretsMap.put("social_host_name", secretVal.get("host"));
            secretsMap.put("social_db_port", secretVal.get("port"));
            secretsMap.put("social_username", secretVal.get("username"));
            secretsMap.put("social_password", secretVal.get("password"));
            secretsMap.put("social_db_name", "social");
        }
        logger.info("[AWS SM] secrets are parsed successfully as: {} ", secretsMap);
        return secretsMap;
    }
}
