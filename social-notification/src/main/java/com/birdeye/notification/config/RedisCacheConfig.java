package com.birdeye.notification.config;


import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import com.birdeye.notification.handler.SocialNotificationCacheErrorHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.AbstractEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.ResourcePropertySource;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectMapper.DefaultTyping;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.redis.jedis.JedisLockProvider;
import net.javacrumbs.shedlock.spring.ScheduledLockConfiguration;
import net.javacrumbs.shedlock.spring.ScheduledLockConfigurationBuilder;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;


@Configuration
@EnableCaching
@PropertySource("classpath:cache.properties")
public class RedisCacheConfig extends CachingConfigurerSupport {

    @Autowired
    private Environment environment;

    @Bean
    public JedisConnectionFactory redisConnectionFactory() {
        JedisConnectionFactory redisConnectionFactory = new JedisConnectionFactory();
        redisConnectionFactory.setHostName(environment.getProperty("redis.hostname",String.class));
        redisConnectionFactory.setPort(environment.getProperty("redis.port",Integer.class,6379));
        redisConnectionFactory.setUsePool(true);
        redisConnectionFactory.setUseSsl(environment.getProperty("redis.ssl.enabled",Boolean.class));
        redisConnectionFactory.setPassword(environment.getProperty("redis.password",String.class));
        redisConnectionFactory.setDatabase(environment.getProperty("redis.database",Integer.class,10));
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(environment.getProperty("redis.max.pool.size",Integer.class, 20));
        poolConfig.setMinIdle(environment.getProperty("redis.min.idle.size",Integer.class, 5));
        poolConfig.setMaxIdle(environment.getProperty("redis.max.idle.size",Integer.class, 10));
        redisConnectionFactory.setPoolConfig(poolConfig);
        return redisConnectionFactory;
    }

    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory cf) {
        RedisTemplate<String, String> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        ObjectMapper mapper = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.enableDefaultTyping(DefaultTyping.NON_FINAL, As.PROPERTY);
        mapper.getDeserializationConfig().getDefaultVisibilityChecker().withFieldVisibility(Visibility.ANY);
        redisTemplate.setDefaultSerializer(new GenericJackson2JsonRedisSerializer(mapper));
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer(mapper));
        redisTemplate.setConnectionFactory(cf);
        return redisTemplate;
    }

    @Bean
    public CacheManager cacheManager(RedisTemplate<?, ?> redisTemplate) {
        RedisCacheManager cacheManager = new RedisCacheManager(redisTemplate);
        Map<String, Long> timeoutMap = new HashMap<>();
        for (org.springframework.core.env.PropertySource<?> ps : ((AbstractEnvironment) environment).getPropertySources()) {
            if (ps instanceof ResourcePropertySource && ps.getName().contains("cache.properties")) {
                Properties source = (Properties) ps.getSource();
                for (final String name : source.stringPropertyNames()) {
                    timeoutMap.put(name, Long.parseLong(source.getProperty(name)));
                }
            }
        }
        cacheManager.setExpires(timeoutMap);
        cacheManager.setUsePrefix(true);
        return cacheManager;
    }

    @Bean
    public JedisPoolConfig jedisPoolConfig() {
        return new JedisPoolConfig();
    }

    @Bean
    public JedisPool jedisPool() {
        return new JedisPool(jedisPoolConfig(), environment.getProperty("redis.hostname",String.class), environment.getProperty("redis.port",Integer.class,6379), environment.getProperty("redis.timeout",Integer.class,3000), environment.getProperty("redis.password",String.class), environment.getProperty("redis.database",Integer.class,10), environment.getProperty("redis.ssl.enabled",Boolean.class));
    }

    @Bean
    public LockProvider lockProvider(JedisPool jedisPool) {
        return new JedisLockProvider(jedisPool, "social-scheduler-lock-provider");
    }

    /**
     * Scheduled tasks Distributed lock configuration, to ensure that under multiple instances, the task runs on only one machine.
     *
     * @param lockProvider
     * @return
     */
    @Bean
    public ScheduledLockConfiguration taskScheduler(LockProvider lockProvider) {
        return ScheduledLockConfigurationBuilder.withLockProvider(lockProvider).withPoolSize(30).withDefaultLockAtMostFor(Duration.ofMinutes(2)).build();
    }

    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new SocialNotificationCacheErrorHandler();
    }
}
