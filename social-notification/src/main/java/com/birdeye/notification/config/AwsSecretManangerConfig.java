package com.birdeye.notification.config;

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.AWSSecretsManagerClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AwsSecretManangerConfig {

    @Value("${aws.secret.region}")
    private String region;

    private static final Logger logger = LoggerFactory.getLogger(AwsSecretManangerConfig.class);


    @Bean("defaultSecretsManagerClient")
    @ConditionalOnProperty(prefix = "aws.secret", value = "enabled", havingValue = "true")
    public AWSSecretsManager getDefaultSecretsManagerClient() {
        logger.info("AWS Secrets Manager client is being created with region: {}", region);
        return AWSSecretsManagerClientBuilder.standard().withCredentials(new DefaultAWSCredentialsProviderChain())
                .withRegion(region).build();
    }
}
