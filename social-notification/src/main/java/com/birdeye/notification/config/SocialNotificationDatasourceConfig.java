package com.birdeye.notification.config;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;

import javax.persistence.EntityManagerFactory;
import javax.sql.DataSource;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
        entityManagerFactoryRef = "socialNotificationEntityManagerFactory",
        transactionManagerRef = "socialNotificationTransactionManager",
        basePackages = "com.birdeye.notification.dao"
)
public class SocialNotificationDatasourceConfig {

    @Bean("socialNotificationHikariConfig")
    @ConfigurationProperties(prefix = "social.datasource")
    public HikariConfig hikariConfig() {
        return new HikariConfig();
    }

    @Autowired(required = false)
    public AwsSecretsLoader secretsLoader;

    @Value("${aws.secret.enabled}")
    public String smEnabled;


    @Bean("socialNotificationDatasource")
    public DataSource dataSource() {
        return new HikariDataSource(updateHikariConfig());
    }

    @Bean(name = "socialNotificationEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(EntityManagerFactoryBuilder builder) {
        return builder
                .dataSource(dataSource())
                .properties(hibernateProperties())
                .packages("com.birdeye.notification.entities")
                .persistenceUnit("socialNotificationPersistenceUnit")
                .build();
    }

    @Bean(name = "socialNotificationTransactionManager")
    public PlatformTransactionManager transactionManager(
            @Qualifier("socialNotificationEntityManagerFactory") EntityManagerFactory entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory);
    }

    private Map<String, Object> hibernateProperties() {
        Resource resource = new ClassPathResource("hibernate.properties");
        try {
            Properties properties = PropertiesLoaderUtils.loadProperties(resource);
            return properties.entrySet().stream()
                    .collect(Collectors.toMap(e -> e.getKey().toString(), e -> e.getValue()));
        } catch (IOException e) {
            return new HashMap<>();
        }
    }

    private HikariConfig updateHikariConfig() {
        HikariConfig hikariConfig = hikariConfig();
        if (Boolean.valueOf(smEnabled) && MapUtils.isNotEmpty(secretsLoader.getSecretObject())) {
            hikariConfig.setJdbcUrl(getJdbcUrl());
            hikariConfig.setUsername(secretsLoader.getSecretObject().get("social_username"));
            hikariConfig.setPassword(secretsLoader.getSecretObject().get("social_password"));
        }
        return hikariConfig;
    }

    private String getJdbcUrl() {
        String host = secretsLoader.getSecretObject().get("social_host_name");
        String dbName = secretsLoader.getSecretObject().get("social_db_name");
        String port = secretsLoader.getSecretObject().get("social_db_port");

        return String.format("********************************************************", host, port, dbName);
    }
}
