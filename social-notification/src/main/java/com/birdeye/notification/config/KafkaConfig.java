/**
 * 
 */
package com.birdeye.notification.config;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
@Configuration
@EnableKafka
public class KafkaConfig {

	@Autowired
	private Environment	env;
	@Bean("kafkaTemplate")
	public KafkaTemplate<String, Object> kafkaTemplate(){
		return new KafkaTemplate<>(this.kafkaProducerFactory());
	}
	
	private ProducerFactory<String, Object> kafkaProducerFactory(){
		return new DefaultKafkaProducerFactory<>(this.kafkaConfig());
	}
	
	private Map<String,Object> kafkaConfig(){
		Map<String,Object> config = new HashMap<>();
		config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, env.getProperty("kafka.server"));
		config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,StringSerializer.class);
		config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		//TODO- Make below configuration property driven
		config.put(ProducerConfig.LINGER_MS_CONFIG, env.getProperty("kafka.linger.ms.config"));
		config.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, env.getProperty("kafka.max.in.flight.requests.per.connection"));
		config.put(ProducerConfig.RETRIES_CONFIG,env.getProperty("kafka.retries.config"));
		config.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG,env.getProperty("kafka.request.timeout.ms.config"));
		config.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, env.getProperty("kafka.retry.backoff.ms.config"));
		return config;
	}
}
