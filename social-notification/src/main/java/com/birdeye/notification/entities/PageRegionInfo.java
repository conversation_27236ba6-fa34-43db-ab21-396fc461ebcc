package com.birdeye.notification.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "page_region_info")
public class PageRegionInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer	id;

    @Column(name = "page_id")
    private String	pageId;

    @Column(name = "source_id")
    private Integer	sourceId;

    @Column(name = "region")
    private String	region;

    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public PageRegionInfo() {
    }

    public PageRegionInfo(String pageId, Integer sourceId, String region) {
        this.pageId = pageId;
        this.sourceId = sourceId;
        this.region = region;
    }

    @Override
    public String toString() {
        return "PageRegionInfo{" +
                "id=" + id +
                ", pageId='" + pageId + '\'' +
                ", region='" + region + '\'' +
                ", createdDate=" + createdDate +
                '}';
    }
}