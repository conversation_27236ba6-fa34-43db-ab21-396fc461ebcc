package com.birdeye.social.linkedin;

public class LinkedinShareContent {
	private String eyebrowUrl;
	private String shortenedUrl;
	private String submittedImageUrl;
	private String submittedUrl;
	private String thumbnailUrl;
	private String title;
	public String getEyebrowUrl() {
		return eyebrowUrl;
	}
	public void setEyebrowUrl(String eyebrowUrl) {
		this.eyebrowUrl = eyebrowUrl;
	}
	public String getShortenedUrl() {
		return shortenedUrl;
	}
	public void setShortenedUrl(String shortenedUrl) {
		this.shortenedUrl = shortenedUrl;
	}
	public String getSubmittedImageUrl() {
		return submittedImageUrl;
	}
	public void setSubmittedImageUrl(String submittedImageUrl) {
		this.submittedImageUrl = submittedImageUrl;
	}
	public String getSubmittedUrl() {
		return submittedUrl;
	}
	public void setSubmittedUrl(String submittedUrl) {
		this.submittedUrl = submittedUrl;
	}
	public String getThumbnailUrl() {
		return thumbnailUrl;
	}
	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
}
