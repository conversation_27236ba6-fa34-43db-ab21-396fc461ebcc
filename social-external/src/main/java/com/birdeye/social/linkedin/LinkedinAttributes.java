package com.birdeye.social.linkedin;

import com.birdeye.social.external.request.linkedin.LinkedinValueMention;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


@JsonIgnoreProperties(ignoreUnknown = true)
public class LinkedinAttributes {

    private Integer length;
    private Integer start;
    private LinkedinValueMention value;

    public Integer getLength() {
        return length;
    }

    public void setLength(Integer length) {
        this.length = length;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public LinkedinValueMention getValue() {
        return value;
    }

    public void setValue(LinkedinValueMention value) {
        this.value = value;
    }

    public LinkedinAttributes(Integer length, Integer start, LinkedinValueMention value) {
        this.length = length;
        this.start = start;
        this.value = value;
    }

    public LinkedinAttributes() {
    }
}
