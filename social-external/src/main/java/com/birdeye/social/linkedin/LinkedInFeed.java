package com.birdeye.social.linkedin;

public class LinkedInFeed {
	private Boolean isCommentable;
	private Boolean isLikable;
	private Boolean isLiked;
	private Integer numLikes;
	private String timestamp;
	private LinkedInFeedLike likes;
	private LinkedInUpdateComment updateComments;
	private LinkedInUpdateContent updateContent;
	private String updateKey;
	private String updateType;
	
	public Boolean getIsCommentable() {
		return isCommentable;
	}
	public void setIsCommentable(Boolean isCommentable) {
		this.isCommentable = isCommentable;
	}
	public Boolean getIsLikable() {
		return isLikable;
	}
	public void setIsLikable(Boolean isLikable) {
		this.isLikable = isLikable;
	}
	public Boolean getIsLiked() {
		return isLiked;
	}
	public void setIsLiked(Boolean isLiked) {
		this.isLiked = isLiked;
	}
	public Integer getNumLikes() {
		return numLikes;
	}
	public void setNumLikes(Integer numLikes) {
		this.numLikes = numLikes;
	}
	public String getTimestamp() {
		return timestamp;
	}
	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}
	public LinkedInFeedLike getLikes() {
		return likes;
	}
	public void setLikes(LinkedInFeedLike likes) {
		this.likes = likes;
	}
	public LinkedInUpdateComment getUpdateComments() {
		return updateComments;
	}
	public void setUpdateComments(LinkedInUpdateComment updateComments) {
		this.updateComments = updateComments;
	}
	public LinkedInUpdateContent getUpdateContent() {
		return updateContent;
	}
	public void setUpdateContent(LinkedInUpdateContent updateContent) {
		this.updateContent = updateContent;
	}
	public String getUpdateKey() {
		return updateKey;
	}
	public void setUpdateKey(String updateKey) {
		this.updateKey = updateKey;
	}
	public String getUpdateType() {
		return updateType;
	}
	public void setUpdateType(String updateType) {
		this.updateType = updateType;
	}

}
