package com.birdeye.social.linkedin.LinkedInPost;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.ToString;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class LinkedInPostSet {

    @JsonProperty(value = "$set")
    LinkedInPostObject set;

    public LinkedInPostObject getSet() {
        return set;
    }

    public void setSet(LinkedInPostObject $set) {
        this.set = $set;
    }
}
