package com.birdeye.social.facebook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPostObject implements Serializable {

        private static final long serialVersionUID = -3330949287176236248L;

        private List<FeedPostData> data;

    public List<FeedPostData> getData() {
        return data;
    }

    public void setData(List<FeedPostData> data) {
        this.data = data;
    }
}
