package com.birdeye.social.facebook;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 *
 */
public class FBPaymentOption implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Integer amex;
	private Integer cash_only;
	private Integer discover;
	private Integer mastercard;
	private Integer visa;
	
	public FBPaymentOption() {
		// TODO Auto-generated constructor stub
	}

	/**
	 * @return the amex
	 */
	public Integer getAmex() {
		return amex;
	}

	/**
	 * @param amex the amex to set
	 */
	public void setAmex(Integer amex) {
		this.amex = amex;
	}

	/**
	 * @return the cash_only
	 */
	public Integer getCash_only() {
		return cash_only;
	}

	/**
	 * @param cash_only the cash_only to set
	 */
	public void setCash_only(Integer cash_only) {
		this.cash_only = cash_only;
	}

	/**
	 * @return the discover
	 */
	public Integer getDiscover() {
		return discover;
	}

	/**
	 * @param discover the discover to set
	 */
	public void setDiscover(Integer discover) {
		this.discover = discover;
	}

	/**
	 * @return the mastercard
	 */
	public Integer getMastercard() {
		return mastercard;
	}

	/**
	 * @param mastercard the mastercard to set
	 */
	public void setMastercard(Integer mastercard) {
		this.mastercard = mastercard;
	}

	/**
	 * @return the visa
	 */
	public Integer getVisa() {
		return visa;
	}

	/**
	 * @param visa the visa to set
	 */
	public void setVisa(Integer visa) {
		this.visa = visa;
	}

}
