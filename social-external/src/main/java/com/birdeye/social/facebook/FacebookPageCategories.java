package com.birdeye.social.facebook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPageCategories {

    private List<FacebookPageCategory> data;

    public List<FacebookPageCategory> getData() {
        return data;
    }

    public void setData(List<FacebookPageCategory> data) {
        this.data = data;
    }
}
