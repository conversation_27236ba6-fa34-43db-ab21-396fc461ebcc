package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPlaceLocation implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6169305881858039062L;
	/*
	 * "city": "Bangalore", "country": "India", "latitude": 13.0196799, "longitude":
	 * 77.63007, "street": "bangalore international airport", "zip": "560001"
	 */
	private String city;
	private String country;
	private Double latitude;
	private Double longitude;
	private String street;
	private String zip;

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public String getStreet() {
		return street;
	}

	public void setStreet(String street) {
		this.street = street;
	}

	public String getZip() {
		return zip;
	}

	public void setZip(String zip) {
		this.zip = zip;
	}

	@Override
	public String toString() {
		return "FacebookPlaceLocation [city=" + city + ", country=" + country + ", latitude=" + latitude
				+ ", longitude=" + longitude + ", street=" + street + ", zip=" + zip + "]";
	}

}
