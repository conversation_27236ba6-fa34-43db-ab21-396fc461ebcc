/**
 * 
 */
package com.birdeye.social.facebook;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class PostInsightData {
	
    private List<PostInsightItem> data;
    private PagingInfo paging;

    public List<PostInsightItem> getData() {
        return data;
    }

    public void setData(List<PostInsightItem> data) {
        this.data = data;
    }

    public PagingInfo getPaging() {
        return paging;
    }

    public void setPaging(PagingInfo paging) {
        this.paging = paging;
    }
}
