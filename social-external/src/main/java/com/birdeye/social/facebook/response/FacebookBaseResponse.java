/**
 *
 */
package com.birdeye.social.facebook.response;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * <AUTHOR>
 *
 */

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookBaseResponse implements Serializable {

	/**
	 *
	 */
	private static final long		serialVersionUID	= 4932505465733645548L;
	private FacebookErrorResponse error;
	private boolean success;

	public FacebookErrorResponse getError() {
		return error;
	}

	public void setError(FacebookErrorResponse error) {
		this.error = error;
	}

	/**
	 * @return the success
	 */
	public boolean isSuccess() {
		return success;
	}

	/**
	 * @param success the success to set
	 */
	public void setSuccess(boolean success) {
		this.success = success;
	}

	@Override
	public String toString() {
		return "FacebookBaseResponse [error=" + error + ", success=" + success + "]";
	}
	
}
