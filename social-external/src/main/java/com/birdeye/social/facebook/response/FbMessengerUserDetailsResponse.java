/**
 *
 *
 */
package com.birdeye.social.facebook.response;

/**
 * <AUTHOR>
 *
 */
public class FbMessengerUserDetailsResponse {
    private String id;

    private String first_name;
    
    private String last_name;
    
    private String profile_pic;

    /**
	 * @return the id
	 */
	public String getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * @return the first_name
	 */
	public String getFirst_name() {
		return first_name;
	}

	/**
	 * @param first_name the first_name to set
	 */
	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}

	/**
	 * @return the last_name
	 */
	public String getLast_name() {
		return last_name;
	}

	/**
	 * @param last_name the last_name to set
	 */
	public void setLast_name(String last_name) {
		this.last_name = last_name;
	}

	/**
	 * @return the profile_pic
	 */
	public String getProfile_pic() {
		return profile_pic;
	}

	/**
	 * @param profile_pic the profile_pic to set
	 */
	public void setProfile_pic(String profile_pic) {
		this.profile_pic = profile_pic;
	}

	@Override
    public String toString()
    {
        return "FbMessengerUserDetailsResponse [id = "+id+", first_name = "+first_name+", last_name = "+last_name+", profile_pic = "+profile_pic+"]";
    }
}