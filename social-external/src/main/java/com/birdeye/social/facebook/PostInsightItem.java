/**
 * 
 */
package com.birdeye.social.facebook;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public class PostInsightItem {
	
	private String name;
    private List<PostInsightItemValue> values;

    //Fields for facebook feed API's post response
    private String id;
    private String message;
    private String created_time;
    private Date postDate;
    private ReviewerData from;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public List<PostInsightItemValue> getValues() {
		return values;
	}
	public void setValues(List<PostInsightItemValue> values) {
		this.values = values;
	}
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public String getCreated_time() {
		return created_time;
	}
	public void setCreated_time(String created_time) {
        this.created_time = created_time;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        //Setting activity date
        try {
            this.postDate = format.parse(this.created_time);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	public Date getPostDate() {
		return postDate;
	}
	public ReviewerData getFrom() {
		return from;
	}
	public void setFrom(ReviewerData from) {
		this.from = from;
	}
}
