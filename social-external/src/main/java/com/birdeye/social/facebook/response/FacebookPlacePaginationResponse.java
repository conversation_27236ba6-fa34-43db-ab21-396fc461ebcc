package com.birdeye.social.facebook.response;

import java.io.Serializable;

import com.birdeye.social.facebook.FacebookPlacePaginationCursors;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPlacePaginationResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -273784778748447997L;
	private String next;
	private FacebookPlacePaginationCursors cursors;

	public String getNext() {
		return next;
	}

	public void setNext(String next) {
		this.next = next;
	}

	public FacebookPlacePaginationCursors getCursors() {
		return cursors;
	}

	public void setCursors(FacebookPlacePaginationCursors cursors) {
		this.cursors = cursors;
	}

	@Override
	public String toString() {
		return "FacebookPlacePaginationResponse [next=" + next + ", cursors=" + cursors + "]";
	}

}
