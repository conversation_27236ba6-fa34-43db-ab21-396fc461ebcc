package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

//TODO: Review FacebookCreds
@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPageAccessInfo implements Serializable {
	/**
	 *
	 */
	private static final long	serialVersionUID	= -6384569606304404346L;
	private String				pageId;
	private String				accessToken;
	private String				baseUrl;
	private String objectId;

	public FacebookPageAccessInfo() {

	}

	public FacebookPageAccessInfo(String pageId, String accessToken, String baseUrl) {
		super();
		this.pageId = pageId;
		this.accessToken = accessToken;
		this.baseUrl = baseUrl;
	}

	public String getPageId() {
		return pageId;
	}

	public void setPageId(String pageId) {
		this.pageId = pageId;
	}

	public String getAccessToken() {
		return accessToken;
	}

	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getBaseUrl() {
		return baseUrl;
	}

	public void setBaseUrl(String baseUrl) {
		this.baseUrl = baseUrl;
	}
	
	public String getObjectId() {
		return objectId;
	}

	public void setObjectId(String objectId) {
		this.objectId = objectId;
	}

	@Override
	public String toString() {
		return "FacebookPageAccessInfo [pageId=" + pageId + ", accessToken=" + accessToken + ", baseUrl=" + baseUrl + "]";
	}

}
