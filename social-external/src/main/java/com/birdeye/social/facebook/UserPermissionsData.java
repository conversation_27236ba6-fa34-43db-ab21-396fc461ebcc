/**
 *
 *
 */
package com.birdeye.social.facebook;

/**
 * <AUTHOR>
 *
 */
public class UserPermissionsData {
	
	private String permission;
	
	private String status;
	
	public String getPermission ()
	{
		return permission;
	}
	
	public void setPermission (String permission)
	{
		this.permission = permission;
	}
	
	public String getStatus ()
	{
		return status;
	}
	
	public void setStatus (String status)
	{
		this.status = status;
	}
	
	@Override
	public String toString()
	{
		return "UserPermissionsData [permission = "+permission+", status = "+status+"]";
	}
}
