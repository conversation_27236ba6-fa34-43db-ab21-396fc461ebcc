package com.birdeye.social.facebook;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPageCategory {

    private String id;
    private String name;
    private String api_enum;
    private List<FacebookPageCategory> fb_page_categories;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getApi_enum() {
        return api_enum;
    }

    public void setApi_enum(String api_enum) {
        this.api_enum = api_enum;
    }

    public List<FacebookPageCategory> getFb_page_categories() {
        return fb_page_categories;
    }

    public void setFb_page_categories(List<FacebookPageCategory> fb_page_categories) {
        this.fb_page_categories = fb_page_categories;
    }
}
