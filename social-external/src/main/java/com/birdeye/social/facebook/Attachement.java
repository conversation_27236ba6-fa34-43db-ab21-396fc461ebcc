package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Attachement implements Serializable {
	/**
	 *
	 */
	private static final long	serialVersionUID	= 7910382235627103800L;
	private String type;
	private FbMedia media;
	private MediaTarget target;
	private FbAttachement subattachments;
	//added for graph api version v6.0
	private String description;
	//added for graph api version v6.0
	private String media_type;
	private String url;

	private String unshimmed_url;
	public String getType() {
		return type;
	}
	public String getMedia_type() {
		return media_type;
	}
	public void setMedia_type(String media_type) {
		this.media_type = media_type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public FbMedia getMedia() {
		return media;
	}
	public void setMedia(FbMedia media) {
		this.media = media;
	}
	public MediaTarget getTarget() {
		return target;
	}
	public void setTarget(MediaTarget target) {
		this.target = target;
	}
	public FbAttachement getSubattachments() {
		return subattachments;
	}
	public void setSubattachments(FbAttachement subattachments) {
		this.subattachments = subattachments;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUnshimmed_url() {
		return unshimmed_url;
	}

	public void setUnshimmed_url(String unshimmed_url) {
		this.unshimmed_url = unshimmed_url;
	}

	public static boolean isImageType(Attachement attatchment) {
		String type = attatchment.getType();
		return type != null && (type.equalsIgnoreCase("photo") || type.equalsIgnoreCase("sticker")
				|| type.equalsIgnoreCase("animated_image_share") || type.equalsIgnoreCase("native_templates"));
	}
}
