package com.birdeye.social.facebook;

import java.util.List;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookFeedData extends FacebookBaseResponse {

	/**
	 *
	 */
	private static final long	serialVersionUID	= 8463760914544675155L;
	private List<FacebookFeed> data;
	private PagingInfo paging;
	
	public List<FacebookFeed> getData() {
		return data;
	}
	
	public void setData(List<FacebookFeed> data) {
		this.data = data;
	}
	
	public PagingInfo getPaging() {
		return paging;
	}
	
	public void setPaging(PagingInfo paging) {
		this.paging = paging;
	}
	
	@Override
	public String toString() {
		return "FacebookFeedData [data=" + data + ", paging=" + paging + "]";
	}
	
}
