package com.birdeye.social.facebook;

import lombok.ToString;

import java.util.List;

@ToString
public class FbComments {
	List<Comment> data;
	private PagingInfo paging;
	private FacebookSummary summary;

	public List<Comment> getData() {
		return data;
	}

	public void setData(List<Comment> data) {
		this.data = data;
	}

	public PagingInfo getPaging() {
		return paging;
	}

	public void setPaging(PagingInfo paging) {
		this.paging = paging;
	}

	public FacebookSummary getSummary() {
		return summary;
	}

	public void setSummary(FacebookSummary summary) {
		this.summary = summary;
	}
}
