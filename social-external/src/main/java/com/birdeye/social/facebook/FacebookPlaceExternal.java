package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPlaceExternal implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1792222104946214687L;
	/**
	 * 
	 */
	/*
	 * { "category": "Business Service", "category_list": [ { "id":
	 * "187133811318958", "name": "Business Service" }, { "id": "2401", "name":
	 * "City" } ], "location": { "city": "New Delhi", "country": "India",
	 * "latitude": 28.63096, "longitude": 77.21728, "zip": "560058" }, "name":
	 * "Bangalore", "id": "452173674990139" },
	 */
	private String id;
	private String name;
	private String singleLineAddress;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@JsonProperty(value = "singleLineAddress")
	public String getSingleLineAddress() {
		return singleLineAddress;
	}

	@JsonProperty(value = "single_line_address")
	public void setSingleLineAddress(String singleLineAddress) {
		this.singleLineAddress = singleLineAddress;
	}

	@Override
	public String toString() {
		return "FacebookPlace [id=" + id + ", name=" + name + ", singleLineAddress=" + singleLineAddress + "]";
	}


}
