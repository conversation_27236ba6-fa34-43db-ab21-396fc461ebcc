package com.birdeye.social.facebook;

public class FacebookCreds {

	private String clientId;
	private String clientSecret;
	private String tempAccessToken;
	private String baseUrl;
	private String pageId;
	private String userId;
	private String authCode;
	private String redirectUri;

	public String getRedirectUri() {return redirectUri;}

	public void setRedirectUri(String redirectUri) {this.redirectUri = redirectUri;}



	public String getAuthCode() {return authCode;}

	public void setAuthCode(String authCode) {this.authCode = authCode;}

	public FacebookCreds() {
	}

	public FacebookCreds(String clientId, String clientSecret,
			String tempAccessToken, String baseUrl) {
		super();
		this.clientId = clientId;
		this.clientSecret = clientSecret;
		this.tempAccessToken = tempAccessToken;
		this.baseUrl = baseUrl;
	}

	public FacebookCreds(String clientId, String clientSecret,
						 String authCode, String redirectUri, String baseUrl) {
		super();
		this.clientId = clientId;
		this.clientSecret = clientSecret;
		this.authCode = authCode;
		this.baseUrl = baseUrl;
		this.redirectUri = redirectUri;
	}

	public String getClientId() {
		return clientId;
	}
	public void setClientId(String clientId) {
		this.clientId = clientId;
	}
	public String getClientSecret() {
		return clientSecret;
	}
	public void setClientSecret(String clientSecret) {
		this.clientSecret = clientSecret;
	}
	public String getTempAccessToken() {
		return tempAccessToken;
	}
	public void setTempAccessToken(String tempAccessToken) {
		this.tempAccessToken = tempAccessToken;
	}
	public String getBaseUrl() {
		return baseUrl;
	}
	public void setBaseUrl(String baseUrl) {
		this.baseUrl = baseUrl;
	}

	public String getPageId() {
		return pageId;
	}

	public void setPageId(String pageId) {
		this.pageId = pageId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

}
