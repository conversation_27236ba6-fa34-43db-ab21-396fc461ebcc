package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FacebookPostInfo implements Serializable {
	
	/**
	 *
	 */
	private static final long	serialVersionUID	= 6988404633437217017L;
	private String				postId;
	
	public String getPostId() {
		return postId;
	}
	
	public void setPostId(String postId) {
		this.postId = postId;
	}
	
	public FacebookPostInfo() {
		
	}
	public FacebookPostInfo(String postId) {
		this.postId = postId;
	}
	
	@Override
	public String toString() {
		return "FacebookPostInfo [postId=" + postId + "]";
	}
	
}
