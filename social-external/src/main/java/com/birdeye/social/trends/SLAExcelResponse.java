package com.birdeye.social.trends;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@lombok.Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class SLAExcelResponse {


    private SLAExcelSummaryResponse summaryResponse;
    private SLAExcelResponseTime responseTime;
    private SLAExcelResponseRate responseRate;
    private SLAExcelLocationLeaderBoard locationLeaderBoard;
    private SLAExcelResponseByUser responseByUser;
    private SLAExcelResponseSummaryOverview summaryOverview;

    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelSummaryResponse {
        private Long msgReceived;
        private Long responded;
        private String responseRate;
        private String avgResponseTime;
        private Long unresponded;
    }

    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelResponseTime {
        private String date;
        private String channel;
        private Long responded;
        private String responseTime;
    }
    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelResponseRate {
        private String date;
        private String channel;
        private Long msgReceived;
        private Long responded;
        private String responseRate;
    }
    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelLocationLeaderBoard {
        private String location;
        private Long msgReceived;
        private Long responded;
        private String responseRate;
        private String responseTime;
    }
    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelResponseByUser {
        private String user;
        private Long responded;
        private String responseTime;
    }
    @Getter
    @Setter
    @lombok.Data
    @Builder
    public static class SLAExcelResponseSummaryOverview {
        private String pageName;
        private String channel;

        private String incomingMsg;
        private String msgReceivedAt;
        private String commentedBy;

        private String response;
        private String respondSendAt;
        private String respondedBy;

        private String responseTime;
        private String locationName;
        private String messageType;

        private String incomingMsgImgUrls;
        private String incomingMsgVidUrls;
        private String responseImgUrls;
        private String responseVidUrls;

    }
}
