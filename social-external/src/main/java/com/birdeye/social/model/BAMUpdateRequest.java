/**
 *
 *
 */
package com.birdeye.social.model;

/**
 * <AUTHOR>
 *
 */
public class BAMUpdateRequest {
	
	private String	channel;
	
	private Integer	businessId;
	
	private String	pageLink;
	
	private String	pageId; // fb facebook page id and gmb location id

	private String placeId; // gmb placeId
	
	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the pageLink
	 */
	public String getPageLink() {
		return pageLink;
	}
	
	/**
	 * @param pageLink
	 *            the pageLink to set
	 */
	public void setPageLink(String pageLink) {
		this.pageLink = pageLink;
	}
	
	/**
	 * @return the pageId
	 */
	public String getPageId() {
		return pageId;
	}
	
	/**
	 * @param pageId
	 *            the pageId to set
	 */
	public void setPageId(String pageId) {
		this.pageId = pageId;
	}
	
	/**
	 * @return the channel
	 */
	public String getChannel() {
		return channel;
	}

	public String getPlaceId() {
		return placeId;
	}

	public void setPlaceId(String placeId) {
		this.placeId = placeId;
	}

	/**
	 * @param channel
	 *            the channel to set
	 */
	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	public BAMUpdateRequest(String channel, Integer businessId, String pageLink, String pageId) {
		super();
		this.channel = channel;
		this.businessId = businessId;
		this.pageLink = pageLink;
		this.pageId = pageId;
	}

	public BAMUpdateRequest(String channel, Integer businessId, String pageLink, String pageId, String placeId) {
		super();
		this.channel = channel;
		this.businessId = businessId;
		this.pageLink = pageLink;
		this.pageId = pageId;
		this.placeId = placeId;
	}
	
	/*
	 * (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */

	@Override
	public String toString() {
		return "BAMUpdateRequest{" +
				"channel='" + channel + '\'' +
				", businessId=" + businessId +
				", pageLink='" + pageLink + '\'' +
				", pageId='" + pageId + '\'' +
				", placeId='" + placeId + '\'' +
				'}';
	}
}
