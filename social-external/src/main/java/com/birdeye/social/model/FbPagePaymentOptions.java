package com.birdeye.social.model;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbPagePaymentOptions {

	private Integer amex;
	private Integer cash_only;
	private Integer discover;
	private Integer mastercard;
	private Integer visa;

	public Integer getAmex() {
		return amex;
	}

	public void setAmex(Integer amex) {
		this.amex = amex;
	}

	public Integer getCash_only() {
		return cash_only;
	}

	public void setCash_only(Integer cash_only) {
		this.cash_only = cash_only;
	}

	public Integer getDiscover() {
		return discover;
	}

	public void setDiscover(Integer discover) {
		this.discover = discover;
	}

	public Integer getMastercard() {
		return mastercard;
	}

	public void setMastercard(Integer mastercard) {
		this.mastercard = mastercard;
	}

	public Integer getVisa() {
		return visa;
	}

	public void setVisa(Integer visa) {
		this.visa = visa;
	}
}




