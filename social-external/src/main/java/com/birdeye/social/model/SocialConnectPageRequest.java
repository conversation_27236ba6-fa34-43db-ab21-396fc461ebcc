package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialConnectPageRequest implements Serializable {

    private List<String> integrationIds;
    private String channel;
    private List<LinkedinConnectRequest> linkedinConnectRequests;

    public List<String> getIntegrationIds() {
        return integrationIds;
    }

    public void setIntegrationIds(List<String> integrationIds) {
        this.integrationIds = integrationIds;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public List<LinkedinConnectRequest> getLinkedinConnectRequests() {
        return linkedinConnectRequests;
    }

    public void setLinkedinConnectRequests(List<LinkedinConnectRequest> linkedinConnectRequests) {
        this.linkedinConnectRequests = linkedinConnectRequests;
    }

    public SocialConnectPageRequest(){}

    public SocialConnectPageRequest(List<String> integrationIds, String channel) {
        this.integrationIds = integrationIds;
        this.channel = channel;
    }
}
