package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class TwitterPlace implements Serializable {

    private String id;
    private String name;
    private String full_name;
    private String country;
    private String country_code;
}
