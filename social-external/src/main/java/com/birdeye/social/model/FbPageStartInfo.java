package com.birdeye.social.model;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbPageStartInfo {

	private String type;
	private PageStartDate date;

	static class PageStartDate {
		private Integer day;
		private Integer month;
		private Integer year;

		public Integer getDay() {
			return day;
		}

		public void setDay(Integer day) {
			this.day = day;
		}

		public Integer getMonth() {
			return month;
		}

		public void setMonth(Integer month) {
			this.month = month;
		}

		public Integer getYear() {
			return year;
		}

		public void setYear(Integer year) {
			this.year = year;
		}
	}
}




