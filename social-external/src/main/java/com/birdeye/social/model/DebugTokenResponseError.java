package com.birdeye.social.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DebugTokenResponseError implements Serializable {

	private static final long serialVersionUID = -1922599533488400053L;

	private Integer code;
	private String message;
	private Integer subcode;

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Integer getSubcode() {
		return subcode;
	}

	public void setSubcode(Integer subcode) {
		this.subcode = subcode;
	}

	@Override
	public String toString() {
		return "DebugTokenResponseError [code=" + code + ", message=" + message + ", subcode=" + subcode + "]";
	}

}
