package com.birdeye.social.model;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbPicture extends FacebookBaseResponse {
	/**
	 *
	 */
	private static final long	serialVersionUID	= -5222330000251341404L;
	private Data data;
	
	public FbPicture(){
		
	}

	public FbPicture(Data data) {
		super();
		this.data = data;
	}

	public Data getData() {
		return data;
	}

	public void setData(Data data) {
		this.data = data;
	}

	public String getUrl() {
		return data.getUrl();
	}
	
	public class Data {
		String url;

		public String getUrl() {
			return url;
		}

		public void setUrl(String url) {
			this.url = url;
		}
		
		@Override
		public String toString() {
			StringBuilder builder = new StringBuilder();
			builder.append("Data [url=");
			builder.append(url);
			builder.append("]");
			return builder.toString();
		}
		
	}

	@Override
	public String toString() {
		return "FbPicture [data=" + data + "]";
	}
	
}


