package com.birdeye.social.model;

import java.io.Serializable;

import com.birdeye.social.facebook.UserPermissions;
import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FbUserProfileInfo implements Serializable {
	/**
	 *
	 */
	private static final long		serialVersionUID	= 7224290698481555808L;
	private String					id;
	private String					first_name;
	private String					last_name;
	private String					link;
	private FbPicture				picture;
	private FacebookErrorResponse	error;
	private String					name;
	private String					email;
	private UserPermissions			permissions;
	private String					phone;
	
	/**
	 * @return the link
	 */
	public String getLink() {
		return link;
	}
	
	/**
	 * @param link
	 *            the link to set
	 */
	public void setLink(String link) {
		this.link = link;
	}
	
	public String getId() {
		return id;
	}
	
	public void setId(String id) {
		this.id = id;
	}
	
	public String getFirst_name() {
		return first_name;
	}
	
	public void setFirst_name(String first_name) {
		this.first_name = first_name;
	}
	
	public String getLast_name() {
		return last_name;
	}
	
	public void setLast_name(String last_name) {
		this.last_name = last_name;
	}
	
	public FbPicture getPicture() {
		return picture;
	}
	
	public void setPicture(FbPicture picture) {
		this.picture = picture;
	}
	
	public FacebookErrorResponse getError() {
		return error;
	}
	
	public void setError(FacebookErrorResponse error) {
		this.error = error;
	}
	
	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}
	
	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	/*
	 * (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "FbUserProfileInfo [id=" + id + ", first_name=" + first_name + ", last_name=" + last_name + ", link=" + link + ", picture=" + picture + ", error=" + error + ", name=" + name
				+ ", email=" + email + "]";
	}
	
	public FbUserProfileInfo() {
		
	}
	
	public FbUserProfileInfo(FbUserProfileInfo user) {
		super();
		this.id = user.getId();
		this.first_name = user.getFirst_name();
		this.last_name = user.getLast_name();
		this.link = user.getLink();
		this.picture = user.getPicture();
		this.error = user.getError();
		this.name = user.getName();
		this.email = user.getEmail();
	}
	
	/**
	 * @return the email
	 */
	public String getEmail() {
		return email;
	}
	
	/**
	 * @param email
	 *            the email to set
	 */
	public void setEmail(String email) {
		this.email = email;
	}

	/**
	 * @return the permissions
	 */
	public UserPermissions getPermissions() {
		return permissions;
	}

	/**
	 * @param permissions the permissions to set
	 */
	public void setPermissions(UserPermissions permissions) {
		this.permissions = permissions;
	}
	
}
