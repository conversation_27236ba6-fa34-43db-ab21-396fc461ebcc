package com.birdeye.social.model;

import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DebugTokenResponse extends FacebookBaseResponse {

	private static final long	serialVersionUID	= -4508297168114449585L;
	private TokenData			data;

	public TokenData getData() {
		return data;
	}

	public void setData(TokenData data) {
		this.data = data;
	}

	@Override
	public String toString() {
		return "DebugTokenResponse [data=" + data + "]";
	}

}
