package com.birdeye.social.google;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.entities.GoogleMessagesAgent;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.google.api.services.businesscommunications.v1.model.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.google.api.services.businesscommunications.v1.BusinessCommunications.*;
import com.google.api.services.businesscommunications.v1.BusinessCommunications.Brands.*;

import java.io.IOException;
import java.time.DayOfWeek;
import java.util.*;
import java.util.List;

@Service
public class GoogleBusinessCommServiceImpl implements GoogleBusinessCommService {

	private final Logger logger = LoggerFactory.getLogger(GoogleBusinessCommServiceImpl.class);

	private final static String INVALID_AGENT_NAME_ERROR = "Google My Business listing with a name matching";

	@Autowired
	private GoogleBusinessCommCredentialsService googleBizCommCredsService;

	private final List<Hours> hoursAvailable = Collections.singletonList(
		new Hours()
			.setStartDay(DayOfWeek.MONDAY.name())
			.setStartTime(new TimeOfDay().setHours(0).setMinutes(0))
			.setEndDay(DayOfWeek.SUNDAY.name())
			.setEndTime(new TimeOfDay().setHours(23).setMinutes(59).setSeconds(59))
			.setTimeZone("America/Los_Angeles")
	);

	@Override
	public Agent createAgent(String brandName, String agentDisplayName, String welcomeMsg, String offlineMsg, String logoUrl) throws Exception {
		final Map<String, ConversationalSetting> conversationalSettings = new HashMap<String, ConversationalSetting>() {{
			put("en", new ConversationalSetting()
				.setPrivacyPolicy(new PrivacyPolicy().setUrl(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
						.getGoogleMessagesPrivacyPolicyUrl()))
				.setWelcomeMessage(new WelcomeMessage().setText(welcomeMsg))
				.setOfflineMessage(new OfflineMessage().setText(offlineMsg)));
		}};

		final Agent agent = new Agent()
			.setDisplayName(agentDisplayName)
			.setBusinessMessagesAgent(new BusinessMessagesAgent()
				.setDefaultLocale("en")
				.setLogoUrl(logoUrl)
				.setConversationalSettings(conversationalSettings)
				.setPrimaryAgentInteraction(new SupportedAgentInteraction()
					.setInteractionType("BOT")
					.setBotRepresentative(new BotRepresentative()
						.setBotMessagingAvailability(new MessagingAvailability()
							.setHours(hoursAvailable))))
				.setAdditionalAgentInteractions(Collections.singletonList(new SupportedAgentInteraction()
					.setInteractionType("HUMAN")
					.setHumanRepresentative(new HumanRepresentative()
						.setHumanMessagingAvailability(new MessagingAvailability()
							.setHours(hoursAvailable)))))
				.setEntryPointConfigs(Collections.singletonList(new BusinessMessagesEntryPointConfig()
						.setAllowedEntryPoint("LOCATION"))));

		final Agents.Create request = googleBizCommCredsService.getAppBuilder()
			.build().brands().agents().create(brandName, agent);

		Agent createdAgent = request.execute();
		logger.info("createAgent: Agent successfully created {}", createdAgent.toPrettyString());
		return createdAgent;
	}

	@Override
	public Agent updateAgentDisplayName(Agent agent, String displayName) {
		return null;
	}

	@Override
	public Agent updateAgentLogo(Agent agent, String logoUrl) {
		return null;
	}

	@Override
	public Agent updateAgentConversationalSettings(Agent agent, ConversationalSetting conversationalSetting) {
		return null;
	}

	@Override
	public Agent updateAgentPrimaryAgentInteraction(Agent agent, SupportedAgentInteraction supportedAgentInteraction) {
		return null;
	}

	@Override
	public Agent updateAgent(String agentName, String agentDisplayName, String welcomeMsg, String offlineMsg, String logoUrl) throws Exception {
		final Map<String, ConversationalSetting> convSettings = new HashMap<String, ConversationalSetting>() {{
			put("en", new ConversationalSetting()
					.setPrivacyPolicy(new PrivacyPolicy().setUrl(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
							.getGoogleMessagesPrivacyPolicyUrl()))
					.setWelcomeMessage(new WelcomeMessage().setText(welcomeMsg))
					.setOfflineMessage(new OfflineMessage().setText(offlineMsg)));
		}};

		final Agent content = new Agent()
				.setDisplayName(agentDisplayName)
				.setBusinessMessagesAgent(new BusinessMessagesAgent()
					.setConversationalSettings(convSettings)
					.setLogoUrl(logoUrl));

		final Agents.Patch patch = googleBizCommCredsService.getAppBuilder()
				.build().brands().agents().patch(agentName, content);
		patch.setUpdateMask("displayName,businessMessagesAgent.conversationalSettings.en,businessMessagesAgent.logoUrl");

		final Agent patchedAgent = patch.execute();
		logger.info("updateLaunchedAgent: Patch successful {}", patchedAgent.toPrettyString());
		return patchedAgent;
	}

	@Override
	public Agent updateLaunchedAgent(String agentName, String welcomeMsg, String offlineMsg) throws IOException {
		final Map<String, ConversationalSetting> convSettings = new HashMap<String, ConversationalSetting>() {{
			put("en", new ConversationalSetting()
				.setPrivacyPolicy(new PrivacyPolicy().setUrl(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
						.getGoogleMessagesPrivacyPolicyUrl()))
				.setWelcomeMessage(new WelcomeMessage().setText(welcomeMsg))
				.setOfflineMessage(new OfflineMessage().setText(offlineMsg)));
		}};

		final Agent content = new Agent()
			.setBusinessMessagesAgent(new BusinessMessagesAgent()
				.setConversationalSettings(convSettings));

		final Agents.Patch patch = googleBizCommCredsService.getAppBuilder()
				.build().brands().agents().patch(agentName, content);
		patch.setUpdateMask("businessMessagesAgent.conversationalSettings.en");

		final Agent patchedAgent = patch.execute();
		logger.info("updateLaunchedAgent: Patch successful {}", patchedAgent.toPrettyString());
		return patchedAgent;
	}

	@Override
	public void unLaunchAgent(GoogleMessagesAgent agent) throws IOException {
		AgentLaunch agentLaunch  = new AgentLaunch().setBusinessMessages(new BusinessMessagesLaunch()
				.setLaunchDetails(new HashMap<String, BusinessMessagesEntryPointLaunch>(){{
					put("LOCATION", new BusinessMessagesEntryPointLaunch()
							.setEntryPoint("LOCATION")
							.setLaunchState("LAUNCH_STATE_UNLAUNCHED")
					);
				}})
		);
		Agents.UpdateLaunch request = googleBizCommCredsService.getAppBuilder()
				.build().brands().agents().updateLaunch(agent.getAgentName() + "/launch",agentLaunch);

		AgentLaunch updateLaunch = request.execute();
		logger.info("requestLocationVerification: Location launch successfully created {}", updateLaunch);

		logger.info("state: {}",updateLaunch.getBusinessMessages().getLaunchDetails());
		//TODO: Manvi add error handling if not unlaunched

	}

	@Override
	public Agent getAgent(String agentName) {
		return null;
	}


	@Override
	public List<Brand> getAllAgents(String brandName) {
		return null;
	}

	@Override
	public void deleteAgent(String agentName) {
		try {
			Agents.Delete delete = googleBizCommCredsService.getAppBuilder().build().brands().agents().delete(agentName);
			delete.execute();
		}catch (Exception e){
			throw new BirdeyeSocialException(ErrorCodes.AGENT_DELETION_ERROR,"Agent's deleted error");
		}
	}

	@Override
	public AgentVerification requestAgentVerification(String agentName, BusinessCoreUser user, String bizWebsiteUrl, String accessToken) throws Exception {
		final Agents.RequestVerification request = googleBizCommCredsService.getUserBuilder(accessToken)
			.build().brands().agents().requestVerification(agentName, new RequestAgentVerificationRequest()
				.setAgentVerificationContact(new AgentVerificationContact()
					.setPartnerName(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGoogleMessagesPartnerName())
					.setPartnerEmailAddress(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGoogleMessagesPartnerEmailId())
					.setBrandContactName(user.getName())
					.setBrandContactEmailAddress(user.getEmailId())
					.setBrandWebsiteUrl(bizWebsiteUrl)
				)
			);

		AgentVerification agentVerification = request.execute();
		logger.info("requestAgentVerification: AgentVerification successfully created {}", agentVerification.toPrettyString());

		// Agent verificationState should be set to VERIFICATION_STATE_VERIFIED
		if (!agentVerification.getVerificationState().equals(AgentVerificationState.VERIFICATION_STATE_VERIFIED.toString())) {
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_VERIFIED,
				"Agent's verificationState is not " + AgentVerificationState.VERIFICATION_STATE_VERIFIED.toString());
		}
		return agentVerification;
	}

	@Override
	public AgentLaunch requestAgentLaunch(String agentName) throws Exception {
		final Agents.RequestLaunch request = googleBizCommCredsService.getAppBuilder()
			.build().brands().agents().requestLaunch(agentName, new RequestAgentLaunchRequest()
				.setAgentLaunch(new AgentLaunch()
					.setName(agentName)
					.setBusinessMessages(new BusinessMessagesLaunch()
						.setLaunchDetails(new HashMap<String, BusinessMessagesEntryPointLaunch>(){{
							put("LOCATION", new BusinessMessagesEntryPointLaunch()
								.setEntryPoint("LOCATION")
								.setLaunchState("LAUNCH_STATE_LAUNCHED")
							);
						}})
					)
				)
			);

		AgentLaunch agentLaunch = request.execute();
		logger.info("requestAgentLaunch: AgentLaunch successfully created {}", agentLaunch.toPrettyString());
		if (!agentLaunch.getBusinessMessages().getLaunchDetails().get("LOCATION").getLaunchState().equals(AgentLaunchState.LAUNCH_STATE_LAUNCHED.name())) {
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_NOT_LAUNCHED,
					"Agent's launchState is not " + AgentLaunchState.LAUNCH_STATE_LAUNCHED.name());
		}
		return agentLaunch;
	}

	@Override
	public AgentVerification getAgentVerification(String agentName) throws Exception {
		final Agents.GetVerification request = googleBizCommCredsService.getAppBuilder()
				.build().brands().agents().getVerification(agentName);

		AgentVerification agentVerification = request.execute();
		logger.info("getAgentLaunch: Get agent launch status {}", agentVerification.toPrettyString());
		return agentVerification;
	}

	@Override
	public AgentLaunch getAgentLaunchState(String agentName) throws Exception{
		final Agents.GetLaunch request = googleBizCommCredsService.getAppBuilder().build().brands().agents().getLaunch(agentName);
		AgentLaunch agentLaunch = request.execute();
		logger.info("getAgentLaunch: Get agent launch status {}", agentLaunch.toPrettyString());
		return agentLaunch;
	}


	@Override
	public Brand createBrand(String brandDisplayName) throws Exception {
		final Brands.Create request = googleBizCommCredsService.getAppBuilder() // TODO @mahak clean imports
				.build().brands().create(new Brand().setDisplayName(brandDisplayName));
		Brand brand = request.execute();
		logger.info("createBrand: Brand successfully created {}", brand.toPrettyString());
		return brand;
	}

	@Override
	public Brand updateBrand(Brand brand, String displayName) throws Exception {
		return null;
	}

	@Override
	public Brand getBrand(String brandName) throws Exception {
		return null;
	}

	@Override
	public List<Brand> getAllBrands() throws Exception {
		return null;
	}

	@Override
	public void deleteBrand(String brandName) throws Exception {
		Delete delete = googleBizCommCredsService.getAppBuilder().build().brands().delete(brandName);
		delete.execute();
	}

	@Override
	public void deleteLocation(String locationName) throws Exception {
		final Brands.Locations.Delete request = googleBizCommCredsService.getAppBuilder()
				.build().brands().locations().delete(locationName);
		request.execute();
		logger.info("deleteLocation: Location deleted successfully");
	}

	@Override
	public boolean isInvalidNameError(String errorMessage) {
		logger.info("errorMessage: {}",errorMessage);
		return !StringUtils.isEmpty(errorMessage) && errorMessage.contains(INVALID_AGENT_NAME_ERROR);
	}
}
