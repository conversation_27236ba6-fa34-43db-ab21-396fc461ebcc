package com.birdeye.social.google;

import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

@Service
public class GoogleBusinessCommAccessTokenServiceImpl implements GoogleBusinessCommAccessTokenService {

	private final String CREDENTIALS_FILE_NAME = "service-account-creds.json";
	private final Logger logger = LoggerFactory.getLogger(GoogleBusinessCommAccessTokenServiceImpl.class);

	@Cacheable(value = "googleMessagesCreds", key = "#root.methodName")
	public String getAppAccessToken() {
		try {
			GoogleCredential creds = GoogleCredential.fromStream(
					new FileInputStream(Objects.requireNonNull(GoogleBusinessCommCredentialsServiceImpl.class.getClassLoader()
							.getResource(CREDENTIALS_FILE_NAME))
							.getFile()))
					.createScoped(Arrays.asList(
							"https://www.googleapis.com/auth/businesscommunications",
							"https://www.googleapis.com/auth/userinfo.email",
							"https://www.googleapis.com/auth/businessmessages"));
			creds.refreshToken();
			return creds.getAccessToken();
		} catch (IOException ioException) {
			logger.error("Error reading credentials JSON file");
		}
		return null;
	}
}
