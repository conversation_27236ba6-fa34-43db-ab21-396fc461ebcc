package com.birdeye.social.google;

import com.birdeye.social.external.request.google.GetAuthTokenRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.businesscommunications.v1.BusinessCommunications;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class GoogleBusinessCommCredentialsServiceImpl implements GoogleBusinessCommCredentialsService {

	@Autowired
	private GoogleBusinessCommAccessTokenService googleBizCommAccessTokenService;

	private static final String SERVICE_ACCOUNT_PROJECT_ID = "aggregation-search-api";
	private final String API_URL = "https://businesscommunications.googleapis.com/";
	private final Logger logger = LoggerFactory.getLogger(GoogleBusinessCommCredentialsServiceImpl.class);

	private GoogleCredential getUserCredentials(String accessToken) {
		return new GoogleCredential().setAccessToken(accessToken);
	}

	private GoogleCredential getAppCredentials() {
		return new GoogleCredential()
				.setAccessToken(googleBizCommAccessTokenService.getAppAccessToken());
	}

	@Override
	@Deprecated
	public BusinessCommunications.Builder getAppBuilder() {
		BusinessCommunications.Builder builder = null;
		try {
			final GoogleCredential creds = getAppCredentials();
			HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
			JacksonFactory jsonFactory = JacksonFactory.getDefaultInstance();

			// Create instance of the Business Communications API
			builder = new BusinessCommunications
					.Builder(httpTransport, jsonFactory, null)
					.setApplicationName(SERVICE_ACCOUNT_PROJECT_ID);

			// Set the API credentials and endpoint
			builder.setHttpRequestInitializer(creds);
			builder.setRootUrl(API_URL);
		} catch (Exception e) {
			logger.error("getAppBuilder: Error creating BusinessCommunications.Builder", e);
		}
		return builder;
	}

	@Override
	public BusinessCommunications.Builder getUserBuilder(String accessToken) {
		BusinessCommunications.Builder builder = null;
		try {
			final GoogleCredential appCreds = getAppCredentials();
			final GoogleCredential userCreds = getUserCredentials(accessToken);
			HttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
			JacksonFactory jsonFactory = JacksonFactory.getDefaultInstance();

			// Create instance of the Business Communications API
			builder = new BusinessCommunications
					.Builder(httpTransport, jsonFactory, null)
					.setApplicationName(SERVICE_ACCOUNT_PROJECT_ID);

			// Set the API credentials and endpoint
			builder.setHttpRequestInitializer(userCreds);
			builder.setRootUrl(API_URL);
		} catch (Exception e) {
			logger.error("getAppBuilder: Error creating BusinessCommunications.Builder", e);
		}
		return builder;
	}
}
