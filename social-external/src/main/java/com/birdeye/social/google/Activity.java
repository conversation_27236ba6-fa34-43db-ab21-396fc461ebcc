/**
 * 
 */
package com.birdeye.social.google;

import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * <AUTHOR>
 *
 */
public class Activity {

    private String id;
    private String url;
    private ActivityObject object;

    private String published;
    private Date activityDate;
    private Actor actor;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public ActivityObject getObject() {
        return object;
    }

    public void setObject(ActivityObject object) {
        this.object = object;
    }

    public String getPublished() {
        return published;
    }

    @Override
	public String toString() {
		return "Activity [id=" + id + ", url=" + url + ", object=" + object + ", published=" + published
				+ ", activityDate=" + activityDate + ", actor=" + actor + "]";
	}

	public void setPublished(String published) {
        this.published = published;

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        //Setting activity date
        try {
            this.activityDate = format.parse(this.published);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Date getActivityDate() {
        return activityDate;
    }

    public Actor getActor() {
        return actor;
    }

    public void setActor(Actor actor) {
        this.actor = actor;
    }
}
