package com.birdeye.social.twitter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class TwitterUser implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private String id_str;
    private String name;
    private String screen_name;
    private String profile_image_url_https;

    public TwitterUser() {
    }
    
    public TwitterUser(String id, String id_str, String name, String screen_name, String profile_image_url_https) {
        this.id = id;
        this.id_str = id_str;
        this.name = name;
        this.screen_name = screen_name;
        this.profile_image_url_https = profile_image_url_https;
    }

    public String getId() {
        return id;
    }

    public String getIdStr() {
        return id_str;
    }

    public String getName() {
        return name;
    }

    public String getScreenName() {
        return screen_name;
    }

    public String getProfileImageUrlHttps() {
        return profile_image_url_https;
    }

	public String getId_str() {
		return id_str;
	}

	public void setId_str(String id_str) {
		this.id_str = id_str;
	}

	public String getScreen_name() {
		return screen_name;
	}

	public void setScreen_name(String screen_name) {
		this.screen_name = screen_name;
	}

	public String getProfile_image_url_https() {
		return profile_image_url_https;
	}

	public void setProfile_image_url_https(String profile_image_url_https) {
		this.profile_image_url_https = profile_image_url_https;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setName(String name) {
		this.name = name;
	}

}
