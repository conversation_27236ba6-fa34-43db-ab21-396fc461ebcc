package com.birdeye.social.twitter.TwitterTimeline;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include. NON_NULL)
public class Get2UsersIdMentionsResponse {

    @JsonProperty("data")
    private List<Tweet> data ;
    @JsonProperty("includes")
    private Expansions includes;
    @JsonProperty("meta")
    private TwitterMetaData meta;
}
