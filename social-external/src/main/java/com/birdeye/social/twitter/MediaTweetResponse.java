package com.birdeye.social.twitter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class MediaTweetResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5649189841863434002L;

	List<MediaTweet> mediaTweets = new ArrayList<MediaTweet>();
	
	public MediaTweetResponse() {

	}

	public MediaTweetResponse(List<MediaTweet> mediaTweets) {
		super();
		this.mediaTweets = mediaTweets;
	}

	public List<MediaTweet> getMediaTweets() {
		return mediaTweets;
	}

	public void setMediaTweets(List<MediaTweet> mediaTweets) {
		this.mediaTweets = mediaTweets;
	}

}
