package com.birdeye.social.twitter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.twitter.clientlib.model.Problem;
import com.twitter.clientlib.model.Tweet;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TwitterGetResponse {
    @JsonProperty("data")
    private TweetDataList data ;
    @JsonProperty("errors")
    private List<Problem> errors;
    @JsonProperty("includes")
    private Expansions includes;
}
