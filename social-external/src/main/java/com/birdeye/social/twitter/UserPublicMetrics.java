package com.birdeye.social.twitter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserPublicMetrics {

    @JsonProperty("followers_count")
    private Integer followersCount;
    @JsonProperty("following_count")
    private Integer followingCount;
    @JsonProperty("listed_count")
    private Integer listedCount;
    @JsonProperty("tweet_count")
    private Integer tweetCount;

}
