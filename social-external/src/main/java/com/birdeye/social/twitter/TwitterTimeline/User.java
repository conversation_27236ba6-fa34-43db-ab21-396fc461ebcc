package com.birdeye.social.twitter.TwitterTimeline;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.twitter.clientlib.model.UserEntities;
import com.twitter.clientlib.model.UserPublicMetrics;
import com.twitter.clientlib.model.UserWithheld;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.net.URL;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include. NON_NULL)
public class User {
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("description")
    private String description;
    @JsonProperty("entities")
    private UserEntities entities;
    @JsonProperty("id")
    private String id;
    @JsonProperty("location")
    private String location;
    @JsonProperty("name")
    private String name;
    @JsonProperty("pinned_tweet_id")
    private String pinnedTweetId;
    @JsonProperty("profile_image_url")
    private URL profileImageUrl;
    @JsonProperty("protected")
    private Boolean _protected;
    @JsonProperty("public_metrics")
    private UserPublicMetrics publicMetrics;
    @JsonProperty("url")
    private String url;
    @JsonProperty("username")
    private String username;
    @JsonProperty("verified")
    private Boolean verified;
    @JsonProperty("withheld")
    private UserWithheld withheld;
}
