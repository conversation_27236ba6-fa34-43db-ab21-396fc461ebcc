package com.birdeye.social.twitter.TwitterTimeline;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.twitter.clientlib.model.FullTextEntities;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include. NON_NULL)
public class Tweet {

    @JsonProperty("attachments")
    private TweetAttachments attachments;
    @JsonProperty("author_id")
    private String authorId;
    @JsonProperty("conversation_id")
    private String conversationId;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("entities")
    private FullTextEntities entities;
    @JsonProperty("id")
    private String id;
    @JsonProperty("in_reply_to_user_id")
    private String inReplyToUserId;
    @JsonProperty("lang")
    private String lang;
    @JsonProperty("public_metrics")
    private TweetPublicMetrics publicMetrics;
    @JsonProperty("referenced_tweets")
    private List<TweetReferencedTweets> referencedTweets;
    @JsonProperty("source")
    private String source;
    @JsonProperty("text")
    private String text;
}
