package com.birdeye.social.twitter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import com.twitter.clientlib.model.*;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Expansions {
    @SerializedName("media")
    private List<TwitterMediaData> media ;
    @SerializedName("places")
    private List<Place> places ;
    @SerializedName("polls")
    private List<Poll> polls ;
    @SerializedName("topics")
    private List<Topic> topics;
    @SerializedName("tweets")
    private List<Tweet> tweets;
    @SerializedName("users")
    private List<TwitterV2ProfileResponse> users;
}
