package com.birdeye.social.es;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ElasticSearchBaseRequest implements Serializable {

	private static final long serialVersionUID = -6798265533235059922L;

	private Object routingIdentifier;
	private String index;
	private String type;
	private String query;

	public Object getRoutingIdentifier() {
		return routingIdentifier;
	}

	public void setRoutingIdentifier(Object routingIdentifier) {
		this.routingIdentifier = routingIdentifier;
	}

	public String getIndex() {
		return index;
	}

	public void setIndex(String index) {
		this.index = index;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getQuery() {
		return query;
	}

	public void setQuery(String query) {
		this.query = query;
	}

	@Override
	public String toString() {
		return "ElasticSearchBaseRequest [routingIdentifier=" + routingIdentifier + ", index=" + index + ", type="
				+ type + ", query=" + query + "]";
	}
}
