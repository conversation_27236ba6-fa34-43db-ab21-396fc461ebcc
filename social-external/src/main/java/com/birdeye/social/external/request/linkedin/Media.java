package com.birdeye.social.external.request.linkedin;

import com.birdeye.social.linkedin.LinkedinDescription;
import com.birdeye.social.linkedin.LinkedinThumbnail;
import com.birdeye.social.linkedin.response.LinkedinProjection;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Media {

    private String status;
    private String originalUrl;
    private Title title;
    private String media;
    private LinkedinDescription description;
    private List<LinkedinThumbnail> thumbnail;

    @JsonProperty("media~")
    private LinkedinProjection mediaProjection;

    public String getMedia() {
        return media;
    }

    public void setMedia(String media) {
        this.media = media;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOriginalUrl() {
        return originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    public Title getTitle() {
        return title;
    }

    public void setTitle(Title title) {
        this.title = title;
    }

    public LinkedinDescription getDescription() {
        return description;
    }

    public void setDescription(LinkedinDescription description) {
        this.description = description;
    }

    public List<LinkedinThumbnail> getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(List<LinkedinThumbnail> thumbnail) {
        this.thumbnail = thumbnail;
    }

    public LinkedinProjection getMediaProjection() {
        return mediaProjection;
    }

    public void setMediaProjection(LinkedinProjection mediaProjection) {
        this.mediaProjection = mediaProjection;
    }

    @Override
    public String toString() {
        return "Media{" +
                "status='" + status + '\'' +
                ", originalUrl='" + originalUrl + '\'' +
                ", title=" + title +
                ", media='" + media + '\'' +
                ", description=" + description +
                '}';
    }
}
