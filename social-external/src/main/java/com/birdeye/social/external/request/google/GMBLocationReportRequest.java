package com.birdeye.social.external.request.google;

import java.util.List;

public class GMBLocationReportRequest {
  
  private List<String> locationNames;
  private BasicMetricRequest basicRequest;
  
  public GMBLocationReportRequest() {
    super();
  }
  public GMBLocationReportRequest(List<String> locationNames, BasicMetricRequest basicRequest) {
    super();
    this.locationNames = locationNames;
    this.basicRequest = basicRequest;
  }
  public List<String> getLocationNames() {
    return locationNames;
  }
  public void setLocationNames(List<String> locationNames) {
    this.locationNames = locationNames;
  }
  public BasicMetricRequest getBasicRequest() {
    return basicRequest;
  }
  public void setBasicRequest(BasicMetricRequest basicRequest) {
    this.basicRequest = basicRequest;
  }
  @Override
  public String toString() {
    return "GMBLocationReportRequest [locationNames=" + locationNames + ", basicRequest="
        + basicRequest + "]";
  } 
  
  
}
  


