package com.birdeye.social.external.request.google;

import java.io.Serializable;
import java.util.List;

public class GMBAttributesResponse implements Serializable {

    private List<AttributeMetadataResponse> attributes;
    private String nextPageToken;

    public List<AttributeMetadataResponse> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<AttributeMetadataResponse> attributes) {
        this.attributes = attributes;
    }

    public String getNextPageToken() {
        return nextPageToken;
    }

    public void setNextPageToken(String nextPageToken) {
        this.nextPageToken = nextPageToken;
    }

    @Override
    public String toString() {
        return "GMBAttributesResponse{" +
                "attributeMetadata=" + attributes +
                ", nextPageToken='" + nextPageToken + '\'' +
                '}';
    }
}
