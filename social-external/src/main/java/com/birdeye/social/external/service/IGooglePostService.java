package com.birdeye.social.external.service;

import com.birdeye.social.external.request.google.AddGMBPostRequest;
import com.birdeye.social.google.GMBPost.GoogleLocalPost;
import com.birdeye.social.google.GMBPost.GoogleLocalPostRequest;
import com.birdeye.social.google.GMBPost.GoogleLocalPostResponseList;


public interface IGooglePostService {

    GoogleLocalPostResponseList getAllPosts(AddGMBPostRequest addGMBPostRequest);

    GoogleLocalPost getPost(AddGMBPostRequest addGMBPostRequest);

    void deletePost(AddGMBPostRequest addGMBPostRequest, String accessToken);

    GoogleLocalPost createPost(GoogleLocalPostRequest request, AddGMBPostRequest addGMBPostRequest);

    GoogleLocalPost editPost(GoogleLocalPostRequest request, AddGMBPostRequest addGMBPostRequest, String postId);
    GoogleLocalPost editPostText(GoogleLocalPostRequest request, AddGMBPostRequest addGMBPostRequest);
}
