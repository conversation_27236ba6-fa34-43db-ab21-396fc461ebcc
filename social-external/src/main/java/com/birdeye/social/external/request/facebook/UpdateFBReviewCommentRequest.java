/**
 * 
 */
package com.birdeye.social.external.request.facebook;

/**
 * <AUTHOR> on Apr 11, 2018
 *
 */
public class UpdateFBReviewCommentRequest {

	private String commentId;
	private String comment;
	private String accessToken;
	
	public UpdateFBReviewCommentRequest(String commentId,String accessToken) {
		this.commentId = commentId;
		this.accessToken = accessToken;
	}
	/**
	 * @return the comment
	 */
	public String getComment() {
		return comment;
	}
	/**
	 * @param comment the comment to set
	 */
	public void setComment(String comment) {
		this.comment = comment;
	}

	/**
	 * @return comment id
	 */
	public String getCommentId() {
		return commentId;
	}
	/**
	 * @return the accessToken
	 */
	public String getAccessToken() {
		return accessToken;
	}
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("AddFacebookReviewCommentRequest [responseId=");
		builder.append(commentId);
		builder.append(", comment=");
		builder.append(comment);
		builder.append(", accessToken=");
		builder.append(accessToken);
		builder.append("]");
		return builder.toString();
	}
	
	public String getUri(){
		StringBuilder uriBuilder = new StringBuilder();
		uriBuilder.append(commentId)
		.append("?access_token=")
		.append(accessToken);
		return uriBuilder.toString();
	}
	
}