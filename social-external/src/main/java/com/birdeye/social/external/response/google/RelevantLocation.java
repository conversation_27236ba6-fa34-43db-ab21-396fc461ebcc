package com.birdeye.social.external.response.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
public class RelevantLocation implements Serializable {
    private String placeId;
    private RelationType relationType;
}
