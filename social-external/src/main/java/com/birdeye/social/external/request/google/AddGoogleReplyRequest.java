/**
 * 
 */
package com.birdeye.social.external.request.google;

/**
 * <AUTHOR> on Apr 17, 2018
 *
 */
public class AddGoogleReplyRequest {
	
	private String	locationUrl;
	private String	authToken;
	private String	sourceReviewId;
	private String	grantType	= "Bearer";
	private String	replyComment;
	
	/**
	 * @param locationUrl
	 * @param authToken
	 * @param sourceReviewId
	 * @param replyComment
	 */
	public AddGoogleReplyRequest(String locationUrl, String authToken, String sourceReviewId, String replyComment) {
		this.locationUrl = locationUrl;
		this.authToken = authToken;
		this.sourceReviewId = sourceReviewId;
		this.replyComment = replyComment;
	}
	
	/**
	 * @return the locationUrl
	 */
	public String getLocationUrl() {
		return locationUrl;
	}
	
	/**
	 * @param locationUrl
	 *            the locationUrl to set
	 */
	public void setLocationUrl(String locationUrl) {
		this.locationUrl = locationUrl;
	}
	
	/**
	 * @return the authToken
	 */
	public String getAuthToken() {
		return authToken;
	}
	
	/**
	 * @param authToken
	 *            the authToken to set
	 */
	public void setAuthToken(String authToken) {
		this.authToken = authToken;
	}
	
	/**
	 * @return the sourceReviewId
	 */
	public String getSourceReviewId() {
		return sourceReviewId;
	}
	
	/**
	 * @param sourceReviewId
	 *            the sourceReviewId to set
	 */
	public void setSourceReviewId(String sourceReviewId) {
		this.sourceReviewId = sourceReviewId;
	}
	
	/**
	 * @return the grantType
	 */
	public String getGrantType() {
		return grantType;
	}
	
	/**
	 * @param grantType
	 *            the grantType to set
	 */
	public void setGrantType(String grantType) {
		this.grantType = grantType;
	}
	
	/**
	 * @return the replyComment
	 */
	public String getReplyComment() {
		return replyComment;
	}
	
	/**
	 * @param replyComment
	 *            the replyComment to set
	 */
	public void setReplyComment(String replyComment) {
		this.replyComment = replyComment;
	}
	
	/*
	 * (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("AddGoogleReplyRequest [locationUrl=");
		builder.append(locationUrl);
		builder.append(", authToken=");
		builder.append(authToken);
		builder.append(", sourceReviewId=");
		builder.append(sourceReviewId);
		builder.append(", grantType=");
		builder.append(grantType);
		builder.append(", replyComment=");
		builder.append(replyComment);
		builder.append("]");
		return builder.toString();
	}
	
}