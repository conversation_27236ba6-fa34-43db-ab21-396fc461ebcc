package com.birdeye.social.external.response.google;

import java.util.List;

import com.birdeye.social.constant.MetricEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

public class MetricValues {
  
  private MetricEnum metric;
  private List<MetricDimensionalValue> dimensionalValues;
  
  public MetricEnum getMetric() {
    return metric;
  }
  public void setMetric(MetricEnum metric) {
    this.metric = metric;
  }
  public List<MetricDimensionalValue> getDimensionalValues() {
    return dimensionalValues;
  }
  public void setDimensionalValues(List<MetricDimensionalValue> dimensionalValues) {
    this.dimensionalValues = dimensionalValues;
  }
  @Override
  public String toString() {
    return "MetricValues [metric=" + metric + ", dimensionalValues=" + dimensionalValues + "]";
  }
  
}


