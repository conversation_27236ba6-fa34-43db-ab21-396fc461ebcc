package com.birdeye.social.external.response.picturesque;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> on 08/12/23
 */


@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialScheduledPostValidationResponse implements Serializable {
    private static final long serialVersionUID = 3527502270437872419L;
    private Boolean success;
    private List<ValidationResponse> validationResponse;


    @Getter
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    public static class ValidationResponse implements Serializable {
        private static final long serialVersionUID = 4744360601212735421L;
        private Boolean success;
        private String type;
        private String url;
        private String compressedURL;
        private String completeCompressedURL;
        private String completeURL;
        private String content;
        private String dateTime;
        private List<String> error;
        private MediaMetaData mediaMetadata;
        private String videoThumbnail;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    public static class MediaMetaData implements Serializable {
        private static final long serialVersionUID = 4744360601212735424L;
        private  String width;
        private  String height;
        private  String aspectRatio;
        private  String size;
        private  String contentType;
    }

}