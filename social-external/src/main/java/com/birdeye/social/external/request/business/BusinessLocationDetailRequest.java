package com.birdeye.social.external.request.business;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> on 04/01/24
 */

@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessLocationDetailRequest {
    private List<Long> businessNumbers;
    private List<Integer> businessIds;
}
