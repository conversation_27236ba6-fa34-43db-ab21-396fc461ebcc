package com.birdeye.social.external.request.linkedin;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultiImageContent implements Serializable {

    List<MediaContent> images;
    private String altText;

    public List<MediaContent> getImages() {
        return images;
    }

    public void setImages(List<MediaContent> images) {
        this.images = images;
    }

    public String getAltText() {
        return altText;
    }

    public void setAltText(String altText) {
        this.altText = altText;
    }
}
