package com.birdeye.social.external.mediaupload;

import com.birdeye.social.dao.mediaupload.SocialMediaUploadRequestRepo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SocialMediaUploadRequestServiceImpl implements SocialMediaUploadRequestService{

    @Autowired
    private SocialMediaUploadRequestRepo socialMediaUploadRequestRepo;

    @Override
    public void save(SocialMediaUploadRequest socialMediaUploadRequest) {
        socialMediaUploadRequestRepo.save(socialMediaUploadRequest);
    }

    @Override
    public SocialMediaUploadRequest findById(Integer requestId) {
        return socialMediaUploadRequestRepo.findOne(requestId);
    }

    @Override
    public void updateUploadStatusById(String status, Integer id) {
        socialMediaUploadRequestRepo.updateUploadStatusById(status,id);
    }

    @Override
    public List<SocialMediaUploadRequest> findByAssetId(Integer assetId) {
        return socialMediaUploadRequestRepo.findByAssetId(assetId);
    }

    @Override
    public SocialMediaUploadRequest findByPublishInfoId(Integer publishInfoId) {
        return socialMediaUploadRequestRepo.findFirstByPublishInfoId(publishInfoId);
    }
}
