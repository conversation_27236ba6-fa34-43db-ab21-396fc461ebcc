/**
 *
 *
 */
package com.birdeye.social.external.service;

/**
 * <AUTHOR>
 *
 */
public class FacebookMessengerSendRequest {
	private String		messaging_type;
	private FbRecipient	recipient;
	private FbMessage	message;
	private String tag;
	
	/**
	 * @return the messaging_type
	 */
	public String getMessaging_type() {
		return messaging_type;
	}
	
	/**
	 * @param messaging_type
	 *            the messaging_type to set
	 */
	public void setMessaging_type(String messaging_type) {
		this.messaging_type = messaging_type;
	}
	
	/**
	 * @return the recipient
	 */
	public FbRecipient getRecipient() {
		return recipient;
	}
	
	/**
	 * @param recipient
	 *            the recipient to set
	 */
	public void setRecipient(FbRecipient recipient) {
		this.recipient = recipient;
	}
	
	/**
	 * @return the message
	 */
	public FbMessage getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(FbMessage message) {
		this.message = message;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	public FacebookMessengerSendRequest(String messaging_type, FbRecipient recipient, FbMessage message) {
		this.messaging_type = messaging_type;
		this.recipient = recipient;
		this.message = message;
	}

	public FacebookMessengerSendRequest(String messaging_type, FbRecipient recipient, FbMessage message, String tag) {
		super();
		this.messaging_type = messaging_type;
		this.recipient = recipient;
		this.message = message;
		this.tag = tag;
	}

	@Override
	public String toString() {
		return "FacebookMessengerSendRequest{" +
				"messaging_type='" + messaging_type + '\'' +
				", recipient=" + recipient +
				", message=" + message +
				", tag='" + tag + '\'' +
				'}';
	}
}