package com.birdeye.social.external.request.google;

import java.io.Serializable;

import com.birdeye.social.external.request.google.GMBAnswerDTO.Answer;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * author <PERSON><PERSON><PERSON><PERSON>
 */

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
@NoArgsConstructor
@Data
public class GMBAnswer implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private Answer answer;

}
