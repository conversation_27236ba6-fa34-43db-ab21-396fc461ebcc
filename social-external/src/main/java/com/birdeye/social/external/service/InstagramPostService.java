package com.birdeye.social.external.service;

import org.springframework.util.MultiValueMap;

import java.util.Map;

public interface InstagramPostService {
    String getMediaPostURL(String mediaId, String accessToken);
    String uploadContent(String contenturl, MultiValueMap<String, String> parametersMap);
    String postContent(String igAccountId, String accessToken, String creationId);
    String checkContainerStatus(String containerId, String accessToken);
    Map<String, Object> checkContainerStatusWithoutRetry(String containerId, String accessToken);
}
