package com.birdeye.social.external.request.google;

import java.io.Serializable;

public class ServiceItemResponse implements Serializable {

    private Money price;
    private Boolean isOffered;
    private StructuredServiceItem structuredServiceItem;
    private FreeFormServiceItemResponse freeFormServiceItem;

    public Boolean getOffered() {
        return isOffered;
    }

    public void setOffered(Boolean offered) {
        isOffered = offered;
    }

    public Money getPrice() {
        return price;
    }

    public void setPrice(Money price) {
        this.price = price;
    }

    public StructuredServiceItem getStructuredServiceItem() {
        return structuredServiceItem;
    }

    public void setStructuredServiceItem(StructuredServiceItem structuredServiceItem) {
        this.structuredServiceItem = structuredServiceItem;
    }

    public FreeFormServiceItemResponse getFreeFormServiceItem() {
        return freeFormServiceItem;
    }

    public void setFreeFormServiceItem(FreeFormServiceItemResponse freeFormServiceItem) {
        this.freeFormServiceItem = freeFormServiceItem;
    }

    @Override
    public String toString() {
        return "ServiceItemResponse{" +
                "price=" + price +
                ", isOffered=" + isOffered +
                ", structuredServiceItem=" + structuredServiceItem +
                ", freeFormServiceItem=" + freeFormServiceItem +
                '}';
    }
}
