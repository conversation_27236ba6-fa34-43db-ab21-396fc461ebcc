package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AttributeMetadata {

    private String parent;
    private AttributeValueType valueType;
    private String displayName;
    private String groupDisplayName;
    private Boolean repeatable;
    private List<AttributeValueMetadata> valueMetadata;
    private Boolean deprecated;

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public AttributeValueType getValueType() {
        return valueType;
    }

    public void setValueType(AttributeValueType valueType) {
        this.valueType = valueType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getGroupDisplayName() {
        return groupDisplayName;
    }

    public void setGroupDisplayName(String groupDisplayName) {
        this.groupDisplayName = groupDisplayName;
    }

    public Boolean getRepeatable() {
        return repeatable;
    }

    public void setRepeatable(Boolean repeatable) {
        this.repeatable = repeatable;
    }

    public List<AttributeValueMetadata> getValueMetadata() {
        return valueMetadata;
    }

    public void setValueMetadata(List<AttributeValueMetadata> valueMetadata) {
        this.valueMetadata = valueMetadata;
    }

    public Boolean getDeprecated() {
        return deprecated;
    }

    public void setDeprecated(Boolean deprecated) {
        this.deprecated = deprecated;
    }

    @Override
    public String toString() {
        return "AttributeMetadata{" +
                "parent='" + parent + '\'' +
                ", valueType=" + valueType +
                ", displayName='" + displayName + '\'' +
                ", groupDisplayName='" + groupDisplayName + '\'' +
                ", repeatable=" + repeatable +
                ", valueMetadata=" + valueMetadata +
                ", deprecated=" + deprecated +
                '}';
    }
}
