package com.birdeye.social.external.response.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBPageAddress implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5848184898667993805L;
	private List<String> addressLines;
	private String locality;
	private String administrativeArea;
	private String regionCode;
	private String postalCode;
	private String languageCode;

	public List<String> getAddressLines() {
		return addressLines;
	}

	public void setAddressLines(List<String> addressLines) {
		this.addressLines = addressLines;
	}

	public String getLocality() {
		return locality;
	}

	public void setLocality(String locality) {
		this.locality = locality;
	}

	public String getAdministrativeArea() {
		return administrativeArea;
	}

	public void setAdministrativeArea(String administrativeArea) {
		this.administrativeArea = administrativeArea;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}
	
	public String getAddressInSingleLine() {
		StringBuilder builder = new StringBuilder();
		String ADDRESS_SEPARATOR = ", ";
		if (this.addressLines != null && !this.addressLines.isEmpty()) {
			for (String addressLine : this.addressLines) {
				builder.append(addressLine).append(ADDRESS_SEPARATOR);
			}
		}
		if (StringUtils.isNotEmpty(this.locality)) {
			builder.append(this.locality).append(ADDRESS_SEPARATOR);
		}
		if (StringUtils.isNotEmpty(this.administrativeArea)) {
			builder.append(this.administrativeArea).append(ADDRESS_SEPARATOR);
		}
		if (StringUtils.isNotEmpty(this.postalCode)) {
			builder.append(this.postalCode).append(ADDRESS_SEPARATOR);
		}
		if (StringUtils.isNotEmpty(this.regionCode)) {
			builder.append(this.regionCode);
		}
		builder = new StringBuilder(StringUtils.trim(builder.toString()));
		if ( StringUtils.endsWith(builder, ",") ) {
			builder.delete(builder.length() - 1, builder.length());
		}
		return builder.toString();
	}

	public String getLanguageCode() {
		return languageCode;
	}

	public void setLanguageCode(String languageCode) {
		this.languageCode = languageCode;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */

	@Override
	public String toString() {
		return "GMBPageAddress{" +
				"addressLines=" + addressLines +
				", locality='" + locality + '\'' +
				", administrativeArea='" + administrativeArea + '\'' +
				", regionCode='" + regionCode + '\'' +
				", postalCode='" + postalCode + '\'' +
				", languageCode='" + languageCode + '\'' +
				'}';
	}
}
