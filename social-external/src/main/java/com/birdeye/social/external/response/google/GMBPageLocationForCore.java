package com.birdeye.social.external.response.google;

import com.birdeye.social.external.request.google.*;
import com.birdeye.social.model.gmb.GMBServiceAreaResponse;
import com.birdeye.social.model.gmb.LatLng;

import java.io.Serializable;
import java.util.List;

public class GMBPageLocationForCore implements Serializable {

    private String name;
    private String storeCode;
    private String locationName;
    private String primaryPhone;
    private List<String> additionalPhones;
    private String websiteUrl;
    private GMBLocationState locationState;
    private GMBServiceAreaResponse serviceArea;
    private GMBPageAddress address;
    private GMBLocationKey locationKey;
    private GMBLocationPhotos photos;
    private GMBLocationPageUrlResponse metadata;
    private List<AttributeResponseForLocation> attributes;
    private OpenInfo openInfo;
    private GMBLocationRequest.CategoryResponse primaryCategory;
    private GMBLocationRequest.BusinessHourForCore regularHours;
    private GMBLocationRequest.SpecialHoursForCore specialHours;
    private String languageCode;
    private GMBLocationRequest.Profile profile;
    private List<GMBLocationRequest.CategoryResponse> additionalCategories;
    private LatLng latlng;
    private ServiceList serviceList;
    private List<AttributeMetadataResponse> supportedAttributes;
    private String regionCode;
    private List<GMBMoreHours> moreHours;
    
    public List<GMBMoreHours> getMoreHours() {
		return moreHours;
	}

	public void setMoreHours(List<GMBMoreHours> moreHours) {
		this.moreHours = moreHours;
	}
	
    public GMBLocationRequest.BusinessHourForCore getRegularHours() {
        return regularHours;
    }

    public void setRegularHours(GMBLocationRequest.BusinessHourForCore regularHours) {
        this.regularHours = regularHours;
    }

    public GMBLocationRequest.SpecialHoursForCore getSpecialHours() {
        return specialHours;
    }

    public void setSpecialHours(GMBLocationRequest.SpecialHoursForCore specialHours) {
        this.specialHours = specialHours;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public ServiceList getServiceList() {
        return serviceList;
    }

    public void setServiceList(ServiceList serviceList) {
        this.serviceList = serviceList;
    }

    public List<AttributeMetadataResponse> getSupportedAttributes() {
        return supportedAttributes;
    }

    public void setSupportedAttributes(List<AttributeMetadataResponse> supportedAttributes) {
        this.supportedAttributes = supportedAttributes;
    }

    public List<GMBLocationRequest.CategoryResponse> getAdditionalCategories() {
        return additionalCategories;
    }

    public void setAdditionalCategories(List<GMBLocationRequest.CategoryResponse> additionalCategories) {
        this.additionalCategories = additionalCategories;
    }

    public List<String> getAdditionalPhones() {
        return additionalPhones;
    }

    public void setAdditionalPhones(List<String> additionalPhones) {
        this.additionalPhones = additionalPhones;
    }

    public LatLng getLatlng() {
        return latlng;
    }

    public void setLatlng(LatLng latlng) {
        this.latlng = latlng;
    }

    public GMBLocationRequest.Profile getProfile() {
        return profile;
    }

    public void setProfile(GMBLocationRequest.Profile profile) {
        this.profile = profile;
    }

    /**
     * @return the metadata
     */
    public GMBLocationPageUrlResponse getMetadata() {
        return metadata;
    }

    /**
     * @param metadata the metadata to set
     */
    public void setMetadata(GMBLocationPageUrlResponse metadata) {
        this.metadata = metadata;
    }

    /**
     * @return the websiteUrl
     */
    public String getWebsiteUrl() {
        return websiteUrl;
    }

    /**
     * @param websiteUrl the websiteUrl to set
     */
    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public String getPrimaryPhone() {
        return primaryPhone;
    }

    public void setPrimaryPhone(String primaryPhone) {
        this.primaryPhone = primaryPhone;
    }

    public GMBLocationState getLocationState() {
        return locationState;
    }

    public void setLocationState(GMBLocationState locationState) {
        this.locationState = locationState;
    }

    public GMBServiceAreaResponse getServiceArea() {
        return serviceArea;
    }

    public void setServiceArea(GMBServiceAreaResponse serviceArea) {
        this.serviceArea = serviceArea;
    }
    public GMBPageAddress getAddress() {
        return address;
    }

    public void setAddress(GMBPageAddress address) {
        this.address = address;
    }

    public GMBLocationKey getLocationKey() {
        return locationKey;
    }

    public void setLocationKey(GMBLocationKey locationKey) {
        this.locationKey = locationKey;
    }

    public GMBLocationPhotos getPhotos() {
        return photos;
    }

    public void setPhotos(GMBLocationPhotos photos) {
        this.photos = photos;
    }

    public List<AttributeResponseForLocation> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<AttributeResponseForLocation> attributes) {
        this.attributes = attributes;
    }

    public OpenInfo getOpenInfo() {
        return openInfo;
    }

    public void setOpenInfo(OpenInfo openInfo) {
        this.openInfo = openInfo;
    }

    public GMBLocationRequest.CategoryResponse getPrimaryCategory() {
        return primaryCategory;
    }

    public void setPrimaryCategory(GMBLocationRequest.CategoryResponse primaryCategory) {
        this.primaryCategory = primaryCategory;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    @Override
    public String toString() {
        return "GMBPageLocation{" +
                "name='" + name + '\'' +
                ", locationName='" + locationName + '\'' +
                ", primaryPhone='" + primaryPhone + '\'' +
                ", additionalPhones=" + additionalPhones +
                ", websiteUrl='" + websiteUrl + '\'' +
                ", locationState=" + locationState +
                ", serviceArea=" + serviceArea +
                ", address=" + address +
                ", locationKey=" + locationKey +
                ", photos=" + photos +
                ", metadata=" + metadata +
                ", attributes=" + attributes +
                ", openInfo=" + openInfo +
                ", primaryCategory=" + primaryCategory +
                ", languageCode='" + languageCode + '\'' +
                ", profile=" + profile +
                ", additionalCategories=" + additionalCategories +
                ", latlng=" + latlng +
                ", serviceList=" + serviceList +
                '}';
    }
}
