package com.birdeye.social.external.request.linkedin;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TargetedEntity implements Serializable {
    List<String> industries;
    List<String> seniorities;
    List<String> jobFunctions;

    public List<String> getIndustries() {
        return industries;
    }

    public void setIndustries(List<String> industries) {
        this.industries = industries;
    }

    public List<String> getSeniorities() {
        return seniorities;
    }

    public void setSeniorities(List<String> seniorities) {
        this.seniorities = seniorities;
    }

    public List<String> getJobFunctions() {
        return jobFunctions;
    }

    public void setJobFunctions(List<String> functions) {
        this.jobFunctions = functions;
    }

    @Override
    public String toString() {
        return "TargetedEntity{" +
                "industries=" + industries +
                ", seniorities=" + seniorities +
                ", jobFunctions=" + jobFunctions +
                '}';
    }
}
