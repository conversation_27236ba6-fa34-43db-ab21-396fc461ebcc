package com.birdeye.social.external.service;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;

import com.birdeye.social.apple.AppleSendRequest;
import com.birdeye.social.apple.AttachmentPreDownloadRequest;
import com.birdeye.social.apple.AttachmentPreUploadRequest;
import com.birdeye.social.apple.GetFileCheckSumRequest;

public interface AppleExternalService {
	void sendAppleMessage(AppleSendRequest appleSendRequest) throws IOException, Exception;

	String attachmentPreDownload(AttachmentPreDownloadRequest request) throws Exception;

	Optional<Map<String, String>> attachmentPreUpload(AttachmentPreUploadRequest request) throws Exception;

	String getUploadFileCheckSum(GetFileCheckSumRequest getFileCheckSumRequest) throws Exception;
}
