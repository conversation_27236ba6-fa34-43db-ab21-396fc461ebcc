package com.birdeye.social.external.request.mediaupload;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UploadStatusResponse implements Serializable {
    private boolean status = false;
    private Integer requestId;
    private String pageId;
    private String videoId;
    private Integer publishInfoId;
    private String channel;
}
