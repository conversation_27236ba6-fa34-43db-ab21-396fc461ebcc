package com.birdeye.social.external.request.media;

import com.birdeye.social.dto.PicturesqueResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class PicturesqueRequest {
    private Long businessNumber;
    List<Picturesque> attachments;

}
