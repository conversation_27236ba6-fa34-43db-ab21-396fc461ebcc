package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBGetMediaRequest implements Serializable {

    private static final long serialVersionUID = -6937259702719245275L;

    private String locationUri;

    private Integer	batchSize;

    private String nextPageToken;

    private GMBMedia gmbMedia;

    private Integer refreshTokenId;

    private Integer businessId;

    private Integer requestId;

    private String requestType;

    private Long retryAfterSeconds;
    private String retryRequestId;
    private Integer retryCounter=0;
}
