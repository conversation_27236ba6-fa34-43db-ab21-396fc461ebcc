package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class OpenInfo {

    private OpenForBusiness status;
    private Boolean canReopen;
    private Date openingDate;

    public OpenForBusiness getStatus() {
        return status;
    }

    public void setStatus(OpenForBusiness status) {
        this.status = status;
    }

    public Boolean getCanReopen() {
        return canReopen;
    }

    public void setCanReopen(Boolean canReopen) {
        this.canReopen = canReopen;
    }

    public Date getOpeningDate() {
        return openingDate;
    }

    public void setOpeningDate(Date openingDate) {
        this.openingDate = openingDate;
    }

    @Override
    public String toString() {
        return "OpenInfo{" +
                "status=" + status +
                ", canReopen=" + canReopen +
                ", openingDate=" + openingDate +
                '}';
    }
}
