package com.birdeye.social.external.request.google;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class GMBMedia implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private List<GMBMediaItem> mediaItems;

	private Integer totalMediaItemCount;

	private String nextPageToken;

	private Integer businessId;

	private Integer requestId;

	private String requestType;

	private boolean lastBatch = false;
	
	
	public GMBMedia() {
		// TODO Auto-generated constructor stub
	}

	public GMBMedia(Integer businessId, Integer requestId, String requestType, List<GMBMediaItem> mediaItems, boolean lastBatch) {
		this.businessId = businessId;
		this.requestId = requestId;
		this.mediaItems = mediaItems;
		this.lastBatch = lastBatch;
		this.requestType = requestType;
	}

	/**
	 * @return the mediaItems
	 */
	public List<GMBMediaItem> getMediaItems() {
		return mediaItems;
	}

	/**
	 * @param mediaItems the mediaItems to set
	 */
	public void setMediaItems(List<GMBMediaItem> mediaItems) {
		this.mediaItems = mediaItems;
	}

	public Integer getTotalMediaItemCount() {
		return totalMediaItemCount;
	}

	public void setTotalMediaItemCount(Integer totalMediaItemCount) {
		this.totalMediaItemCount = totalMediaItemCount;
	}

	public String getNextPageToken() {
		return nextPageToken;
	}

	public void setNextPageToken(String nextPageToken) {
		this.nextPageToken = nextPageToken;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getRequestId() {
		return requestId;
	}

	public void setRequestId(Integer requestId) {
		this.requestId = requestId;
	}

	public boolean isLastBatch() {
		return lastBatch;
	}

	public void setLastBatch(boolean lastBatch) {
		this.lastBatch = lastBatch;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}
}
