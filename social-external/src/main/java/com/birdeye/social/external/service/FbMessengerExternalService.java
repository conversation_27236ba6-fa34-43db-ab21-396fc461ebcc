/**
 *
 *
 */
package com.birdeye.social.external.service;

import com.birdeye.social.facebook.notification.FbSubscribeWebhookResponse;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FbMessengerUserDetails;
import com.birdeye.social.facebook.response.FbMessengerUserDetailsResponse;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface FbMessengerExternalService {
	
	FacebookBaseResponse fbPageSubscribeApps(String pageId, String accessToken, String subscriptionFields) throws IOException;

	FacebookBaseResponse fbPageUnsubscribeApps(String pageId, String accessToken);

	FbMessengerUserDetails getFbMessengerUserDetails(String accessToken, String userId) throws IOException;

	Map<String, Object> sendFbMessage(String pageId, String accessToken, FacebookMessengerSendRequest sendRequest) throws IOException;

	FacebookBaseResponse fbPageUnsubscribeMessenger(String pageId, String accessToken, String unSubscriptionFields) throws IOException;

	FbSubscribeWebhookResponse fbPageGetSubscribeFields(String pageId, String accessToken);
}
