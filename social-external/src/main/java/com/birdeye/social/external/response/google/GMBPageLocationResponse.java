package com.birdeye.social.external.response.google;

import java.io.Serializable;
import java.util.List;


public class GMBPageLocationResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3005969966448734944L;

	private List<GMBPageLocation> locations;
	private String nextPageToken;

	public List<GMBPageLocation> getLocations() {
		return locations;
	}

	public void setLocations(List<GMBPageLocation> locations) {
		this.locations = locations;
	}

	public String getNextPageToken() {
		return nextPageToken;
	}

	public void setNextPageToken(String nextPageToken) {
		this.nextPageToken = nextPageToken;
	}

	@Override
	public String toString() {
		return "GMBPageLocationResponse [locations=" + locations + ", nextPageToken=" + nextPageToken + "]";
	}

}
