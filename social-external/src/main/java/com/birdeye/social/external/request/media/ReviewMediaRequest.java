package com.birdeye.social.external.request.media;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> on 25/10/23
 */

@Getter
@Setter
@ToString
public class ReviewMediaRequest {

    private Integer reviewId;
    private String reviewText;
    private Float starRating;
    private String reviewerName;
    private String toSource;
    private Integer businessId;
    private String pageId;
    private String fromSource;
    private String date;
    private Integer userId;
    private Boolean autoShare;
    private Long businessNumber;
    private Integer fromSourceId;
    private String reviewUrl;
    private String uniqueReviewUrl;
    private Integer layoutId;
    private String mediaUrl;
    private String reviewerProfilePictureUrl;
    private Object defaultMetaConfig;
}
