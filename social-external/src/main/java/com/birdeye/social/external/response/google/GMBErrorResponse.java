package com.birdeye.social.external.response.google;

public class GMBErrorResponse {
  
  private CustomError error;
  
  public CustomError getError() {
    return error;
  }
  
  public void setError(CustomError error) {
    this.error = error;
  }

  public class CustomError {
    private Integer code;
    private String message;
    private String status;
    public Integer getCode() {
      return code;
    }
    public void setCode(Integer code) {
      this.code = code;
    }
    public String getMessage() {
      return message;
    }
    public void setMessage(String message) {
      this.message = message;
    }
    public String getStatus() {
      return status;
    }
    public void setStatus(String status) {
      this.status = status;
    }
    @Override
    public String toString() {
      return "GMBErrorResponse [code=" + code + ", message=" + message + ", status=" + status + "]";
    }   
  }
}
