package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AttributeResponseForLocation implements Serializable {

    private String attributeId;
    private AttributeValueType valueType;
    private List<Object> values;
    private RepeatedEnumAttributeValue repeatedEnumValue;
    private List<UrlAttributeValueRequest> urlValues;

    public String getAttributeId() {
        return attributeId;
    }

    public void setAttributeId(String attributeId) {
        this.attributeId = attributeId;
    }

    public AttributeValueType getValueType() {
        return valueType;
    }

    public void setValueType(AttributeValueType valueType) {
        this.valueType = valueType;
    }

    public List<Object> getValues() {
        return values;
    }

    public void setValues(List<Object> values) {
        this.values = values;
    }

    public RepeatedEnumAttributeValue getRepeatedEnumValue() {
        return repeatedEnumValue;
    }

    public void setRepeatedEnumValue(RepeatedEnumAttributeValue repeatedEnumValue) {
        this.repeatedEnumValue = repeatedEnumValue;
    }

    public List<UrlAttributeValueRequest> getUrlValues() {
        return urlValues;
    }

    public void setUrlValues(List<UrlAttributeValueRequest> urlValues) {
        this.urlValues = urlValues;
    }
}
