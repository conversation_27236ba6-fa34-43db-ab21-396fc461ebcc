package com.birdeye.social.external.request.google;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class UrlAttributeValue {

    private String uri;

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    @Override
    public String toString() {
        return "UrlAttributeValue{" +
                "uri='" + uri + '\'' +
                '}';
    }
}
