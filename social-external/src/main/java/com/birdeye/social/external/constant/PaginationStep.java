/**
 * 
 */
package com.birdeye.social.external.constant;

/**
 * <AUTHOR> on Apr 10, 2018
 *
 */
public enum PaginationStep {

	AFTER("after"),BEFORE("before"),NONE("none");
	
	private String parinationStepString;
	PaginationStep(String step) {
		parinationStepString = step;
	}
	/**
	 * @return the parinationStepString
	 */
	public String getParinationStepString() {
		return parinationStepString;
	}
	
}