package com.birdeye.social.external.request.linkedin;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValueV2 implements Serializable {

    private Long uploadUrlExpiresAt;
    private String uploadUrl;
    private String image;
    private String video;
    private String uploadToken;
    private List<Map<String, Object>> uploadInstructions;
    private String captionsUploadUrl;
    private String thumbnailUploadUrl;
}

