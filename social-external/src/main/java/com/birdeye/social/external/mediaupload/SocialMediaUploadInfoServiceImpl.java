package com.birdeye.social.external.mediaupload;

import com.birdeye.social.dao.mediaupload.SocialMediaUploadInfoRepo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SocialMediaUploadInfoServiceImpl implements SocialMediaUploadInfoService{

    @Autowired
    SocialMediaUploadInfoRepo socialMediaUploadInfoRepo;

    @Override
    public void save(SocialMediaUploadInfo socialMediaUploadInfo) {
        socialMediaUploadInfoRepo.save(socialMediaUploadInfo);
    }

    @Override
    public SocialMediaUploadInfo findByUploadRequestIdAndSequenceId(Integer requestId ,Integer sequenceId) {
        return socialMediaUploadInfoRepo.findByUploadRequestIdAndSequenceId(requestId,sequenceId);
    }

    @Override
    public List<String> findByRequestId(Integer id) {
        return socialMediaUploadInfoRepo.findETagByUploadRequestId(id);
    }

    @Override
    public List<SocialMediaUploadInfo> findByUploadRequestId(Integer requestId) {
        return socialMediaUploadInfoRepo.findByUploadRequestId(requestId);
    }
}
