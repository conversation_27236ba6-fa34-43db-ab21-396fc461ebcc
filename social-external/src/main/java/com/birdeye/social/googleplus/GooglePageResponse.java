package com.birdeye.social.googleplus;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class GooglePageResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1878731852739507835L;

	private String objectType;
	private String id;
	private String displayName;
	private String url;
	private GoogleProfilePicture image;

	public String getObjectType() {
		return objectType;
	}

	public void setObjectType(String objectType) {
		this.objectType = objectType;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public GoogleProfilePicture getImage() {
		return image;
	}

	public void setImage(GoogleProfilePicture image) {
		this.image = image;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("GooglePageResponse [objectType=");
		builder.append(objectType);
		builder.append(", id=");
		builder.append(id);
		builder.append(", displayName=");
		builder.append(displayName);
		builder.append(", url=");
		builder.append(url);
		builder.append(", image=");
		builder.append(image);
		builder.append("]");
		return builder.toString();
	}

}
