package com.birdeye.social.googleplus;

import java.util.ArrayList;
import java.util.List;

import com.birdeye.social.sro.SocialRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityAccess extends SocialRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4042496478833221684L;

	private String kind;
	private List<AccessItem> items;
	private boolean domainRestricted;

	public ActivityAccess(String accessKind, boolean domainRestricted) {
		this.kind = accessKind;
		List<AccessItem> items = new ArrayList<AccessItem>();
		items.add(new AccessItem("public"));
		this.items = items;
		this.domainRestricted = domainRestricted;
	}

	public String getKind() {
		return kind;
	}

	public void setKind(String kind) {
		this.kind = kind;
	}

	public List<AccessItem> getItems() {
		return items;
	}

	public void setItems(List<AccessItem> items) {
		this.items = items;
	}

	public boolean isDomainRestricted() {
		return domainRestricted;
	}

	public void setDomainRestricted(boolean domainRestricted) {
		this.domainRestricted = domainRestricted;
	}

}
