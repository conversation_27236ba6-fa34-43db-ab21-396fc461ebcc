package com.birdeye.social.googleplus;

import java.util.List;

import org.apache.commons.collections4.CollectionUtils;

import com.birdeye.social.sro.SocialRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityContent extends SocialRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -589755682270179796L;

	private String id;
	private String originalContent;
	private List<ActivityAttachment> attachments;

	public ActivityContent(String content, List<ActivityAttachment> attachments) {
		this.originalContent = content;
		if (CollectionUtils.isNotEmpty(attachments)) {
			this.attachments = attachments;
		}
	}

	public ActivityContent(String id, String originalContent, List<ActivityAttachment> attachments) {
		this.id = id;
		this.originalContent = originalContent;
		if (CollectionUtils.isNotEmpty(attachments)) {
			this.attachments = attachments;
		}
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getOriginalContent() {
		return originalContent;
	}

	public void setOriginalContent(String originalContent) {
		this.originalContent = originalContent;
	}

	public List<ActivityAttachment> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<ActivityAttachment> attachments) {
		this.attachments = attachments;
	}

}
