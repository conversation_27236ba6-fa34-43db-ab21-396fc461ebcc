package com.birdeye.social.googleplus;

import com.birdeye.social.sro.SocialRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccessItem extends SocialRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4866300844156625472L;

	private String type;

	public AccessItem(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
