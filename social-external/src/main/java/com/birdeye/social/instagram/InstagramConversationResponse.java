package com.birdeye.social.instagram;

import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramConversationResponse implements Serializable {
    List<InstagramConversationDetail> data;
    private FacebookErrorResponse error;

    public FacebookErrorResponse getError() {
        return error;
    }

    public void setError(FacebookErrorResponse error) {
        this.error = error;
    }

    public List<InstagramConversationDetail> getData() {
        return data;
    }

    public void setData(List<InstagramConversationDetail> data) {
        this.data = data;
    }
}
