package com.birdeye.social.instagram.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramVideosDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2273754550122876867L;
	private InstagramImageAndVideoDTO lowResolution;
	private InstagramImageAndVideoDTO standardResolution;
	private InstagramImageAndVideoDTO lowBandWidth;

	@JsonProperty(value = "lowResolution")
	public InstagramImageAndVideoDTO getLowResolution() {
		return lowResolution;
	}

	@JsonProperty(value = "low_resolution")
	public void setLowResolution(InstagramImageAndVideoDTO lowResolution) {
		this.lowResolution = lowResolution;
	}

	@JsonProperty(value = "standardResolution")
	public InstagramImageAndVideoDTO getStandardResolution() {
		return standardResolution;
	}

	@JsonProperty(value = "standard_resolution")
	public void setStandardResolution(InstagramImageAndVideoDTO standardResolution) {
		this.standardResolution = standardResolution;
	}

	@JsonProperty(value = "lowBandWidth")
	public InstagramImageAndVideoDTO getLowBandWidth() {
		return lowBandWidth;
	}

	@JsonProperty(value = "low_bandwidth")
	public void setLowBandWidth(InstagramImageAndVideoDTO lowBandWidth) {
		this.lowBandWidth = lowBandWidth;
	}

	@Override
	public String toString() {
		return "InstagramVideosDTO [lowResolution=" + lowResolution + ", standardResolution=" + standardResolution
				+ ", lowBandWidth=" + lowBandWidth + "]";
	}

}
