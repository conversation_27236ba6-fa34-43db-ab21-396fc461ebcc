/**
 *
 *
 */
package com.birdeye.social.instagram;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * <AUTHOR>
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramMessengeSendRequest {
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private InstagramMessageRecipient	recipient;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String sender_action;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private InstagramMessage	message;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private InstagramReactionPayload payload;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String tag;
	
	public InstagramMessengeSendRequest(InstagramMessageRecipient recipient, InstagramMessage message, String tag) {
		super();
		this.recipient = recipient;
		this.message = message;
		this.tag = tag;
	}

	public InstagramMessengeSendRequest(InstagramMessageRecipient recipient, InstagramMessage message) {
		super();
		this.recipient = recipient;
		this.message = message;
	}

	public InstagramMessengeSendRequest(InstagramMessageRecipient recipient, String sender_action, InstagramReactionPayload payload) {
		this.recipient = recipient;
		this.sender_action = sender_action;
		this.payload = payload;
	}

	/**
	 * @return the recipient
	 */
	public InstagramMessageRecipient getRecipient() {
		return recipient;
	}
	
	/**
	 * @param recipient
	 *            the recipient to set
	 */
	public void setRecipient(InstagramMessageRecipient recipient) {
		this.recipient = recipient;
	}
	
	/**
	 * @return the message
	 */
	public InstagramMessage getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(InstagramMessage message) {
		this.message = message;
	}

	public String getTag() {
		return tag;
	}

	public void setTag(String tag) {
		this.tag = tag;
	}

	@Override
	public String toString() {
		return "InstagramMessengeSendRequest{" +
				"recipient=" + recipient +
				", message=" + message +
				", tag='" + tag + '\'' +
				'}';
	}
}