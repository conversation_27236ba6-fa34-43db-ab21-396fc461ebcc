package com.birdeye.social.instagram.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramUserDTO implements Serializable {

	/**
	 * 
	 */

	/*
	 * {"access_token": "744851973.b53964e.03f94013776642f3bbfb47e0ff0c63de",
	 * "user": {"id": "744851973", "username": "sahilarora3", "profile_picture":
	 * "https://scontent.cdninstagram.com/t51.2885-19/s150x150/18722016_270079790124564_5031341255670366208_a.jpg",
	 * "full_name": "Sahil Arora", "bio": "", "website": "", "is_business": false}}
	 */
	private static final long serialVersionUID = -4915618185644079214L;

	private String id;

	private String username;

	private String profilePicture;

	private String fullName;

	private String bio;

	private String website;

	private boolean instagramBusinessAccount;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	@JsonProperty(value = "profilePicture")
	public String getProfilePicture() {
		return profilePicture;
	}
	
	@JsonProperty(value = "profile_picture")
	public void setProfilePicture(String profilePicture) {
		this.profilePicture = profilePicture;
	}

	@JsonProperty(value = "fullName")
	public String getFullName() {
		return fullName;
	}
	
	@JsonProperty(value = "full_name")
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getBio() {
		return bio;
	}

	public void setBio(String bio) {
		this.bio = bio;
	}

	public String getWebsite() {
		return website;
	}

	public void setWebsite(String website) {
		this.website = website;
	}
	
	@JsonProperty(value = "instagramBusinessAccount")
	public boolean isInstagramBusinessAccount() {
		return instagramBusinessAccount;
	}
	
	@JsonProperty(value = "is_business")
	public void setInstagramBusinessAccount(boolean instagramBusinessAccount) {
		this.instagramBusinessAccount = instagramBusinessAccount;
	}
	
	@Override
	public String toString() {
		return "InstagramUser [id=" + id + ", username=" + username + ", profilePicture=" + profilePicture
				+ ", fullName=" + fullName + ", bio=" + bio + ", website=" + website + ", instagramBusinessAccount="
				+ instagramBusinessAccount + "]";
	}

}
