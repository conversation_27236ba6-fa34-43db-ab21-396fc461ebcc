package com.birdeye.social.instagram.dto;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramCounterDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7136776579632278405L;
	private Integer count = 0;

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	@Override
	public String toString() {
		return "InstagramCounterDTO [count=" + count + "]";
	}

}
