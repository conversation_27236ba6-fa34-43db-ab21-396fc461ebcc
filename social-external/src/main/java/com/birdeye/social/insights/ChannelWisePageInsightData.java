package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class ChannelWisePageInsightData {
    private Integer totalAudience;
    private Integer net;
    private Integer followerLost;
    private Integer impressions;
    private Integer reach;
    private Integer engagements;
    private Double engRate;
    private Integer postCount;
    private Integer sentMsgs;
    private Integer receivedMsgs;
    private Integer likeCount;
    private Integer commentCount;
    private Integer shareCount;
    private Integer linkClickCount;
    private Integer otherClickCount;
    private Integer videoViews;
    private Integer videoCompleteViews30s;
    private Integer videoPartialViews;
    private Integer videoViewsClickToPlay;
    private Integer videoCompleteViews30sClickToPlay;
    private Integer videoPartialViewsClickToPlay;
    private Integer videoViewsAutoplayed;
    private Integer videoCompleteViews30sAutoplayed;
    private Integer videoPartialViewsAutoplayed;
    private Integer publishedVideoPosts;
    private Integer publishedImagePosts;
    private Integer publishedTextPosts;
    private Integer publishedLinkPosts;
    private Integer publishedStories;
    private Integer storyImpressions;
    private Integer storyReach;
    private Integer storyEngagement;
    private Integer storyEngagementRate;
    private Integer storyLike;
    private Integer storyReply;
    private Integer storyShare;
}
