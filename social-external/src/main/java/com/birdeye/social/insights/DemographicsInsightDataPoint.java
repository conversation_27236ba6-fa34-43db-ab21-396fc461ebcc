package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DemographicsInsightDataPoint implements Serializable {

    private Integer totalFollowers;

    @Getter
    @Setter
    @ToString
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    public static class Data {
        private String label;
        private String name;
        private Double percentage;
        private Integer total;
        private String code;
        @JsonProperty("Men")
        private Integer men;
        @JsonProperty("Women")
        private Integer women;
        @JsonProperty("Other")
        private Integer other;

        public Data( Integer men, Integer women, Integer other) {
            this.other = other;
            this.men = men;
            this.women = women;
        }
    }
}
