package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class PageInsightsV2Response extends PageInsightData {

    private Integer totalAudience;
  private Integer followerLost;
  /*  private Double followerLostPer;
    private Double likeLostPer;
    private Double likeGainPer;
    private Double engRatePer;
    private Double engagementPer;
    private Double impressionPer;
    private Double reachPer;
    private Double totalPostPer;*/
    private long dateDiff;
    private String groupByType;
    private Integer net;
    private Map<String, ChannelWisePageInsightData> channelWiseData;
    private List<PageInsightV2DataPoint> dataPoints;
    private Long businessId;

   // New fb video metrics
  private Integer totalVideoViews;
  private Integer fullVideoViews;

  // Fb Video breakdown by play type
  private VideoBreakdown breakDown;

  @Getter
  @Setter
  @ToString
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @AllArgsConstructor
  @NoArgsConstructor
  public static class VideoBreakdown {
    private VideoViewsData clickPlays;
    private VideoViewsData autoPlays;
  }

  @Getter
  @Setter
  @ToString
  @JsonInclude(JsonInclude.Include.NON_NULL)
  @AllArgsConstructor
  @NoArgsConstructor
  public static class VideoViewsData {
    private Integer fullVideoViews;
    private Integer partialVideoViews;
  }
}
