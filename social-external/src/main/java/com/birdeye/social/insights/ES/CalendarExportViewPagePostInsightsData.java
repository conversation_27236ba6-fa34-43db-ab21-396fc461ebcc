package com.birdeye.social.insights.ES;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

@ToString
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CalendarExportViewPagePostInsightsData {
    private String socialSite;
    private Integer totalImpression;
    private Integer totalEngagement;
    private Integer totalReach;
    private Integer totalLike;
    private Integer totalComment;
    private Integer totalShare;
    private Integer totalClick;
    private Double totalEngagementRate;
}
