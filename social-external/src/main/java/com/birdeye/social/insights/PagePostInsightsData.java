package com.birdeye.social.insights;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
public class PagePostInsightsData {
    private Date date;
    private Integer likeCount = 0;
    private Integer shareCount = 0;
    private Integer commentCount = 0;
    private Integer linkClickCount = 0;
    private Integer otherClickCount = 0;
    private Integer totalPagePostLikes = 0;
    private Integer totalPagePostComments = 0;
    private Integer totalPagePostShares = 0;
    private Integer totalPageClickCount = 0;
}
