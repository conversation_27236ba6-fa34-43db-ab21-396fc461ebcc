package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;
import java.util.Objects;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
public class PageInsightV2DataPoint extends PageInsightDataPoint implements Serializable {
    private String startDate;
    private String endDate;
    private String label;
    private String shortLabel;
    private Integer fbNetGrowth;
    private Integer igNetGrowth;
    private Integer twNetGrowth;
    private Integer lnNetGrowth;
    private Integer ytNetGrowth;
    private Integer ttNetGrowth;

    private Integer fbPostImpressions;
    private Integer igPostImpressions;
    private Integer twPostImpressions;
    private Integer lnPostImpressions;
    private Integer ytPostImpressions;
    private Integer ttPostImpressions;

    private Integer fbPostEngagements;
    private Integer igPostEngagements;
    private Integer twPostEngagements;
    private Integer lnPostEngagements;
    private Integer ytPostEngagements;
    private Integer ttPostEngagements;

    private Double fbPostEngRate;
    private Double igPostEngRate;
    private Double twPostEngRate;
    private Double lnPostEngRate;
    private Double ytPostEngRate;
    private Double ttPostEngRate;

    private Integer fbPostCount;
    private Integer igPostCount;
    private Integer twPostCount;
    private Integer lnPostCount;
    private Integer ytPostCount;
    private Integer ttPostCount;

    private Integer fbVideoViews;
    private Integer igVideoViews;
    private Integer twVideoViews;
    private Integer lnVideoViews;
    private Integer ytVideoViews;
    private Integer ttVideoViews;

    private Integer fbPostReach;

    private Integer sentMsgs;
    private Integer receivedMsgs;

    private Integer fbFollowers;


    private Integer total;
    private Double totalEngRate;

    private String date;

    /**
     * Inner class to represent the count breakdown by view type
     */
    @Getter
    @Setter
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CountByViews implements Serializable {
        private Integer full;
        private Integer partial;
    }

    // Add the field to the main class
    private CountByViews countByViews;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PageInsightV2DataPoint that = (PageInsightV2DataPoint) o;
        return Objects.equals(date, that.date);
    }

    @Override
    public int hashCode() {
        return Objects.hash(date);
    }
}
