package com.birdeye.social.insights.Instagram.ExternalApiResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class InstagramAccountDataResponse {

    @JsonProperty(value = "media_count")
    private Integer mediaCount;

    @JsonProperty(value = "followers_count")
    private Integer followersCount;

    @JsonProperty(value = "id")
    private String id;

    @JsonProperty(value = "comments_count")
    private Integer commentCount;

    @JsonProperty(value = "like_count")
    private Integer likeCount;

}
