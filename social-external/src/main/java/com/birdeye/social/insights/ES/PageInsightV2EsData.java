package com.birdeye.social.insights.ES;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
public class PageInsightV2EsData extends PageInsightEsData implements Serializable {


    List<? extends Histogram.Bucket> buckets;
    private Integer total;
    private Integer totalMsgSent;
    private Integer totalMsgReceived;
    List<? extends Histogram.Bucket> msgSentBuckets;
    List<? extends Histogram.Bucket> msgReceivedBuckets;
    private long dateDiff;

}
