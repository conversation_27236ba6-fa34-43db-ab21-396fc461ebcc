package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class ExecutiveSummaryRequest {

    @NotNull
    private Long enterpriseId;
    // business ids of the above enterprise
    @NotEmpty
    private List<Integer> businessIds;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    private String groupByType;
    @NotNull
    private List<String> reportMetrix;
    private Integer months;
    private Integer days;
    private String customStartDate;
    private List<String> socialChannels;
    private Boolean includeExtendedBounds = false;
}
