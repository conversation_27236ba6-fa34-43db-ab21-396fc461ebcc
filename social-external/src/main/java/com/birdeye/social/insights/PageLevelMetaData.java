package com.birdeye.social.insights;

import com.birdeye.social.tiktok.TikTokPageData;
import lombok.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@ToString
public class PageLevelMetaData {

    private Date date;
    private Integer followerGainCount;
    private Integer followerLostCount;
    private Integer followerChange;
    private Integer totalFollower;
    private Integer likesGainCount;
    private Integer likesLostCount;
    private Integer totalLikes;
    private Integer postEngagements;
    private Integer postTotalCount;
    private Integer postReach;
    private Integer postImpressions;
    private Integer postImpressionTotal;
    private Double postEngagementRate;
    private Integer postEngagementTotal;
    private Integer shareCount;
    private Integer linkClickCount;
    private Integer otherClickCount;
    private Integer postCount;
    private Integer commentCount;
    private Integer profileVideoViews;      // metric: page_video_views, total video views
    private Integer messageSent;
    private Integer totalProfileVideoViews;
    private Integer totalPostShareCount;
    private Integer totalPostCommentCount;
    private Integer totalPostLikeCount;
    private Integer pagePostLikeCount;
    private String dateString;
    private List<TikTokPageData.PageMetrics.AudienceActivity> audienceActivity;
    private List<DemographicsInsightDataPoint.Data> audienceCountries;
    private List<DemographicsInsightDataPoint.Data> audienceGenders;
    private List<DemographicsInsightDataPoint.Data> audienceCities;
    private List<DemographicsInsightDataPoint.Data> audienceAges;
    private Integer videoCompleteViews30s;   // full video views
    private Integer videoPartialViews;      // partial video views
    private Integer videoViewsClickToPlay;  // video views, click to play
    private Integer videoCompleteViews30sClickToPlay; // click to play, full video views
    private Integer videoPartialViewsClickToPlay; // click to play partial video views
    private Integer videoViewsAutoplayed;      // video views, autoplayed
    private Integer videoCompleteViews30sAutoplayed;  // autoplayed, full video views
    private Integer videoPartialViewsAutoplayed;  // autoplayed, partial video views
    private Integer fbStoryCount;
    private Integer storyImpressions;
    private Integer storyEngagements;
    private Integer storyReach;
    private Integer storyLikes;
    private Integer storyComments;
    private Integer storyShares;
    private Boolean isBusinessAccount;
}