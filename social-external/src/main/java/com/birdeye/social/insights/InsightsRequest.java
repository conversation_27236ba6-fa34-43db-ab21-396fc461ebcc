package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class InsightsRequest {

    @NotNull
    private Long enterpriseId;
    // business ids of the above enterprise
    @NotEmpty
    private List<Integer> businessIds;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;
    private String groupByType;
    @NotNull
    private String reportType;
    private Integer months;
    private Integer days;
    private String customStartDate;
    private List<String> socialChannels;
    private Boolean includeExtendedBounds = false;

    private Set<Long> tagIds;
    private String postStatus;
    private Set<Integer> publisherIds;
    Integer startingIndex;
    Integer size;
    String sortBy;
    Integer sortingOrder;

    public InsightsRequest(InsightsRequest insightsRequest) {
        this.enterpriseId = insightsRequest.getEnterpriseId();
        this.businessIds = insightsRequest.getBusinessIds();
        this.startDate = insightsRequest.getStartDate();
        this.endDate = insightsRequest.getEndDate();
        this.groupByType = insightsRequest.getGroupByType();
        this.reportType = insightsRequest.getReportType();
        this.months = insightsRequest.getMonths();
        this.days = insightsRequest.getDays();
        this.includeExtendedBounds = insightsRequest.getIncludeExtendedBounds();
        this.tagIds = insightsRequest.getTagIds();
        this.socialChannels = insightsRequest.getSocialChannels();
        this.publisherIds = insightsRequest.getPublisherIds();
    }
}
