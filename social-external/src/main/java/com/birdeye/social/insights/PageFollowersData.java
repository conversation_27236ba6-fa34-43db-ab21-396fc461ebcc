package com.birdeye.social.insights;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import java.util.List;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageFollowersData {

    private Integer totalNewFollowers;
    private Long dateDiff;
    private String groupByType;
    private List<PageFollowersDataPoint> dataPoints;
}
