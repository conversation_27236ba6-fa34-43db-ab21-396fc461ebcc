package com.birdeye.social.insights.Facebook.ExternalAPIResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Value implements Serializable {

    private Object value;
    @JsonProperty(value = "end_time")
    private String endTime;
}
