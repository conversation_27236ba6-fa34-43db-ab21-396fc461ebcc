package com.birdeye.social.insights.constants;

import java.util.Arrays;
import java.util.List;

public class InsightsConstants {

    public static final String page_post_engagements = "page_post_engagements";
    public static final String page_impressions = "page_impressions";
    public static final String page_impressions_unique = "page_impressions_unique";
    public static final String page_fan_removes = "page_fan_removes"; // unlikes of page
    public static final String page_fan_adds = "page_fan_adds"; // new people who liked the page
    public static final String page_fans = "page_fans";// total people who liked the page
    public static final String page_daily_follows = "page_daily_follows";
    public static final String page_daily_unfollows = "page_daily_unfollows";
    public static final String page_follows = "page_follows";
    public static final String POST_CLICKS_BY_TYPE = "post_clicks_by_type";
    public static final String POST_CLICKS = "post_clicks";
 // public static final String page_consumptions_by_consumption_type = "page_consumptions_by_consumption_type";
    public static final String page_video_views = "page_video_views";
    public static final String page_video_complete_views_30s = "page_video_complete_views_30s";
    public static final String page_video_views_click_to_play = "page_video_views_click_to_play";
    public static final String page_video_views_autoplayed = "page_video_views_autoplayed";
    public static final String page_video_complete_views_30s_click_to_play = "page_video_complete_views_30s_click_to_play";
    public static final String page_video_complete_views_30s_autoplayed = "page_video_complete_views_30s_autoplayed";
    public static final String page_actions_post_reactions_total = "page_actions_post_reactions_total";

    //Metric for twitter
    public static final String POST_ENGAGEMENTS_TWITTER = "engagements";
    public static final String POST_IMPRESSIONS_TWITTER = "impressions";
    public static final String POST_FAVORITES_TWITTER = "favorites";
    public static final String POST_RETWEETS_TWITTER = "retweets";
    public static final String POST_REPLIES_TWITTER = "replies";
    public static final String POST_GROUPING1_TWITTER = "tweet.id";
    public static final String POST_GROUPING2_TWITTER = "engagement.type";
    public static final String POST_GROUPING3_TWITTER = "engagement.day";


    public static final String POST_ENGAGEMENT_METRIC = "post_engaged_users";
    public static final String POST_IMPRESSIONS_METRIC = "post_impressions";
    public static final String POST_REACH_METRIC = "post_impressions_unique";
    public static final String POSTED_DATE_FORMAT = "yyyy-MM-dd'T'HH:mm:ssZ";
    public static final String FB_POST_INSIGHT_ES = "facebook-post-insight-es";
    public static final String TIKTOK_POST_INSIGHT_ES = "tiktok-post-insight-es";
    public static final String APPLE_POST_INSIGHT_ES = "apple-post-insight-es";
    public static final String GMB_POST_INSIGHT_ES = "gmb-post-insight-es";
    public static final String TWITTER_POST_INSIGHT_TOPIC = "twitter-post-insight-topic";
    public static final String FB_POST_INSIGHT_TOPIC = "facebook-post-insight-topic";
    public static final String TWITTER_POST_INSIGHT_ES = "twitter-post-insight-es";
    public static final String LINKEDIN_POST_INSIGHT_ES = "linkedin-post-insight-es";

    public static final String IG_POST_INSIGHT_TOPIC = "instagram-post-insight-topic";
    public static final String IG_ENGAGE_SYNC_EVENT = "instagram_engage_sync_event";
    public static final String POST_INSIGHT_TOPIC = "post-insight-topic";
    public static final String PAGE_AND_POST_INSIGHTS = "page_and_post_insights";

    // Index for es
    public static final String FACEBOOK_PAGE_INSIGHTS = "facebook_page_insight";
   public static final String TIKTOK_PAGE_INSIGHTS = "tiktok_page_insight";
    public static final String APPLE_PAGE_INSIGHTS = "apple_page_insight";
    public static final String POST_INSIGHT = "post_insight";
    public static final String FACEBOOK_POST_INSIGHT = "facebook_post_insight";

    public static final String INSTAGRAM_PAGE_INSIGHTS = "instagram_page_insight";

    public static final String IMPRESSIONS = "impressions";
    public static final String VIEWS = "views";

    public static final String REACH = "reach";

    public static final String ENGAGEMENT = "engagement";

    public static final String FOLLOWER_COUNT = "follower_count";

    public static final String TWITTER_PAGE_INSIGHTS = "twitter_page_insight";
    public static final String TWITTER_POST_INSIGHT = "twitter_post_insight";
    public static final String GMB_POST_INSIGHT = "gmb_post_insight";
    public static final String GMB_PAGE_INSIGHTS = "gmb_report_page_insight"; // TODO : rename to gmb_page_insight_report
    public static final String TOTAL = "Total";
    public static final String LINKEDIN_PAGE_INSIGHTS = "linkedin_page_insight";
    public static final String LINKEDIN_POST_INSIGHT = "linkedin_post_insight";
    public static final String GMB_REPORT_ANALYTICS = "gmb_report_analytics";

    public static final String YOUTUBE_PAGE_INSIGHTS = "youtube_page_insight";
    public static final String IG_TOTAL_INTERACTION = "total_interactions";
    public static final String IG_VIDEO_VIEWS = "video_views";
    public static final String IG_PLAYS = "plays";
    public static final List<String> FB_REELS_METRICS = Arrays.asList("fb_reels_total_plays", "post_impressions_unique", "post_video_likes_by_reaction_type",
            "post_video_social_actions", "post_video_avg_time_watched", "post_video_view_time");



    public static final List<String> FB_STORIES_METRICS = Arrays.asList("PAGE_STORY_IMPRESSIONS_BY_STORY_ID", "PAGE_STORY_IMPRESSIONS_BY_STORY_ID_UNIQUE",
            "STORY_INTERACTION", "PAGES_FB_STORY_THREAD_LIGHTWEIGHT_REACTIONS", "PAGES_FB_STORY_REPLIES", "PAGES_FB_STORY_SHARES");

    public static final String LIKE_COUNT = "likeCount";
    public static final String COMMENT_COUNT = "commentCount";
    public static final String SHARE_COUNT = "shareCount";
    public static final String IMPRESSION = "impression";


    public InsightsConstants() {
    }
}
