package com.birdeye.social.insights.Facebook;

import com.birdeye.social.insights.ChannelWisePagePostData;
import com.birdeye.social.sro.SocialTagBasicDetail;
import lombok.*;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PagePostData {

    private String postId;
    private String bePostId;
    private Integer bePostPageCount;
    private String postContent;
    private List<String> imageUrls;
    private List<String> videoUrls;
    private String linkPreviewUrl;
    private int impression;
    private int reach;
    private int engagement;
    private double engagementRate;
    private String postedDate;
    private String postEndDate;
    private Boolean isBePost;
    private String pageName;
    private List<ChannelWisePagePostData> channelWisePostInsights;
    private Integer likeCount;
    private Integer shareCount;
    private Integer clickCount;
    private Integer commentCount;
    private String postType;
    private Boolean isExpired;
    private List<SocialTagBasicDetail> tags;
    private Boolean isDeleted = false;
    private Integer publisherId;
    private String publisherName;
    private String publisherEmail;
    private String pageId;
    private Integer businessId;
    private Integer sourceId;
    private Integer videoViews;
    private boolean aiPost = false;
    private Integer plays;
    private Integer minutesViewed;
    private Integer avgMinutesViewed;

}
