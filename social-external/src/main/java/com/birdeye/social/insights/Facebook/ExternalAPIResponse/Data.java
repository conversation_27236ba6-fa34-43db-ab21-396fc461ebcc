package com.birdeye.social.insights.Facebook.ExternalAPIResponse;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Data implements Serializable {

    private String name;
    private String period;
    @JsonProperty(value = "created_time")
    private String createdTime;
    private List<Value> values;
}
