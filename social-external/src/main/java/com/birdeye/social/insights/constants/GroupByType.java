package com.birdeye.social.insights.constants;

import org.springframework.util.StringUtils;


public enum GroupByType {

    DAY("day", "MM/dd/yyyy", 89L,1),
    WEEK("week", "MM/dd/yyyy", 364L,7),
    MONTH("month", "MMM yyyy", 1824L,30),
    QUARTER("quarter", "MMM yyyy", 8099L,120),
    YEAR("year", "yyyy", 32850L,365),
    ALL_TIME("all_time", "dd/MMM/yyyy", Long.MAX_VALUE,720);

//    DAY("day", "MM/dd/yyyy", 1),
//    WEEK("week", "MM/dd/yyyy", 7),
//    MONTH("month", "MMM yyyy", 30),
//    QUARTER("quarter", "MMM yyyy", 120),
//    YEAR("year", "yyyy", 365),
//    ALL_TIME("all_time", "dd/MMM/yyyy", 720);

    private final String type;
    private final String labelFormat;
    private final Long maxRange; // in days
    private final int days;

    private GroupByType(String type, String labelFormat, Long maxRange,int days) {
        this.type = type;
        this.labelFormat = labelFormat;
        this.maxRange = maxRange;
        this.days = days;
    }

    // converts string value(case insensitive) to enum
    // default value for null/unknown string is ALL_TIME
    public static GroupByType fromString(String str) {
        for (GroupByType type : GroupByType.values()) {
            if (type.name().equalsIgnoreCase(str)) {
                return type;
            }
        }
        return ALL_TIME;
    }

    public String getLabelFormat() {
        return labelFormat;
    }

    public String getType() {
        return type;
    }

    public Long getMaxRange() {
        return maxRange;
    }

    public int getDays() {
        return days;
    }

    // returns true if given range(days) is valid for given grouping type
    public boolean isValidRange(long days) {
        return (days <= this.maxRange);
    }

    public static GroupByType validateGroupingType(GroupByType groupBy, long days) {
        if (GroupByType.ALL_TIME.equals(groupBy)) {
            // using days directly
            groupBy = getMaxGroupingForRange(days);
        } else if (!groupBy.isValidRange(days)) {
            groupBy = getMaxGroupingForRange(days);
        }
        return groupBy;
    }

    /*
     * returns widest grouping supported for given date range
     */
    public static GroupByType getMaxGroupingForRange(long days) {
        GroupByType maxType = GroupByType.YEAR;
        for (GroupByType type : GroupByType.values()) {
            if (type.isValidRange(days)) {
                maxType = type;
                break;
            }
        }
        return maxType;
    }

    public static GroupByType getByType(String type) {
        for (GroupByType groupByType : GroupByType.values()) {
            if (groupByType.type.equalsIgnoreCase(type)) {
                return groupByType;
            }
        }
        return ALL_TIME;
    }

    public static GroupByType getByName(String name) {

        if(StringUtils.isEmpty(name)) {
            return ALL_TIME;
        }
        for(GroupByType g: GroupByType.values()) {
            if(name.equalsIgnoreCase(g.getType())) {
                return g;
            }
        }
        return ALL_TIME;
    }
}
