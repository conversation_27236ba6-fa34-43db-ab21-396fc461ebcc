package com.birdeye.social.insights.ES;

public enum ReportType {
    POST_INSIGHTS("post_insight");

    private final String name;

    public String getName() {
        return name;
    }

    ReportType(String name) {
        this.name = name;
    }

    public static ReportType searchTemplate(String name){
        try{
            for (ReportType reportType : ReportType.values()){
                if(reportType.getName().equalsIgnoreCase(name)){
                    return reportType;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
