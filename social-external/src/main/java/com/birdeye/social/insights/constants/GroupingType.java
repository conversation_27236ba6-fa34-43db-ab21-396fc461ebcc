package com.birdeye.social.insights.constants;

import org.springframework.util.StringUtils;

import java.time.temporal.ChronoUnit;
import java.util.Optional;

public enum GroupingType {

    DAY("day", ChronoUnit.DAYS,1), WEEK("week",ChronoUnit.WEEKS,1),
    MONTH("month", ChronoUnit.MONTHS, 1), QUARTER("quarter", ChronoUnit.MONTHS,3),
    YEAR("year", ChronoUnit.YEARS, 1);

    private final String name;
    private final ChronoUnit timeUnit;
    private final int timeDuration;


    private GroupingType(String name, ChronoUnit timeUnit, int timeDuration) {
        this.name = name;
        this.timeUnit = timeUnit;
        this.timeDuration = timeDuration;
    }

    public static Optional<GroupingType> getByName(String name) {

        if(StringUtils.isEmpty(name)) {
            return Optional.empty();
        }
        for(GroupingType g: GroupingType.values()) {
            if(name.equalsIgnoreCase(g.getName())) {
                return Optional.of(g);
            }
        }
        return Optional.empty();
    }

    public String getName() {
        return name;
    }

    public int getTimeDuration() {
        return timeDuration;
    }

    public ChronoUnit getTimeUnit() {
        return timeUnit;
    }
}

