/**
 * 
 */
package com.birdeye.social.youtube;

/**
 * <AUTHOR>
 *
 */
public class YoutubeActivity {

    private YoutubeActivityId id;
    private String url;
    
    private String channelTitle;
    private YoutubeActivitySnippet snippet;

    public String getUrl() {
        return url;
    }
    
    public YoutubeActivityId getId() {
        return id;
    }

    public void setId(YoutubeActivityId id) {
        this.id = id;
        
        this.url = "https://www.youtube.com/watch?v=" + id.getVideoId();
    }

    public String getChannelTitle() {
        return channelTitle;
    }

    public void setChannelTitle(String channelTitle) {
        this.channelTitle = channelTitle;
    }

    public YoutubeActivitySnippet getSnippet() {
        return snippet;
    }

    public void setSnippet(YoutubeActivitySnippet snippet) {
        this.snippet = snippet;
    }

}
