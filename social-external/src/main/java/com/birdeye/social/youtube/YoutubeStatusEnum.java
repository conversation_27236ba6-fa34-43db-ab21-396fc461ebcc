package com.birdeye.social.youtube;

public enum YoutubeStatusEnum {

    DELETED("deleted"),FAILED("failed"),PROCESSED("processed"),REJECTED("rejected"),UPLOADED("uploaded");

    private String name;

    public String getName() {
        return name;
    }

    void setName(String name) {
        this.name = name;
    }

    YoutubeStatusEnum(String name) {
        this.name = name;
    }

    public static YoutubeStatusEnum getYoutubeStatusEnum(String name) {
        for (YoutubeStatusEnum status : YoutubeStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
