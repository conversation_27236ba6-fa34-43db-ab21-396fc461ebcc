package com.birdeye.social.youtube;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class YoutubeCommentThreadResponse {
    String kind;
    String etag;
    PageInfo pageInfo;
    String nextPageToken;
    List<Item> items;

    @Getter
    @Setter
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PageInfo {
        Integer totalResults;
        Integer resultsPerPage;
    }
}
