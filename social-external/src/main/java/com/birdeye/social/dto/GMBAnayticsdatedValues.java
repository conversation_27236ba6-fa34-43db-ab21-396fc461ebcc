package com.birdeye.social.dto;

import java.io.Serializable;

import com.birdeye.social.external.request.google.Date;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.NoArgsConstructor;


@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBAnayticsdatedValues implements Serializable{

	private static final long serialVersionUID = 1L;
	private Date date;
	private Long value;
	
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public Long getValue() {
		return value;
	}
	public void setValue(Long value) {
		this.value = value;
	}
	public GMBAnayticsdatedValues(Date date, Long value) {
		super();
		this.date = date;
		this.value = value;
	}
}
