package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)

public class SocialPostDTO implements Serializable {

    @Size(min = 1 , max = 100)
    @NotNull
    @NotEmpty
    private List<Integer> socialPostId;

    public List<Integer> getSocialPostId() {
        return socialPostId;
    }

    public void setSocialPostId(List<Integer> socialPostId) {
        this.socialPostId = socialPostId;
    }
}
