package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WhitelistedDetail {

    private static String whitelistEmail;
    private static  String whitelistIp;

    public static String getWhitelistEmail() {
        return whitelistEmail;
    }

    public static void setWhitelistEmail(String whitelistEmail) {
        WhitelistedDetail.whitelistEmail = whitelistEmail;
    }

    public static String getWhitelistIp() {
        return whitelistIp;
    }

    public static void setWhitelistIp(String whitelistIp) {
        WhitelistedDetail.whitelistIp = whitelistIp;
    }
}
