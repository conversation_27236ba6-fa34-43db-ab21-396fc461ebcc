package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

public class User {
	
	public Integer id;
    public String emailId;
    public String phone;
    public String name;
    public String role;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public String enterpriseUser;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public List<Integer> locationIds;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	public String accountId;


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getEnterpriseUser() {
		return enterpriseUser;
	}

	public void setEnterpriseUser(String enterpriseUser) {
		this.enterpriseUser = enterpriseUser;
	}

	public List<Integer> getLocationIds() {
		return locationIds;
	}

	public void setLocationIds(List<Integer> locationIds) {
		this.locationIds = locationIds;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	@Override
	public String toString() {
		return "User [id=" + id + ", emailId=" + emailId + ", phone=" + phone + ", name=" + name + ", role="
				+ role + "]";
	}
}
