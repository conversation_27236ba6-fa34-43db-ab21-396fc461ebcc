package com.birdeye.social.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class GoogleLocationSearchResponse {

    private List<GoogleLocation> googleLocations;

    @Getter
    @Setter
    public static class GoogleLocation {
        private String name;
        private Location location;
        private String requestAdminRightsUri;
    }

    @Getter
    @Setter
    public static class Location {
        private String title;
        private PhoneNumbers phoneNumbers;
        private StorefrontAddress storefrontAddress;
        private LatLng latlng;
        private Metadata metadata;
        private String websiteUri;
    }

    @Getter
    @Setter
    public static class PhoneNumbers {
        private String primaryPhone;
    }

    @Getter
    @Setter
    public static class StorefrontAddress {
        private String regionCode;
        private String languageCode;
        private String postalCode;
        private String administrativeArea;
        private String locality;
        private List<String> addressLines;
    }

    @Getter
    @Setter
    public static class LatLng {
        private double latitude;
        private double longitude;
    }

    @Getter
    @Setter
    public static class Metadata {
        private String placeId;
        private String mapsUri;
        private String newReviewUri;
    }
}

