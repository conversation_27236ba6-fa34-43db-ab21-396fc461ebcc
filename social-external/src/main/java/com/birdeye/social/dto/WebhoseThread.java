package com.birdeye.social.dto;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.google.gson.annotations.SerializedName;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
"uuid",
"url",
"site_full",
"site",
"site_section",
"site_categories",
"section_title",
"title",
"title_full",
"published",
"replies_count",
"participants_count",
"site_type",
"country",
"spam_score",
"main_image",
"performance_score",
"domain_rank",
"reach",
"social"
})
public class WebhoseThread {

@JsonProperty("uuid")
private String uuid;
@JsonProperty("url")
private String url;
@JsonProperty("site_full")
private String siteFull;
@JsonProperty("site")
private String site;
@SerializedName("site_section")
private String siteSection;
@SerializedName("site_categories")
private List<String> siteCategories = null;
@SerializedName("section_title")
private String sectionTitle;
@JsonProperty("title")
private String title;
@SerializedName("title_full")
private String titleFull;
@JsonProperty("published")
private String published;
@SerializedName("replies_count")
private Integer repliesCount;
@SerializedName("participants_count")
private Integer participantsCount;
@SerializedName("site_type")
private String siteType;
@JsonProperty("country")
private String country;
@SerializedName("spam_score")
private Double spamScore;
@SerializedName("main_image")
private String mainImage;
@SerializedName("performance_score")
private Integer performanceScore;
@SerializedName("domain_rank")
private Object domainRank;
@JsonProperty("reach")
private Object reach;
	/*
	 * @JsonProperty("social") private Social social;
	 */
@JsonIgnore
private Map<String, Object> additionalProperties = new HashMap<String, Object>();

@JsonProperty("uuid")
public String getUuid() {
return uuid;
}

@JsonProperty("uuid")
public void setUuid(String uuid) {
this.uuid = uuid;
}

@JsonProperty("url")
public String getUrl() {
return url;
}

@JsonProperty("url")
public void setUrl(String url) {
this.url = url;
}

@JsonProperty("site_full")
public String getSiteFull() {
return siteFull;
}

@JsonProperty("site_full")
public void setSiteFull(String siteFull) {
this.siteFull = siteFull;
}

@JsonProperty("site")
public String getSite() {
return site;
}

@JsonProperty("site")
public void setSite(String site) {
this.site = site;
}

@JsonProperty("site_section")
public String getSiteSection() {
return siteSection;
}

@JsonProperty("site_section")
public void setSiteSection(String siteSection) {
this.siteSection = siteSection;
}

@JsonProperty("site_categories")
public List<String> getSiteCategories() {
return siteCategories;
}

@JsonProperty("site_categories")
public void setSiteCategories(List<String> siteCategories) {
this.siteCategories = siteCategories;
}

@JsonProperty("section_title")
public String getSectionTitle() {
return sectionTitle;
}

@JsonProperty("section_title")
public void setSectionTitle(String sectionTitle) {
this.sectionTitle = sectionTitle;
}

@JsonProperty("title")
public String getTitle() {
return title;
}

@JsonProperty("title")
public void setTitle(String title) {
this.title = title;
}

@JsonProperty("title_full")
public String getTitleFull() {
return titleFull;
}

@JsonProperty("title_full")
public void setTitleFull(String titleFull) {
this.titleFull = titleFull;
}

@JsonProperty("published")
public String getPublished() {
return published;
}

@JsonProperty("published")
public void setPublished(String published) {
this.published = published;
}

@JsonProperty("replies_count")
public Integer getRepliesCount() {
return repliesCount;
}

@JsonProperty("replies_count")
public void setRepliesCount(Integer repliesCount) {
this.repliesCount = repliesCount;
}

@JsonProperty("participants_count")
public Integer getParticipantsCount() {
return participantsCount;
}

@JsonProperty("participants_count")
public void setParticipantsCount(Integer participantsCount) {
this.participantsCount = participantsCount;
}

@JsonProperty("site_type")
public String getSiteType() {
return siteType;
}

@JsonProperty("site_type")
public void setSiteType(String siteType) {
this.siteType = siteType;
}

@JsonProperty("country")
public String getCountry() {
return country;
}

@JsonProperty("country")
public void setCountry(String country) {
this.country = country;
}

@JsonProperty("spam_score")
public Double getSpamScore() {
return spamScore;
}

@JsonProperty("spam_score")
public void setSpamScore(Double spamScore) {
this.spamScore = spamScore;
}

@JsonProperty("main_image")
public String getMainImage() {
return mainImage;
}

@JsonProperty("main_image")
public void setMainImage(String mainImage) {
this.mainImage = mainImage;
}

@JsonProperty("performance_score")
public Integer getPerformanceScore() {
return performanceScore;
}

@JsonProperty("performance_score")
public void setPerformanceScore(Integer performanceScore) {
this.performanceScore = performanceScore;
}

@JsonProperty("domain_rank")
public Object getDomainRank() {
return domainRank;
}

@JsonProperty("domain_rank")
public void setDomainRank(Object domainRank) {
this.domainRank = domainRank;
}

@JsonProperty("reach")
public Object getReach() {
return reach;
}

@JsonProperty("reach")
public void setReach(Object reach) {
this.reach = reach;
}

	/*
	 * @JsonProperty("social") public Social getSocial() { return social; }
	 * 
	 * @JsonProperty("social") public void setSocial(Social social) { this.social =
	 * social; }
	 */

@JsonAnyGetter
public Map<String, Object> getAdditionalProperties() {
return this.additionalProperties;
}

@JsonAnySetter
public void setAdditionalProperty(String name, Object value) {
this.additionalProperties.put(name, value);
}

}