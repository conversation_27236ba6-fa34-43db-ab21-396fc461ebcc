package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessCategoriesDTO {

    List<String>categories;

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }

    @Override
    public String toString() {
        return "BusinessCategoriesDTO{" +
                "categories=" + categories +
                '}';
    }
}
