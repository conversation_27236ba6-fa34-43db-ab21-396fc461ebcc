package com.birdeye.social.dto;

public class BusinessOptionsDTO {
    private Integer enableMessenger;
    private Integer enableChatWidget;
    private Integer isGoogleMessageEnabled;

    public Integer getIsGoogleMessageEnabled() {
        return isGoogleMessageEnabled;
    }

    public void setIsGoogleMessageEnabled(Integer isGoogleMessageEnabled) {
        this.isGoogleMessageEnabled = isGoogleMessageEnabled;
    }

    public Integer getEnableChatWidget() {
        return enableChatWidget;
    }

    public void setEnableChatWidget(Integer enableChatWidget) {
        this.enableChatWidget = enableChatWidget;
    }

    public Integer getEnableMessenger() {
        return enableMessenger;
    }

    public void setEnableMessenger(Integer enableMessenger) {
        this.enableMessenger = enableMessenger;
    }
}
