package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MentionEsRequest {
    private Long id;
    private String pageId;
    private Long businessId;
    private Long enterpriseId;
    private String images;
    private String video;
    private String mediaSequence;
    private String type;
    private Integer sourceId;
    private String url;
    private String text;
    private String postId;
    private String date;
    private EsMentionMetaData mentionMetaData;
    private EsMentionReviewerDataPoint reviewerData;
    private EsTicketDataPoint ticketDetail;
    private String notificationId;
    private List<String> imagesList;
}
