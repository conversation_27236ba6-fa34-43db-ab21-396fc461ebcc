package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessHours {

    private Integer wholeWeekOperating;
    private String timeZone;
    private String timeZoneId;
    private List<OperationHour> operationHours;
    List<OperationHour>  contactHours = new ArrayList<>();
    private Integer contactwholeWeekOperating;
    Map<Integer, List<OperationHour>> dailyWorkingHours = new HashMap<>();

    public List<OperationHour> getContactHours() {
        return contactHours;
    }

    public void setContactHours(List<OperationHour> contactHours) {
        this.contactHours = contactHours;
    }

    public Integer getContactwholeWeekOperating() {
        return contactwholeWeekOperating;
    }

    public void setContactwholeWeekOperating(Integer contactwholeWeekOperating) {
        this.contactwholeWeekOperating = contactwholeWeekOperating;
    }

    public void setDailyWorkingHours(Map<Integer, List<OperationHour>> dailyWorkingHours) {
        this.dailyWorkingHours = dailyWorkingHours;
    }
    public void setWholeWeekOperating(Integer wholeWeekOperating) {
        this.wholeWeekOperating = wholeWeekOperating;
    }
    public Integer getWholeWeekOperating() {
        return wholeWeekOperating;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public List<OperationHour> getOperationHours() {
        return operationHours;
    }

    public void setOperationHours(List<OperationHour> operationHours) {
        this.operationHours = operationHours;
    }

    public String getTimeZoneId() {
        return timeZoneId;
    }

    public void setTimeZoneId(String timeZoneId) {
        this.timeZoneId = timeZoneId;
    }

    @JsonIgnore
    public Map<Integer, List<OperationHour>> getDailyWorkingHours() {
        List<OperationHour> dailyContactHours = CollectionUtils.isNotEmpty(this.contactHours) ? this.contactHours : this.operationHours ;
        if(MapUtils.isNotEmpty(dailyWorkingHours)) return dailyWorkingHours;
        dailyContactHours.forEach(operationHour -> {
            List<OperationHour> workingHour = this.dailyWorkingHours.getOrDefault(operationHour.getDay(), new ArrayList<>());
            if(Objects.nonNull(operationHour.getIsOpen()) && operationHour.getIsOpen() == 1) {
                workingHour.add(operationHour);
                this.dailyWorkingHours.put(operationHour.getDay(), workingHour);
            }
        });
        return dailyWorkingHours;
    }

    @JsonIgnore
    public Integer getContactStatus(){
        if( this.contactwholeWeekOperating == null){
            return this.wholeWeekOperating;
        }else{
            return this.contactwholeWeekOperating;
        }
    }

}
