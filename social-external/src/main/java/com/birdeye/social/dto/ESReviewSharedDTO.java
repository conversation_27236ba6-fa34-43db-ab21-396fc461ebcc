/**
 * 
 */
package com.birdeye.social.dto;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ESReviewSharedDTO implements Serializable {
	private static final long serialVersionUID = 8095754442330887045L;

	private Integer s_id;
	private String s_name;
	private String s_url;
	
	public ESReviewSharedDTO() {
		// TODO Auto-generated constructor stub
	}
	
	/**
	 * @param s_id
	 * @param s_name
	 * @param s_url
	 * @throws UnsupportedEncodingException 
	 */
	public ESReviewSharedDTO(Integer s_id, String s_name, String s_url){
		this.s_id = s_id;
		this.s_name = s_name;
		if(StringUtils.isNotBlank(s_url)){
			try {
				this.s_url = URLEncoder.encode(s_url,"UTF-8");
			} catch (UnsupportedEncodingException e) {
			}
		}
	}
	
}
