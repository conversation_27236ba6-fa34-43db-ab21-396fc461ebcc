package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessUserNotificationDTO {
    private  List<Integer> locationsAccess;
    private  BusinessUserNotification notification;
    private  List<Integer> teams;
    private  UserNotification user;
    private  String userRole;
    private  WhitelistedDetail whitelistedDetail;

    public List<Integer> getLocationsAccess() {
        return locationsAccess;
    }

    public void setLocationsAccess(List<Integer> locationsAccess) {
        this.locationsAccess = locationsAccess;
    }

    public BusinessUserNotification getNotification() {
        return notification;
    }

    public void setNotification(BusinessUserNotification notification) {
        this.notification = notification;
    }

    public List<Integer> getTeams() {
        return teams;
    }

    public void setTeams(List<Integer> teams) {
        this.teams = teams;
    }

    public UserNotification getUser() {
        return user;
    }

    public void setUser(UserNotification user) {
        this.user = user;
    }

    public String getUserRole() {
        return userRole;
    }

    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }

    public WhitelistedDetail getWhitelistedDetail() {
        return whitelistedDetail;
    }

    public void setWhitelistedDetail(WhitelistedDetail whitelistedDetail) {
        this.whitelistedDetail = whitelistedDetail;
    }
}
