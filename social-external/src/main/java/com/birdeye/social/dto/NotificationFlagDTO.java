package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import javax.persistence.criteria.CriteriaBuilder;


@JsonIgnoreProperties(ignoreUnknown = true)
public class NotificationFlagDTO {

    private  Boolean isBrowserNotificationEnabled;
    private Boolean isFailedPostEmailAlertEnabled;

    public NotificationFlagDTO(Boolean isBrowserNotificationEnabled, Boolean isFailedPostEmailAlertEnabled) {
        this.isBrowserNotificationEnabled = isBrowserNotificationEnabled;
        this.isFailedPostEmailAlertEnabled = isFailedPostEmailAlertEnabled;
    }

    public Boolean getBrowserNotificationEnabled() {
        return isBrowserNotificationEnabled;
    }

    public void setBrowserNotificationEnabled(Boolean browserNotificationEnabled) {
        isBrowserNotificationEnabled = browserNotificationEnabled;
    }

    public Boolean getFailedPostEmailAlertEnabled() {
        return isFailedPostEmailAlertEnabled;
    }

    public void setFailedPostEmailAlertEnabled(Boolean failedPostEmailAlertEnabled) {
        isFailedPostEmailAlertEnabled = failedPostEmailAlertEnabled;
    }
}
