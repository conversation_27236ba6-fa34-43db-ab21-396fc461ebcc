package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.XmlRootElement;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@XmlRootElement
public class BusinessBrandingMessage {
    private String name;
    private String logoURL;
    private String backgroundColor;
    private String textColor;
    private Integer override;
    private String twitterName;
    private Long businessNumber;
    private String websiteUrl;
    private Integer businessId;
    private Long id;
    private String brandColor;
    private String brandTextColor;
}
