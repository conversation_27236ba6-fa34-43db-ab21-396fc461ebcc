package com.birdeye.social.dto;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GMBAnalyticsDates implements Serializable{

	private static final long serialVersionUID = 1L;
	private Date deskMapsDate;
	private Date mobMapsDate;
	private Date deskSearchDate;
	private Date mobSearchDate;
	private Date webClicksDate;
	private Date callClickDate;
	private Date dirReqDate;	
	
}
