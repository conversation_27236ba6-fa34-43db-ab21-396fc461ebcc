package com.birdeye.social.dto.businessProfile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessProfileLocation {
    private String address1;
    private String city;
    private String state;
    private String stateName;
    private String zip;
    private String countryCode;
    private String countryName;

    public String getAddress1() { return address1; }
    public void setAddress1(String value) { this.address1 = value; }

    public String getCity() { return city; }
    public void setCity(String value) { this.city = value; }

    public String getState() { return state; }
    public void setState(String value) { this.state = value; }

    public String getStateName() { return stateName; }
    public void setStateName(String value) { this.stateName = value; }

    public String getZip() { return zip; }
    public void setZip(String value) { this.zip = value; }

    public String getCountryCode() { return countryCode; }
    public void setCountryCode(String value) { this.countryCode = value; }

    public String getCountryName() { return countryName; }
    public void setCountryName(String value) { this.countryName = value; }
}
