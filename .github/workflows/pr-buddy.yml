name: PR Validator Action

on:
  pull_request:
    types: [opened, edited, reopened, labeled]

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  validator:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: bazaarify/prbuddy@v2.0.0
        with:
          max-file-changes: 40
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          gemini-api-key: ${{ secrets.GEMINI_API_KEY }}
          rc_webhook_url: ${{ vars.RC_HOOK_URL }}
          enable-ai-review: true
          ai-reviewer-label: ai-codereview
          days_threshold: 2
          ai-review-engine: gemini
          ai-review-prompt: >
            **Focus Areas:**
            * **Security:** Identify vulnerabilities such as hardcoded credentials, SQL injection risks, improper input validation, potential deserialization issues, and other common security breaches.
            * **Performance:** Highlight bottlenecks—showcase only high severity—also issues like N+1 problems and I/O calls in loop.
            * **Concurrency:** Potential concurrency issues, race conditions, usage of `Thread.sleep()` or thread blocking operations that can degrade performance or cause deadlocks.
            * **Database Performance:** Identify slow queries in MySQL, Elasticsearch, Aerospike; improper use of Aerospike commands or Elastic query.
            * **Spring Boot & JPA Optimization:** Highlight inefficient Spring JPA queries, incorrect transaction management, and improper use of caching or lazy/eager loading.
            * **Concurrency & Multithreading:** Detect improper synchronization, potential deadlocks, race conditions, and suggest thread-safe alternatives such as `ConcurrentHashMap` over `HashMap`.
            * **Infinite Loops & Recursion:** Identify cases of unbounded loops or recursive calls that may lead to stack overflow or application hangs.
            * **Logging & Exception Handling:** Ensure meaningful logging, avoid excessive verbosity, detect potential exceptions like NullPointerException, detect improper exception handling like swallowing exceptions, and recommend structured logging with SLF4J or Logback.
            * **Duplicate Code:** Detect repeated blocks of code and recommend reuse through methods or utilities to improve maintainability.
            * **Code Readability & Maintainability:** Encourage meaningful variable and method names, suggest breaking down long methods into reusable functions, and ensure proper documentation for complex logic. Also ensure proper code comments are present wherever necessary.
            * Provide a concise and actionable feedback with line numbers. Highlight only required changes. Avoid lengthy explanations unless necessary.
            * Identify potential regression issues.
            **Example:**
              * "Line 10: Consider using a StringBuilder instead of string concatenation for better performance."
              * "Line 532: The method `getBusinessUnionUserIds` uses a union of two queries. Depending on the size of tables, this could be slow."
              * "Line 30-35: Move DB call outside loop to avoid N+1 issue."
              * "Line 212: Detected infinite recursive call in `buildGraph()` — could lead to stack overflow."
              * "Line 178-186: Duplicate block seen in `validateUser()` and `checkAccess()` — refactor into common method."