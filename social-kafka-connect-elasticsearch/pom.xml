<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.confluent</groupId>
        <artifactId>common</artifactId>
        <version>6.0.3</version>
    </parent>

    <groupId>io.confluent</groupId>
    <artifactId>kafka-connect-elasticsearch</artifactId>
    <version>11.1.1</version>
    <packaging>jar</packaging>
    <name>kafka-connect-elasticsearch</name>
    <organization>
        <name>Confluent, Inc.</name>
        <url>http://confluent.io</url>
    </organization>
    <url>http://confluent.io</url>
    <description>
        Elasticsearch Sink Connector for Kafka Connect
    </description>

    <licenses>
        <license>
            <name>Confluent Community License</name>
            <url>http://www.confluent.io/confluent-community-license</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:git://github.com/confluentinc/kafka-connect-elasticsearch.git</connection>
        <developerConnection>scm:git:**************:confluentinc/kafka-connect-elasticsearch.git</developerConnection>
        <url>https://github.com/confluentinc/kafka-connect-elasticsearch</url>
        <tag>v11.1.1</tag>
    </scm>

    <properties>
        <es.version>7.9.3</es.version>
        <hamcrest.version>1.3</hamcrest.version>
        <mockito.version>2.28.2</mockito.version>
        <gson.version>2.8.6</gson.version>
        <test.containers.version>1.15.0</test.containers.version>
        <kafka.connect.maven.plugin.version>0.11.1</kafka.connect.maven.plugin.version>
        <maven.release.plugin.version>2.5.3</maven.release.plugin.version>
        <hadoop.version>3.3.0</hadoop.version>
        <apacheds-jdbm1.version>2.0.0-M2</apacheds-jdbm1.version>
        <jackson.databind.version>2.10.5.1</jackson.databind.version>
        <!-- temporary fix by pinning the version until we upgrade to a version of common that contains this or newer version.
            See https://github.com/confluentinc/common/pull/332 for details -->
        <dependency.check.version>6.1.6</dependency.check.version>
        <confluent.maven.repo>http://packages.confluent.io/maven/</confluent.maven.repo>
        <commons.codec.version>1.15</commons.codec.version>
    </properties>

    <repositories>
        <repository>
            <id>confluent</id>
            <name>Confluent</name>
            <url>https://packages.confluent.io/maven/</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-json</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${es.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${es.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
            <version>1.11.969</version>
        </dependency>
        <!-- pin jackson-dataformat-cbor for CVE - the version comes from confluentinc/common -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-cbor</artifactId>
        </dependency>
        <!-- pin commons-codec for CVE -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <!-- pin snakeyaml for CVE -->
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.27</version>
        </dependency>
        <!-- pin httpclient for CVE -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <!-- Force commons-codec (httpclient transitive dependency) version to address CVE CCMSG-835 -->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.15</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons.codec.version}</version>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-all</artifactId>
            <version>${hamcrest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-runtime</artifactId>
            <version>${kafka.version}</version>
            <classifier>test</classifier>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <!-- Use a repackaged version of log4j with security patches. Default log4j v1.2 is a transitive dependency of connect-runtime, but it is excluded in common/pom.xml -->
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>confluent-log4j</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>connect-runtime</artifactId>
            <version>${kafka.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>${kafka.version}</version>
            <classifier>test</classifier>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_${kafka.scala.version}</artifactId>
            <version>${kafka.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka_${kafka.scala.version}</artifactId>
            <type>test-jar</type>
            <classifier>test</classifier>
            <scope>test</scope>
            <version>${kafka.version}</version>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${test.containers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-minikdc</artifactId>
            <version>${hadoop.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.20.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.1.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.tomakehurst</groupId>
            <artifactId>wiremock-jre8</artifactId>
            <version>2.30.1</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.databind.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.0.0-M3</version>
            </plugin>
            <plugin>
                <groupId>io.confluent</groupId>
                <version>${kafka.connect.maven.plugin.version}</version>
                <artifactId>kafka-connect-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>kafka-connect</goal>
                        </goals>
                        <configuration>
                            <title>Kafka Connect Elasticsearch</title>
                            <documentationUrl>https://docs.confluent.io/kafka-connect-elasticsearch/current/index.html</documentationUrl>
                            <description>
                                The Elasticsearch connector allows moving data from Kafka to Elasticsearch 2.x, 5.x, 6.x, and 7.x. It writes data from a topic in Kafka to an index in Elasticsearch and all data for a topic have the same type.

                                Elasticsearch is often used for text queries, analytics and as an key-value store (use cases). The connector covers both the analytics and key-value store use cases. For the analytics use case, each message is in Kafka is treated as an event and the connector uses topic+partition+offset as a unique identifier for events, which then converted to unique documents in Elasticsearch. For the key-value store use case, it supports using keys from Kafka messages as document ids in Elasticsearch and provides configurations ensuring that updates to a key are written to Elasticsearch in order. For both use cases, Elasticsearch’s idempotent write semantics guarantees exactly once delivery.

                                Mapping is the process of defining how a document, and the fields it contains, are stored and indexed. Users can explicitly define mappings for types in indices. When a mapping is not explicitly defined, Elasticsearch can determine field names and types from data, however, some types such as timestamp and decimal, may not be correctly inferred. To ensure that the types are correctly inferred, the connector provides a feature to infer a mapping from the schemas of Kafka messages.
                            </description>
                            <logo>logos/elasticsearch.jpg</logo>

                            <supportProviderName>Confluent, Inc.</supportProviderName>
                            <supportSummary><![CDATA[This connector is <a href="https://www.confluent.io/subscription/">supported by Confluent</a> as part of a <a href="https://www.confluent.io/product/confluent-platform/">Confluent Platform</a> subscription.]]></supportSummary>
                            <supportUrl>https://docs.confluent.io/home/<USER>/supportUrl>
                            <supportLogo>logos/confluent.png</supportLogo>

                            <ownerUsername>confluentinc</ownerUsername>
                            <ownerType>organization</ownerType>
                            <ownerName>Confluent, Inc.</ownerName>
                            <ownerUrl>https://confluent.io/</ownerUrl>
                            <ownerLogo>logos/confluent.png</ownerLogo>

                            <componentTypes>
                                <componentType>sink</componentType>
                            </componentTypes>

                            <tags>
                                <tag>Elastic</tag>
                                <tag>elasticsearch</tag>
                                <tag>search</tag>
                                <tag>analytics</tag>
                                <tag>log</tag>
                            </tags>

                            <requirements>
                                <requirement>Elasticsearch 7.x</requirement>
                            </requirements>

                            <deliveryGuarantee>
                                <deliveryGuarantee>exactlyOnce</deliveryGuarantee>
                            </deliveryGuarantee>

                            <confluentControlCenterIntegration>true</confluentControlCenterIntegration>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <inherited>true</inherited>
                <configuration>
                    <compilerArgs>
                        <arg>-Xlint:all,-try</arg>
                        <arg>-Werror</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/assembly/development.xml</descriptor>
                        <descriptor>src/assembly/package.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <argLine>@{argLine} -Djava.awt.headless=true -Dtests.security.manager=false -Dtests.jarhell.check=false</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <configuration>
                            <suppressionsLocation>checkstyle/suppressions.xml</suppressionsLocation>
                        </configuration>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>${maven.release.plugin.version}</version>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <remoteTagging>false</remoteTagging>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                </configuration>
            </plugin>
        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>standalone</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <configuration>
                            <descriptors>
                                <descriptor>src/assembly/standalone.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jenkins</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>3.0.0-M3</version>
                        <configuration>
                            <excludes>
                                <!--
                                The jenkins container is only accessible through its DOCKER_HOST IP
                                address. This makes it hard to test both hostname verification
                                enabled and disabled cases with the same cert. So, disable this IT
                                in jenkins.
                                -->
                                <exclude>**/ElasticsearchConnectorSslIT.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>licenses-package</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.2.1</version>
                        <executions>
                            <execution>
                                <id>create-licenses</id>
                                <configuration>
                                    <mainClass>io.confluent.licenses.LicenseFinder</mainClass>
                                    <arguments>
                                        <!-- Note use of development instead of package so we pick up all dependencies. -->
                                        <argument>-i ${project.build.directory}/${project.build.finalName}-package/share/java/kafka-connect-elasticsearch</argument>
                                        <argument>-o ${project.basedir}/licenses</argument>
                                        <argument>-f</argument>
                                        <argument>-h ${project.build.directory}/${project.build.finalName}-package/share/doc/kafka-connect-elasticsearch/licenses.html</argument>
                                        <argument>-l ${project.build.directory}/${project.build.finalName}-package/share/doc/kafka-connect-elasticsearch/licenses</argument>
                                        <argument>-n ${project.build.directory}/${project.build.finalName}-package/share/doc/kafka-connect-elasticsearch/notices</argument>
                                        <argument>-t ${project.name}</argument>
                                        <argument>-x licenses-${confluent.version}.jar</argument>
                                    </arguments>
                                </configuration>
                                <phase>package</phase>
                                <goals>
                                    <goal>java</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <includeProjectDependencies>true</includeProjectDependencies>
                            <includePluginDependencies>true</includePluginDependencies>
                            <executableDependency>
                                <groupId>io.confluent</groupId>
                                <artifactId>licenses</artifactId>
                            </executableDependency>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>io.confluent</groupId>
                                <artifactId>licenses</artifactId>
                                <version>${confluent.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>licenses-source</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.2.1</version>
                        <executions>
                            <execution>
                                <id>create-licenses</id>
                                <configuration>
                                    <mainClass>io.confluent.licenses.LicenseFinder</mainClass>
                                    <arguments>
                                        <!-- Note use of development instead of package so we pick up all dependencies. -->
                                        <argument>-i ${project.build.directory}/${project.build.finalName}-development/share/java/kafka-connect-elasticsearch</argument>
                                        <argument>-o ${project.basedir}/licenses</argument>
                                        <argument>-f</argument>
                                        <argument>-h ${project.basedir}/licenses.html</argument>
                                        <argument>-l ${project.basedir}/licenses</argument>
                                        <argument>-n ${project.basedir}/notices</argument>
                                        <argument>-t ${project.name}</argument>
                                        <argument>-x licenses-${confluent.version}.jar</argument>
                                    </arguments>
                                </configuration>
                                <phase>package</phase>
                                <goals>
                                    <goal>java</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <includeProjectDependencies>true</includeProjectDependencies>
                            <includePluginDependencies>true</includePluginDependencies>
                            <executableDependency>
                                <groupId>io.confluent</groupId>
                                <artifactId>licenses</artifactId>
                            </executableDependency>
                        </configuration>
                        <dependencies>
                            <dependency>
                                <groupId>io.confluent</groupId>
                                <artifactId>licenses</artifactId>
                                <version>${confluent.version}</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                    <plugin>
                        <artifactId>maven-clean-plugin</artifactId>
                        <version>3.0.0</version>
                        <configuration>
                            <filesets>
                                <fileset>
                                    <directory>.</directory>
                                    <includes>
                                        <include>licenses.html</include>
                                        <directory>licenses/*.*</directory>
                                        <directory>notices/*.*</directory>
                                    </includes>
                                </fileset>
                            </filesets>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
