<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2
          http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <!-- Assembles all dependencies in target/ directory so scripts can easily run in a development
         environment -->
    <id>development</id>
    <formats>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <dependencySets>
        <dependencySet>
            <outputDirectory>share/java/kafka-connect-elasticsearch/</outputDirectory>
            <useProjectArtifact>true</useProjectArtifact>
            <useTransitiveFiltering>true</useTransitiveFiltering>
            <excludes>
                <!-- Exclude these jars during packaging.-->
                <exclude>org.apache.kafka:connect-api</exclude>
                <exclude>org.apache.kafka:connect-json</exclude>
            </excludes>
        </dependencySet>
    </dependencySets>
</assembly>
