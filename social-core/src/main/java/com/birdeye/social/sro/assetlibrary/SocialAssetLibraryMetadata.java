package com.birdeye.social.sro.assetlibrary;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 09/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class SocialAssetLibraryMetadata implements Serializable {

    private static final long serialVersionUID = 3549666510629883390L;

    private SocialAssetLibraryMediaUIMetadata mediaUIMetadata;
    private SocialAssetLibraryOrganicMetadata organicMetadata = new SocialAssetLibraryOrganicMetadata();

    @Getter
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    public static class SocialAssetLibraryMediaUIMetadata implements Serializable {

        private static final long serialVersionUID = -4575718570396200677L;

        private Long size;
        private String baseMediaUrl;
        private String editorConfigMetadataUrl;
        private String extension;

        // These fields are populated as passed without needing to be defined in a
        // separate field name
        private Map<String, Object> otherFields = new HashMap<>();

        // Ensures Deserialization includes all fields other than mentioned above
        @JsonAnySetter
        public void setOtherFields(String name, Object value) {
            this.otherFields.put(name, value);
        }

        // Ensures Serialisation includes all fields other than mentioned above
        @JsonAnyGetter
        public Map<String, Object> getOtherFields() {
            return otherFields;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @ToString
    public static class SocialAssetLibraryOrganicMetadata implements Serializable {

        private static final long serialVersionUID = 3637796430965952833L;

        private Boolean starred = Boolean.FALSE;
    }
}
