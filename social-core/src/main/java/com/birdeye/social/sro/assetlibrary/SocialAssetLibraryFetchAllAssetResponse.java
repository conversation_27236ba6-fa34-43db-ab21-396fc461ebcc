package com.birdeye.social.sro.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 09/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialAssetLibraryFetchAllAssetResponse extends SocialAssetLibraryResponse {

    private static final long serialVersionUID = -8766615061650034864L;

    private Long totalCount;
    private List<Asset> assets;

    @Get<PERSON>
    @Setter
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Asset implements Serializable {

        private static final long serialVersionUID = -7760671085870857711L;

        private Long id;
        private String name;
        private SocialAssetLibraryAssetType type;
        private String url;
        private String thumbnailUrl;
        private Long size;
        private SocialAssetLibraryMetadata.SocialAssetLibraryMediaUIMetadata metadata;
        private List<SocialAssetLibraryTagResponse> tags;
        private Boolean starred;
        private String createdBy;
        private String updatedBy;
        private String createdAt;
        private String updatedAt;

        // Fields used for sorting
        private Date createdAtDt;
        private Date updatedAtDt;

    }
}
