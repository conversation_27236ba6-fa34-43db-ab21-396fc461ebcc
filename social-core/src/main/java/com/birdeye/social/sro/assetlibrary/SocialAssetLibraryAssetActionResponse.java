package com.birdeye.social.sro.assetlibrary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> on 12/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialAssetLibraryAssetActionResponse extends SocialAssetLibraryResponse {

    private static final long serialVersionUID = -664895448023842052L;

    private Long id;
    // This is required so that the asset naming can be propagated
    private String name;

    // Not used on UI as of now
    private Boolean isActionExecuted;

    public SocialAssetLibraryAssetActionResponse(Long id, Boolean isActionExecuted) {
        this.id = id;
        this.isActionExecuted = isActionExecuted;
    }

    public SocialAssetLibraryAssetActionResponse(Long id, String name, Boolean isActionExecuted) {
        this.id = id;
        this.name = name;
        this.isActionExecuted = isActionExecuted;
    }
}
