package com.birdeye.social.sro.assetlibrary;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> on 23/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialAssetLibraryBusinessTagOperationRequest extends AbstractSocialAssetLibraryBusinessTagOperation {

    private static final long serialVersionUID = 3060234968016034420L;
}
