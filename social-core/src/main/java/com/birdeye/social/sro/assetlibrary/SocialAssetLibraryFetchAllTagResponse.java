package com.birdeye.social.sro.assetlibrary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 20/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialAssetLibraryFetchAllTagResponse extends SocialAssetLibraryResponse {

    private static final long serialVersionUID = -4972273096372593147L;

    private Integer totalCount = 0;
    private List<SocialAssetLibraryFetchTagResponse> tags;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SocialAssetLibraryFetchTagResponse extends SocialAssetLibraryTagBasicDetail implements Serializable {

        private static final long serialVersionUID = -8505682470828949872L;

        private String createdAt;
        private String updatedAt;
        private String createdBy;
        private String updatedBy;

        // Fields used for sorting
        private Date createdAtDt;
        private Date updatedAtDt;
    }
}
