package com.birdeye.social.sro.assetlibrary;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> on 31/10/23
 */
@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialPostAssetLibraryAssetActionResponse extends SocialAssetLibraryAssetActionResponse {

    private static final long serialVersionUID = -2197692259831797775L;

    public SocialPostAssetLibraryAssetActionResponse(Long id, Boolean isActionExecuted) {
        super(id, isActionExecuted);
    }
}
