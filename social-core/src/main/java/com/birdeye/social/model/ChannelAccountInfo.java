package com.birdeye.social.model;

import com.birdeye.social.model.gmb.GMBBusinessHour;
import com.birdeye.social.model.gmb.GMBPageCategories;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChannelAccountInfo implements Serializable {
	
	private static final long serialVersionUID = 2304487767647991789L;

	private String id;
	private Boolean birdeyeConnected;
	private String image;
	private String link;
	private String pageName;
	private String handle;
	private Boolean disabled = Boolean.FALSE;
	private String validType;
	private String address;
	private String errorCode;
	private String errorMessage;
	private Integer businessId;
	private String postalCode;
	private String city;
	private String state;
	private String countryCode;
	private String primaryPhone;
	private GMBBusinessHour regularHours;
	private GMBPageCategories categories;
	private String description;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String type;
	private String websiteLink;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean readInsightsPermission;
	private Boolean isMessagingEnabled;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String messagingInvalidType;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String accountName;
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String placeId;
	private String userId;
	private Boolean connectedToAnotherLocation = false;
	private Boolean connectedToAnotherAccount = false;
	private Boolean sameAccountConnections = false;
	private Boolean diffAccountConnections = false;
	private Boolean isMapped = false;
}
