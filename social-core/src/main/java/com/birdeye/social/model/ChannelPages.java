/**
 * 
 */
package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChannelPages implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5070668590525088142L;
	private String id;
	private String image;
	private String link;
	private String pageName;
	private String handle;
	private String address;
	private Integer locationId;
	private String locationName;
	private String validType;
	private String errorCode;
	private String errorMessage;
	private String userId;

	private String addedBy;

	private String locationAddress;

	private String parentName;


	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean isMessagingEnabled = null;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String messagingInvalidType = null;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String type;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean readInsightsPermission;


	//whatsapp specific fields
	private String qualityRating;
	private String wabaName;
	private String status;
	private String messagingLimit;
	private String country;
	private String whatsppNumber;
	private Integer isMetaBusinessVerified;

	public Integer getIsMetaBusinessVerified() {
		return isMetaBusinessVerified;
	}

	public void setIsMetaBusinessVerified(Integer isMetaBusinessVerified) {
		this.isMetaBusinessVerified = isMetaBusinessVerified;
	}

	public String getWhatsppNumber() {
		return whatsppNumber;
	}

	public void setWhatsppNumber(String whatsppNumber) {
		this.whatsppNumber = whatsppNumber;
	}

	public String getQualityRating() {
		return qualityRating;
	}

	public void setQualityRating(String qualityRating) {
		this.qualityRating = qualityRating;
	}

	public String getWabaName() {
		return wabaName;
	}

	public void setWabaName(String wabaName) {
		this.wabaName = wabaName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessagingLimit() {
		return messagingLimit;
	}

	public void setMessagingLimit(String messagingLimit) {
		this.messagingLimit = messagingLimit;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public Boolean getReadInsightsPermission() {
		return readInsightsPermission;
	}

	public void setReadInsightsPermission(Boolean readInsightsPermission) {
		this.readInsightsPermission = readInsightsPermission;
	}

	public Boolean getMessagingEnabled() {
		return isMessagingEnabled;
	}

	public void setMessagingEnabled(Boolean messagingEnabled) {
		isMessagingEnabled = messagingEnabled;
	}

	public String getMessagingInvalidType() {
		return messagingInvalidType;
	}

	public void setMessagingInvalidType(String messagingInvalidType) {
		this.messagingInvalidType = messagingInvalidType;
	}

	/**
	 * @return the id
	 */
	public String getId() {
		return id;
	}
	/**
	 * @param id the id to set
	 */
	public void setId(String id) {
		this.id = id;
	}
	/**
	 * @return the image
	 */
	public String getImage() {
		return image;
	}
	/**
	 * @param image the image to set
	 */
	public void setImage(String image) {
		this.image = image;
	}
	/**
	 * @return the link
	 */
	public String getLink() {
		return link;
	}
	/**
	 * @param link the link to set
	 */
	public void setLink(String link) {
		this.link = link;
	}
	/**
	 * @return the pageName
	 */
	public String getPageName() {
		return pageName;
	}
	/**
	 * @param pageName the pageName to set
	 */
	public void setPageName(String pageName) {
		this.pageName = pageName;
	}
	/**
	 * @return the handle
	 */
	public String getHandle() {
		return handle;
	}
	/**
	 * @param handle the handle to set
	 */
	public void setHandle(String handle) {
		this.handle = handle;
	}
	/**
	 * @return the address
	 */
	public String getAddress() {
		return address;
	}
	/**
	 * @param address the address to set
	 */
	public void setAddress(String address) {
		this.address = address;
	}
	/**
	 * @return the locationId
	 */
	public Integer getLocationId() {
		return locationId;
	}
	/**
	 * @param locationId the locationId to set
	 */
	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
	/**
	 * @return the locationName
	 */
	public String getLocationName() {
		return locationName;
	}
	/**
	 * @param locationName the locationName to set
	 */
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public String getValidType() {
		return validType;
	}

	public void setValidType(String validType) {
		this.validType = validType;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public  String getUserId() {
		return userId;
	}

	public void  setUserId(String userId) { this.userId = userId;}

	public String getLocationAddress() {
		return locationAddress;
	}

	public void setLocationAddress(String locationAddress) {
		this.locationAddress = locationAddress;
	}

	public String getAddedBy() {
		return addedBy;
	}

	public void setAddedBy(String addedBy) {
		this.addedBy = addedBy;
	}


	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "ChannelPages [id=" + id + ", image=" + image + ", link=" + link + ", pageName=" + pageName + ", handle=" + handle + ", address=" + address + ", locationId=" + locationId
				+ ", locationName=" + locationName + ", locationAddress=" + locationAddress + ", validType=" + validType + ", errorMessage=" + errorMessage + "]";
	}
}
