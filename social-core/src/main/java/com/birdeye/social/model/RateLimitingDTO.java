package com.birdeye.social.model;

import com.birdeye.social.constant.RateLimitLevel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.ToString;

import java.io.Serializable;
import java.time.temporal.ChronoUnit;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class RateLimitingDTO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 5070668590525088142L;
    private String channelName;
    private String apiMethod;

    private String sharedLimit;
    private String serviceLimitSync;
    private String serviceLimitAsync;
    private String serviceName;
    private String apiUrl;
    private String sharedLimitAccess;
    private String apiIdentifier;
    private RateLimitLevel level;
    private ChronoUnit timeUnit;
    private Integer refillTime;

    public Integer getRefillTime() {
        return refillTime;
    }

    public void setRefillTime(Integer refillTime) {
        this.refillTime = refillTime;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getApiMethod() {
        return apiMethod;
    }

    public void setApiMethod(String apiMethod) {
        this.apiMethod = apiMethod;
    }

    public String getSharedLimit() {
        return sharedLimit;
    }

    public void setSharedLimit(String sharedLimit) {
        this.sharedLimit = sharedLimit;
    }

    public String getServiceLimitSync() {
        return serviceLimitSync;
    }

    public void setServiceLimitSync(String serviceLimitSync) {
        this.serviceLimitSync = serviceLimitSync;
    }

    public String getServiceLimitAsync() {
        return serviceLimitAsync;
    }

    public void setServiceLimitAsync(String serviceLimitAsync) {
        this.serviceLimitAsync = serviceLimitAsync;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getSharedLimitAccess() {
        return sharedLimitAccess;
    }

    public void setSharedLimitAccess(String sharedLimitAccess) {
        this.sharedLimitAccess = sharedLimitAccess;
    }

    public String getApiIdentifier() {
        return apiIdentifier;
    }


    public void setApiIdentifier(String apiIdentifier) {
        this.apiIdentifier = apiIdentifier;
    }

    public RateLimitLevel getLevel() {
        return level;
    }

    public void setLevel(RateLimitLevel level) {
        this.level = level;
    }

    public ChronoUnit getTimeUnit() {
        return timeUnit;
    }

    public void setTimeUnit(ChronoUnit timeUnit) {
        this.timeUnit = timeUnit;
    }

    public RateLimitingDTO(String channelName, String apiMethod, String sharedLimit, String serviceLimitSync, String serviceLimitAsync,
                           String serviceName, String apiUrl, String sharedLimitAccess, String apiIdentifier,
                           RateLimitLevel level, Integer refillTime, ChronoUnit timeUnit) {
        this.channelName = channelName;
        this.apiMethod = apiMethod;
        this.sharedLimit = sharedLimit;
        this.serviceLimitSync = serviceLimitSync;
        this.serviceLimitAsync = serviceLimitAsync;
        this.serviceName = serviceName;
        this.apiUrl = apiUrl;
        this.sharedLimitAccess = sharedLimitAccess;
        this.apiIdentifier = apiIdentifier;
        this.level = level;
        this.refillTime = refillTime;
        this.timeUnit = timeUnit;
    }


    public RateLimitingDTO() {
    }
}

