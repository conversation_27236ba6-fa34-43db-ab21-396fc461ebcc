package com.birdeye.social.utils;


public class AssumeRole {
    /**
     *args = new String[] {"arn:aws:iam::322807588216:role/demo-social-es-role","mysession"}; setting this role and session name can be anything
     *we need to set two environment variable in our local system bash_config file
     * export AWS_ACCESS_KEY_ID=********************
     * export AWS_SECRET_ACCESS_KEY=7qTfu5CnVBY9Hj6KF0BFKONQV/MGN3n50TIBjcOd
     * system file will not take these directly we need to log out current user and then log in back
     */
   /* public static Credentials main() {

        String[] args = new String[] {"arn:aws:iam::322807588216:role/demo-social-es-role","mysession"};
        System.getenv();
        final String USAGE = "\n" +
                "Usage:\n" +
                "    AssumeRole <roleArn> <roleSessionName> \n\n" +
                "Where:\n" +
                "    roleArn - the Amazon Resource Name (ARN) of the role to assume (for example, rn:aws:iam::000008047983:role/s3role). \n"+
                "    roleSessionName - an identifier for the assumed role session (for example, mysession). \n";

        if (args.length != 2) {
            System.out.println(USAGE);
            System.exit(1);
        }

        String roleArn = args[0];
        String roleSessionName = args[1];

        Region region = Region.US_EAST_1;
        StsClient stsClient = StsClient.builder()
                .region(region)
                .build();

        Credentials creds = assumeGivenRole(stsClient, roleArn, roleSessionName);
        stsClient.close();
        return creds;
    }

    public static Credentials assumeGivenRole(StsClient stsClient, String roleArn, String roleSessionName) {
        try {
            AssumeRoleRequest roleRequest = AssumeRoleRequest.builder()
                    .roleArn(roleArn)
                    .roleSessionName(roleSessionName)
                    .build();

            AssumeRoleResponse roleResponse = stsClient.assumeRole(roleRequest);
            Credentials myCreds = roleResponse.credentials();

            // Display the time when the temp creds expire
            Instant exTime = myCreds.expiration();
            String tokenInfo = myCreds.sessionToken();

            // Convert the Instant to readable date
            DateTimeFormatter formatter =
                    DateTimeFormatter.ofLocalizedDateTime(FormatStyle.SHORT)
                            .withLocale(Locale.US)
                            .withZone(ZoneId.systemDefault());

            formatter.format(exTime);
            System.out.println("The token " + tokenInfo + "  expires on " + exTime);
            return myCreds;
        } catch (StsException e) {
            System.err.println(e.getMessage());
        }
        return null;
    }*/
}
