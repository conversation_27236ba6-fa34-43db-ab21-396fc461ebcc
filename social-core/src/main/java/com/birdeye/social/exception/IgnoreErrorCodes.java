package com.birdeye.social.exception;

import java.util.HashMap;
import java.util.Map;

public class IgnoreErrorCodes {

    static Map<Integer, ErrorCodes> map;

    static {
        map = new HashMap<>();
        map.put(1238, ErrorCodes.FB_READ_INSIGHTS_PERMISSION_MISSING);
        map.put(1205, ErrorCodes.NO_SUCH_FACEBOOK_PAGE_EXIST);
        map.put(1011, ErrorCodes.INVALID_REQUEST);
        map.put(1723, ErrorCodes.GMB_MAPPING_INVALID);
        map.put(2067, ErrorCodes.FB_MESSENGER_USER_DETAILS);
        map.put(1710, ErrorCodes.GMB_PAGE_NOT_FOUND);
        map.put(2071, ErrorCodes.ERROR_WHILE_POSTING_FB_COMMENT);
        map.put(2088, ErrorCodes.SESSION_TOKEN_NOT_FOUND);
        map.put(3019, ErrorCodes.INVALID_REQUEST_REMOVE_MAPPING);
        map.put(1010, ErrorCodes.USER_NOT_FOUND);
        map.put(2095, ErrorCodes.FB_UNABLE_TO_FIND_FILTER_DATA);
        map.put(709, ErrorCodes.NO_PAGE_FOR_BUSINESS_IDS);
        map.put(ErrorCodes.ASSET_LIB_INVALID_INPUT.value(), ErrorCodes.ASSET_LIB_INVALID_INPUT);
        map.put(ErrorCodes.ASSET_LIB_ASSET_DNE.value(), ErrorCodes.ASSET_LIB_ASSET_DNE);
        map.put(ErrorCodes.ASSET_LIB_BIZ_TAG_DNE.value(), ErrorCodes.ASSET_LIB_BIZ_TAG_DNE);
        map.put(ErrorCodes.SOCIAL_TAG_DNE.value(), ErrorCodes.SOCIAL_TAG_DNE);
        map.put(ErrorCodes.INVALID_TAG_REQUEST.value(), ErrorCodes.INVALID_TAG_REQUEST);
        map.put(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value(),ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
        map.put(ErrorCodes.GMB_REVIEW_NOT_FOUND.value(), ErrorCodes.GMB_REVIEW_NOT_FOUND);
        map.put(ErrorCodes.FB_PAGE_NOT_FOUND_IGNORE.value(), ErrorCodes.FB_PAGE_NOT_FOUND_IGNORE);
        map.put(ErrorCodes.FB_MULTIPLE_MAPPING_FOUND_IGNORE.value(), ErrorCodes.FB_MULTIPLE_MAPPING_FOUND_IGNORE);
        map.put(ErrorCodes.INVALID_FB_PAGE_IGNORE.value(), ErrorCodes.INVALID_FB_PAGE_IGNORE);
        map.put(ErrorCodes.FB_ACKNOWLEDGE_ERROR.value(), ErrorCodes.FB_ACKNOWLEDGE_ERROR);
        map.put(ErrorCodes.GMB_MAPPING_NOT_FOUND.value() , ErrorCodes.GMB_MAPPING_NOT_FOUND);
        map.put(ErrorCodes.INSTAGRAM_DELETE_POST_FAILED.value(), ErrorCodes.INSTAGRAM_DELETE_POST_FAILED);
        map.put(ErrorCodes.APPLE_BUSINESS_REPORT_ERROR.value(), ErrorCodes.APPLE_BUSINESS_REPORT_ERROR);
        map.put(ErrorCodes.FB_UNABLE_TO_FIND_FILTER_DATA.value(), ErrorCodes.FB_UNABLE_TO_FIND_FILTER_DATA);
        map.put(ErrorCodes.GMB_REVIEW_NOT_FOUND.value(), ErrorCodes.GMB_REVIEW_NOT_FOUND);
        map.put(ErrorCodes.NO_SUCH_FACEBOOK_PAGE_EXIST.value(), ErrorCodes.NO_SUCH_FACEBOOK_PAGE_EXIST);
        map.put(ErrorCodes.TOO_MANY_REQUESTS.value(), ErrorCodes.TOO_MANY_REQUESTS);
        map.put(ErrorCodes.APPLE_ACCOUNT_NOT_FOUND.value(), ErrorCodes.APPLE_ACCOUNT_NOT_FOUND);
        map.put(ErrorCodes.INSTAGRAM_CONTAINER_NOT_CREATED.value(), ErrorCodes.INSTAGRAM_CONTAINER_NOT_CREATED);
        map.put(ErrorCodes.GMP_PAGE_PIN_DROP_REQUIRED.value(), ErrorCodes.GMP_PAGE_PIN_DROP_REQUIRED);
        map.put(ErrorCodes.APPROVAL_NOT_FOUND.value(), ErrorCodes.APPROVAL_NOT_FOUND);
        map.put(ErrorCodes.ENTERPRISE_ID_NOT_FOUND.value(), ErrorCodes.ENTERPRISE_ID_NOT_FOUND);
        map.put(ErrorCodes.GMB_ACCOUNT_ID_NOT_FOUND.value(), ErrorCodes.GMB_ACCOUNT_ID_NOT_FOUND);
    }

    public static Map<Integer, ErrorCodes> getValues() {
        return map;
    }

    public static boolean checkValue(int value) {
        return map.containsKey(value);
    }
}
