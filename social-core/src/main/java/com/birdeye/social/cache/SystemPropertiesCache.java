/**
 *
 */
package com.birdeye.social.cache;

import com.birdeye.social.aspect.Cache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 *
 */

@Cache(name = "systemPropertiesCache")
public class SystemPropertiesCache {

	public static final String MEDIA_UPLOAD_CALL_BACK_URL = "media-upload-call-back-url";
	public static final String MEDIA_UPLOAD_CHUNK_SIZE = "media-upload-chunk-size";
	public static final String MEDIA_UPLOAD_VIA_CHUNK_SIZE_GREATER_THAN = "media-upload-via-chunk-size-greater-than";
	public static final String TWITTER_MEDIA_UPLOAD_VIA_CHUNK_SIZE_GREATER_THAN = "twitter-media-upload-via-chunk-size-greater-than";

	private static final Logger LOGGER = LoggerFactory.getLogger(SystemPropertiesCache.class);

	private final Properties properties = new Properties();

	public static final int EXECUTOR_DEFAULT_TIMEOUT = 10000;
	public static final String EXECUTOR_TIMEOUT = "executor.timeout";
	public static final String POSTING_IMAGE_OR_VIDEO_LINK_ENABLED = "posting.image.video.link.enabled";
	public static final String GMB_API_LOCATION_LIMIT = "gmb.api.location.limit";
	public static final String GMB_CUSTOMER_MEDIA_BATCH_SIZE = "gmb.customer.media.batch.size";
	public static final String CORE_BUSINESS_DETAILS_BATCH_SIZE = "core.business.details.batch.size";
	public static final String FB_ACCESS_TOKEN = "facebook.session.token";
	public static final String FETCH_PERMISSION = "fetch.permission";
	public static final String ALLOWED_ENTERPRISES = "allowed.enterprises";

	public static final String YOUTUBE_REDIRECT_URI = "youtube.redirect.uri";

	public static final String ENABLE_SOCIAL_AUTOPOST = "enable.social.autopost";

	public static final String MAX_DELETE_MAPPINGS_COUNT = "max.delete.mappings.count";

	public static final String PUSH_MAPPING_STATUS_TO_FIREBASE = "push.mapping.status.to.firebase";

	public static final String GNIP_MONTHLY_LIMIT = "gnip.monthly.limit";

	public static final String GNIP_TO_EMAIL_IDS = "gnip.rules.email.ids";

	public static final String IG_STORY_INSIGHT_INTERVAL = "ig.story.insight.interval";

	public static final String TEST_ACCOUNT_BUSINESS_NUMBERS = "test.account.business.numbers";

	public static final String GNIP_EMAIL_ENDPOINT_URL = "gnip.email.endpoint.url";

	public static final String GNIP_EMAIL_FROM_NAME = "gnip.email.from.name";

	public static final String GNIP_EMAIL_FROM = "gnip.email.from";

	public static final String GNIP_EMAIL_TYPE = "gnip.email.type";

	public static final String GNIP_EMAIL_SUB_TYPE = "gnip.email.sub.type";

	public static final String GNIP_EMAIL_EXTERNAL_UID = "gnip.email.external.uid";

	public static final String GNIP_EMAIL_SENDGRID_API_KEY = "gnip.email.sendgrid.api.key";

	public static final String RETWEET_ACTIVE_EMAIL_IDS = "retweet.active.email.ids";

	public static final String SOCIAL_GMB_MAPPING_DOMAINS = "social.gmb.mapping.domains";

	public static final String FB_INVALID_API_REFERENCE = "fb.invalid.api.reference";


	public static final String FB_GRAPH_API_VERSION = "fb.graph.api.version";

	public static final String FB_GRAPH_API_VERSION_2 = "fb.graph.api.version.2";

	public static final String VALIDATE_INSTA_ACCOUNT = "validate.insta.account";

	public static final String DEFAULT_FB_INSTA_ROLE = "default.fb.insta.role";

	public static final String DISCONNECT_EMAIL_JOB_BATCH_SIZE = "disconnect.email.job.batch.size";

	public static final String SOCIAL_FB_PAGES_LIMIT = "social.fb.pages.limit";
	public static final String RETRY_MAX_ATTEMPTS = "retry.max.attempts";
	public static final String VOICE_OF_MERCHANT = "voice.of.merchant";

	public static final String DISABLE_FB_REQUEST_TO_BAM = "disable.fb.request.to.bam";

	public static final String DEFAULT_WELCOME_MESSAGE = "default.welcome.message";
	public static final String DEFAULT_OFFLINE_MESSAGE = "default.offline.message";
	public static final String DEFAULT_LOGO_URL = "default.logo.url";

	public static final String GOOGLE_MESSAGES_PRIVACY_POLICY_URL = "google.messages.privacy.policy.url";

	public static final String GOOGLE_MESSAGES_PARTNER_NAME = "google.messages.partner.name";

	public static final String GOOGLE_MESSAGES_PARTNER_EMAIL_ID = "google.messages.partner.emailId";

	public static final String ANONYMOUS_USER_NAMES = "anonymous.user.names";

	public static final String WEBHOSE_ENABLED_ENTERPRISES = "webhose.enabled.enterprises";

	public static final String WEBHOSE_FILTER_QUERY = "webhose.filter.query";

	public static final String WEBHOSE_BATCH_LIMIT = "webhose.batch.limit";

	public static final String BIRDEYE_ADMIN_BUSINESS_ID = "birdeye.admin.businessId";

	public static final String OPENURL_HIDDEN_DASHBOARD_ACCOUNTS = "openurl.hidden.dashboard.accounts";


	public static final String WEBHOSE_CRAWLING_DAYS_LIMIT = "webhose_crawling_days_limit";

	public static final String LIMIT_RECONNECT_ACCOUNTS = "limit.reconnect.accounts";

	public static final String SIZE_LIMIT_DAILY_SYNC = "size.limit.daily.sync";

	public static final String DAILY_SYNC_SIZE_LINKEDIN_PAGES = "daily.sync.size.linkedin.pages";
	public static final String TWITTER_POST_INSIGHT_LIMIT = "twitter.post.insight.limit";

	public static final String LIMIT_DELETE_PAGES = "delete.pages.limit";

	public static final String TEMPLATE_SERVICE_URL = "social.template.services.url";
	public static final String MEDIA_SERVICE_URL = "social.media.services.url";

	public static final String SOCIAL_LAST_POST_DAYS = "social.last.post.days";

	public static final String PAGE_INSIGHT_DAYS_LIMIT = "page.insight.days.limit";

	public static final String SOCIAL_SCAN_PAGES_LIMIT = "social.scan.pages.limit";

	public static final String BIRDEYE_ACCESS_TOKEN = "birdeye.access.token";

	public static final Integer YOUTUBE_PAGE_SIZE_DEFAULT = 50;
	public static final String LINKEDIN_WEBHOOK_URL = "linkedin.webhook.url";
	public static final String LINKEDIN_APPLICATION_ID = "linkedin.application.id";

	public static final String FEED_DATA_HOURS = "feed.data.time";
	public static final String GOOGLE_POSTING_MAX_PROCESSING_DURATION_IN_HOURS = "google.posting.max.processing.duration.in.hours";
	public static final String SOCIAL_PAGES_RETRY_LIMIT = "social.pages.retry.limit";
	public static final String RETRY_DURATION_DELAY = "retry.duration.delay";

	public static final String RETRY_DURATION_DELAY_APPLE = "retry.duration.delay.apple";
	public static final String MAX_POSTING_RETRY_DURATION_DELAY_HOURS = "max.posting.retry.duration.delay.hours";
	public static final String REDIS_RETRY_LIMIT = "redis.retry.limit";
	public static final String MENTION_DISABLE_BUSINESS_IDS = "mention.disable.business.ids";
	public static final String ENABLE_VOM_BUSINESS_IDS = "enable.vom.business.ids";

	public static final String S3_UPLOAD_URL = "social.s3.upload.url";
	public static final String PUBLIC_USER_ID = "public.user.id";
	public static final String BULK_DRAFT_USER_ID = "bulk.draft.user.id";
	public static final String BIRD_AI_USER_ID = "bird.ai.user.id";
	public static final String SOCIAL_AI_USER_ID = "social.ai.user.id";
	public static final String RESTRICTED_FEATURES = "restricted.features";
	public static final String POST_LIMIT_IG = "post.limit.ig";
	public static final String FAILED_POST_DURATION_DELAY = "failed.post.duration.delay";
	public static final String PICTURESQUE_MEDIA_UPLOAD_URL = "picturesque.media.upload.url";

	public static final String PICTURESQUE_MEDIA_UPLOAD_URL_V2 = "picturesque.media.upload.url.v2";

	public static final String PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL = "picturesque.media.upload.url.callback.url";

	public static final String UPLOAD_PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL = "upload.picturesque.media.upload.url.callback.url";

	public static final String THRESHOLD_FAILED_POST_DURATION = "threshold.failed.post.duration";
	public static final String FINAL_THRESHOLD_FAILED_POST_DURATION = "final.threshold.failed.post.duration";

	public static final String EMAIL_NOTIFICATION_AI_POSTS_COUNT = "email.notification.ai.posts.count";

	public static final String AI_POST_MIGRATION_ACCOUNT_BATCH_SIZE = "ai.post.migration.account.batch.size";

	public static final String ENABLED_NOTIFICATION_BIRDEYE_USER = "enable.notification";

	public static final String ENABLED_NOTIFICATION_BIRDEYE_ACCOUNT = "enable.notifications.account";

	public static final String SOCIAL_API_PARITY_ENABLED = "social.api.parity.enabled";
	public static final String SOCIAL_API_RATE_LIMITING_ENABLED = "social.api.rate.limiting.enabled";
	public static final String TWITTER_WEBHOOK_PRIVATE_KEY = "twitter.webhook.private.key";

	public static final String SOCIAL_WALL_POST_COUNT_LIMIT = "social.wallpost.count.limit";

	public static final String YT_ENGAGE_LIMIT = "yt.engage.limit";


	public static final String YT_ENGAGE_HOURS = "yt.engage.hours";
	public static final String YT_ENGAGE_BATCH_SIZE = "yt.engage.batch.size";

	public static final String YT_ENGAGE_ACCOUNTS_COUNT = "yt.engage.accounts.count";
	public static final String IG_MEDIA_TAG_ENGAGE_HOURS = "ig.mediatag.engage.hours";

	public static final String IG_MEDIA_TAG_ENGAGE_ACCOUNTS_COUNT = "ig.mediatag.engage.accounts.count";
	public static final String SOCIAL_DISCONNECT_COUNT_LIMIT = "social.disconnect.count.limit";

	public static final String GOOGLE_REDIRECT_URI = "google.redirect.uri";

	public static final String GOOGLE_LOGIN_REDIRECT_URI = "google.login.redirect.uri";
	public static final String LINKEDIN_VERSION = "linkedin.version";
	public static final String PUBLIC_URL = "approval.public.url";
	public static final String WHITE_LABEL_PUBLIC_URL = "whitelabel.public.url";
	public static final String BIRDEYE_URL = "birdeye.dashboard.url";
	public static final String CORE_PUBLIC_FORM_SERVICE_URL = "core.public.form.service.url";
	public static final String APPLE_BASE_URL = "apple.base.url";
	public static final String APPLE_BASE_URL_V3 = "apple.base.url.v3";
	public static final String SOCIAL_ENGAGE_PAGINATION_COUNT_LIMIT = "social.engage.pagination.count.limit";
	public static final String APPLE_VALIDITY_DAY_DIFFERENCE = "apple.showcase.day.difference.for.validity.check";
	public static final String APPLE_SCHEDULE_DAY = "apple.schedule.day";

	public static final String IS_APPLE_SHOWCASE_ENABLED = "social.is.apple.showcase.enabled";
	public static final String APPLE_SHOWCASE_WAIT_DAYS = "social.apple.showcase.wait.days";

	public static final String APPLE_SHOWCASE_IN_REVIEW_ERROR_MESSAGE = "social.apple.showcase.in.review.error.message";

	public static final String APPLE_METADATA_INFO_NULL = "apple.info.metadata.null";


	public static final String REPORTING_ENABLED_BUSINESS_NUMBER = "reporting.enabled.business.number";
	public static final String AI_POST_ENABLED_BUSINESS_NUMBER = "ai.post.enabled.business.number";
	public static final String SOCIAL_POSTING_AND_REPORTING_ENABLED = "social.posting.and.reporting.enabled.values";
	public static final String X_MAX_RESULTS_SIZE = "social.x.max.result.size";
	public static final String GET_FACEBOOK_POST_PAGE_RETRY = "get.facebook.post.page.retry";
	public static final String IG_CONTAINER_CHECK_ASYNC = "ig.container.check.async";

	public static final String COMPETITOR_PAGE_SCAN_COUNT = "competitor.page.scan.count";

	private static final String FACEBOOK_COMP_POST_LIMIT_FOR_JOB = "facebook.comp.post.limit.for.job";
	private static final String FACEBOOK_COMP_POST_LIMIT = "facebook.comp.post.limit";
	private static final String INSTAGRAM_COMP_POST_LIMIT_FOR_JOB = "instagram.comp.post.limit.for.job";
	private static final String INSTAGRAM_COMP_POST_LIMIT = "instagram.comp.post.limit";

	private static final String TWITTER_COMP_POST_LIMIT_FOR_JOB = "twitter.comp.post.limit.for.job";

	private static final String TWITTER_COMP_POST_LIMIT = "twitter.comp.post.limit";

	private static final String TWITTER_IMAGE_MEDIA_UPLOAD_URL = "twitter.image.media.upload.url";

	private static final String FACEBOOK_COMP_POST_COUNT_LIMIT = "facebook.comp.post.count.limit";

	public static final String ACKNOWLEDGED_COMMA_SEPARATED_FB_INSIGHTS_SUB_CODES = "fb.insights.acknowledged.sub.codes";
	public static final String CALENDAR_REVAMP_BUSINESS_GET = "calendar.revamp.business.gb";
	public static final String CALENDAR_POSTS_ES_V2_ENABLED = "calendar.posts.es.v2.enabled";
	public static final String CALENDAR_REVAMP_BUSINESS_POST = "calendar.revamp.business.pb";

	private static final String GRANULAR_SCOPE_FLAG = "granular.scope.flag";

	private static final String SCHEDULE_EDIT_LOCK_FLAG = "schedule.edit.lock.flag";

	private static final String BTP_CACHE_FLAG = "btp.cache.flag";
	private static final String ES_BULK_DELETE_SIZE = "es.bulk.delete.size";
	private static final String AI_POSTS_GENERATION_BATCH_SIZE = "ai.posts.generation.batch.size";
	private static final String AI_POSTS_MIGRATION_BATCH_SIZE = "ai.posts.migration.batch.size";
	private static final String AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_START = "ai.one.time.migration.months.back.for.start";
	private static final String AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_END = "ai.one.time.migration.months.back.for.end";
	private static final String AI_DAILY_POST_DAYS_BACK_FOR_START = "ai.daily.post.days.back.for.start";
	private static final String AI_DAILY_POST_DAYS_BACK_FOR_END = "ai.daily.post.days.back.for.end";
	private static final String AI_IMAGE_MIGRATION_BATCH_SIZE = "ai.image.migration.batch.size";
	private static final String AI_POSTS_EMAIL_BATCH_SIZE = "ai.posts.email.batch.size";

	public static final String REVIEW_SHARE_CREATED_BY_ANALYZE_TAB = "review.share.username.analyze.tab";

	public static final String COMPETITOR_ACCOUNT_SIZE_LIMIT = "competitor.account.limit";
	public static final String LN_WHITELISTED_REDIRECT_URI = "linkedin.whitelisted.redirect.uri";
	public static final String FB_REDIRECT_DOMAIN = "facebook.redirect.base.domain.name";
	public static final String FB_AUTH_SCOPES = "facebook.authorization.scopes";
	public static final String IG_REDIRECT_DOMAIN = "instagram.redirect.base.domain.name";
	public static final String IG_AUTH_SCOPES = "instagram.authorization.scopes";

	private static final String SAMAY_EVENT_CHECK_FLAG = "samay.event.check.flag";

	public static final String SHARED_QUOTA_PERCENTAGE = "rate.limit.shared.quota.percentage";
	public static final String RC_ALERT_ENABLE = "rate.limit.rc.alert.enable";
	public static final String ASYNC_RATE_LIMIT_COUNTER = "retry.rate.limit.counter";
	public static final String SYNC_BUSINESS_POSTS = "sync.business.posts";
	public static final String CDN_IMAGE_BASE_URL = "cdn.image.base.url";
	public static final String TRENDS_REPORT_START_DATE = "TRENDS_REPORT_START_DATE";

	public static final String TIKTOK_REDIRECT_URI = "tiktok.redirect.uri";
	public static final String AI_POST_CONFIG_DEFAULT_CHANNELS = "ai.post.config.default.channels";
	public static final String AI_POST_DEFAULT_CATEGORIES = "ai.post.default.categories";
	public static final String AI_POST_DEFAULT_GENERATION_TYPE = "ai.post.default.generation.type";
	public static final String AI_POST_DEFAULT_FREQUENCY = "ai.post.default.frequency";
	private static final String AI_POSTS_ENGAGEMENT_COUNT_THRESHOLD = "ai.posts.engagement.count.threshold";


	public static final String AI_USER_ID = "ai.user.id";

	public static final String AI_ASSISTANT_DAY = "ai.assistant.day";

	public static final String ENGAGE_COMMENT_FETCH_RESTRICTION_BUSINESS = "engage.comment.fetch.restriction.business";
	public static final String TIKTOK_VIDEO_FETCH_LIMIT = "tiktok.video.fetch.limit";

	public static final String HOURS_SAVED_POST_AI = "hours.saved.post.ai";
	public static final String BTP_SET_WEEK_COUNT = "btp.set.week.count";


	public static final String FACEBOOK_STATUS_CHECK_COUNT = "facebook.status.check.count";
	public static final String PROCESS_PENDING_POST_TIME = "process.pending.post.time";
	public static final String STORY_INSIGHTS_REFRESH_REMAINING_MINUTES = "story.insights.refresh.remaining.minutes";
	public static final String ENGAGE_COMMENTS_REPROCESS_POST_DETAILS = "engage.comments.reprocess.post.details";
	public static final String ENGAGE_COMMENTS_REPROCESS_CONTENT_DETAILS = "engage.comments.reprocess.content.details";

	public void addProperty(String name, String value) {
		properties.setProperty(name, value);
	}

	public Integer getGMBAPILocatonLimit() {

		int queryBatchSize = 500;
		if (StringUtils.isNotBlank(properties.getProperty(GMB_API_LOCATION_LIMIT))) {

			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(GMB_API_LOCATION_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the GMB batch size", exe);
			}
		}
		return queryBatchSize;
	}

	public Integer getGmbCustomerMediaBatchSize() {

		int queryBatchSize = 200;
		if (StringUtils.isNotBlank(properties.getProperty(GMB_CUSTOMER_MEDIA_BATCH_SIZE))) {

			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(GMB_CUSTOMER_MEDIA_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the gmb customer media batch size", exe);
			}
		}
		return queryBatchSize;
	}

	public Integer getBusinessDetailsBatchSize() {

		int queryBatchSize = 1000;
		if (StringUtils.isNotBlank(properties.getProperty(CORE_BUSINESS_DETAILS_BATCH_SIZE))) {

			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(CORE_BUSINESS_DETAILS_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the business details batch size", exe);
			}
		}
		return queryBatchSize;
	}

	public Integer deleteLimitForReseller() {

		int queryBatchSize = 500;
		if (StringUtils.isNotBlank(properties.getProperty(LIMIT_DELETE_PAGES))) {

			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(LIMIT_DELETE_PAGES));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return queryBatchSize;
	}

	public String getTemplateServiceUrl() {
		String templateServiceUrl = null;
		if (StringUtils.isNotBlank(properties.getProperty(TEMPLATE_SERVICE_URL))) {
			try {
				templateServiceUrl = properties.getProperty(TEMPLATE_SERVICE_URL);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fetching templateServiceUrl: ", exe);
			}
		}
		return templateServiceUrl;
	}

	public String getMediaServiceUrl() {
		String mediaServiceUrl = null;
		if (StringUtils.isNotBlank(properties.getProperty(MEDIA_SERVICE_URL))) {
			try {
				mediaServiceUrl = properties.getProperty(MEDIA_SERVICE_URL);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fetching mediaServiceUrl: ", exe);
			}
		}
		return mediaServiceUrl;
	}

	public Integer reconnectLimitForReseller() {

		int queryBatchSize = 500;
		if (StringUtils.isNotBlank(properties.getProperty(LIMIT_RECONNECT_ACCOUNTS))) {

			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(LIMIT_RECONNECT_ACCOUNTS));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return queryBatchSize;
	}

	public Integer pageSizeForDailyLinkedInPageScan() {

		int queryBatchSize = 300;
		if (StringUtils.isNotBlank(properties.getProperty(DAILY_SYNC_SIZE_LINKEDIN_PAGES))) {
			try {
				queryBatchSize = Integer.parseInt(properties.getProperty(DAILY_SYNC_SIZE_LINKEDIN_PAGES));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe.getMessage());
			}
		}
		return queryBatchSize;
	}

	public Integer postInsightLimitTwitter() {

		int twitterPostInsightLimit = 1;
		if (StringUtils.isNotBlank(properties.getProperty(TWITTER_POST_INSIGHT_LIMIT))) {

			try {
				twitterPostInsightLimit = Integer.parseInt(properties.getProperty(TWITTER_POST_INSIGHT_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the twitter batch size", exe.getMessage());
			}
		}
		return twitterPostInsightLimit;
	}

	public Integer getFacebookPostPageRetry() {

		int getFacebookPostPageRetry = 1;
		if (StringUtils.isNotBlank(properties.getProperty(GET_FACEBOOK_POST_PAGE_RETRY))) {

			try {
				getFacebookPostPageRetry = Integer.parseInt(properties.getProperty(GET_FACEBOOK_POST_PAGE_RETRY));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the facebook post page retry", exe.getMessage());
			}
		}
		return getFacebookPostPageRetry;
	}

	public String getProperty(String name) {
		return properties.getProperty(name);
	}

	public String getProperty(String name, String defaultValue) {
		String propertyValue = properties.getProperty(name);
		if (StringUtils.isEmpty(propertyValue)) {
			return defaultValue;
		}
		return propertyValue;
	}

	public boolean getBooleanProperty(String name) {
		String propertyValue = properties.getProperty(name);
		if (StringUtils.isEmpty(propertyValue)) {
			return false;
		}
		return BooleanUtils.toBoolean(propertyValue);
	}

	public boolean getBooleanProperty(String name, boolean defaultValue) {
		String propertyValue = properties.getProperty(name);
		if (StringUtils.isEmpty(propertyValue)) {
			return defaultValue;
		}
		return BooleanUtils.toBoolean(propertyValue);
	}

	public Integer getIntegerProperty(String name) {
		String propertyValue = properties.getProperty(name);
		if (StringUtils.isEmpty(propertyValue)) {
			return null;
		}
		try {
			return Integer.valueOf(propertyValue);
		} catch (NumberFormatException e) {
			LOGGER.error("Exception while parsing integer property value {} for key {}", propertyValue, name);
		}
		return null;
	}

	public Integer getExecutorTimeout() {

		int executorTimeOut = EXECUTOR_DEFAULT_TIMEOUT;
		if (StringUtils.isNotBlank(properties.getProperty(EXECUTOR_TIMEOUT))) {

			try {
				executorTimeOut = Integer.parseInt(properties.getProperty(EXECUTOR_TIMEOUT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the executor time out", exe);
			}
		}
		return executorTimeOut;
	}


	public boolean isImageOrVedioLinkPostingEnabled() {
		if (properties.containsKey(POSTING_IMAGE_OR_VIDEO_LINK_ENABLED)) {
			return "true".equalsIgnoreCase(properties.getProperty(POSTING_IMAGE_OR_VIDEO_LINK_ENABLED));
		}
		return false;
	}

	public Integer getIntegerProperty(String name, Integer defaultValue) {
		String propertyValue = properties.getProperty(name);
		if (StringUtils.isEmpty(propertyValue)) {
			return defaultValue;
		}
		try {
			return Integer.valueOf(propertyValue);
		} catch (NumberFormatException e) {
			LOGGER.error("Exception while parsing integer property value {} for key {}", propertyValue, name);
		}
		return defaultValue;
	}

	public String getFBAccessToken() {

		String appAccessToken = "";
		try {
			appAccessToken = properties.getProperty(FB_ACCESS_TOKEN);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for FB_ACCESS_TOKEN", exe);
		}
		return appAccessToken;
	}

	public String getFetchPermissionFlag() {
		String fetchPermission = "";
		try {
			fetchPermission = properties.getProperty(FETCH_PERMISSION);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for FETCH_PERMISSION", ex);
		}
		return fetchPermission;
	}

	public String[] getAllowedEnterprises() {
		String allowedEnterprises = "";
		try {
			allowedEnterprises = properties.getProperty(ALLOWED_ENTERPRISES);
			if (StringUtils.isNotEmpty(allowedEnterprises))
				return allowedEnterprises.split(",");
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for ALLOWED_ENTERPRISES", ex);
		}
		return null;
	}

	public Integer getSocialAutoPostFlag() {
		Integer socialAutoPostFlag = 0;
		try {
			socialAutoPostFlag = Integer.parseInt(properties.getProperty(ENABLE_SOCIAL_AUTOPOST));
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for ENABLE_SOCIAL_AUTOPOST", ex);
		}
		return socialAutoPostFlag;
	}

	public String getYoutubeRedirectUri() {
		String uri = "";
		try {
			uri = properties.getProperty(YOUTUBE_REDIRECT_URI);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for YOUTUBE_REDIRECT_URI", ex.getMessage());
		}
		return uri;
	}

	public Integer getGNIPMonthlyLimit() {
		int gnipMonthlyLimit = 100;
		try {
			gnipMonthlyLimit = Integer.parseInt(properties.getProperty(GNIP_MONTHLY_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while parsing integer property value for GNIP_MONTHLY_LIMIT", exe);
		}
		return gnipMonthlyLimit;
	}

	public List<String> getGNIPEmailIds() {
		List<String> emailIds = new ArrayList<>();
		String gnipIds = "";
		try {
			gnipIds = properties.getProperty(GNIP_TO_EMAIL_IDS);

			for (String id : gnipIds.split(",")) {
				emailIds.add(id.trim());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_TO_EMAIL_IDS", exe);
		}
		return emailIds;
	}

	public String getGNIPEmailEndPointURL() {
		String url = "";
		try {
			url = properties.getProperty(GNIP_EMAIL_ENDPOINT_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_ENDPOINT_URL", exe);
		}
		return url;
	}

	public String getGNIPEmailFromName() {
		String fromName = "";
		try {
			fromName = properties.getProperty(GNIP_EMAIL_FROM_NAME);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_FROM_NAME", exe);
		}
		return fromName;
	}

	public String getGNIPEmailFrom() {
		String from = "";
		try {
			from = properties.getProperty(GNIP_EMAIL_FROM);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_FROM", exe);
		}
		return from;
	}

	public String getGNIPEmailType() {

		String emailType = "";
		try {
			emailType = properties.getProperty(GNIP_EMAIL_TYPE);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_TYPE", exe);
		}
		return emailType;
	}

	public String getGNIPEmailSubType() {
		String emailSubType = "";
		try {
			emailSubType = properties.getProperty(GNIP_EMAIL_SUB_TYPE);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_SUB_TYPE", exe);
		}
		return emailSubType;
	}

	public String getGNIPEmailExternalUid() {
		String externalUID = "";
		try {
			externalUID = properties.getProperty(GNIP_EMAIL_EXTERNAL_UID);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_EXTERNAL_UID", exe);
		}
		return externalUID;
	}

	public String getGNIPEmailSendgridAPIKey() {
		String apiKey = "";
		try {
			apiKey = properties.getProperty(GNIP_EMAIL_SENDGRID_API_KEY);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GNIP_EMAIL_SENDGRID_API_KEY", exe);
		}
		return apiKey;
	}

	public List<String> getRetweetActiveIds() {
		List<String> emailIds = new ArrayList<>();
		String retweetIds = "";
		try {
			retweetIds = properties.getProperty(RETWEET_ACTIVE_EMAIL_IDS);
			for (String id : retweetIds.split(",")) {
				emailIds.add(id.trim());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for RETWEET_ACTIVE_EMAIL_IDS", exe);
		}
		return emailIds;
	}

	public List<String> getGMBLocationDomains() {
		List<String> gmbDomainList = new ArrayList<>();
		String gmbDomains = "";
		try {
			gmbDomains = properties.getProperty(SOCIAL_GMB_MAPPING_DOMAINS);
			for (String id : gmbDomains.split(",")) {
				gmbDomainList.add(id.trim());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for SOCIAL_GMB_MAPPING_DOMAINS", exe);
		}
		return gmbDomainList;
	}

	public List<String> getFbInvalidApiReference() {
		List<String> apiReferenceList = new ArrayList<>();
		String apiReference = "";
		try {
			apiReference = properties.getProperty(FB_INVALID_API_REFERENCE);
			for (String apiName : apiReference.split(",")) {
				apiReferenceList.add(apiName.trim());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for FB_INVALID_API_REFERENCE", exe);
		}
		return apiReferenceList;
	}

	public String getFbGraphApiVersion() {

		String version = null;
		if (StringUtils.isNotBlank(properties.getProperty(FB_GRAPH_API_VERSION))) {

			try {
				version = properties.getProperty(FB_GRAPH_API_VERSION);

			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return version;

	}

	public String getFbGraphApiVersionV2() {

		String version = null;
		if (StringUtils.isNotBlank(properties.getProperty(FB_GRAPH_API_VERSION_2))) {

			try {
				version = properties.getProperty(FB_GRAPH_API_VERSION_2);
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return version;

	}

	public Boolean isInstaValidationAllowed() {
		Boolean isValidate = false;
		if (StringUtils.isNotBlank(properties.getProperty(VALIDATE_INSTA_ACCOUNT))) {
			try {
				isValidate = Boolean.valueOf(properties.getProperty(VALIDATE_INSTA_ACCOUNT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return isValidate;
	}

	public String getDefaultFbInstaRole() {
		String role = null;
		if (StringUtils.isNotBlank(properties.getProperty(DEFAULT_FB_INSTA_ROLE))) {
			try {
				role = properties.getProperty(DEFAULT_FB_INSTA_ROLE);
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the Gplus batch size", exe);
			}
		}
		return role;
	}

	public Integer getMaxDeleteMappingsCount() {
		Integer maxMappingCount = 100;
		if (StringUtils.isNotBlank(properties.getProperty(MAX_DELETE_MAPPINGS_COUNT))) {
			try {
				maxMappingCount = Integer.parseInt(properties.getProperty(MAX_DELETE_MAPPINGS_COUNT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while retrieving MAX_DELETE_MAPPINGS_COUNT", exe);
			}
		}
		return maxMappingCount;
	}

	public Boolean getPushMappingStatusToFirebase() {
		Boolean value = true;
		if (StringUtils.isNotBlank(properties.getProperty(PUSH_MAPPING_STATUS_TO_FIREBASE))) {
			try {
				value = Boolean.parseBoolean(properties.getProperty(PUSH_MAPPING_STATUS_TO_FIREBASE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while retrieving PUSH_MAPPING_STATUS_TO_FIREBASE", exe);
			}
		}
		return value;
	}

	public int getDisconnectEmailBatchSize() {

		int limit = 20;
		if (StringUtils.isNotBlank(properties.getProperty(DISCONNECT_EMAIL_JOB_BATCH_SIZE))) {

			try {
				limit = Integer.parseInt(properties.getProperty(DISCONNECT_EMAIL_JOB_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while fething the disconnect email batch size", exe);
			}
		}
		return limit;

	}

	public boolean getDisableFbRequestToBam() {
		Boolean data = true;
		if (StringUtils.isNotBlank(properties.getProperty(DISABLE_FB_REQUEST_TO_BAM))) {

			try {
				data = Boolean.parseBoolean(properties.getProperty(DISABLE_FB_REQUEST_TO_BAM));
			} catch (Exception exe) {
				LOGGER.error("Error {} while fething the disconnect email batch size", exe);
			}
		}
		return data;

	}

	public String getDefaultWelcomeMessage() {
		String data = null;
		if (StringUtils.isNotBlank(properties.getProperty(DEFAULT_WELCOME_MESSAGE))) {
			try {
				data = properties.getProperty(DEFAULT_WELCOME_MESSAGE);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fething the disconnect email batch size", exe);
			}
		}
		return data;
	}

	public String getDefaultOfflineMessageMessage() {
		String data = null;
		if (StringUtils.isNotBlank(properties.getProperty(DEFAULT_OFFLINE_MESSAGE))) {
			try {
				data = properties.getProperty(DEFAULT_OFFLINE_MESSAGE);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fething the disconnect email batch size", exe);
			}
		}
		return data;
	}

	public String getDefaultLogoUrl() {
		String data = null;
		if (StringUtils.isNotBlank(properties.getProperty(DEFAULT_LOGO_URL))) {
			try {
				data = properties.getProperty(DEFAULT_LOGO_URL);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fething the disconnect email batch size", exe);
			}
		}
		return data;
	}

	public String getGoogleMessagesPrivacyPolicyUrl() {
		return properties.getProperty(GOOGLE_MESSAGES_PRIVACY_POLICY_URL);
	}

	public String getGoogleMessagesPartnerName() {
		return properties.getProperty(GOOGLE_MESSAGES_PARTNER_NAME);
	}

	public String getGoogleMessagesPartnerEmailId() {
		return properties.getProperty(GOOGLE_MESSAGES_PARTNER_EMAIL_ID);
	}

	public Set<String> getAnonymousUserNames() {
		String userNames = null;
		if (StringUtils.isNotBlank(properties.getProperty(ANONYMOUS_USER_NAMES))) {

			try {
				userNames = properties.getProperty(ANONYMOUS_USER_NAMES);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fethcinh the Anonymous user names", exe);
			}
		}
		return convertToSet(userNames, ",");
	}

	public Set<Integer> getWebhoseEnabledEnterprises() {
		String enterprises = null;
		if (StringUtils.isNotBlank(properties.getProperty(WEBHOSE_ENABLED_ENTERPRISES))) {

			try {
				enterprises = properties.getProperty(WEBHOSE_ENABLED_ENTERPRISES);
			} catch (Exception exe) {
				LOGGER.error("Error {} while fetching enterprise list for webhose", exe);
			}
		}
		return convertToIntegerSet(enterprises, ",");
	}

	public String getWebhoseFilterQuery() {
		String filterQuery = "";
		if (StringUtils.isNotBlank(properties.getProperty(WEBHOSE_FILTER_QUERY))) {
			try {
				filterQuery = properties.getProperty(WEBHOSE_FILTER_QUERY);
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for WEBHOSE_FILTER_QUERY", exe);
			}
		}
		return filterQuery;
	}

	public Integer getWebhoseBatchLimit() {
		Integer limit = 100;
		if (StringUtils.isNotBlank(properties.getProperty(WEBHOSE_BATCH_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(WEBHOSE_BATCH_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing WEBHOSE_BATCH_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getWebhoseCrawlingLimit() {
		Integer limit = 1;
		if (StringUtils.isNotBlank(properties.getProperty(WEBHOSE_CRAWLING_DAYS_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(WEBHOSE_CRAWLING_DAYS_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing WEBHOSE_CRAWLING_DATE_LIMIT", exe);
			}
		}
		return limit;
	}

	public static Set<String> convertToSet(String value, String delimeter) {
		Set<String> result = new HashSet<>();
		if (delimeter == null) {
			delimeter = ",";
		}
		if (value != null) {
			String[] split = value.split(delimeter);
			for (String keys : split) {
				result.add(keys.trim());
			}
		}
		return result;
	}

	public static Set<Integer> convertToIntegerSet(String value, String delimeter) {
		Set<Integer> result = new HashSet<>();
		if (delimeter == null) {
			delimeter = ",";
		}
		if (StringUtils.isNotEmpty(value)) {
			String[] split = value.split(delimeter);
			for (String keys : split) {
				result.add(Integer.valueOf(keys));
			}
		}
		return result;
	}

	public String getSocialFbPagesLimit() {
		String limit = "100";
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_FB_PAGES_LIMIT))) {
			try {
				limit = properties.getProperty(SOCIAL_FB_PAGES_LIMIT);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_FB_PAGES_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getRetryMaxAttempts() {
		Integer attempts = 3;
		if (StringUtils.isNotBlank(properties.getProperty(RETRY_MAX_ATTEMPTS))) {
			try {
				attempts = Integer.parseInt(properties.getProperty(RETRY_MAX_ATTEMPTS));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing RETRY_MAX_ATTEMPTS", exe);
			}
		}
		return attempts;
	}

	public Integer getBirdeyeAdminBusinessId() {
		Integer businessId = 285410;
		if (StringUtils.isNotBlank(properties.getProperty(BIRDEYE_ADMIN_BUSINESS_ID))) {
			try {
				businessId = Integer.parseInt(properties.getProperty(BIRDEYE_ADMIN_BUSINESS_ID));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing BIRDEYE_ADMIN_BUSINESS_ID", exe);
			}
		}
		return businessId;
	}

	public List<Integer> getAccountIds() {
		List<Integer> resellerIds = new ArrayList<>();
		try {
			resellerIds = Stream.of(properties.getProperty(OPENURL_HIDDEN_DASHBOARD_ACCOUNTS).split(","))
					.map(String::trim).map(Integer::parseInt).collect(Collectors.toList());
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting reseller ids", exe);
		}
		return resellerIds;
	}


	public String getVoiceOfMerchant() {

		String limit = "disable";
		if (StringUtils.isNotBlank(properties.getProperty(VOICE_OF_MERCHANT))) {
			try {
				limit = properties.getProperty(VOICE_OF_MERCHANT);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing VOICE_OF_MERCHANT", exe.getMessage());
			}
		}
		return limit;
	}

	public Integer getSocialLastPostDays(String socialChannelName) {
		int daysCount = 30;

		if (StringUtils
				.isNotBlank(properties.getProperty(SOCIAL_LAST_POST_DAYS.concat(".").concat(socialChannelName)))) {
			try {
				daysCount = Integer
						.parseInt(properties.getProperty(SOCIAL_LAST_POST_DAYS.concat(".").concat(socialChannelName)));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_LAST_POST_DAYS for channel {}", exe, socialChannelName);
			}
		}
		return daysCount;
	}

	public Integer getSocialScanLimit() {
		int limit = 100;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_SCAN_PAGES_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(SOCIAL_SCAN_PAGES_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_SCAN_PAGES_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getSocialPageInsightsLimit(String socialChannelName) {
		int daysCount = 30;
		if (StringUtils
				.isNotBlank(properties.getProperty(PAGE_INSIGHT_DAYS_LIMIT.concat(".").concat(socialChannelName)))) {
			try {
				daysCount = Integer.parseInt(
						properties.getProperty(PAGE_INSIGHT_DAYS_LIMIT.concat(".").concat(socialChannelName)));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_LAST_POST_DAYS for channel {}", exe, socialChannelName);
			}
		}
		return daysCount;
	}

	public String getLinkedinWebhookUrl() {
		String url = "";
		if (StringUtils.isNotBlank(properties.getProperty(LINKEDIN_WEBHOOK_URL))) {
			try {
				url = properties.getProperty(LINKEDIN_WEBHOOK_URL);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing LINKEDIN_WEBHOOK_URL", exe);
			}
		}
		return url;
	}

	public String getLinkedinAppDeveloperId() {
		String developerId = "";
		if (StringUtils.isNotBlank(properties.getProperty(LINKEDIN_APPLICATION_ID))) {
			try {
				developerId = properties.getProperty(LINKEDIN_APPLICATION_ID);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing LINKEDIN_APPLICATION_ID", exe);
			}
		}
		return developerId;
	}

	public Integer getFeedDataTimeDuration() {
		Integer attempts = 4;
		if (StringUtils.isNotBlank(properties.getProperty(FEED_DATA_HOURS))) {
			try {
				attempts = Integer.parseInt(properties.getProperty(FEED_DATA_HOURS));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing FEED_DATA_HOURS", exe);
			}
		}
		return attempts;
	}

	public Integer getMaxDurationForGooglePostingInHours() {
		int duration = Integer.MAX_VALUE;
		if (StringUtils.isNotBlank(properties.getProperty(GOOGLE_POSTING_MAX_PROCESSING_DURATION_IN_HOURS))) {
			try {
				duration = Integer.parseInt(properties.getProperty(GOOGLE_POSTING_MAX_PROCESSING_DURATION_IN_HOURS));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing GOOGLE_POSTING_MAX_PROCESSING_DURATION_IN_HOURS", exe);
			}
		}
		return duration;
	}

	public String getSocialFbPagesRetryLimit() {
		String limit = "10";
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_PAGES_RETRY_LIMIT))) {
			try {
				limit = properties.getProperty(SOCIAL_PAGES_RETRY_LIMIT);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_PAGES_RETRY_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getTimeDurationDelay() {
		Integer limit = 15;
		if (StringUtils.isNotBlank(properties.getProperty(RETRY_DURATION_DELAY))) {
			try {
				limit = Integer.parseInt(properties.getProperty(RETRY_DURATION_DELAY));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing RETRY_DURATION_DELAY", exe);
			}
		}
		return limit;
	}

	public Integer getTimeDurationDelayForApple() {
		Integer limit = 10;
		if (StringUtils.isNotBlank(properties.getProperty(RETRY_DURATION_DELAY_APPLE))) {
			try {
				limit = Integer.parseInt(properties.getProperty(RETRY_DURATION_DELAY_APPLE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing RETRY_DURATION_DELAY", exe);
			}
		}
		return limit;
	}

	public Integer getMaxPostingRetryTimeInHours() {
		Integer limit = 3;
		if (StringUtils.isNotBlank(properties.getProperty(MAX_POSTING_RETRY_DURATION_DELAY_HOURS))) {
			try {
				limit = Integer.parseInt(properties.getProperty(MAX_POSTING_RETRY_DURATION_DELAY_HOURS));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing MAX_POSTING_RETRY_DURATION_DELAY_HOURS", exe);
			}
		}
		return limit;
	}

	public String getRedisRetryLimit() {
		String retryLimit = "1";
		try {
			retryLimit = properties.getProperty(REDIS_RETRY_LIMIT);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for REDIS_RETRY_LIMIT", exe.getMessage());
		}
		return retryLimit;
	}

	@Deprecated
	public String getBirdeyeAccessToken() {
		String accessToken;
		if (StringUtils.isNotBlank(properties.getProperty(BIRDEYE_ACCESS_TOKEN))) {
			accessToken = properties.getProperty(BIRDEYE_ACCESS_TOKEN);
			return accessToken;
		}
		return "EAABm1gNd1MUBAOEsJOOfHlwzZBUHgHpyIkHWM5XHQlyGWRAlXIBcYhe5OXECVCG6uiKMqKW4EZCQVFixtU0HdWYIdGj5l" +
				"qDBNe7whifRkZBfAHKsp4avSSjr3CZAVONPtogNMDXXq8xd4ZCDhQLYb6SletFrJZBVrfmpc3KPfY9UIGZAKZCxieZAv";
	}

	public List<Integer> getDisableMentionsBusinessId() {

		List<Integer> disableBusinessIds = new ArrayList<>();
		try {
			disableBusinessIds = Stream.of(properties.getProperty(MENTION_DISABLE_BUSINESS_IDS).split(","))
					.map(String::trim).map(Integer::parseInt).collect(Collectors.toList());
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for MENTION_DISABLE_BUSINESS_IDS", exe);
		}
		return disableBusinessIds;
	}

	public List<Long> getEnableVOMBusinessIds() {

		List<Long> enableVOMBusinessIds = new ArrayList<>();
		try {
			if (StringUtils.isEmpty(properties.getProperty(ENABLE_VOM_BUSINESS_IDS))) {
				return enableVOMBusinessIds;
			}
			enableVOMBusinessIds = Stream.of(properties.getProperty(ENABLE_VOM_BUSINESS_IDS).split(","))
					.map(String::trim).map(Long::parseLong).collect(Collectors.toList());
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for MENTION_DISABLE_BUSINESS_IDS",
					exe.getMessage());
		}
		return enableVOMBusinessIds;
	}

	public String getS3UploadUrl() {
		String retryLimit = "";
		try {
			retryLimit = properties.getProperty(S3_UPLOAD_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for S3_UPLOAD_URL", exe.getMessage());
		}
		return retryLimit;
	}

	public Integer getPublicPostingUserId() {
		Integer userId = null;
		try {
			userId = Integer.parseInt(properties.getProperty(PUBLIC_USER_ID));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PUBLIC_USER_ID", exe.getMessage());
		}
		return userId;
	}

	public Integer getFailedPostTimeDurationDelay() {
		Integer limit = 20;
		if (StringUtils.isNotBlank(properties.getProperty(FAILED_POST_DURATION_DELAY))) {
			try {
				limit = Integer.parseInt(properties.getProperty(FAILED_POST_DURATION_DELAY));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing FAILED_POST_DURATION_DELAY", exe);
			}
		}
		return limit;
	}

	public Integer getThresholdPostTimeDurationDelay() {
		Integer limit = 20;
		if (StringUtils.isNotBlank(properties.getProperty(THRESHOLD_FAILED_POST_DURATION))) {
			try {
				limit = Integer.parseInt(properties.getProperty(THRESHOLD_FAILED_POST_DURATION));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing THRESHOLD_FAILED_POST_DURATION", exe);
			}
		}
		return limit;
	}

	/*
	 * public String getPicturesqueMediaUploadUrl() {
	 * String picturesqueUrl = "";
	 * try {
	 * picturesqueUrl = properties.getProperty(TEMPLATE_SERVICE_URL);
	 * } catch (Exception exe) {
	 * LOGGER.
	 * error("Exception {} while fetching string property value for PICTURESQUE_MEDIA_UPLOAD_URL"
	 * , exe.getMessage());
	 * }
	 * return picturesqueUrl;
	 * }
	 */

	public Integer getDefaultUserNameForDraft() {
		Integer userId = null;
		try {
			userId = Integer.parseInt(properties.getProperty(BULK_DRAFT_USER_ID));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PUBLIC_USER_ID", exe.getMessage());
		}
		return userId;
	}

	public Integer getIgFeedLimitPosts() {
		Integer postLimit = null;
		try {
			postLimit = Integer.parseInt(properties.getProperty(POST_LIMIT_IG));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PUBLIC_USER_ID", exe.getMessage());
		}
		return postLimit;
	}

	public Integer getCheckEnableNotification() {
		Integer enableBirdeyeUser = 1;
		try {
			enableBirdeyeUser = Integer.parseInt(properties.getProperty(ENABLED_NOTIFICATION_BIRDEYE_USER));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for ENABLED_NOTIFICATION_BIRDEYE_USER",
					exe.getMessage());
		}
		return enableBirdeyeUser;
	}

	public List<String> getCheckEnableNotificationBirdeyeAcc() {
		List<String> accountIds = new ArrayList<>();
		String accountList = "";
		try {
			accountList = properties.getProperty(ENABLED_NOTIFICATION_BIRDEYE_ACCOUNT);

			for (String id : accountList.split(",")) {
				accountIds.add(id.trim());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for ENABLED_NOTIFICATION_BIRDEYE_ACCOUNT",
					exe);
		}
		return accountIds;
	}

	public Integer getFinalThresholdPostTimeDurationDelay() {
		Integer limit = 60;
		if (StringUtils.isNotBlank(properties.getProperty(FINAL_THRESHOLD_FAILED_POST_DURATION))) {
			try {
				limit = Integer.parseInt(properties.getProperty(FINAL_THRESHOLD_FAILED_POST_DURATION));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing FINAL_THRESHOLD_FAILED_POST_DURATION", exe);
			}
		}
		return limit;
	}

	public Integer getEmailNotificationAIPostsCount() {
		Integer limit = 2;
		if (StringUtils.isNotBlank(properties.getProperty(EMAIL_NOTIFICATION_AI_POSTS_COUNT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(EMAIL_NOTIFICATION_AI_POSTS_COUNT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing EMAIL_NOTIFICATION_AI_POSTS_COUNT", exe);
			}
		}
		return limit;
	}

	public Integer getPostsMigrationAccountBatchSize() {
		Integer limit = 10;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POST_MIGRATION_ACCOUNT_BATCH_SIZE))) {
			try {
				limit = Integer.parseInt(properties.getProperty(AI_POST_MIGRATION_ACCOUNT_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing POST_MIGRATION_ACCOUNT_BATCH_SIZE", exe);
			}
		}
		return limit;
	}

	public List<Integer> getStoryInsightInterval() {
		List<Integer> storyInsightInterval = new ArrayList<>();
		String value = "";
		try {
			value = properties.getProperty(IG_STORY_INSIGHT_INTERVAL);

			for (String interval : value.split(",")) {
				storyInsightInterval.add(Integer.parseInt(interval));
			}
		} catch (Exception exe) {
			LOGGER.error("Exception while fetching string property value for IG_STORY_INSIGHT_INTERVAL", exe);
		}
		return storyInsightInterval;
	}

	public Boolean getParityCheckEnabled() {
		Boolean parityEnabled = false;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_API_PARITY_ENABLED))) {
			try {
				parityEnabled = Boolean.parseBoolean(properties.getProperty(SOCIAL_API_PARITY_ENABLED));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_API_PARITY_ENABLED", exe);
			}
		}
		return parityEnabled;
	}

	public String getSocialFbWallPostCountLimit() {
		String limit = "10";
		if (org.apache.commons.lang3.StringUtils.isNotBlank(properties.getProperty(SOCIAL_WALL_POST_COUNT_LIMIT))) {
			try {
				limit = properties.getProperty(SOCIAL_WALL_POST_COUNT_LIMIT);
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_WALL_POST_COUNT_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getYTEngageLimit() {
		int limit = 500;
		if (StringUtils.isNotBlank(properties.getProperty(YT_ENGAGE_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(YT_ENGAGE_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing YT_ENGAGE_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getYTEngageHours() {
		int limit = 1;
		if (StringUtils.isNotBlank(properties.getProperty(YT_ENGAGE_HOURS))) {
			try {
				limit = Integer.parseInt(properties.getProperty(YT_ENGAGE_HOURS));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing YT_ENGAGE_HOURS", exe);
			}
		}
		return limit;
	}

	public Integer getYTEnagageIdsBatchSize() {
		int limit = 50;
		if (StringUtils.isNotBlank(properties.getProperty(YT_ENGAGE_BATCH_SIZE))) {
			try {
				limit = Integer.parseInt(properties.getProperty(YT_ENGAGE_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing YT_ENGAGE_BATCH_SIZE", exe);
			}
		}
		return limit;
	}

	public Integer getYTEngageAccountsCount() {
		int limit = 500;
		if (StringUtils.isNotBlank(properties.getProperty(YT_ENGAGE_ACCOUNTS_COUNT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(YT_ENGAGE_ACCOUNTS_COUNT));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing YT_ENGAGE_ACCOUNTS_COUNT", exe);
			}
		}
		return limit;
	}

	public Integer getIGMediaTagEngageHours() {
		int limit = 1;
		if (StringUtils.isNotBlank(properties.getProperty(IG_MEDIA_TAG_ENGAGE_HOURS))) {
			try {
				limit = Integer.parseInt(properties.getProperty(IG_MEDIA_TAG_ENGAGE_HOURS));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing IG_MEDIA_TAG_ENGAGE_HOURS", exe);
			}
		}
		return limit;
	}

	public Integer getIGMediaTagEngageAccountsCount() {
		int limit = 500;
		if (StringUtils.isNotBlank(properties.getProperty(IG_MEDIA_TAG_ENGAGE_ACCOUNTS_COUNT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(IG_MEDIA_TAG_ENGAGE_ACCOUNTS_COUNT));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing IG_MEDIA_TAG_ENGAGE_ACCOUNTS_COUNT", exe);
			}
		}
		return limit;
	}

	public Boolean getRateLimitingCheckEnabled() {
		Boolean parityEnabled = false;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_API_RATE_LIMITING_ENABLED))) {
			try {
				parityEnabled = Boolean.parseBoolean(properties.getProperty(SOCIAL_API_RATE_LIMITING_ENABLED));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_API_RATE_LIMITING_ENABLED", exe);
			}
		}
		return parityEnabled;
	}

	public String getPicturesqueMediaUploadUrl() {
		String picturesqueUrl = "";
		try {
			picturesqueUrl = properties.getProperty(PICTURESQUE_MEDIA_UPLOAD_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PICTURESQUE_MEDIA_UPLOAD_URL",
					exe.getMessage());
		}
		return picturesqueUrl;
		// return "https://preprod-picturesque.birdeye.com";
	}

	public String getPicturesqueMediaUploadUrlV2() {
		String picturesqueUrl = "";
		try {
			picturesqueUrl = properties.getProperty(PICTURESQUE_MEDIA_UPLOAD_URL_V2);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PICTURESQUE_MEDIA_UPLOAD_URL_V2",
					exe.getMessage());
		}
		return picturesqueUrl;
		// return "https://preprod-picturesque.birdeye.com";
	}


	public String getTwitterWebhookKey() {
		String key = "";
		if (StringUtils.isNotEmpty(properties.getProperty(TWITTER_WEBHOOK_PRIVATE_KEY))) {
			try {
				key = String.valueOf(properties.getProperty(TWITTER_WEBHOOK_PRIVATE_KEY));
			} catch (Exception exe) {
				LOGGER.error("Error while parsing SOCIAL_API_RATE_LIMITING_ENABLED", exe);
			}
		}
		return key;
	}

	public Integer getSocialDisconnectPostCountLimit() {
		int limit = 10;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_DISCONNECT_COUNT_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(SOCIAL_DISCONNECT_COUNT_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_DISCONNECT_COUNT_LIMIT", exe);
			}
		}
		return limit;
	}

	public Integer getSocialEngagePaginationCountLimit() {
		int limit = 5;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_ENGAGE_PAGINATION_COUNT_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(SOCIAL_ENGAGE_PAGINATION_COUNT_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing SOCIAL_DISCONNECT_COUNT_LIMIT", exe);
			}
		}
		return limit;
	}

	public String getCorePublicUrl() {
		String corePublicUrl = "http://corepublicform.birdeye.internal/";
		try {
			corePublicUrl = properties.getProperty(CORE_PUBLIC_FORM_SERVICE_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for CORE_PUBLIC_FORM_SERVICE_URL",
					exe.getMessage());
		}
		return corePublicUrl;
	}

	public String getGoogleRedirectUri() {
		String uri = "";
		try {
			uri = properties.getProperty(GOOGLE_REDIRECT_URI);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for GOOGLE_REDIRECT_URI", ex.getMessage());
		}
		return uri;
	}

	public String getGoogleRedirectUriForLogin() {
		String uri = "";
		try {
			uri = properties.getProperty(GOOGLE_LOGIN_REDIRECT_URI);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for GOOGLE_LOGIN_REDIRECT_URI",
					ex.getMessage());
		}
		return uri;
	}

	public String getLinkedinVersion() {
		String version = "202301";
		try {
			version = properties.getProperty(LINKEDIN_VERSION);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for LINKEDIN_VERSION", ex.getMessage());
		}
		return version;
	}

	public String getAppleBaseUrl() {
		String baseUrl = "https://data-qualification.businessconnect.apple.com/api/v1";
		try {
			baseUrl = properties.getProperty(APPLE_BASE_URL);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for APPLE_BASE_URL", ex.getMessage());
		}
		return baseUrl;
	}

	public String getAppleBaseUrlV3() {
		String baseUrl = "https://data-qualification.businessconnect.apple.com/api/v3";
		try {
			baseUrl = properties.getProperty(APPLE_BASE_URL_V3);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for APPLE_BASE_URL_V2", ex.getMessage());
		}
		return baseUrl;
	}

	public String getPublicUrl() {
		String url = "";
		try {
			url = properties.getProperty(PUBLIC_URL);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for LINKEDIN_VERSION", ex.getMessage());
		}
		return url;
	}

	public String getBirdeyeUrl() {
		String url = "";
		try {
			url = properties.getProperty(BIRDEYE_URL);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for LINKEDIN_VERSION", ex.getMessage());
		}
		return url;
	}


	public Integer getApplePublishDateDiff() {
		Integer day = 5;
		try {
			day = Integer.valueOf(properties.getProperty(APPLE_SCHEDULE_DAY));
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for LINKEDIN_VERSION", ex.getMessage());
		}
		return day;
	}

	public boolean isAppleShowcaseEnabled() {
		if (properties.containsKey(IS_APPLE_SHOWCASE_ENABLED)) {
			return "true".equalsIgnoreCase(properties.getProperty(IS_APPLE_SHOWCASE_ENABLED));
		}
		return false;
	}

	public Integer getAppleShowcaseWaitDays() {
		int day = 7;
		try {
			day = Integer.parseInt(properties.getProperty(APPLE_SHOWCASE_WAIT_DAYS));
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for APPLE_SHOWCASE_WAIT_DAYS",
					ex.getMessage());
		}
		return day;
	}

	public String getAppleShowcaseStatusInReviewRejectedMessage() {
		String message = "something went wrong";
		try {
			message = properties.getProperty(APPLE_SHOWCASE_IN_REVIEW_ERROR_MESSAGE);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for APPLE_SHOWCASE_IN_REVIEW_ERROR_MESSAGE",
					ex.getMessage());
		}
		return message;
	}

	public String getAppleInfoMetaDataNullMessage() {
		String message = "something went wrong";
		try {
			message = properties.getProperty(APPLE_METADATA_INFO_NULL);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for APPLE_METADATA_INFO_NULL",
					ex.getMessage());
		}
		return message;
	}

	public String getWhiteLabelUrl() {
		String url = "https://socialmedia.link/";
		try {
			url = properties.getProperty(WHITE_LABEL_PUBLIC_URL);
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for LINKEDIN_VERSION", ex.getMessage());
		}
		return url;
	}

	public Integer getDefaultUserIdForPostLib() {
		Integer userId = null;
		try {
			userId = Integer.parseInt(properties.getProperty(BIRD_AI_USER_ID));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for BIRD_AI_USER_ID", exe.getMessage());
		}
		return userId;
	}

	public Integer getDefaultUserIdForAIGeneratedPosts() {
		Integer userId = -10108;
		if (StringUtils.isNotBlank(properties.getProperty(SOCIAL_AI_USER_ID))) {
			try {
				userId = Integer.parseInt(properties.getProperty(SOCIAL_AI_USER_ID));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for BIRD_AI_USER_ID", exe.getMessage());
			}
		}
		return userId;
	}

	public Integer getXMaxResultSize() {
		int maxSize = 100;
		try {
			maxSize = Integer.parseInt(properties.getProperty(X_MAX_RESULTS_SIZE));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for X_MAX_RESULTS_SIZE", exe.getMessage());
		}
		return maxSize;
	}

	public String getValueForSocialRestriction() {
		String value = null;
		try {
			value = properties.getProperty(RESTRICTED_FEATURES);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for RESTRICTED_FEATURES", exe.getMessage());
		}
		return value;
	}

	public List<Integer> getSocialPostingAndReportingEnableValues() {
		List<Integer> response = new ArrayList<>();
		try {
			String value = properties.getProperty(SOCIAL_POSTING_AND_REPORTING_ENABLED);
			response = Stream.of(value.split(",")).map(Integer::parseInt).collect(Collectors.toList());
		} catch (Exception ex) {
			LOGGER.error("Exception {} while fetching string property value for SOCIAL_POSTING_AND_REPORTING_ENABLED", ex.getMessage());
		}
		return response;
	}

	public boolean getIgContainerCheckAsync() {
		String propertyValue = properties.getProperty(IG_CONTAINER_CHECK_ASYNC);
		if (StringUtils.isEmpty(propertyValue)) {
			return false;
		}
		return BooleanUtils.toBoolean(propertyValue);
	}

	public Integer getCompetitorPageScanCount() {
		int maxSize = 100;
		try {
			maxSize = Integer.parseInt(properties.getProperty(COMPETITOR_PAGE_SCAN_COUNT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for COMPETITOR_PAGE_SCAN_COUNT", exe.getMessage());
		}
		return maxSize;
	}

	public String getPicturesqueMediaUploadCallBackUrl() {
		String picturesqueUrl = "";
		try {
			picturesqueUrl = properties.getProperty(PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL",
					exe.getMessage());
		}
		return picturesqueUrl;
	}

	public String getUploadPicturesqueMediaUploadCallBackUrl() {
		String picturesqueUrl = "";
		try {
			picturesqueUrl = properties.getProperty(UPLOAD_PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for PICTURESQUE_MEDIA_UPLOAD_CALLBACK_URL",
					exe.getMessage());
		}
		return picturesqueUrl;
	}

	public Integer getFacebookCompPostLimitDayForJob() {
		Integer limit = 10;
		try {
			limit = Integer.parseInt(properties.getProperty(FACEBOOK_COMP_POST_LIMIT_FOR_JOB));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for FACEBOOK_COMP_POST_LIMIT_FOR_JOB",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getFacebookCompPostLimitDay() {
		Integer limit = 30;
		try {
			limit = Integer.parseInt(properties.getProperty(FACEBOOK_COMP_POST_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for FACEBOOK_COMP_POST_LIMIT",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getInstagramCompPostLimitDayForJob() {
		Integer limit = 7;
		try {
			limit = Integer.parseInt(properties.getProperty(INSTAGRAM_COMP_POST_LIMIT_FOR_JOB));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for INSTAGRAM_COMP_POST_LIMIT_FOR_JOB",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getInstagramCompPostLimitDay() {
		Integer limit = 30;
		try {
			limit = Integer.parseInt(properties.getProperty(INSTAGRAM_COMP_POST_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for INSTAGRAM_COMP_POST_LIMIT",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getTwitterCompPostLimitDayForJob() {
		Integer limit = 10;
		try {
			limit = Integer.parseInt(properties.getProperty(TWITTER_COMP_POST_LIMIT_FOR_JOB));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for TWITTER_COMP_POST_LIMIT_FOR_JOB",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getTwitterCompPostLimitDay() {
		Integer limit = 30;
		try {
			limit = Integer.parseInt(properties.getProperty(TWITTER_COMP_POST_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for TWITTER_COMP_POST_LIMIT",
					exe.getMessage());
		}
		return limit;
	}

	public Integer getFacebookCompPostCountLimit() {
		Integer limit = 20;
		try {
			limit = Integer.parseInt(properties.getProperty(FACEBOOK_COMP_POST_COUNT_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for FACEBOOK_COMP_POST_LIMIT_FOR_JOB",
					exe.getMessage());
		}
		return limit;
	}

	/**
	 * This method returns acknowledged error sub codes list that we can suppress with error code as 190 as part of JIRA
	 * BIRD-63742
	 *
	 * @return
	 */
	public List<Integer> getFbInSightsAcknowledgedErrorSubCodes() {
		List<Integer> ackSubCodes = new ArrayList<>();
		try {
			String commaSeparatedSubCodes = getProperty(ACKNOWLEDGED_COMMA_SEPARATED_FB_INSIGHTS_SUB_CODES);
			if (StringUtils.isNotEmpty(commaSeparatedSubCodes)) {
				ackSubCodes = Stream.of(commaSeparatedSubCodes.split(",")).map(Integer::parseInt).collect(Collectors.toList());
			}
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for ACKNOWLEDGED_COMMA_SEPARATED_FB_INSIGHTS_SUB_CODES",
					exe.getMessage());
		}
		return ackSubCodes;
	}

	public String getCalendarRevampPostFlag() {
		try {
			return getProperty(CALENDAR_REVAMP_BUSINESS_POST);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for CALENDAR_REVAMP_BUSINESS_POST",
					exe.getMessage());
		}
		return null;
	}

	public String getCalendarRevampGetFlag() {
		try {
			return getProperty(CALENDAR_REVAMP_BUSINESS_GET);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for CALENDAR_REVAMP_BUSINESS_GET",
					exe.getMessage());
		}
		return null;
	}

	public String getCalendarEsV2Enabled() {
		try {
			return getProperty(CALENDAR_POSTS_ES_V2_ENABLED);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for CALENDAR_POSTS_ES_V2_ENABLED",
					exe.getMessage());
		}
		return "0";
	}

	public String getGranularScopeFlag() {
		String granularScopeFlag = "";
		try {
			granularScopeFlag = properties.getProperty(GRANULAR_SCOPE_FLAG);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for GRANULAR_SCOPE_FLAG",
					exe.getMessage());
		}
		return granularScopeFlag;
	}

	public Integer getCompetitorAccountMaxSize() {
		int maxSize = 20;
		try {
			maxSize = Integer.parseInt(properties.getProperty(COMPETITOR_ACCOUNT_SIZE_LIMIT));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for X_MAX_RESULTS_SIZE", exe.getMessage());
		}
		return maxSize;
	}

	public String getSamayEventCheckFlag() {
		String samayEventCheckFlag = "";
		try {
			samayEventCheckFlag = properties.getProperty(SAMAY_EVENT_CHECK_FLAG);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for SAMAY_EVENT_CHECK_FLAG",
					exe.getMessage());
		}
		return samayEventCheckFlag;
	}

	public Double getSharedQuotaPercentageLimit() {

		Double limit = 0.8;
		if (StringUtils.isNotBlank(properties.getProperty(SHARED_QUOTA_PERCENTAGE))) {

			try {
				limit = Double.parseDouble(properties.getProperty(SHARED_QUOTA_PERCENTAGE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the shared quota percentage limit", exe);
			}
		}
		return limit;
	}

	public boolean getRCAlertEnableDisable() {

		boolean enable = true;
		if (StringUtils.isNotBlank(properties.getProperty(RC_ALERT_ENABLE))) {

			try {
				enable = Boolean.parseBoolean(properties.getProperty(RC_ALERT_ENABLE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the enable rc alert", exe);
			}
		}
		return enable;
	}

	public Integer getAsyncRateLimitRetryCounter() {

		Integer counter = 3;
		if (StringUtils.isNotBlank(properties.getProperty(ASYNC_RATE_LIMIT_COUNTER))) {

			try {
				counter = Integer.parseInt(properties.getProperty(ASYNC_RATE_LIMIT_COUNTER));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the ASYNC_RATE_LIMIT_COUNTER", exe);
			}
		}
		return counter;
	}

	public String getBusinessIdsForSyncBusinessPosts() {
		try {
			return getProperty(SYNC_BUSINESS_POSTS);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching property: SYNC_BUSINESS_POSTS ",
					exe.getMessage());
		}
		return null;
	}

	public String getCdnImageBaseUrl() {
		try {
			return getProperty(CDN_IMAGE_BASE_URL);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching property: CDN_IMAGE_BASE_URL ",
					exe.getMessage());
		}
		return null;
	}

	public List<String> getAiPostConfigDefaultChannels() {
		List<String> response = new ArrayList<>();
		try {
			String value = properties.getProperty(AI_POST_CONFIG_DEFAULT_CHANNELS);
			response = Arrays.asList(value.split(","));
		} catch (Exception ex) {
			LOGGER.error(
					"Exception {} while fetching string property AI_POST_CONFIG_DEFAULT_CHANNELS",
					ex.getMessage());
			response.addAll(Arrays.asList(SocialChannel.FACEBOOK.getName(), SocialChannel.INSTAGRAM.getName(),
					SocialChannel.LINKEDIN.getName(), SocialChannel.TWITTER.getName(), SocialChannel.GMB.getName(),
					SocialChannel.YOUTUBE.getName(), SocialChannel.APPLE_CONNECT.getName()));
		}
		return response;
	}

	public List<String> getAiPostConfigDefaultCategories() {
		List<String> response = new ArrayList<>();
		try {
			String value = properties.getProperty(AI_POST_DEFAULT_CATEGORIES);
			response = Arrays.asList(value.split(","));
		} catch (Exception ex) {
			LOGGER.error(
					"Exception {} while fetching string property AI_POST_DEFAULT_CATEGORIES",
					ex.getMessage());
			response.addAll(Arrays.asList("Holiday post", "Your top performing post", "Business specific idea"));
		}
		return response;
	}

	public Integer getAiPostGenerationFrequencyType() {

		Integer value = 1;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POST_DEFAULT_GENERATION_TYPE))) {
			try {
				value = Integer.parseInt(properties.getProperty(AI_POST_DEFAULT_GENERATION_TYPE));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the AI_POST_DEFAULT_GENERATION_TYPE", exe);
			}
		}
		return value;
	}


	public Integer getAiPostDefaultFrequency() {

		Integer value = 2;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POST_DEFAULT_FREQUENCY))) {
			try {
				value = Integer.parseInt(properties.getProperty(AI_POST_DEFAULT_FREQUENCY));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the AI_POST_DEFAULT_FREQUENCY", exe);
			}
		}
		return value;
	}

	public Integer getSocialAiUserId() {
		Integer value = -10108;
		if (StringUtils.isNotBlank(properties.getProperty(AI_USER_ID))) {
			try {
				value = Integer.parseInt(properties.getProperty(AI_USER_ID));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the AI_USER_ID", exe);
			}
		}
		return value;
	}

	public String getAiAssistantDefaultDay() {
		String value = "MONDAY";
		try {
			String propValue = getProperty(AI_ASSISTANT_DAY);
			if (StringUtils.isEmpty(propValue)) return value;
			return propValue;
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching property: AI_ASSISTANT_DAY ",
					exe.getMessage());
		}
		return value;
	}

	public boolean getBTPCacheFlag() {
		boolean btpCacheFlag = false;
		try {
			btpCacheFlag = Boolean.parseBoolean(properties.getProperty(BTP_CACHE_FLAG));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for BTP_CACHE_FLAG",
					exe.getMessage());
		}
		return btpCacheFlag;
	}

	public Integer getESBulkDeleteSize() {
		Integer bulkDeleteSize = 500;
		try {
			bulkDeleteSize = Integer.parseInt(properties.getProperty(ES_BULK_DELETE_SIZE));
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for ES_BULK_DELETE_SIZE",
					exe.getMessage());
		}
		return bulkDeleteSize;
	}

	public int getAiPostEngagementCountThreshold() {
		Integer aiPostEngagementCountThreshold = 50;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POSTS_ENGAGEMENT_COUNT_THRESHOLD))) {
			try {
				aiPostEngagementCountThreshold = Integer.parseInt(properties.getProperty(AI_POSTS_ENGAGEMENT_COUNT_THRESHOLD));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_POSTS_ENGAGEMENT_COUNT_THRESHOLD",
						exe.getMessage());
			}
		}
		return aiPostEngagementCountThreshold;
	}

	public int getAIPostGenerationBatchSize() {
		Integer aiPostGenerationBatchSize = 3000;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POSTS_GENERATION_BATCH_SIZE))) {
			try {
				aiPostGenerationBatchSize = Integer.parseInt(properties.getProperty(AI_POSTS_GENERATION_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_POSTS_GENERATION_BATCH_SIZE",
						exe.getMessage());
			}
		}
		return aiPostGenerationBatchSize;
	}

	public int getAIPostMigrationBatchSize() {
		Integer aiPostMigrationBatchSize = 30;
		if (StringUtils.isNotBlank(properties.getProperty(AI_POSTS_MIGRATION_BATCH_SIZE))) {
			try {
				aiPostMigrationBatchSize = Integer.parseInt(properties.getProperty(AI_POSTS_MIGRATION_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_POSTS_MIGRATION_BATCH_SIZE",
						exe.getMessage());
			}
		}
		return aiPostMigrationBatchSize;
	}

	public int getAIImageMigrationBatchSize() {
		Integer aiImageMigrationBatchSize = 10;
		if (StringUtils.isNotBlank(properties.getProperty(AI_IMAGE_MIGRATION_BATCH_SIZE))) {
			try {
				aiImageMigrationBatchSize = Integer.parseInt(properties.getProperty(AI_IMAGE_MIGRATION_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_IMAGE_MIGRATION_BATCH_SIZE",
						exe.getMessage());
			}
		}
		return aiImageMigrationBatchSize;
	}

	public int getOneTimeMigrationMonthsBackForStart() {
		Integer aiOneTimeMigrationMonthsBackForStart = 3;
		if (StringUtils.isNotBlank(properties.getProperty(AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_START))) {
			try {
				aiOneTimeMigrationMonthsBackForStart = Integer.parseInt(properties.getProperty(AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_START));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_START",
						exe.getMessage());
			}
		}
		return aiOneTimeMigrationMonthsBackForStart;
	}

	public int getOneTimeMigrationMonthsBackForEnd() {
		Integer aiOneTimeMigrationMonthsBackForEnd = 1;
		if (StringUtils.isNotBlank(properties.getProperty(AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_END))) {
			try {
				aiOneTimeMigrationMonthsBackForEnd = Integer.parseInt(properties.getProperty(AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_END));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_ONE_TIME_MIGRATION_MONTHS_BACK_FOR_END",
						exe.getMessage());
			}
		}
		return aiOneTimeMigrationMonthsBackForEnd;
	}

	public int getDailyPostDaysBackForStart() {
		int dailyPostDaysBackForStart = 31; // default value
		if (StringUtils.isNotBlank(properties.getProperty(AI_DAILY_POST_DAYS_BACK_FOR_START))) {
			try {
				dailyPostDaysBackForStart = Integer.parseInt(properties.getProperty(AI_DAILY_POST_DAYS_BACK_FOR_START));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching property value for AI_DAILY_POST_DAYS_BACK_FOR_START", exe.getMessage());
			}
		}
		return dailyPostDaysBackForStart;
	}

	public int getDailyPostDaysBackForEnd() {
		int dailyPostDaysBackForEnd = 30; // default value
		if (StringUtils.isNotBlank(properties.getProperty(AI_DAILY_POST_DAYS_BACK_FOR_END))) {
			try {
				dailyPostDaysBackForEnd = Integer.parseInt(properties.getProperty(AI_DAILY_POST_DAYS_BACK_FOR_END));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching property value for AI_DAILY_POST_DAYS_BACK_FOR_END", exe.getMessage());
			}
		}
		return dailyPostDaysBackForEnd;
	}


	public int getAIPostsEmailBatchSize() {
		Integer aiPostEmailBatchSize = 2000;
		if(StringUtils.isNotBlank(properties.getProperty(AI_POSTS_EMAIL_BATCH_SIZE))){
			try {
				aiPostEmailBatchSize = Integer.parseInt(properties.getProperty(AI_POSTS_EMAIL_BATCH_SIZE));
			} catch (Exception exe) {
				LOGGER.error("Exception {} while fetching string property value for AI_POSTS_EMAIL_BATCH_SIZE",
						exe.getMessage());
			}
		}
		return aiPostEmailBatchSize;
	}
	public Integer getHoursSavedPostAI() {

		Integer hoursSaved =15;
		if (StringUtils.isNotBlank(properties.getProperty(HOURS_SAVED_POST_AI))) {

			try {
				hoursSaved = Integer.parseInt(properties.getProperty(HOURS_SAVED_POST_AI));
			} catch (Exception exe) {
				LOGGER.error("Error {} while paring the HOURS_SAVED_POST_AI", exe);
			}
		}
		return hoursSaved;
	}

	public String getScheduleEditLockFlag() {
		String scheduleLockFlag = "";
		try {
			scheduleLockFlag = properties.getProperty(SCHEDULE_EDIT_LOCK_FLAG);
		} catch (Exception exe) {
			LOGGER.error("Exception {} while fetching string property value for SCHEDULE_EDIT_LOCK_FLAG",
					exe.getMessage());
		}
		return scheduleLockFlag;
	}

	public String getTrendsReportStartDate(){
		String startDate = null;
		try {
			startDate = properties.getProperty(TRENDS_REPORT_START_DATE);
		} catch (Exception exe) {
			// Default start date
			startDate = "2025-02-03 00:00:00";
			LOGGER.error("Exception {} while fetching string property value for TRENDS_REPORT_START_DATE",
					exe.getMessage());
		}
		return startDate;
	}

	public List<Integer> getEngageCommentFetchedRestrictions() {
		List<Integer> accountIds = new ArrayList<>();
		try {
			accountIds = Stream.of(properties.getProperty(ENGAGE_COMMENT_FETCH_RESTRICTION_BUSINESS).split(","))
					.map(String::trim).map(Integer::parseInt).collect(Collectors.toList());
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting reseller ids", exe);
		}
		return accountIds;
	}


	public Integer getTiktokVideoLimitForBackfill() {
		int limit = 500;
		if (StringUtils.isNotBlank(properties.getProperty(TIKTOK_VIDEO_FETCH_LIMIT))) {
			try {
				limit = Integer.parseInt(properties.getProperty(TIKTOK_VIDEO_FETCH_LIMIT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing TIKTOK_VIDEO_FETCH_LIMIT", exe);
			}
		}
		return limit;
	}
	public List<Long> getTestAccountsBusinessNumbers() {
		List<Long> testAccountsBusinessNumbers = new ArrayList<>();
		String value = "***************";
		try {
			if (StringUtils.isNotBlank(properties.getProperty(TEST_ACCOUNT_BUSINESS_NUMBERS))) {
				value = properties.getProperty(TEST_ACCOUNT_BUSINESS_NUMBERS);
			}
			for (String interval : value.split(",")) {
				testAccountsBusinessNumbers.add(Long.parseLong(interval));
			}
		} catch (Exception exe) {
			LOGGER.error("Exception while fetching string property value for TEST_ACCOUNT_BUSINESS_NUMBERS", exe);
		}
		return testAccountsBusinessNumbers;
	}
	public Integer getBTPSetWeekCount() {
		int weekCount = 6;
		if (StringUtils.isNotBlank(properties.getProperty(BTP_SET_WEEK_COUNT))) {
			try {
				weekCount = Integer.parseInt(properties.getProperty(BTP_SET_WEEK_COUNT));
			} catch (Exception exe) {
				LOGGER.error("Error {} while parsing BTP_SET_WEEK_COUNT", exe);
			}
		}
		return weekCount;
	}

	public String getTwitterImageMediaUploadUrl(){
		String uploadUrl;
		try {
			uploadUrl = properties.getProperty(TWITTER_IMAGE_MEDIA_UPLOAD_URL);
		} catch (Exception exe) {
			uploadUrl = "https://api.twitter.com/2/media/upload";
			LOGGER.error("Exception {} while fetching string property value for TWITTER_IMAGE_MEDIA_UPLOAD_URL",
					exe.getMessage());
		}
		return uploadUrl;
	}
}