package com.birdeye.social.cache;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.birdeye.social.aspect.Cache;

/**
 * <AUTHOR>
 *
 */
public class CacheManager {
	
	private CacheManager() {
	}
	
	private static final Logger			LOG			= LoggerFactory.getLogger(CacheManager.class);
	
	private static CacheManager			instance	= new CacheManager();
	
	private final Map<String, Object>	caches		= new ConcurrentHashMap<>();
	
	public static CacheManager getInstance() {
		
		return instance;
	}
	
	public Object getCache(final String cacheName) {
		
		return  caches.get(cacheName);
	}
	
	public Object getCacheByName(final String cacheName) {
		
		return getCache(cacheName);
	}
	
	@SuppressWarnings("unchecked")
	public <T> T getCache(final Class<T> cacheClass) {
		
		if (cacheClass.isAnnotationPresent(Cache.class)) {
			return (T) getCache(cacheClass.getAnnotation(Cache.class).name());
		} else {
			throw new IllegalArgumentException("@Cache annotation should be present for cache class:" + cacheClass.getName());
		}
	}
	
	public synchronized void setCache(final Object cache) {
		
		final Class<? extends Object> cacheClass = cache.getClass();
		if (cacheClass.isAnnotationPresent(Cache.class)) {
			for (final Method m : cacheClass.getDeclaredMethods()) {
				if ("freeze".equals(m.getName())) {
					try {
						m.invoke(cache);
					} catch (final Exception e) {
						LOG.error("unable to set cache:" + cacheClass.getName(), e);
					}
				}
			}
			final Cache annotation = cacheClass.getAnnotation(Cache.class);
			caches.put(annotation.name(), cache);
		} else {
			throw new IllegalArgumentException("@Cache annotation should be present for cache class:" + cache.getClass().getName());
		}
		
	}
}
