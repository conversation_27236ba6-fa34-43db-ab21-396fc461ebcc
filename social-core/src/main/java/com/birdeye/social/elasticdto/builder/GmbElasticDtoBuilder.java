package com.birdeye.social.elasticdto.builder;

import com.birdeye.social.elasticdto.GmbElasticDto;

import java.util.function.Consumer;

public class GmbElasticDtoBuilder {

    public String account_id;
    public String account_name;
    public String location_id;
    public String location_name;
    public String place_id;

    public GmbElasticDtoBuilder(){}

    public GmbElasticDtoBuilder(String account_id, String account_name, String location_id, String location_name, String place_id) {
        this.account_id = account_id;
        this.account_name = account_name;
        this.location_id = location_id;
        this.location_name = location_name;
        this.place_id = place_id;
    }

    public GmbElasticDtoBuilder with(Consumer<GmbElasticDtoBuilder> builderFunction) {
        builderFunction.accept(this);
        return this;
    }


    public GmbElasticDto createGmbElasticDto(){
        return new GmbElasticDto(account_id,account_name,location_id,location_name,place_id);
    }
}
