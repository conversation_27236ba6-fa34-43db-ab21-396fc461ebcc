package com.birdeye.social.constant;

public enum SocialAlertTypeEnum {
    DASHBOARD("dashboard"), WEEKLY("weekly"), DISCONNECTED("disconnected");

    String name;

    private SocialAlertTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static SocialAlertTypeEnum getSocialAlertByName(String name) {
        for (SocialAlertTypeEnum status : SocialAlertTypeEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }

}
