package com.birdeye.social.constant;

import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public enum SocialPropertyStatusEnum {
//    Listen.   4,5,6,13,14,22,29,30
//    Publish.  10,13,14,26,29,30 (10,14,26,30)
//    Engage.   17,22,26,29,30
//    Social Hidden:  0,1,2

    ALL(30);

    private final int value;
    public static final List<Integer> SOCIAL_LISTEN_ENABLED = Arrays.asList(4,5,6,13,14,22,29,30);
    public static final List<Integer> SOCIAL_PUBLISH_ENABLED = Arrays.asList(10,13,14,26,29,30);
    public static final List<Integer> SOCIAL_ENAGAGE_ENABLED = Arrays.asList(17,22,26,29,30);
    public static final List<Integer> SOCIAL_DISABLED = Arrays.asList(0,1,2);

    SocialPropertyStatusEnum(final int newValue) {
        value = newValue;
    }

    public int getValue() { return value; }

    public static int reportingStatus(Integer value) {
        // disable reporting only when social is disabled for account
        if(SOCIAL_DISABLED.contains(value)) {
            return 0;
        }
        return 1;
    }

    public static boolean listenStatus(Integer value) {
        return SOCIAL_LISTEN_ENABLED.contains(value);

    }

    public static int publishStatus(Integer value) {
        if(SOCIAL_PUBLISH_ENABLED.contains(value)) {
            return 1;
        }
        return 0;
    }

    public static boolean engageStatus(Integer value) {
        return SOCIAL_ENAGAGE_ENABLED.contains(value);
    }

    public static boolean socialStatus(Integer value) {
        return !SOCIAL_DISABLED.contains(value);
    }


}