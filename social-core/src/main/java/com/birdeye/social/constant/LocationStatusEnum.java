package com.birdeye.social.constant;

public enum LocationStatusEnum {
    MAPPED("mapped"), UNMAPPED("unmapped"), ALL("all");

    String name;

    private LocationStatusEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static LocationStatusEnum getLocationStatusByName(String name) {
        for (LocationStatusEnum status : LocationStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }

}
