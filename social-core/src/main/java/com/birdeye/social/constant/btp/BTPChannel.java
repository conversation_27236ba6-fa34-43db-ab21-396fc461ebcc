package com.birdeye.social.constant.btp;

import lombok.Getter;

@Getter
public enum BTPChannel {
    GLOBAL,
    FACEBOOK,
    INSTAGRAM,
    GOO<PERSON>LE,
    LINKEDIN,
    YOUTUBE,
    TWITTER,
    TIKTOK;

    public static BTPChannel getSocialChannel(String channel){
        for(BTPChannel c : BTPChannel.values()){
            if(c.name().equalsIgnoreCase(channel)){
                return c;
            }
        }
        return null;
    }
}
