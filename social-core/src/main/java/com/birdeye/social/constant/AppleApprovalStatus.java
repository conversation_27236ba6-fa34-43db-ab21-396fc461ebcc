package com.birdeye.social.constant;

public enum AppleApprovalStatus {
    APPROVED("APPROVED"), PENDING("PENDING"),
    REJECTED("REJECTED"), TERMINATED("TERMINATED"),
    LIVE("LIVE"),EXTENSION_REQUESTED("EXTENSION_REQUESTED"),
    EXTENSION_APPROVED("EXTENSION_APPROVED"),EXTENSION_LIVE("EXTENSION_LIVE");

    String name;
    AppleApprovalStatus(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static AppleApprovalStatus getApprovalStatusByName(String name) {
        for (AppleApprovalStatus status : AppleApprovalStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
