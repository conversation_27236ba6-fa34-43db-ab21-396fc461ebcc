package com.birdeye.social.constant.assetlibrary;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 09/10/23
 */
public enum SocialAssetLibraryAssetSortOption {

    NAME("name"),
    DATE_ADDED("date-added"),
    DATE_MODIFIED("date-modified"),
    CREATOR("creator"),
    UPDATER("updater"),
    SIZE("size"),
    TYPE("type");

    @Getter
    private String name;

    SocialAssetLibraryAssetSortOption(String name) {
        this.name = name;
    }

    private static final Map<String, SocialAssetLibraryAssetSortOption> cache = new HashMap<>();

    static {
        for (SocialAssetLibraryAssetSortOption option : SocialAssetLibraryAssetSortOption.values()) {
            cache.put(option.getName(), option);
        }
    }

    public static SocialAssetLibraryAssetSortOption byName(String name) {
        return cache.get(StringUtils.lowerCase(StringUtils.strip(name)));
    }

}
