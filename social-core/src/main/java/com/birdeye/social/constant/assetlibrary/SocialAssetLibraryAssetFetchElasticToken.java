package com.birdeye.social.constant.assetlibrary;

import lombok.Getter;

/**
 * <AUTHOR> on 10/10/23
 */
public enum SocialAssetLibraryAssetFetchElasticToken {

    FROM("from"),
    SIZE("size"),
    ACCOUNT_ID("accountId"),
    PARENT_FOLDER_ID("parentFolderId"),
    START_DATE("startDate"),
    END_DATE("endDate"),
    DATE_RANGE_FIELD("dateRangeField"),
    UNTAGGED("untagged"),
    TAG_IDS("tagIds"),
    ASSET_SELECTIONS_FILTER("assetSelections"),
    SEARCH_TEXT("searchText"),
    SORT_BY("sortBy"),
    SORT_ORDER("sortOrder");

    @Getter
    private String name;

    SocialAssetLibraryAssetFetchElasticToken(String name) {
        this.name = name;
    }


}
