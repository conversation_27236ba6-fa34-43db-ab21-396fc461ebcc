package com.birdeye.social.constant;

public enum ApprovalCountUpdateType {
    UPDATE("UPDATE"), DELETE("DELETE");

    String name;
    ApprovalCountUpdateType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static ApprovalCountUpdateType getCountUpdateTypeByName(String name) {
        for (ApprovalCountUpdateType type : ApprovalCountUpdateType.values()) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }
}
