package com.birdeye.social.constant;

public enum SocialPublicChannelEnum {

    GOOGLEMYBUSINESS(2, "GOOGLEMYBUSINESS"), TWITTER(108, "TWITTER"), LINKEDIN(109, "<PERSON>INKEDI<PERSON>"),
    FACEBOOK(110, "FACEBOOK"), INSTAGRAM(195, "INSTAGRAM");

    Integer id;
    String name;

    private SocialPublicChannelEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static SocialPublicChannelEnum getPostChannelById(Integer id) {
        for (SocialPublicChannelEnum status : SocialPublicChannelEnum.values()) {
            if (status.getId().equals(id)) {
                return status;
            }
        }
        return null;
    }
}
