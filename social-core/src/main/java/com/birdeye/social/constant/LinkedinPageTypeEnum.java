package com.birdeye.social.constant;

public enum LinkedinPageTypeEnum {
    PROFILE(1,"profile"),
    COMPANY(1,"company");

    int id;
    String name;
    private LinkedinPageTypeEnum(int id, String name) {
        this.id=id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static LinkedinPageTypeEnum getLinkedinPageTypeEnum(String name) {
        for (LinkedinPageTypeEnum status : LinkedinPageTypeEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
