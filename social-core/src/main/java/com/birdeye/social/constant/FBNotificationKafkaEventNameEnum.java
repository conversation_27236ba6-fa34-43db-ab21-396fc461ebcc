package com.birdeye.social.constant;

public enum FBNotificationKafkaEventNameEnum {

    SOCIAL_ENGAGE_FEED_ES_TOPIC(1, "social-engage-feed-es-submit"),
    //post/details
    SOCIAL_ENGAGE_FEED_GENERATE_TOPIC(2, "social-engage-feed-generate"),
    SOCIAL_ENGAGE_FEED_SYNC_REQUEST(3, "social-engage-feed-sync-request"),
    SOCIAL_ENGAGE_WEBHOOK_SUBSCRIPTION_TOPIC(4,"social-engage-webhook-subscription"),
    SOCIAL_ENGAGE_FEED_ES_TOPIC_UPDATE(5, "social-engage-feed-es-update-submit"),
    RETRY_UPLOAD_MEDIA_TO_S3(6, "retry-engage-upload-media");

    int id;
    String name;

    FBNotificationKafkaEventNameEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }



}
