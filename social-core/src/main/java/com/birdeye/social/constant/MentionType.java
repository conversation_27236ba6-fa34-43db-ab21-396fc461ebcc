package com.birdeye.social.constant;

public enum MentionType {

    NEWS("news",318),BLOGS("blogs",319),DISCUSSIONS("discussions",320),TAGGED("tagged",321),POST("post",322);

    final String type;
    final int code;

    public String getType() {
        return type;
    }

    public int getCode() {
        return code;
    }

    MentionType(String type, int code) {
        this.type = type;
        this.code = code;
    }

    public static MentionType getMention(String mention){
        for (MentionType mentionType : MentionType.values()){
            if(mentionType.getType().equalsIgnoreCase(mention)){
                return mentionType;
            }
        }
        return null;
    }
}
