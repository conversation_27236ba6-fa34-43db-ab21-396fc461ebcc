/**
 * 
 */
package com.birdeye.social.constant;

/**
 * <AUTHOR>
 *
 */
public enum Status {
	INITIAL("init"),
	COMPLETE("complete"),
	CANCEL("cancel"),
	FETCHED("fetched"),
	NO_PAGES_FOUND("no_pages_found"),
	ACCOUNT_INITIAL("account_init"),
	ACCOUNT_FETCHED("account_fetched"),
	ACCOUNT_REFRESH("account_refresh"),
	FAILED("failed"),
	PROCESSING("processing");
	
	String name;
	
	private Status(String name) {
		this.name = name;
	}
	
	public String getName() {
		return this.name;
	}

	public Status getStatusByName(String name) {
		for(Status status : Status.values()) {
			if(status.getName().equalsIgnoreCase(name)) {
				return status;
			}
		}
		return null;
	}
}
