package com.birdeye.social.constant;

import com.birdeye.social.utils.StringUtils;

public enum FBNotificationEventTypeEnum {

    ADD(1, "add"),
    REMOVE(2, "remove"),
    UPDATE(3, "update"),
    EDITED(4, "edited");


    int id;
    String name;

    FBNotificationEventTypeEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public static FBNotificationEventTypeEnum getEnumByName(String name){
        if(StringUtils.isEmpty(name)){
            return null;
        }
        for(FBNotificationEventTypeEnum eventTypeEnum :  FBNotificationEventTypeEnum.values()){
            if(eventTypeEnum.name().equalsIgnoreCase(name)){
                return eventTypeEnum;
            }
        }
        return null;
    }



    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static BusinessAccountTypeEnum getBusinessAccoutTypeByName(String name) {
        for (BusinessAccountTypeEnum status : BusinessAccountTypeEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }

}
