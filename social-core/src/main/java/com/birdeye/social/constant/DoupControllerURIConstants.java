package com.birdeye.social.constant;

public class DoupControllerURIConstants {
    public static final String DOUP_RESPONSE_CONSTANT = "/doup/response";
    public static final String PDF_TYPE = "/pdf";
    public static final String CHANNEL_PATH_PARAM = "/{channel}";
    public static final String ENGAGEMENT_RATE_URI = CHANNEL_PATH_PARAM + "/eng-rate" + DOUP_RESPONSE_CONSTANT;
    public static final String FOLLOWER_URI = CHANNEL_PATH_PARAM + "/follower" + DOUP_RESPONSE_CONSTANT;
    public static final String POST_METRIC_URI = CHANNEL_PATH_PARAM + "/post-metric" + DOUP_RESPONSE_CONSTANT;
    public static final String LIKE_URI = CHANNEL_PATH_PARAM + "/like" + DOUP_RESPONSE_CONSTANT;
    public static final String ENGAGEMENT_RATE_URI_PDF = PDF_TYPE + CHANNEL_PATH_PARAM + "/eng-rate" + DOUP_RESPONSE_CONSTANT;
    public static final String FOLLOWER_URI_PDF =  PDF_TYPE + CHANNEL_PATH_PARAM + "/follower" + DOUP_RESPONSE_CONSTANT;
    public static final String POST_METRIC_URI_PDF = PDF_TYPE +  CHANNEL_PATH_PARAM + "/post-metric" + DOUP_RESPONSE_CONSTANT;
    public static final String LIKE_URI_PDF =  PDF_TYPE + CHANNEL_PATH_PARAM + "/like" + DOUP_RESPONSE_CONSTANT;

    public static final String PERFORMANCE_REPORT =   "/excel" + DOUP_RESPONSE_CONSTANT;
    public static final String ANALYZE_REPORT =  CHANNEL_PATH_PARAM +  "/analyze/excel" + DOUP_RESPONSE_CONSTANT;
    public static final String EXECUTIVE_SUMMARY = "/excel/executive-summary" + DOUP_RESPONSE_CONSTANT;
    public static final String LOCATION_LEADERSHIP = "/excel/location-leadership" + DOUP_RESPONSE_CONSTANT;

    public static final String SLA_REPORT =   "sla/excel" + DOUP_RESPONSE_CONSTANT;

    public static final String CHANNEL_SPECIFIC_REPORT_URI = CHANNEL_PATH_PARAM + "/{type}" + DOUP_RESPONSE_CONSTANT;

    public static final String CALENDAR_EXPORT_PDF = "/calendar/posts" + PDF_TYPE + DOUP_RESPONSE_CONSTANT;
}