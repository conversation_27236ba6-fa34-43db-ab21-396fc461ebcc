package com.birdeye.social.constant;

public enum GnipRuleStatusEnum {
	
	ACTIVE(1, "active"), IN_ACTIVE(2, "inactive");
	Integer id;
	String status;

	private GnipRuleStatusEnum(Integer id, String status) {
		this.id = id;
		this.status = status;
	}

	public Integer getId() {
		return id;
	}

	public String getStatus() {
		return status;
	}

	public static GnipRuleStatusEnum getGnipRuleStatusEnumById(Integer id) {
		for (GnipRuleStatusEnum status : GnipRuleStatusEnum.values()) {
			if (status.getId().equals(id)) {
				return status;
			}
		}
		return null;
	}

}
