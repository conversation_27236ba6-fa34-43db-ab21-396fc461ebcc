package com.birdeye.social.constant;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;


public enum SocialPostEntityType {

    POST(1),
    ENGAGE(2),
    ASSET(3),
    DRAFT(4),
    POST_LIB(5),
    AI_POST(6);

    @Getter
    private Integer id;

    SocialPostEntityType(Integer id) {
        this.id = id;
    }

    private static final Map<Integer, SocialPostEntityType> ENTITY_TYPE_ID_TO_ENTITY_TYPE_MAP = new HashMap<>();

    static {
        for (SocialPostEntityType entityType : SocialPostEntityType.values()) {
            ENTITY_TYPE_ID_TO_ENTITY_TYPE_MAP.put(entityType.getId(), entityType);
        }
    }

    public static SocialPostEntityType byId(Integer id) {
        return ENTITY_TYPE_ID_TO_ENTITY_TYPE_MAP.get(id);
    }
}
