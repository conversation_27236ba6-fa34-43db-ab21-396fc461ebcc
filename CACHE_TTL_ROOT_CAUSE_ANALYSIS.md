# Cache TTL Investigation: Root Cause Found

## **ROOT CAUSE IDENTIFIED: Cache Key Inconsistency**

### **The Real Issue**
The problem is NOT with the TTL configuration, but with **inconsistent cache keys** in the same cache. The `CACHE_VALUE="business.core"` is used by 3 different methods with DIFFERENT key patterns:

1. **Method 1** (`@Cacheable`): Key = `"businessId + locationRequired"` (e.g., "123true")
2. **Method 2** (`@CacheEvict`): Key = `"businessId + locationRequired"` (e.g., "123true") ✅ Matches #1
3. **Method 3** (`@CachePut`): Key = `"businessId"` only (e.g., "123") ❌ **DIFFERENT!**

### **Code Evidence:**
```java
// Methods 1 & 2: Consistent keys
@Cacheable(value = CACHE_VALUE, key = "#businessId.toString().concat(#locationRequired.toString())")
@CacheEvict(value = CACHE_VALUE, key = "#businessId.toString().concat(#locationRequired.toString())")

// Method 3: INCONSISTENT key pattern
@CachePut(value = CACHE_VALUE, key = "#businessId.toString()") // ❌ PROBLEM!
```

### **Why This Causes TTL -1**
1. **Cache Manager Confusion**: Spring's RedisCacheManager gets confused when the same cache name has inconsistent key patterns
2. **TTL Application Failure**: The cache manager fails to apply TTL correctly to entries with different key structures
3. **Redis Behavior**: Redis may not set expiration when cache operations are inconsistent within the same cache namespace

## **Solution Applied**

### **Fixed the Cache Key Inconsistency**
Changed the `@CachePut` method to use the same key pattern:

**Before:**
```java
@CachePut(value = CACHE_VALUE, key = "#businessId.toString()", unless = "#result == null")
```

**After:**
```java
@CachePut(value = CACHE_VALUE, key = "#businessId.toString().concat('true')", unless = "#result == null")
```

### **Why This Fix Works**
1. **Consistent Key Pattern**: All methods now use the same key structure
2. **Logical Consistency**: The `getBusinessLiteWithUpdated` method sets `locationRequired=true`, so the cache key should reflect this
3. **Cache Manager Harmony**: RedisCacheManager can now properly apply TTL to all entries in the cache

## **Verification Steps**

### **1. Deploy the Fix**
Deploy the updated `BusinessCoreServiceImpl.java` with the corrected cache key.

### **2. Test Cache Behavior**
```bash
# Check if TTL is now properly set
curl "http://localhost:8080/api/cache-diagnostic/business-core"
```

### **3. Monitor Redis**
```bash
redis-cli
> KEYS *business.core*
> TTL <key-name>  # Should now show 86400 instead of -1
```

## **Additional Investigation Tools**
The diagnostic tools created earlier are still valuable for ongoing monitoring:
- `CacheTTLDiagnosticUtil.java` - For Redis inspection
- `CacheDiagnosticController.java` - For runtime diagnostics
- Enhanced logging in cache configuration

## **Key Learnings**

### **Cache Design Best Practices**
1. **Consistent Key Patterns**: All cache operations on the same cache should use consistent key patterns
2. **Cache Validation**: Test cache operations together, not in isolation
3. **TTL Monitoring**: Implement monitoring to detect TTL issues early

### **Spring Cache Gotchas**
1. **Key Consistency**: Different key patterns in the same cache can cause TTL issues
2. **Cache Manager Sensitivity**: RedisCacheManager is sensitive to key pattern consistency
3. **Silent Failures**: Cache TTL issues often fail silently without obvious errors

## **Expected Outcome**
After deploying this fix:
- ✅ Cache TTL should be properly set to 86400 seconds
- ✅ All cache operations will work harmoniously
- ✅ No more -1 TTL values in Redis
- ✅ Consistent cache behavior across all methods

## **Files Modified**
1. `social-external/src/main/java/com/birdeye/social/businessCore/BusinessCoreServiceImpl.java` - Fixed cache key inconsistency
2. `social-web/src/main/java/com/birdeye/social/config/RedisCacheConfig.java` - Added debug logging
3. `social-notification/src/main/java/com/birdeye/notification/config/RedisCacheConfig.java` - Added debug logging
4. `social-web/src/main/java/com/birdeye/social/util/CacheTTLDiagnosticUtil.java` - Diagnostic utility
5. `social-web/src/main/java/com/birdeye/social/controller/CacheDiagnosticController.java` - Diagnostic endpoints
