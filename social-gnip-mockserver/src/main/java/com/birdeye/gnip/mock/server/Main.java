package com.birdeye.gnip.mock.server;

import java.io.InputStream;

import com.birdeye.gnip.mock.server.netty.MockServer;

/**
 * Main entry point to start the {@link GnipServer}
 */
public final class Main {
    private static final int DEFAUL_SERVER_PORT = 9898;

    private static final InputStream ACTIVITIES = Main.class.getClassLoader().getResourceAsStream(
            "com/birdeye/gnip/server/activity/unlimitedActivity.json");

    public static void main(String[] args)  {
               final MockServer mockServer = new MockServer(DEFAUL_SERVER_PORT);
               mockServer.startLimited(ACTIVITIES, 1);
               //mockServer.startSlowly(ACTIVITIES, 60000);
               //mockServer.start(ACTIVITIES);
    }

}
