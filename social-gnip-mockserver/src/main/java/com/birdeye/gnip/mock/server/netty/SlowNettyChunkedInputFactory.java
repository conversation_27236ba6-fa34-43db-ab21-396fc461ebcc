package com.birdeye.gnip.mock.server.netty;

import java.io.InputStream;

import com.birdeye.gnip.mock.server.GnipServerFactory;

/**
 * A {@link GnipServerFactory} that creates {@link NettyGnipServer} instances
 *
 * * <AUTHOR>
 */
public final class SlowNettyChunkedInputFactory extends NettyChunkedInputFactory{

    private final int timeBetweenChunks;
    
    /**
     * 
     * Creates the NettyHandlerAggregator.
     * @param limitedActivities
     */
    public SlowNettyChunkedInputFactory(final InputStream limitedActivities,
            final int timeBetweenChunks) {
        super(limitedActivities);
        this.timeBetweenChunks = timeBetweenChunks;
    }
    
    @Override
    public GnipChunkedInput getChunkedInput() {
        return new GnipChunkedInput(new NextChunkSlow(getActivities(), timeBetweenChunks));
    }

}
