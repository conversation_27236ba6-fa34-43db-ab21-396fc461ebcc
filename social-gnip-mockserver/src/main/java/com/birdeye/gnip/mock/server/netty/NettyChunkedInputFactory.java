package com.birdeye.gnip.mock.server.netty;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;

import com.birdeye.gnip.mock.server.GnipChunkedInputFactory;

/**
 * A {@link GnipChunkedInputFactory} that knows how to create an {@link NettyChunkedInputFactory} 
 *
* <AUTHOR>
 */
public class NettyChunkedInputFactory implements GnipChunkedInputFactory{

    private final Collection<String> activities;

    /**
     * 
     * Creates the NettyHandlerAggregator.
     *
     * @param activities An input stream of activities. The stream should contain a JSON activity per line.
     * This stream will be read and kept in memory in order to serve the activities to the clients. Once
     * all the activities have been served, the server will start serving the first activity again.
     */
    public NettyChunkedInputFactory(final InputStream activities) {
        
        if (activities == null) {
            throw new IllegalArgumentException("The activities stream cannot be null");
        }
        
        this.activities = parseActivities(activities);
    }
    
    @Override
    public GnipChunkedInput getChunkedInput() {
        return new GnipChunkedInput(new NextChunkUnlimited(activities));
    }
    
    public Collection<String> getActivities() {
        return activities;
    }

    protected static final Collection<String> parseActivities(final InputStream activities) {
        try {
            final BufferedReader reader = new BufferedReader(new InputStreamReader(activities, "UTF-8"));
            final Collection<String> result = new ArrayList<String>();
            String line;
            while ((line = reader.readLine()) != null) {
                result.add(line.trim() + "\r\n");
            }
            return result;
        } catch (final IOException e) {
            throw new RuntimeException(e);
        }
    }


}
