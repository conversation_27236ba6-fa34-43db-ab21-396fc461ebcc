package com.birdeye.gnip.mock.server.netty;

import java.io.InputStream;

import com.birdeye.gnip.mock.server.GnipServer;
import com.birdeye.gnip.mock.server.GnipServerFactory;

/**
 * 
 * Implementation of Mock Server  
 * 
* <AUTHOR>
 */
public class MockServer {

    private static GnipServer gnipServer;


    private NettyChunkedInputFactory chunkedInputFactory;

    private int portNumber;
   
    public MockServer(int portNumber){
        this.portNumber = portNumber;
    }    
    
    void start() {
            final GnipServerFactory gnipServerFactory = new NettyGnipServerFactory();
            gnipServer = gnipServerFactory.createServer(portNumber, chunkedInputFactory);

            gnipServer.start();
            System.out.println("Gnip mock server started at port " + portNumber);
    }
    
    public void start(final InputStream activities) {
        chunkedInputFactory = new NettyChunkedInputFactory(activities);
        start();
    }
    
    public void startSlowly(final InputStream activities, final int timeBetweenChunks) {
        chunkedInputFactory = new SlowNettyChunkedInputFactory(activities, timeBetweenChunks);
        start();
    }
    
    public void startLimited(final InputStream activities, final int numberOfChunks) {
        chunkedInputFactory = new LimitedNettyChunkedInputFactory(activities, numberOfChunks);
        start();
    }
    
    public static void shutdown() {
        gnipServer.shutdown();
    }
    
}
