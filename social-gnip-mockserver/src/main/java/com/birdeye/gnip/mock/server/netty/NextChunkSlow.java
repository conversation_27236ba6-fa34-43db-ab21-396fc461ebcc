package com.birdeye.gnip.mock.server.netty;

import static org.jboss.netty.buffer.ChannelBuffers.wrappedBuffer;

import java.util.Collection;

import org.jboss.netty.handler.codec.http.DefaultHttpChunk;

public class NextChunkSlow extends NextChunkStrategy {

    private final int timeBetweenChunks;

    public NextChunkSlow(final Collection<String> activities, final int timeBetweenChunks) {
        super(activities);
        this.timeBetweenChunks = timeBetweenChunks;
    }

    @Override
    public Object nextChunk() throws Exception {
        if (getIterator().hasNext()) {
            setIterator(getActivities().iterator());  
        }
        
        Thread.sleep(timeBetweenChunks);
        
        final String activity = getIterator().next();
        return new DefaultHttpChunk(wrappedBuffer(activity.getBytes("UTF-8")));
    }

}
