/**
 *
 */
package com.birdeye.social.executor.services;

/**
 * <AUTHOR>
 *
 */
public interface ExecutorCommonService {

	/**
	 * Execution completion service with default timeout
	 *
	 * @param executorService
	 * @throws Exception
	 */
	public void executeTasks(SocialExecutorService<Boolean> executorService) throws Exception;

	/**
	 * Execution completion service with custom timeout
	 *
	 * @param executorService
	 * @param timeout
	 */
	void executeTasks(SocialExecutorService<Boolean> executorService, Integer timeout) throws Exception;

}
