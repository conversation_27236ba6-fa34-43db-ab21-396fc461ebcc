package com.birdeye.social.utils;

import java.util.*;
import java.util.regex.Pattern;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.dto.*;
import com.birdeye.social.sro.LocationPageMapping;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.entities.BusinessKeywordMapping;
import com.birdeye.social.platform.dao.BusinessAggregationRepository;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.platform.dao.ReviewerRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessAggregation;
import com.birdeye.social.platform.entities.BusinessFacebookPageNew;
import com.birdeye.social.platform.entities.Reviewer;
import com.birdeye.social.platform.entities.User;

/**
 *
 * <AUTHOR>
 */
@Service
public class BusinessUtilsServiceImpl implements BusinessUtilsService {

    @Autowired
    private BusinessRepository businessRepository;

	@Autowired
	private IBusinessCoreService businessCoreService;

    @Autowired
    private BusinessUserRepository businessUserRepo;
    
    @Autowired
    private BusinessAggregationRepository businessAggRepo;
    
    @Autowired
    private ReviewerRepository reviewerRepo;
    
	private static Logger LOGGER = LoggerFactory.getLogger(BusinessUtilsServiceImpl.class);

    @Override
    public List<Integer> getBusinessLocations(Business business, Integer userId) {
        return getBusinessLocations(business, userId, null);
    }
    
    @Override
    public List<Integer> getBusinessLocations(Business business, Integer userId, List<Integer> locationIds) {
        if (("Business").equalsIgnoreCase(business.getType()) || ("Product").equalsIgnoreCase(business.getType())) {
            if (CollectionUtils.isNotEmpty(locationIds) && !locationIds.contains(business.getId())) {
                return null;
            }
            return Collections.singletonList(business.getId());
        } else if ("Enterprise-Location".equalsIgnoreCase(business.getType()) || "Enterprise-Product".equalsIgnoreCase(business.getType())) {
            List<Integer> businessIds;
			if (CollectionUtils.isNotEmpty(locationIds)) {
				LOGGER.info("Location IDs for business id {} are {}", business.getId(), locationIds);
				businessIds = businessRepository.findFilteredEnterpriseLocations(business.getId(), locationIds);
				if(CollectionUtils.isNotEmpty(businessIds))
				{
					if(userId != null) {
						LOGGER.info("Business IDs for user id {} are {}", userId, businessIds);
						businessIds = businessUserRepo.findBusiness(userId, businessIds);
					}
				}
			} else {
                businessIds = businessRepository.findEnterpriseLocations(business.getId());
                if(CollectionUtils.isNotEmpty(businessIds))
				{
                		if(userId != null) {
                			businessIds = businessUserRepo.findBusiness(userId, businessIds);
                		}
				}
            }
            return businessIds;
        }
        return null;
    }
    
    @Override
    public Business getEnterpriseBusiness(Business business) {
        int i = 0;
        while (business != null && i < Constants.MAX_RESELLER_HIERARCHY_DEPTH) {
            if (business.getEnterprise() == null) 
            {
                return business;
            }
            if (business.getEnterprise() != null) 
            {
                business = business.getEnterprise();
            } 
            i++;
        }
        return business;
    }
    
    /**
     * Get all business ids
     * 
     * @param business
     * @param user
     * @return businessIds:
     * 1. business_id if smb, just validates user
     * 2. all locations under enterprise if business is enterprise
     * 3. all locations under enterprise of a location 
     */
    @Override
    public List<Integer> getBusinessLocationsForEnterprise(Business business, User user) {
    	List<Integer> businessIds = new ArrayList<>();
    	if ("Business".equals(business.getType()) || "Product".equals(business.getType())) {
            if (business.getEnterpriseId() == null) {
                businessIds = new ArrayList<>();
                businessIds.add(business.getId());
            } else {
                businessIds = businessRepository.findEnterpriseLocations(business.getEnterpriseId());
                if(user != null && CollectionUtils.isNotEmpty(businessIds)) {
                		businessIds = businessUserRepo.findBusiness(user.getId(), businessIds);
                }
            }
        } else if ("Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType())) {
        		businessIds = businessRepository.findEnterpriseLocations(business.getId());
        		if(user != null && CollectionUtils.isNotEmpty(businessIds)) {
        			businessIds = businessUserRepo.findBusiness(user.getId(), businessIds);
        		}
        }
    	return businessIds;
    }

    @Override
	public List<Integer> getBusinessLocationsForEnterpriseUsingLite(BusinessLiteDTO business, User user) {
		List<Integer> businessIds = new ArrayList<>();
		List<LocationDetailsDTO> locationDetailsDTOS = businessCoreService.getBusinessLocations(business.getBusinessId());
		if(locationDetailsDTOS != null && locationDetailsDTOS.size()>0) {
			for (LocationDetailsDTO p : locationDetailsDTOS) {
				businessIds.add(p.getBusinessId());
			}
		}
		return businessIds;
	}
    
    @Override
    @Async
    public void updateBusinessAggregation(BusinessFacebookPageNew businessFacebookPage, Integer reviewScanned) {
		List<BusinessAggregation> businessAggregations = businessAggRepo.findByBusinessIdAndSrcId(businessFacebookPage.getBusinessId(), 110);
		String baUrl = null;
		if (CollectionUtils.isNotEmpty(businessAggregations)) {
			for (BusinessAggregation ba : businessAggregations) {
				if (StringUtils.isNotEmpty(ba.getProfileId()) && ba.getProfileId()
						.equalsIgnoreCase(businessFacebookPage.getFacebookPageId())) {
					ba.setReviewScanned(reviewScanned);
					businessAggRepo.saveAndFlush(ba);
					LOGGER.info(
							"[Social Setup] Saved business aggregation :{}, facebook page with id: {}, for business: {}",
							ba, businessFacebookPage.getFacebookPageId(), businessFacebookPage.getBusinessId());
				} else if (StringUtils.isNotEmpty(ba.getSourceUrl())) {
					if (ba.getSourceUrl().contains("?")) {
						baUrl = ba.getSourceUrl().substring(0, ba.getSourceUrl().indexOf("?"));
					} else {
						baUrl = ba.getSourceUrl();
					}
					if (org.apache.commons.lang3.StringUtils.contains(businessFacebookPage.getLink(), baUrl)) {
						ba.setReviewScanned(reviewScanned);
						businessAggRepo.saveAndFlush(ba);
						LOGGER.info(
								"[Social Setup] Saved business aggregation :{}, facebook page with id: {}, for business: {}",
								ba,businessFacebookPage.getFacebookPageId(), businessFacebookPage.getBusinessId());
					}
				}
			}
		}
	
    }
    
    @Override
    public List<BusinessAggregation> getAggregationForBusinessAndSource(List<Integer> businessIds, Integer sourceId){
    		List<BusinessAggregation> aggregation = businessAggRepo.findByBusinessIdInAndSrcId(businessIds, sourceId);
    		return aggregation;
    }
    
    @Override
	public Map<Integer, List<String>> getExclusionKeywordsDetails(List<BusinessKeywordMapping> rules) {
		Map<Integer, List<String>> result = new HashMap<>();
		for (BusinessKeywordMapping rule : rules) {
			if (rule.getIsExcluded() == 1) {
				String exclusionKeyword = rule.getKeyword();
				Integer businessId = rule.getBusinessId();
				// Checking whether the keyword is already existing in the result data structure
				List<String> exclusionKeywords = result.get(businessId);
				if (exclusionKeywords == null) {
					exclusionKeywords = new ArrayList<>();
					result.put(businessId, exclusionKeywords);
				}
				exclusionKeywords.add(exclusionKeyword);
			}
		}
		return result;
	}
	
	@Override
	public Map<Integer, List<BusinessKeywordMapping>> getGnipRulesByBusinessId(List<BusinessKeywordMapping> rules) {
		Map<Integer, List<BusinessKeywordMapping>> result = new HashMap<>();
		for (BusinessKeywordMapping rule : rules) {
			if (rule.getIsExcluded() == 0) {
				Integer businessId = rule.getBusinessId();
				// Checking whether the keyword is already existing in the result data structure
				List<BusinessKeywordMapping> businessKeywordMappings = result.get(businessId);
				if (businessKeywordMappings == null) {
					businessKeywordMappings = new ArrayList<>();
					result.put(businessId, businessKeywordMappings);
				}
				businessKeywordMappings.add(rule);
			}
		}
		return result;
	}
	
	@Override
	public Map<String, List<BusinessKeywordMapping>> getInclusionKeywordsDetails(List<BusinessKeywordMapping> rules) {
        Map<String, List<BusinessKeywordMapping>> result = new HashMap<>();
        for (BusinessKeywordMapping rule : rules) {
            if (rule.getIsExcluded() == 0) {
                String inclusionKeyword = rule.getKeyword();
                //Creating keyword entry
                List<BusinessKeywordMapping> keywordLinkedRules = result.get(inclusionKeyword.toLowerCase());
                //Checking whether the keyword is already existing in the result data structure
                if (keywordLinkedRules == null) {
                    keywordLinkedRules = new ArrayList<>();
                    result.put(inclusionKeyword.toLowerCase(), keywordLinkedRules);
                }
                keywordLinkedRules.add(rule);
            }
        }
        return result;
    }
	
	@Override
	public boolean isRuleFulfilled(String inclusionKeyword, List<String> exclusionKeywords, String content, String reviewerId) {
		boolean isRuleFulfilled = isKeywordPresent(content, inclusionKeyword);
		if (isRuleFulfilled && exclusionKeywords != null) {
			for (String exclusionKeyword : exclusionKeywords) {
				// Checking whether the exclusion keyword is of reviewer id filter type :- from:<reviewer-id>
				if (isExclusionKeyRevFilterType(exclusionKeyword)) {
					String[] splitted = exclusionKeyword.split(":");
					// Handling the case where reviewer id is not provided
					if (splitted.length > 1) {
						String revIdFromKeyword = splitted[1].trim();
						if (revIdFromKeyword.equals(reviewerId)) {
							//logger.log(Level.INFO, "Filtering out the review as the reviewer id is matching with the restricted reviewer rule.");
							isRuleFulfilled = false;
							break;
						}
					}
				} else {
					if (isKeywordPresent(content, exclusionKeyword)) {
						isRuleFulfilled = false;
						break;
					}
				}
			}
		}
		return isRuleFulfilled;
	}
	
	private boolean isExclusionKeyRevFilterType(String exclusionKeyword) {
		return exclusionKeyword.contains("from:");
	}
	
	private boolean isKeywordPresent(String content, String keyword) {
		boolean keywordPresent = false;
		if (keyword != null && !("").equalsIgnoreCase(keyword) && content != null && !("").equalsIgnoreCase(content) 
				&& content.length() <= MAX_REVIEW_COMMENT_LENGTH) {
			String regex = "(.*)(?i)" + keyword.replaceAll("\\s+", "\\\\s+.*").replaceAll("\\++", "") + "(.*)";
			if (keyword.matches("\".*\"")) {
				keyword = keyword.substring(1, keyword.length() - 1);
				keywordPresent = content.toLowerCase().contains(keyword.toLowerCase());
			} else  {
				Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
				keywordPresent = pattern.matcher(content).find();
			}
		}
		return keywordPresent;
	}
	
	@Override
	public Reviewer getReviewerByProfileUrl(String profileUrl) {
        if(StringUtils.isBlank(profileUrl)) {
            return null;
        }
        return reviewerRepo.findByProfileUrl(profileUrl);
    }
	
	@Override
    public Boolean isUpdateScopeEnabled(Long businessId) {
    		Business business = businessRepository.findByBusinessId(businessId);
    		String businessToBeChecked = null;
    		if("Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType())) {
    			businessToBeChecked = String.valueOf(businessId);
    		}else if("Business".equals(business.getType()) || "Product".equals(business.getType())) {
    			if(business.getEnterprise() != null)
    				businessToBeChecked = String.valueOf(business.getEnterprise().getBusinessId());
    			else
    				businessToBeChecked = String.valueOf(businessId);
    		}
    		String fetchPermission = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFetchPermissionFlag();
    		if(fetchPermission.equalsIgnoreCase("true"))
    			return Boolean.TRUE;
    		else {
    			String[] allowedEnterprises = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAllowedEnterprises();
    			if(allowedEnterprises == null) {
    				return Boolean.FALSE;
    			}else {
    				for(String ent : allowedEnterprises) {
    					if(ent.equalsIgnoreCase(businessToBeChecked))
    						return Boolean.TRUE;
    				}
    			}
    		}
    		return Boolean.FALSE;
}

	@Override
	public Map<Integer, BusinessLocationLiteEntity> getBusinessLocationsLiteMap(List<Integer> businessIds) {
    	if ( CollectionUtils.isNotEmpty(businessIds) ) {
			List<BusinessLocationLiteEntity> businessLocations = businessRepository.getBusinessLocationsByBusinessIdIn(businessIds);
			if ( CollectionUtils.isNotEmpty(businessLocations) ) {
				Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap = new HashMap<>();
				businessLocations.forEach(b -> businessIdLocationMap.put(b.getId(), b));
				return businessIdLocationMap;
			}
		}
    	return null;
	}

	@Override
	public Map<Integer, BusinessLocationLiteEntity> getBusinessLocationsLiteMapPaginated(List<Integer> businessIds,Integer page,Integer size) {
		Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap = new LinkedHashMap<>();
		if ( CollectionUtils.isNotEmpty(businessIds) ) {
			List<BusinessLocationLiteEntity> businessLocations = businessRepository.getBusinessLocationsByBusinessIdIn(businessIds);
			if ( CollectionUtils.isNotEmpty(businessLocations) ) {
				businessLocations.forEach(b -> businessIdLocationMap.put(b.getId(), b));
			}
		}
		return businessIdLocationMap;
	}


	@Override
	public Map<Integer, BusinessLocationLiteEntity> getBusinessLocationsLiteMapPaginatedSearch(List<Integer> businessIds, Integer page, Integer size, String search, LocationPageMapping response) {
		LinkedHashMap<Integer, BusinessLocationLiteEntity> businessIdLocationMap = new LinkedHashMap<>();
		if ( CollectionUtils.isNotEmpty(businessIds) ) {
			List<BusinessLocationLiteEntity> businessLocations = businessRepository.getBusinessLocationsByBusinessIdInPaginatedSearch(businessIds,search);
//			response.setTotalCount(Math.toIntExact(businessLocations.getTotalElements()));
			if (!CollectionUtils.isEmpty(businessLocations)) {
				businessLocations.forEach(b -> businessIdLocationMap.put(b.getId(), b));
			}
		}
		return businessIdLocationMap;
	}
}
