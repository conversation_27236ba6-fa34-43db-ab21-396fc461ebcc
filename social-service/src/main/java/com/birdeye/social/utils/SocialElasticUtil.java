package com.birdeye.social.utils;

import com.birdeye.social.constant.LinkedinPageTypeEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.elasticdto.builder.GmbElasticDtoBuilder;
import com.birdeye.social.elasticdto.builder.LinkedinElasticDtoBuilder;
import com.birdeye.social.elasticdto.builder.SocialElasticDtoBuilder;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

public class SocialElasticUtil {

    private static final Logger logger = LoggerFactory.getLogger(SocialElasticUtil.class);

    public static List<SocialElasticDto> getSocialEsDtoFromObjects(List<? extends Object> objects,String channel){
        List<SocialElasticDto> socialElasticDtos = new ArrayList<>();
        AtomicReference<SocialElasticDto> socialElasticDto = new AtomicReference<>(new SocialElasticDtoBuilder().build());

        if(CollectionUtils.isEmpty(objects)){
            return socialElasticDtos;
        }

        if(SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)){
            objects.stream().forEach(o -> {
                BusinessTwitterAccounts accounts = (BusinessTwitterAccounts)o;
                socialElasticDtos.add(accounts.getElasticDto());
            });
        }else if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)){
            objects.stream().forEach(o -> {
                BusinessFBPage accounts = (BusinessFBPage)o;
                socialElasticDto.set(new SocialElasticDtoBuilder()
                        .with($ -> {
                            $.channel = SocialChannel.FACEBOOK.getName();
                            $.integration_id=accounts.getFacebookPageId();
                            $.is_valid=accounts.getIsValid();
                            $.enterprise_id = accounts.getEnterpriseId()!=null ?accounts.getEnterpriseId().toString():null;
                            $.reseller_id = accounts.getResellerId()!=null ?accounts.getResellerId().toString():null;
                            $.page_name=accounts.getFacebookPageName();
                            $.page_url= accounts.getLink();
                            $.raw_id= accounts.getId();
                            $.handle= accounts.getHandle();
                            $.is_selected= accounts.getIsSelected();
                            $.business_id= accounts.getBusinessId()!=null?accounts.getBusinessId().toString():null;
                            $.fullName= accounts.getFirstName()+" "+accounts.getLastName();
                            $.firstName=accounts.getFirstName();
                            $.lastName=accounts.getLastName();
                            $.single_line_address=accounts.getSingleLineAddress();
                        }).build());
                socialElasticDtos.add(socialElasticDto.get());
            });
        }else if(SocialChannel.GMB.getName().equalsIgnoreCase(channel)){
            objects.stream().forEach(o -> {
                BusinessGoogleMyBusinessLocation accounts = (BusinessGoogleMyBusinessLocation)o;
                socialElasticDto.set(new SocialElasticDtoBuilder()
                        .with($ -> {
                            $.channel = SocialChannel.GMB.getName();
                            $.integration_id=accounts.getLocationId();
                            $.is_valid=accounts.getIsValid();
                            $.enterprise_id=accounts.getEnterpriseId()!=null?accounts.getEnterpriseId().toString():null;
                            $.reseller_id = accounts.getResellerId()!=null?accounts.getResellerId().toString():null;
                            $.page_name=accounts.getLocationName();
                            $.page_url= accounts.getLocationUrl();
                            $.raw_id= accounts.getId();
                            $.is_selected= accounts.getIsSelected();
                            $.business_id= accounts.getBusinessId()!=null?accounts.getBusinessId().toString():null;
                            $.fullName= accounts.getAccountName();
                            $.gmbElasticDto = new GmbElasticDtoBuilder().with($_gmb->{
                                $_gmb.account_id=accounts.getAccountId();
                                $_gmb.place_id= accounts.getPlaceId();
                                $_gmb.location_id= accounts.getLocationId();
                                $_gmb.account_name= accounts.getAccountName();
                                $_gmb.location_name= accounts.getLocationName();
                            }).createGmbElasticDto();
                        }).build());
                socialElasticDtos.add(socialElasticDto.get());
            });
        }else if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)){
            objects.stream().forEach(o -> {
                BusinessLinkedinPage accounts = (BusinessLinkedinPage)o;
                socialElasticDto.set(new SocialElasticDtoBuilder()
                        .with($ -> {
                            $.channel = SocialChannel.LINKEDIN.getName();
                            $.integration_id=accounts.getProfileId();
                            $.is_valid=accounts.getIsValid();
                            $.enterprise_id=accounts.getEnterpriseId()!=null?accounts.getEnterpriseId().toString():null;
                            $.reseller_id = accounts.getResellerId()!=null?accounts.getResellerId().toString():null;
                            $.page_url= accounts.getPageUrl();
                            $.raw_id= accounts.getId();
                            $.business_id= accounts.getBusinessId()!=null?accounts.getBusinessId().toString():null;
                            $.is_selected= accounts.getIsSelected();
                            if(accounts.getPageType().equalsIgnoreCase(LinkedinPageTypeEnum.COMPANY.name())){
                                $.page_name=accounts.getCompanyName();
                            }else{
                                $.page_name=accounts.getFirstName()+" "+accounts.getLastName();
                                $.firstName=accounts.getFirstName();
                                $.lastName=accounts.getLastName();
                            }
                            $.linkedinElasticDto = new LinkedinElasticDtoBuilder().with($_linkedin->{
                                $_linkedin.urn=accounts.getUrn();
                                $_linkedin.page_type=accounts.getPageType();
                                $_linkedin.expires_on=accounts.getExpiresOn();
                            }).createLinkedinElasticDto();
                        }).build());
                socialElasticDtos.add(socialElasticDto.get());
            });
        }
        return socialElasticDtos;
    }
}

