package com.birdeye.social.utils;

import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialStreams;
import com.birdeye.social.linkedin.*;
import com.birdeye.social.linkedin.LinkedInPost.LinkedInContent;
import com.birdeye.social.linkedin.response.LinkedInAPIResponse;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.MentionData;
import com.birdeye.social.model.SocialTimeline;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class LinkedinUtils {

    private static final String LINKEDIN_FEED_URL = "https://www.linkedin.com/feed/update/";
    private static final String LINKEDIN_MENTION_MEMBER = "com.linkedin.common.MemberAttributedEntity";
    private static final String LINKEDIN_MENTION_BUSINESS = "com.linkedin.common.CompanyAttributedEntity";

    private static final String dateFormat = "yyyy/MM/dd HH:mm:ss";


    private static final Logger LOG = LoggerFactory.getLogger(LinkedinUtils.class);

    public static Feed getLinkedinComment(LinkedInCommentResponse commentDto, String companyUrn) {
        if(commentDto == null) {
            return null;
        }
        Feed comment = new Feed();
        comment.setPublisherId(commentDto.getActor());
        comment.setDatePublished(commentDto.getCreated().getTime().toString());
        if(Objects.nonNull(commentDto.getObjectUrn())) {
            comment.setFeedId(commentDto.getObjectUrn());
        } else {
            comment.setFeedId(commentDto.getParentComment());
        }

        // to set mentions in the text field itself
        if (Objects.nonNull(commentDto.getMessage())) {
            if (CollectionUtils.isEmpty(commentDto.getMessage().getAttributes())) {
                comment.setFeedText(commentDto.getMessage().getText());
            } else {
                // setting mention or hashtags in text
                List<LinkedinAttributes> attributes = commentDto.getMessage().getAttributes();
                String text = commentDto.getMessage().getText();
                int count = 0;
                for (LinkedinAttributes attribute: attributes) {
                    // not including hashtags
                    if (text.charAt(attribute.getStart()) != '#') {
                        text = LinkedinUtils.getMentionsFromText(text, attribute.getStart(), attribute.getLength(), count++);
                    }
                }
                comment.setFeedText(text);
            }
        }

        // check this via api
        // comment.setCan_like(true);
        comment.setCanDelete(StringUtils.equalsIgnoreCase(companyUrn, comment.getPublisherId()));
        comment.setFeedUrl(LINKEDIN_FEED_URL.concat(commentDto.getObject()));
        comment.setEditable(StringUtils.equalsIgnoreCase(companyUrn, comment.getPublisherId()));
        comment.setDateModified(commentDto.getLastModified().getTime().toString());

        // setting mentions in feed
        /* if (!ObjectUtils.isEmpty(commentDto.getMessage().getAttributes())){
            setMentions(commentDto);
        }*/

        // setting media in feed
        if (!CollectionUtils.isEmpty(commentDto.getContent())) {
            comment.setImages(commentDto.getContent().stream().filter(content -> content.getType().equals("IMAGE"))
                    .map(LinkedInContent::getUrl).collect(Collectors.toList()));
        }

        return comment;
    }

    public static Feed getLinkedinCommentEng(LinkedInCommentResponse commentDto, String companyUrn) {
        if(commentDto == null) {
            return null;
        }
        Feed comment = new Feed();
        comment.setPublisherId(commentDto.getActor());
        comment.setDatePublished(new SimpleDateFormat(dateFormat).format(commentDto.getCreated().getTime()));
        if(Objects.nonNull(commentDto.getObjectUrn())) {
            comment.setFeedId(commentDto.getObjectUrn());
        } else {
            comment.setFeedId(commentDto.getParentComment());
        }

        // to set mentions in the text field itself
        if (Objects.nonNull(commentDto.getMessage())) {
            if (CollectionUtils.isEmpty(commentDto.getMessage().getAttributes())) {
                comment.setFeedText(commentDto.getMessage().getText());
            } else {
                // setting mention or hashtags in text
                List<LinkedinAttributes> attributes = commentDto.getMessage().getAttributes();
                String text = commentDto.getMessage().getText();
                int count = 0;
                for (LinkedinAttributes attribute: attributes) {
                    // not including hashtags
                    if (text.charAt(attribute.getStart()) != '#') {
                        text = LinkedinUtils.getMentionsFromText(text, attribute.getStart(), attribute.getLength(), count++);
                    }
                }
                comment.setFeedText(text);
            }
        }

        // check this via api
        // comment.setCan_like(true);
        comment.setCanDelete(StringUtils.equalsIgnoreCase(companyUrn, comment.getPublisherId()));
        comment.setFeedUrl(LINKEDIN_FEED_URL.concat(commentDto.getObject()));
        comment.setEditable(StringUtils.equalsIgnoreCase(companyUrn, comment.getPublisherId()));
        comment.setDateModified(new SimpleDateFormat(dateFormat).format(commentDto.getLastModified().getTime()));
        comment.setComments(Objects.nonNull(commentDto.getCommentsSummary()) ? commentDto.getCommentsSummary().getAggregatedTotalComments() : 0);
        comment.setLikes(Objects.nonNull(commentDto.getLikesSummary()) ? commentDto.getLikesSummary().getTotalLikes() : 0);
        // setting mentions in feed
        /* if (!ObjectUtils.isEmpty(commentDto.getMessage().getAttributes())){
            setMentions(commentDto);
        }*/

        // setting media in feed
        if (!CollectionUtils.isEmpty(commentDto.getContent())) {
            comment.setImages(commentDto.getContent().stream().filter(content -> content.getType().equals("IMAGE"))
                    .map(LinkedInContent::getUrl).collect(Collectors.toList()));
        }

        return comment;
    }

    private static void setMentions(LinkedInCommentResponse commentDto) {
        List<MentionData> mentions = new ArrayList<>();

        for (LinkedinAttributes attribute: commentDto.getMessage().getAttributes()) {
            // Map<String, Object> value = new ObjectMapper().convertValue(attribute.getValue(), Map.class);

            MentionData mentionData = new MentionData();
            // person mentions

          /*  if (Objects.nonNull(attribute.getValue().getMemberAttributedEntity())) {
                mentionData.setValue(attribute.getValue().getMemberAttributedEntity().getMember());
            }

            if (Objects.nonNull(attribute.getValue().getCompanyAttributedEntity())) {
                mentionData.setValue(attribute.getValue().getCompanyAttributedEntity().getCompany());
            }
*/
            mentionData.setChannel("Linkedin");
            mentionData.setName(extractNameFromComment(commentDto.getMessage().getText(), attribute.getStart(), attribute.getLength()));
            mentions.add(mentionData);
        }
    }

    private static String extractNameFromComment(String comment, int startPos, int length) {
        return comment.substring(startPos, startPos+length);
    }


    public static SocialTimeline getSocialTimeline(LinkedInAPIResponse posts, BusinessLinkedinPage linkedinPage, Date maxDate, int nextToken) {
        SocialTimeline socialTimeline = new SocialTimeline();
        socialTimeline.setStreamName(linkedinPage.getCompanyName());
        socialTimeline.setStreamType(SocialStreams.StreamType.MY_POSTS.toString());
        ConversionUtils.convertLinkedinPostToTimelineObject(posts.getElements(), linkedinPage, maxDate);
        return socialTimeline;
    }

    public static String getIdFromUrn(String urn) {
        try {
            return urn.substring(urn.lastIndexOf(":") + 1);
        } catch(Exception e) {
            LOG.error("[Linkedin] Error while getting id from urn {}", e);
        }
        return null;
    }

    public static String removeURNInMentionsFromText(String text) {
        /*String[] split = text.split("\\(urn:li:[a-z]+:[0-9]+\\)");
        StringBuilder finalText = new StringBuilder();
        for (String s: split) {
            finalText.append(s);
        }
        return finalText.toString();*/
        return text.replaceAll("]\\(urn:li:[a-z]+:[0-9]+\\)", "");
    }

    public static String getMentionsFromText(String text, int start, int length, int count) {
        return new StringBuilder(text.substring(0, start + count)).append("@").append(text.substring(start + count)).toString();
    }

    public static String removeNewMentionsFromText(String text) {
        return text.replaceAll("@\\[", "");
    }

    public static String removeMentionsFromText(String message) {
        return message.replaceAll("@", "");
    }


    public static String removeMentionsHashtag(String message) {
        if(StringUtils.isNotEmpty(message)) {
            try {
                //remove mentions text
                message = message.replaceAll("@\\[", "");
                message = message.replaceAll("]\\(urn:li:[a-z]+:[0-9]+\\)", "");
                //Pattern p = Pattern.compile("\\{hashtag|\\#|.*?\\}");
                Pattern p = Pattern.compile("\\{hashtag\\|\\#|(.*?)\\}");
                Matcher m = p.matcher(message);
                while (m.find())
                    message = message.replace(m.group().subSequence(m.group().indexOf("{hashtag|\\#|"),
                                    m.group().length()),
                            "#".concat(String.valueOf(m.group().subSequence(m.group().indexOf("{hashtag|\\#|") + 12, m.group().length() - 1))));
                message= StringEscapeUtils.unescapeJava(message);

            } catch (Exception e) {
                LOG.error("Unexpected exception thrown while removing mentions and hashtag from text for message {} :: {}", message, e.getMessage());
            }
        }
        return message;
    }

    /** New method created to handle corner cases where urn is alphanumeric  : such as @[Navroj Sandhu](urn:li:person:Ahf-M4U9-6) **/
    public static String removeMentionsHashtagNew(String message) {
        if(StringUtils.isNotEmpty(message)) {
            try {
                //remove mentions text
                message = message.replaceAll("@\\[", "");
                message = message.replaceAll("]\\(urn:li:[a-z]+:[0-9a-zA-Z-]+\\)", "");
                //Pattern p = Pattern.compile("\\{hashtag|\\#|.*?\\}");
                Pattern p = Pattern.compile("\\{hashtag\\|\\#|(.*?)\\}");
                Matcher m = p.matcher(message);
                while (m.find())
                    message = message.replace(m.group().subSequence(m.group().indexOf("{hashtag|\\#|"),
                                    m.group().length()),
                            "#".concat(String.valueOf(m.group().subSequence(m.group().indexOf("{hashtag|\\#|") + 12, m.group().length() - 1))));
                message= StringEscapeUtils.unescapeJava(message);

            } catch (Exception e) {
                LOG.error("Unexpected exception thrown while removing mentions and hashtag from text for message {} :: {}", message, e.getMessage());
            }
        }
        return message;
    }
}
