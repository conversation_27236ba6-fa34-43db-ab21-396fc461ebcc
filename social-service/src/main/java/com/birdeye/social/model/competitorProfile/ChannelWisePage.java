package com.birdeye.social.model.competitorProfile;

import com.birdeye.social.constant.SocialChannel;

import java.util.Collections;
import java.util.List;

@lombok.Data
public class ChannelWisePage {
    private List<String> facebook;
    private List<String> instagram;
    private List<String> twitter;
    public List<String> getPageIdsByChannel(String channel) {
        if(channel.equals(SocialChannel.FACEBOOK.getName())) {
            return facebook;
        } else if(channel.equals(SocialChannel.INSTAGRAM.getName())) {
            return instagram;
        } else if(channel.equals(SocialChannel.TWITTER.getName())) {
            return twitter;
        } else {
            return Collections.emptyList();
        }
    }
}
