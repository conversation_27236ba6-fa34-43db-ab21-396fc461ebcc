/**
 *
 */
package com.birdeye.social.model;

import java.util.List;

import com.birdeye.social.sro.SocialResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 *
 * 15 Mar 2018
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
public class AutoSuggesterPagesResponse extends SocialResponse {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean pageMapped;
	private List<SocialPageListInfo>	pages;
	
	public List<SocialPageListInfo> getPages() {
		return pages;
	}
	
	public void setPages(List<SocialPageListInfo> pages) {
		this.pages = pages;
	}

	public Boolean getPageMapped() {
		return pageMapped;
	}

	public void setPageMapped(Boolean pageMapped) {
		this.pageMapped = pageMapped;
	}
}
