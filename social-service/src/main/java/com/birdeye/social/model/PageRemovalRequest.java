package com.birdeye.social.model;

import com.birdeye.social.constant.SocialChannel;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PageRemovalRequest {

    private SocialChannel socialChannel;
    private Collection<String> pageIds;

    public PageRemovalRequest(SocialChannel socialChannel, List<String> pageIds) {
        this.socialChannel = socialChannel;
        this.pageIds = pageIds;
    }

    public SocialChannel getSocialChannel() {
        return socialChannel;
    }

    public void setSocialChannel(SocialChannel socialChannel) {
        this.socialChannel = socialChannel;
    }

    public Collection<String> getPageIds() {
        return pageIds;
    }

    public void setPageIds(List<String> pageIds) {
        this.pageIds = pageIds;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("PageRemovalRequest{");
        sb.append("channel=");
        sb.append(getSocialChannel().toString());
        sb.append(",");
        sb.append("pageIds=");
        sb.append(getPageIds());
        sb.append("}");
        return sb.toString();
    }

}
