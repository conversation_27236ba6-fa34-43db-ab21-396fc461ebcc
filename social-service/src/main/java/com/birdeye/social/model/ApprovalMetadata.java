package com.birdeye.social.model;

import lombok.*;

import javax.persistence.AttributeConverter;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ApprovalMetadata {
    private Integer approvalRequestId;
    private String approvalUUId;
    private Integer referenceStepId;
    private String conversationId;
    private String rejectedReason;
    private String rejectedBy;
    private String isSameContent;
    private String approvalWorkflowName;
    CurrentApprovalDetails currentActionDetails;
    List<ApprovalSteps> steps;
    private Long updatedAt;
    private String updatedBy;
}
