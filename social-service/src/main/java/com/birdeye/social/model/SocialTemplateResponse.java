package com.birdeye.social.model;

import com.birdeye.social.entities.SocialTemplate;

import java.util.List;

public class SocialTemplateResponse {

    List<SocialTemplateDTO> templateList;

    public List<SocialTemplateDTO> getTemplateList() {
        return templateList;
    }

    public void setTemplateList(List<SocialTemplateDTO> templateList) {
        this.templateList = templateList;
    }

    @Override
    public String toString() {
        return "SocialTemplateResponse{" +
                "templateList=" + templateList +
                '}';
    }
}
