package com.birdeye.social.model;

import javax.validation.constraints.NotNull;
import java.util.List;

@lombok.Data
public class SetupAgentRequest {

    // Fields needed by Social
    @NotNull
    private Long enterpriseId;
    @NotNull
    private Integer userId;

    // Fields needed by Google BM
    @NotNull
    private String displayName;

    private String logoUrl;
    @NotNull
    private String welcomeMessage;
    @NotNull
    private String offlineMessage;
    private String websiteUrl;

    private Integer agentId;

    private GoogleAgentStatus status;

}
