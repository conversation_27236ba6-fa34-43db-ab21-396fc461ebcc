package com.birdeye.social.model;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialAddPagesResponse implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4196269159306034858L;

	private Integer pagesCount;
	private List<SocialChannelPage> pages;

	public Integer getPagesCount() {
		return pagesCount;
	}

	public void setPagesCount(Integer pagesCount) {
		this.pagesCount = pagesCount;
	}

	public List<SocialChannelPage> getPages() {
		return pages;
	}

	public void setPages(List<SocialChannelPage> pages) {
		this.pages = pages;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("SocialAddPagesResponse [pagesCount=");
		builder.append(pagesCount);
		builder.append(", pages=");
		builder.append(pages);
		builder.append("]");
		return builder.toString();
	}

}
