package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialPostMetadata implements Serializable {

	public enum PostType {
		REVIEW("review");

		private String name;

		PostType(String name) {
			this.name = name;
		}

		public String getName() {
			return name;
		}
	}

	private static final long serialVersionUID = -8876783736122139173L;

	private String postType;

	private String gmbPostMetaData;

	private String youtubePostMetaData;

	public String getPostType() {
		return postType;
	}

	public void setPostType(String postType) {
		this.postType = postType;
	}

	public String getGmbPostMetaData() {
		return gmbPostMetaData;
	}

	public GMBPostMetaData getGmbPostMetaDataObj() throws IOException {
		if(StringUtils.isNotBlank(this.gmbPostMetaData)){
			return new ObjectMapper().readValue(this.gmbPostMetaData, GMBPostMetaData.class);
		}else{
			return null;
		}
	}

	public void setGmbPostMetaData(String gmbPostMetaData) {
		this.gmbPostMetaData = gmbPostMetaData;
	}

	public void setGmbPostMetaData(GMBPostMetaData gmbPostMetaData) throws JsonProcessingException {
		this.gmbPostMetaData = new ObjectMapper().writeValueAsString(gmbPostMetaData);
	}

	public String getYoutubePostMetaData() {
		return youtubePostMetaData;
	}

	public void setYoutubePostMetaData(String youtubePostMetaData) {
		this.youtubePostMetaData = youtubePostMetaData;
	}
}
