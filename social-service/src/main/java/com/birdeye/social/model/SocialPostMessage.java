package com.birdeye.social.model;

import java.util.Date;
import java.util.List;

public class SocialPostMessage {
	 private Integer id;
	    private String postText;
	    private List<String> images;
	    private String videoUrl;
	    private String publishDate;
	    private String publishedBy;
	    private Date datePublish;
	    private List<SocialPostResponse> sitePublishStatus;
	    
	    public Integer getId() {
	        return id;
	    }

	    public void setId(Integer id) {
	        this.id = id;
	    }

	    public String getPostText() {
	        return postText;
	    }

	    public void setPostText(String postText) {
	        this.postText = postText;
	    }

	    public List<String> getImages() {
	        return images;
	    }

	    public void setImages(List<String> images) {
	        this.images = images;
	    }

	    public String getVideoUrl() {
	        return videoUrl;
	    }

	    public void setVideoUrl(String videoUrl) {
	        this.videoUrl = videoUrl;
	    }

	    public String getPublishDate() {
	        return publishDate;
	    }

	    public void setPublishDate(String publishDate) {
	        this.publishDate = publishDate;
	    }

	    public String getPublishedBy() {
	        return publishedBy;
	    }

	    public void setPublishedBy(String publishedBy) {
	        this.publishedBy = publishedBy;
	    }

	    public List<SocialPostResponse> getSitePublishStatus() {
	        return sitePublishStatus;
	    }

	    public void setSitePublishStatus(List<SocialPostResponse> sitePublishStatus) {
	        this.sitePublishStatus = sitePublishStatus;
	    }

	    public Date getDatePublish() {
	        return datePublish;
	    }

	    public void setDatePublish(Date datePublish) {
	        this.datePublish = datePublish;
	    }
}
