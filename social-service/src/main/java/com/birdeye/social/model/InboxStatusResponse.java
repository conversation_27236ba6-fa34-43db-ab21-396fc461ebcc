package com.birdeye.social.model;

import com.birdeye.social.sro.SocialResponse;

public class InboxStatusResponse extends SocialResponse {
    private Boolean isMessagingEnabled;
    private String messagingInvalidType;

    public Boolean getMessagingEnabled() {
        return isMessagingEnabled;
    }

    public void setMessagingEnabled(Boolean messagingEnabled) {
        isMessagingEnabled = messagingEnabled;
    }

    public String getMessagingInvalidType() {
        return messagingInvalidType;
    }

    public void setMessagingInvalidType(String messagingInvalidType) {
        this.messagingInvalidType = messagingInvalidType;
    }
}
