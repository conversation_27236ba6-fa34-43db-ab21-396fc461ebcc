package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ChannelPageRemoved {

    private String channelName; // SocialChannel name
    private String pageId; // locationId for GMB, pageId for Facebook, profileId for twitter
    private String pageName; // name of page
    private Integer businessId; // Mapped businessId
    private Long enterpriseNumber;
    private String placeId; // GMB specific
    private String pageType; // Linkedin specific
    private Integer rawId;

    public ChannelPageRemoved() {
    }

    public ChannelPageRemoved(String channelName, String pageId, String pageName, Integer businessId, Long enterpriseNumber, String placeId, String pageType) {
        this.channelName = channelName;
        this.pageId = pageId;
        this.pageName = pageName;
        this.businessId = businessId;
        this.enterpriseNumber = enterpriseNumber;
        this.placeId = placeId;
        this.pageType = pageType;
    }
    
    public ChannelPageRemoved(String channelName, String pageId, String pageName, Integer businessId, Long enterpriseNumber, String placeId, String pageType,Integer rawId) {
        this.channelName = channelName;
        this.pageId = pageId;
        this.pageName = pageName;
        this.businessId = businessId;
        this.enterpriseNumber = enterpriseNumber;
        this.placeId = placeId;
        this.pageType = pageType;
        this.rawId = rawId;
    }
   
    
    public Integer getRawId() {
		return rawId;
	};

	public void setRawId(Integer rawId) {
		this.rawId = rawId;
	}

	public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }

    public String getPlaceId() {
        return placeId;
    }

    public void setPlaceId(String placeId) {
        this.placeId = placeId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Long getEnterpriseNumber() {
        return enterpriseNumber;
    }

    public void setEnterpriseNumber(Long enterpriseNumber) {
        this.enterpriseNumber = enterpriseNumber;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    @Override
    public String toString() {
        return "ChannelPageRemoved{" +
                "channelName='" + channelName + '\'' +
                ", pageId='" + pageId + '\'' +
                ", pageName='" + pageName + '\'' +
                ", businessId=" + businessId +
                ", enterpriseNumber=" + enterpriseNumber +
                ", placeId='" + placeId + '\'' +
                ", pageType='" + pageType + '\'' +
                ", rawId=" + rawId +
                '}';
    }
}
