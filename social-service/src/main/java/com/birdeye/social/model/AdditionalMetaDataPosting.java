package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AdditionalMetaDataPosting implements Serializable {

    private static final long serialVersionUID = -5573490493935590043L;

    private String callToActionType;
    private String callToActionUrl;

    public String getCallToActionType() {
        return callToActionType;
    }

    public void setCallToActionType(String callToActionType) {
        this.callToActionType = callToActionType;
    }

    public String getCallToActionUrl() {
        return callToActionUrl;
    }

    public void setCallToActionUrl(String callToActionUrl) {
        this.callToActionUrl = callToActionUrl;
    }
}
