package com.birdeye.social.model.usage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Used
{
    private String historicalPowertrackJobs;

    private String timePeriod;

    private String historicalPowertrackDays;

    private Double activities;

    public String getHistoricalPowertrackJobs ()
    {
        return historicalPowertrackJobs;
    }

    public void setHistoricalPowertrackJobs (String historicalPowertrackJobs)
    {
        this.historicalPowertrackJobs = historicalPowertrackJobs;
    }

    public String getTimePeriod ()
    {
        return timePeriod;
    }

    public void setTimePeriod (String timePeriod)
    {
        this.timePeriod = timePeriod;
    }

    public String getHistoricalPowertrackDays ()
    {
        return historicalPowertrackDays;
    }

    public void setHistoricalPowertrackDays (String historicalPowertrackDays)
    {
        this.historicalPowertrackDays = historicalPowertrackDays;
    }

    public Double getActivities ()
    {
        return activities;
    }

    public void setActivities (Double activities)
    {
        this.activities = activities;
    }

    @Override
    public String toString()
    {
        return "ClassPojo [historicalPowertrackJobs = "+historicalPowertrackJobs+", timePeriod = "+timePeriod+", historicalPowertrackDays = "+historicalPowertrackDays+", activities = "+activities+"]";
    }
}
