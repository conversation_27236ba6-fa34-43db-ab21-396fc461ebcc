package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ReviewRequest implements Serializable {

    private static final long serialVersionUID = -5573490493935590043L;
    private Integer reviewId;
    private String reviewText;
    private Float starRating;
    private String reviewerName;
    private String toSource;
    private Integer businessId;
    private String fromSource;
    private String date;
    private Integer templateId;
    private Integer userId;
    private Boolean autoShare;
    private Integer fromSourceId;
    private String reviewUrl;
    private String uniqueReviewUrl;

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public String getReviewText() {
        return reviewText;
    }

    public void setReviewText(String reviewText) {
        this.reviewText = reviewText;
    }

    public Float getStarRating() {
        return starRating;
    }

    public void setStarRating(Float starRating) {
        this.starRating = starRating;
    }

    public String getReviewerName() {
        return reviewerName;
    }

    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }

    public String getToSource() {
        return toSource;
    }

    public void setToSource(String toSource) {
        this.toSource = toSource;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Boolean getAutoShare() {
        return autoShare;
    }

    public void setAutoShare(Boolean autoShare) {
        this.autoShare = autoShare;
    }

    public Integer getFromSourceId() {
        return fromSourceId;
    }

    public void setFromSourceId(Integer fromSourceId) {
        this.fromSourceId = fromSourceId;
    }

    public String getReviewUrl() {
        return reviewUrl;
    }

    public void setReviewUrl(String reviewUrl) {
        this.reviewUrl = reviewUrl;
    }

    public String getUniqueReviewUrl() {
        return uniqueReviewUrl;
    }

    public void setUniqueReviewUrl(String uniqueReviewUrl) {
        this.uniqueReviewUrl = uniqueReviewUrl;
    }

    @Override
    public String toString() {
        return "ReviewRequest{" +
                "reviewId=" + reviewId +
                ", reviewText='" + reviewText + '\'' +
                ", starRating=" + starRating +
                ", reviewerName='" + reviewerName + '\'' +
                ", toSource='" + toSource + '\'' +
                ", businessId=" + businessId +
                ", fromSource='" + fromSource + '\'' +
                ", date='" + date + '\'' +
                ", templateId=" + templateId +
                ", userId=" + userId +
                ", autoShare=" + autoShare +
                ", fromSourceId=" + fromSourceId +
                ", reviewUrl='" + reviewUrl + '\'' +
                ", uniqueReviewUrl='" + uniqueReviewUrl + '\'' +
                '}';
    }
}
