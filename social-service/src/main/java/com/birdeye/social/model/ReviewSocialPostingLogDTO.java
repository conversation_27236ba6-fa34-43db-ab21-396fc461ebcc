package com.birdeye.social.model;

import java.io.Serializable;
import java.util.Date;

public class ReviewSocialPostingLogDTO implements Serializable {

    private static final long serialVersionUID = -1130598945034525649L;

    private Integer id;
    private String source;
    private Integer reviewId;
    private String pageUrl;
    private Date postedOn;
    private Integer postedBy;
    private String postStatus;
    private String postId;
    private String failureLog;
    private Integer processed = 0;
    private Integer followerCount;
    private String googleplusPostId;

    public ReviewSocialPostingLogDTO() {
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getReviewId() {
        return reviewId;
    }

    public void setReviewId(Integer reviewId) {
        this.reviewId = reviewId;
    }

    public String getPageUrl() {
        return pageUrl;
    }

    public void setPageUrl(String pageUrl) {
        this.pageUrl = pageUrl;
    }

    public Date getPostedOn() {
        return postedOn;
    }

    public void setPostedOn(Date postedOn) {
        this.postedOn = postedOn;
    }

    public Integer getPostedBy() {
        return postedBy;
    }

    public void setPostedBy(Integer postedBy) {
        this.postedBy = postedBy;
    }

    public String getPostStatus() {
        return postStatus;
    }

    public void setPostStatus(String postStatus) {
        this.postStatus = postStatus;
    }

    public String getPostId() {
        return postId;
    }

    public void setPostId(String postId) {
        this.postId = postId;
    }

    public String getFailureLog() {
        return failureLog;
    }

    public void setFailureLog(String failureLog) {
        this.failureLog = failureLog;
    }

    public Integer getProcessed() {
        return processed;
    }

    public void setProcessed(Integer processed) {
        this.processed = processed;
    }

    public Integer getFollowerCount() {
        return followerCount;
    }

    public void setFollowerCount(Integer followerCount) {
        this.followerCount = followerCount;
    }

    public String getGoogleplusPostId() {
        return googleplusPostId;
    }

    public void setGoogleplusPostId(String googleplusPostId) {
        this.googleplusPostId = googleplusPostId;
    }

    @Override
    public String toString() {
        return "ReviewSocialPostingLogDTO{" +
                "id=" + id +
                ", source='" + source + '\'' +
                ", reviewId=" + reviewId +
                ", pageUrl='" + pageUrl + '\'' +
                ", postedOn=" + postedOn +
                ", postedBy=" + postedBy +
                ", postStatus='" + postStatus + '\'' +
                ", postId='" + postId + '\'' +
                ", failureLog='" + failureLog + '\'' +
                ", processed=" + processed +
                ", followerCount=" + followerCount +
                ", googleplusPostId='" + googleplusPostId + '\'' +
                '}';
    }
}
