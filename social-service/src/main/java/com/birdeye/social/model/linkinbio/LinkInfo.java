package com.birdeye.social.model.linkinbio;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LinkInfo {

    private Integer linkId;
    @NotEmpty
    private String buttonText;
    @NotEmpty
    private String url;
    private String image;
    private Integer numberOfClicks = 0;
    private Integer sequenceNumber;
    private Boolean showLink = Boolean.TRUE;
    private Boolean pinned = Boolean.FALSE;
    private Date created;
}
