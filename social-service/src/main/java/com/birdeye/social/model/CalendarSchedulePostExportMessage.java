package com.birdeye.social.model;

import com.birdeye.social.insights.ES.CalendarExportViewPagePostInsightsData;
import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
@Builder
@Data
public class CalendarSchedulePostExportMessage {
    private Integer id;
    private String postText;
    private String postHeader;
    private List<MediaData> images;
    private List<String> compressedImages;
    private List<MediaData> videos;
    private List<String> videoThumbnails;
    private String linkPreviewUrl;
    private String publishDate;
    private String publishedBy;
    private Date datePublish;
    private Integer scheduleInfoId;
    private List<String> postingSites;
    private Integer isPublished;
    private List<String> mediaSequence;
    private List<MentionData> mentions;
    private SocialPostPermissionStatusResponse permissionStatus;
    private List<CalendarViewPagePostInsightsData> postInsights;
    private Boolean hasAccess = true;
    private List<String> incompleteChannel;
    private Boolean hasPostFailed = false;
    private Integer failedChannelCount;
    private Integer failedPageCount;
    private List<String> failedSites;
    private String type;
    private GoogleOfferDetails gmbOfferDetails;
    private Boolean isExpired;
    private Integer duplicatedCount;
    private Boolean isQuotedTweet;
    private List<SocialTagBasicDetail> tags;
    private Boolean isCreator = false;
    private Boolean isApprover = false;
    private Integer approveWorkflowId;
    private String approvalStatus;
    private String approvalUUId;
    private Integer approvalRequestId;
    private String conversationId;
    private Integer referenceStepId;
    private String endDate;
    private String applePublishStatus;
    private Boolean isOperationAllowed = false;
    private String quotedTweetSource;
    private String createdByName;
    private Boolean aiPost;
    private Boolean aiSuggestion = false;


    private Map<String, List<PostPageMessage>> pageData;
    private Map<String, Integer> pageDataCount;
    Object approvalData;
    CalendarExportViewPagePostInsightsData insightsData;

}
