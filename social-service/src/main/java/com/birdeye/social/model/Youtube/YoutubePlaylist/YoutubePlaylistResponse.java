package com.birdeye.social.model.Youtube.YoutubePlaylist;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class YoutubePlaylistResponse implements Serializable {

    private String id;
    private YoutubePlaylistSnippet snippet;
}
