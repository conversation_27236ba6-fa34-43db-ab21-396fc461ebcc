package com.birdeye.social.model.Youtube.YoutubeCategory;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class YoutubeCategorySnippet implements Serializable {

    private String title;
    private boolean assignable;
}
