package com.birdeye.social.model;

import com.birdeye.social.external.request.google.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBPostMetaData {

    // The language of the local post
    private String languageCode;
    // The URL that users are sent to when clicking through the promotion. Ignored for topic type OFFER.
    private CallToAction callToAction;
    // Event information. Required for topic types EVENT and OFFER.
    private LocalPostEvent event;
    // Required. The topic type of the post: standard, event, offer, or alert.
    private LocalPostTopicType topicType;
    // The type of alert the post is created for. This field is only applicable for posts of topicType Alert, and behaves as a sub-type of Alerts.
    private AlertType alertType;
    // Additional data for offer posts. This should only be set when the topicType is OFFER.
    private LocalPostOffer offer;

    public String getLanguageCode() {
        return languageCode;
    }

    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }

    public CallToAction getCallToAction() {
        return callToAction;
    }

    public void setCallToAction(CallToAction callToAction) {
        this.callToAction = callToAction;
    }

    public LocalPostEvent getEvent() {
        return event;
    }

    public void setEvent(LocalPostEvent event) {
        this.event = event;
    }

    public LocalPostTopicType getTopicType() {
        return topicType;
    }

    public void setTopicType(LocalPostTopicType topicType) {
        this.topicType = topicType;
    }

    public AlertType getAlertType() {
        return alertType;
    }

    public void setAlertType(AlertType alertType) {
        this.alertType = alertType;
    }

    public LocalPostOffer getOffer() {
        return offer;
    }

    public void setOffer(LocalPostOffer offer) {
        this.offer = offer;
    }

    @Override
    public String toString() {
        return "GMBPostMetaData{" +
                "languageCode='" + languageCode + '\'' +
                ", callToAction=" + callToAction +
                ", event=" + event +
                ", topicType=" + topicType +
                ", alertType=" + alertType +
                ", offer=" + offer +
                '}';
    }
}
