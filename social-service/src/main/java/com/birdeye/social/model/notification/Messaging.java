package com.birdeye.social.model.notification;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Messaging implements Serializable {

    private Long timestamp;
    private Message message;
    private User sender;
    private User recipient;
    private Status read;
    private Status delivery;
}
