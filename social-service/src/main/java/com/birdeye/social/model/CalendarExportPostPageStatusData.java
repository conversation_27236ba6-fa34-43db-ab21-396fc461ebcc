package com.birdeye.social.model;

import com.birdeye.social.dao.SocialPostInfoRepository;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

@Data
@ToString
public class CalendarExportPostPageStatusData {
    private Map<String, LocationPagePair> pageData;
    private Map<Integer, List<SocialPostInfoRepository.PostPageStatus>> postPageStatusMap;
    private Map<Integer, List<String>> postIdVsPageIdsMap;
}
