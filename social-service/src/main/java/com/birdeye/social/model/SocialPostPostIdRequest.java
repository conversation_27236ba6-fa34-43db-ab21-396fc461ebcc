package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialPostPostIdRequest {
    Integer masterPostId;
    Integer postId;
    String approverUserId;
    List<String> pendingApproverUserIds;
    Boolean isEligibleForSingleEvent = false;
}
