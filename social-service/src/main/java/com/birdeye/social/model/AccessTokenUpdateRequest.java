package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccessTokenUpdateRequest implements Serializable {

    private boolean updateScope;

    private List<String> pageIds;
    private Integer userId;
    private Integer businessRequestId;
    private Long businessId;

    public void setUpdateScope(boolean updateScope) {
        this.updateScope = updateScope;
    }

    public void setPageIds(List<String> pageIds) {
        this.pageIds = pageIds;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public void setBusinessRequestId(Integer businessRequestId) {
        this.businessRequestId = businessRequestId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public boolean isUpdateScope() {
        return updateScope;
    }

    public List<String> getPageIds() {
        return pageIds;
    }

    public Integer getUserId() {
        return userId;
    }

    public Integer getBusinessRequestId() {
        return businessRequestId;
    }

    public Long getBusinessId() {
        return businessId;
    }

    public AccessTokenUpdateRequest() {
    }

    public AccessTokenUpdateRequest(boolean updateScope, List<String> pageIds, Integer userId, Integer businessRequestId, Long businessId) {
        this.updateScope = updateScope;
        this.pageIds = pageIds;
        this.userId = userId;
        this.businessRequestId = businessRequestId;
        this.businessId = businessId;
    }
}
