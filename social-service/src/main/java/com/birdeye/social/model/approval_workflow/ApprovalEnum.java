package com.birdeye.social.model.approval_workflow;

public enum ApprovalEnum {

    SOCIAL,CREATED,EDITED,DELETED,
    enterpriseId,channel,approval_user_ids,postIds,
    approval_status,enterprise_id,pending,
    page_ids,created_by,approval_workflow_id,created_date,
    tagIds,
    page_name,post_text,id,userId,approvalStatus,count,
    social_approval_request_template,POST_CREATED,
    POST_TOKEN("postToken"), REJECTED_TOKEN("rejectedToken"), APPROVAL_TOKEN("approvalToken"),
    EDIT_POST_TOKEN("editPostToken"), VIEW_DETAIL_TOKEN("viewDetailToken"),
    PENDING , REJECTED , TERMINATED , APPROVED, PROCESSING,
    RESCHEDULE_SUBMIT_TOKEN("rescheduleAndSubmitToken"),
    APPROVE_TOKEN_ALL, REJECT_TOKEN_ALL, social_bulk_approval_request_template;

    private String name;

    ApprovalEnum(String name) {
        this.name = name;
    }
    ApprovalEnum() {} //default constructor

    public String getName() {
        return (name != null) ? name : this.name();
    }
}
