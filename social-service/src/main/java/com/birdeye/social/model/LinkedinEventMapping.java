package com.birdeye.social.model;

import com.birdeye.social.constant.BusinessAccountTypeEnum;

public enum LinkedinEventMapping {

    SHARE_MENTION("post", "SHARE_MENTION"), ADMIN_COMMENT("ADMIN_COMMENT", "ADMIN_COMMENT"),
    COMMENT("COMMENT", "COMMENT"), PHOTO_MENTION("tagged", "PHOTO_MENTION");

    private String id;
    private String name;

    private LinkedinEventMapping(String id, String name) {
        this.id = id;
        this.name = name;
    }

    public String getName() {
        return this.name;
    }

    public String getId() {
        return this.id;
    }

    public static LinkedinEventMapping getEventActionTypeByName(String name) {
        for (LinkedinEventMapping status : LinkedinEventMapping.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
}
