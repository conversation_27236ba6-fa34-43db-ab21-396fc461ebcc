package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class TrafficReportKafkaMessage implements Serializable {

    private static final long serialVersionUID = -5573490493935590043L;
    private Long biz_id;
    private Long src_id;
    private String src_name;
    private String updated_at;
    private String visit_day;
    private Integer visitor_count;
    private Integer badge_type;
    private Long enterprise_id;
}
