/**
 *
 *
 */
package com.birdeye.social.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class GMBMappingRequest implements Serializable {
	
	private static final long	serialVersionUID	= 3304403791464540572L;
	
	private String				domainName;
	
	private Long				businessId;
	
	private String				locationId;
	
	/**
	 * @return the domainName
	 */
	public String getDomainName() {
		return domainName;
	}
	
	/**
	 * @param domainName
	 *            the domainName to set
	 */
	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}
	
	/**
	 * @return the businessId
	 */
	public Long getBusinessId() {
		return businessId;
	}
	
	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Long businessId) {
		this.businessId = businessId;
	}
	
	/**
	 * @return the locationId
	 */
	public String getLocationId() {
		return locationId;
	}
	
	/**
	 * @param locationId
	 *            the locationId to set
	 */
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}
	
}
