package com.birdeye.social.model;
/**
 * <AUTHOR>
 *
 */

import com.birdeye.social.sro.SocialTagBasicDetail;
import lombok.*;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MasterPostLibEditPostResponse {
    private Integer masterPostId;
    private List<PostLibEditPostResponse> postLibEditPostResponseList;
    private List<SocialTagBasicDetail> tags;
    private Boolean showEditBanner = false;
    private Integer usageCount;
    private Boolean aiPost;
}
