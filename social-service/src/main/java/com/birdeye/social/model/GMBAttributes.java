package com.birdeye.social.model;

import com.birdeye.social.external.request.google.AttributeMetadata;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GMBAttributes {

    private List<AttributeMetadata> attributeMetadata;
    private String nextPageToken;

    public List<AttributeMetadata> getAttributeMetadata() {
        return attributeMetadata;
    }

    public void setAttributeMetadata(List<AttributeMetadata> attributeMetadata) {
        this.attributeMetadata = attributeMetadata;
    }

    public String getNextPageToken() {
        return nextPageToken;
    }

    public void setNextPageToken(String nextPageToken) {
        this.nextPageToken = nextPageToken;
    }

    @Override
    public String toString() {
        return "GMBAttributes{" +
                "attributes=" + attributeMetadata +
                ", nextPageToken='" + nextPageToken + '\'' +
                '}';
    }
}
