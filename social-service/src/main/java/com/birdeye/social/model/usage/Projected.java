package com.birdeye.social.model.usage;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Projected
{
    private String timePeriod;

    private Double activities;

    public String getTimePeriod ()
    {
        return timePeriod;
    }

    public void setTimePeriod (String timePeriod)
    {
        this.timePeriod = timePeriod;
    }

    public Double getActivities ()
    {
        return activities;
    }

    public void setActivities (Double activities)
    {
        this.activities = activities;
    }

    @Override
    public String toString()
    {
        return "ClassPojo [timePeriod = "+timePeriod+", activities = "+activities+"]";
    }
}
