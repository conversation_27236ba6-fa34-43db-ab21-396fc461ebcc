package com.birdeye.social.model;

import com.birdeye.social.twitter.MediaTweetList;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TwitterFollowNotification implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long for_user_id;
    private List<TwitterFollowEvent> follow_events;
}
