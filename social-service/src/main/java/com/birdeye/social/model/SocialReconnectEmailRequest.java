package com.birdeye.social.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class SocialReconnectEmailRequest implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4441765961082393563L;
	
	Map<Integer, List<Integer>> accounts;

	public Map<Integer, List<Integer>> getAccounts() {
		return accounts;
	}

	public void setAccounts(Map<Integer, List<Integer>> accounts) {
		this.accounts = accounts;
	}

	@Override
	public String toString() {
		return "SocialReconnectEmailRequest [accounts=" + accounts + "]";
	}

}
