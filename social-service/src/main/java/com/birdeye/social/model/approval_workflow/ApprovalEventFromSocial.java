package com.birdeye.social.model.approval_workflow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalEventFromSocial {

    private String entityName;
    private Integer entityId;
    private Integer enterpriseId;
    private Integer approvalWorkflowId;
    private Integer approvalRequestId;
    private String approvalUUId;
    private Integer submitterId;
    private String updatedBy;
    private String eventType;
}
