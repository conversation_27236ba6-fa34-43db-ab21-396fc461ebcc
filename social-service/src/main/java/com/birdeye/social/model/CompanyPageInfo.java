package com.birdeye.social.model;

public class CompanyPageInfo {
	private Integer companyId;
	private String companyName;
	private String companyUrl;
	private String logoUrl;
	private Boolean isSelected = Boolean.FALSE;
	private Boolean disabled = Boolean.FALSE;
	
	public CompanyPageInfo() {
		
	}
	
	public CompanyPageInfo(CompanyPageInfo company) {
		super();
		this.companyId = company.getCompanyId();
		this.companyName = company.getCompanyName();
		this.companyUrl = company.getCompanyUrl();
		this.logoUrl = company.getLogoUrl();
		this.isSelected = company.getIsSelected();
		this.disabled = company.getDisabled();
	}
	public Integer getCompanyId() {
		return companyId;
	}
	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyUrl() {
		return companyUrl;
	}
	public void setCompanyUrl(String companyUrl) {
		this.companyUrl = companyUrl;
	}
	public String getLogoUrl() {
		return logoUrl;
	}
	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}
	public Boolean getIsSelected() {
		return isSelected;
	}
	public void setIsSelected(Boolean isSelected) {
		this.isSelected = isSelected;
	}
	public Boolean getDisabled() {
		return disabled;
	}
	public void setDisabled(Boolean disabled) {
		this.disabled = disabled;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("CompanyPageInfo [companyId=");
		builder.append(companyId);
		builder.append(", companyName=");
		builder.append(companyName);
		builder.append(", companyUrl=");
		builder.append(companyUrl);
		builder.append(", logoUrl=");
		builder.append(logoUrl);
		builder.append(", isSelected=");
		builder.append(isSelected);
		builder.append(", disabled=");
		builder.append(disabled);
		builder.append("]");
		return builder.toString();
	}
}
