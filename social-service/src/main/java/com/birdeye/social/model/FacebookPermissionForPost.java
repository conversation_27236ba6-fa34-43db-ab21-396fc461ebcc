package com.birdeye.social.model;


import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Getter
@Setter
public class FacebookPermissionForPost {

    public static final List<String> FACEBOOK_POST_PERMISSIONS =
            Collections.unmodifiableList(Arrays.asList("pages_read_user_content", "pages_read_engagement","pages_show_list","pages_manage_posts"));
}
