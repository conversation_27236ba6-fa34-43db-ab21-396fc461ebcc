package com.birdeye.social.model.tiktok;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@ToString
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TikTokCommentEventRequest {

    @JsonProperty("client_key")
    private String clientKey;

    private String event;

    @JsonProperty("create_time")
    private Long createTime;

    @JsonProperty("user_openid")
    private String userOpenid;

    private TikTokCommentContent content;

    @ToString
    @Getter
    @Setter
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TikTokCommentContent {

        @JsonProperty("comment_id")
        private String commentId;

        @JsonProperty("video_id")
        private String videoId;

        @JsonProperty("parent_comment_id")
        private String parentCommentId;

        @JsonProperty("comment_type")
        private String commentType;

        @JsonProperty("comment_action")
        private String commentAction;

        @JsonProperty("unique_identifier")
        private String uniqueIdentifier;

        @JsonProperty("timestamp")
        private Long timestamp;
    }
}