package com.birdeye.social.model.listen;

import com.birdeye.social.google.Attachment;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MediaInfo implements Serializable {

    private String mediaSequence; // instagram
    private YoutubeMedia youtubeMedia; // youtube
    private List<TwitterMedia> media; // twitter
    private List<Attachment> attachments; // google
    private LinkedinMedia linkedinMedia;// linkedin

}
