/**
 *
 *
 */
package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

/**
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
public class InstagramMessageRequest {
	
	private String	accountId;
	
	private String	receiverId;
	
	private String	message;
	
	private String	attachmentType;
	
	private String	url;

	private String type;

	private String socialFeedId;

	public String getSocialFeedId() {
		return socialFeedId;
	}

	public void setSocialFeedId(String socialFeedId) {
		this.socialFeedId = socialFeedId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	/**
	 * @return the receiverId
	 */
	public String getReceiverId() {
		return receiverId;
	}
	
	/**
	 * @param receiverId
	 *            the receiverId to set
	 */
	public void setReceiverId(String receiverId) {
		this.receiverId = receiverId;
	}
	
	/**
	 * @return the message
	 */
	public String getMessage() {
		return message;
	}
	
	/**
	 * @param message
	 *            the message to set
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	/**
	 * @return the attachmentType
	 */
	public String getAttachmentType() {
		return attachmentType;
	}
	
	/**
	 * @param attachmentType
	 *            the attachmentType to set
	 */
	public void setAttachmentType(String attachmentType) {
		this.attachmentType = attachmentType;
	}
	
	/**
	 * @return the url
	 */
	public String getUrl() {
		return url;
	}
	
	/**
	 * @param url
	 *            the url to set
	 */
	public void setUrl(String url) {
		this.url = url;
	}

	@Override
	public String toString() {
		return "InstagramMessageRequest{" +
				"accountId='" + accountId + '\'' +
				", receiverId='" + receiverId + '\'' +
				", message='" + message + '\'' +
				", attachmentType='" + attachmentType + '\'' +
				", url='" + url + '\'' +
				", type='" + type + '\'' +
				", socialPostId='" + socialFeedId + '\'' +
				'}';
	}
}
