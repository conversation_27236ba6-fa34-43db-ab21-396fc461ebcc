package com.birdeye.social.model;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown=true)
public class ChannelLocationInfo implements Serializable
{
	
	private static final long serialVersionUID = 2304487767647991789L;
	
	private Integer locationId;
	private String locationName;
	private String address;
	// this is used for mapped page
	private Map<String,LocationPageListInfo> pageData;
	/**
	 * @return the locationId
	 */
	public Integer getLocationId() {
		return locationId;
	}
	/**
	 * @param locationId the locationId to set
	 */
	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}
	/**
	 * @return the locationName
	 */
	public String getLocationName() {
		return locationName;
	}
	/**
	 * @param locationName the locationName to set
	 */
	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}
	/**
	 * @return the address
	 */
	public String getAddress() {
		return address;
	}
	/**
	 * @param address the address to set
	 */
	public void setAddress(String address) {
		this.address = address;
	}
	/**
	 * @return the pageData
	 */
	/*public LocationPageListInfo getPageData() {
		return pageData;
	}
	*//**
	 * @param pageData the pageData to set
	 *//*
	public void setPageData(LocationPageListInfo pageData) {
		this.pageData = pageData;
	}*/
	/**
	 * @return the pageData
	 */
	public Map<String, LocationPageListInfo> getPageData() {
		return pageData;
	}
	/**
	 * @param pageData the pageData to set
	 */
	public void setPageData(Map<String, LocationPageListInfo> pageData) {
		this.pageData = pageData;
	}

	@Override
	public String toString() {
		return "ChannelLocationInfo{" +
				"locationId=" + locationId +
				", locationName='" + locationName + '\'' +
				", address='" + address + '\'' +
				", pageData=" + pageData +
				'}';
	}
}
