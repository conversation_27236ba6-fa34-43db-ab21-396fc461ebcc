package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class CompetitorPostData {
    private String pageId;
    private String postId;
    private String postUrl;
    private String profilePictureUrl;
    private String channel;
    private String postText;
    private List<String> imageUrls;
    private List<String> videoUrls;
    private List<String> thumbnailUrls;
    private Integer likeCount = 0;
    private Integer commentCount = 0;
    private Integer shareCount = 0;
    private Integer engagement = 0;
    private Long publishedDate;
    private String userName;
    private String name;
    private List<MediaData> imageMetaData;
    private List<MediaData> videoMetaData;
    private List<MediaData> thumbnailMetaData;
    private Boolean isVerified;
    private Boolean isCompetitor;
}
