package com.birdeye.social.model;

import com.birdeye.social.facebook.PostInsightItem;
import com.birdeye.social.google.Activity;
import com.birdeye.social.youtube.YoutubeActivity;

public class GNIPActivity {
	private String source;
	private PostInsightItem post; //FB
	private Integer linkedRule;
	private GnipActivityDTO gnipAct;
	private Activity googleActivity; //Google
	private YoutubeActivity youtubeActivity; // Youtube
	
	private String activity;// Twitter support.
	
	public GNIPActivity() {
		
	}

	public GNIPActivity(String source, PostInsightItem post, Integer linkedRule, GnipActivityDTO gnipAct) {
		super();
		this.source = source;
		this.post = post;
		this.linkedRule = linkedRule;
		this.gnipAct = gnipAct;
	}

    public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}

	public PostInsightItem getPost() {
		return post;
	}
	public void setPost(PostInsightItem post) {
		this.post = post;
	}

	public Integer getLinkedRule() {
		return linkedRule;
	}
	public void setLinkedRule(Integer linkedRule) {
		this.linkedRule = linkedRule;
	}

	public GnipActivityDTO getGnipAct() {
		return gnipAct;
	}
	public void setGnipAct(GnipActivityDTO gnipAct) {
		this.gnipAct = gnipAct;
	}

	public Activity getGoogleActivity() {
		return googleActivity;
	}

	public void setGoogleActivity(Activity googleActivity) {
		this.googleActivity = googleActivity;
	}

	/**
	 * @return the youtubeActivity
	 */
	public YoutubeActivity getYoutubeActivity() {
		return youtubeActivity;
	}

	/**
	 * @param youtubeActivity the youtubeActivity to set
	 */
	public void setYoutubeActivity(YoutubeActivity youtubeActivity) {
		this.youtubeActivity = youtubeActivity;
	}
	
	public String getActivity(){
		return activity;
	}
	
	public void setActivity(String activity){
		this.activity=activity;
	}
}
