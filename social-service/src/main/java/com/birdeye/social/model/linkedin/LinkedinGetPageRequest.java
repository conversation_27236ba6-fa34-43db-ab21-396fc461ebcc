package com.birdeye.social.model.linkedin;

import com.birdeye.social.sro.SocialRequest;

public class LinkedinGetPageRequest extends SocialRequest {

    private String channel;
    private Long enterpriseId;
    private Integer userId;
    private String type = "all";

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
