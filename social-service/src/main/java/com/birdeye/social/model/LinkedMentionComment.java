package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@ToString
public class LinkedMentionComment {
    public String entity;
    public String owner;
    public String object;
    public String text;
    public String parentComment;
    public Integer likeCount;
    public Integer commentCount;
}
