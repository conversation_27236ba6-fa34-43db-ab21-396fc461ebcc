/**
 * 
 */
package com.birdeye.social.model;

import java.io.Serializable;
import java.util.Date;

import com.birdeye.social.platform.entities.GnipActivity;

/**
 * <AUTHOR>
 *
 */
public class GnipActivityDTO implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = -3058215234269348493L;
	private Integer id;
    private String activityId;
    private Integer sourceId;
    private Date createdAt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    
    public GnipActivityDTO() {
    	
    }
    
    public GnipActivityDTO(GnipActivity gactivity) {
    		this.id = gactivity.getId();
    		this.activityId = gactivity.getActivityId();
    		this.createdAt = gactivity.getCreatedAt();
    		this.sourceId = gactivity.getSourceId();
    }
}
