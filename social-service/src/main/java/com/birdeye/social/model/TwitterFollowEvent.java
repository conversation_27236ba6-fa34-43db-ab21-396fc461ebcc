package com.birdeye.social.model;

import java.util.*;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TwitterFollowEvent implements Serializable {
    private static final long serialVersionUID = 1L;

    private String type;
    private Long created_timestamp;
    private TwitterUser target;
    private TwitterUser source;
}