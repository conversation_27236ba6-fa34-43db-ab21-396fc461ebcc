package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApprovalSteps {
    private Integer id;
    private String stepName;
    private String stepOrder;
    private String condition;
    private String status;
    private List<Object> approvers;
}
