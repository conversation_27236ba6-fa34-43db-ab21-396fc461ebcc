package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
@Getter
@Setter
@ToString
public class FailedPostFilter implements Serializable {

    private static final long serialVersionUID = 2792646741766042362L;
    private List<String> socialChannels;
    private List<Integer> businessIds;
    private List<Integer> accessibleLocationIds;
    private Integer size;
    private Integer pageNo;
    private String sortOrder;
    private Set<Long> tagIds;
}
