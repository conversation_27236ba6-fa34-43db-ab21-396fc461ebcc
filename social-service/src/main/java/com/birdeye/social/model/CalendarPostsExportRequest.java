package com.birdeye.social.model;

import com.birdeye.social.constant.FilterPostStatuses;
import com.birdeye.social.constant.FilterPostType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;
import java.util.Set;

@ToString
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CalendarPostsExportRequest {
    private Long startDate;
    private Long endDate;
    private String postStatus;
    private List<String> socialChannels;
    private Integer businessId;
    private List<Integer> businessIds;
    private List<Integer> accessibleLocationIds;
    private Set<Long> tagIds;
    private List<Integer> approvals;
    private List<Integer> creators;
    private String reportType = "";

    private List<FilterPostType> postType;
    private List<FilterPostType> postContent;
    private List<FilterPostStatuses> postStatuses;

    private Integer pageNo;
    private Integer pageSize;
    private String order;

    private Boolean businessIdsSelected;
    private Boolean showAiSuggestions;
    private Boolean hasFullAccess = false;

    private Map<String, Object> filtersSelected;

    private String userTimezoneId;
}
