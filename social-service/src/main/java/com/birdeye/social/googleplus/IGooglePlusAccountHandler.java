package com.birdeye.social.googleplus;

import java.io.IOException;

import com.birdeye.social.model.MappingResponse;

/**
 *
 * <AUTHOR>
 */
public interface IGooglePlusAccountHandler {

	void getPagesToCreateDump(Integer refreshTokenId) throws IOException;

	/**
	 * Map google page to a location.
	 * 
	 * @param plusPageId
	 * @param businessId
	 * @param refreshTokenId
	 * @return
	 */
	MappingResponse mapGooglePlusPage(String plusPageId, Integer businessId, Integer refreshTokenId);

}
