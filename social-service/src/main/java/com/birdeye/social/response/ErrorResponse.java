/**
 *
 *
 */
package com.birdeye.social.response;

import java.io.Serializable;

import com.birdeye.social.exception.ErrorCodes;

/**
 * <AUTHOR>
 *
 */
public class ErrorResponse implements Serializable {
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -4328483239089469691L;
	
	private ErrorCodes			errorCode;
	private String				errorMessage;
	
	/**
	 * @return the errorCode
	 */
	public ErrorCodes getErrorCode() {
		return errorCode;
	}
	
	/**
	 * @param errorCode
	 *            the errorCode to set
	 */
	public void setErrorCode(ErrorCodes errorCode) {
		this.errorCode = errorCode;
	}
	
	/**
	 * @return the errorMessage
	 */
	public String getErrorMessage() {
		return errorMessage;
	}
	
	/**
	 * @param errorMessage
	 *            the errorMessage to set
	 */
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public ErrorResponse(ErrorCodes errorCode, String errorMessage) {
		this.errorCode = errorCode;
		this.errorMessage = errorMessage;
	}
	
	/* (non-Javadoc)
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return "ErrorResponse [errorCode=" + errorCode + ", errorMessage=" + errorMessage + "]";
	}
}
