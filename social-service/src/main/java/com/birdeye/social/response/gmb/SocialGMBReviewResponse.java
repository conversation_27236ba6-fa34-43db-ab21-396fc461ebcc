/**
 *
 */
package com.birdeye.social.response.gmb;

import java.util.List;

import com.birdeye.social.response.SocialReviewBaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 ** File: SocialGMBReviewResponse.java
 ** Created: 13 Jul 2018
 ** Author: sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialGMBReviewResponse extends SocialReviewBaseResponse {
	
	private static final long	serialVersionUID	= 7956498253227312604L;
	private List<Object>		reviews;
	private String averageRating;
	private String totalReviewCount;
	private String nextPageToken;
	
	public List<Object> getReviews() {
		return reviews;
	}
	
	public void setReviews(List<Object> reviews) {
		this.reviews = reviews;
	}
	
	/**
	 * @return the averageRating
	 */
	public String getAverageRating() {
		return averageRating;
	}

	/**
	 * @param averageRating the averageRating to set
	 */
	public void setAverageRating(String averageRating) {
		this.averageRating = averageRating;
	}

	/**
	 * @return the totalReviewCount
	 */
	public String getTotalReviewCount() {
		return totalReviewCount;
	}

	/**
	 * @param totalReviewCount the totalReviewCount to set
	 */
	public void setTotalReviewCount(String totalReviewCount) {
		this.totalReviewCount = totalReviewCount;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("SocialGMBReviewResponse [reviews=");
		builder.append(reviews);
		builder.append(", averageRating=");
		builder.append(averageRating);
		builder.append(", totalReviewCount=").append(totalReviewCount).append("]");
		return builder.toString();
	}

	/**
	 * @return the nextPageToken
	 */
	public String getNextPageToken() {
		return nextPageToken;
	}

	/**
	 * @param nextPageToken the nextPageToken to set
	 */
	public void setNextPageToken(String nextPageToken) {
		this.nextPageToken = nextPageToken;
	}
	
}
