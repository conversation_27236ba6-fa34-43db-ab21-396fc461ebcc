package com.birdeye.social.sro;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class AppleUpdateLocationStateRequest {

    private String appleCompanyId;
    @NotEmpty
    private String appleBusinessId;
    @NotNull
    private String appleLocationId;
    @NotNull
    private String appleLocationState;
    private Integer birdeyeBusinessId;
}
