/**
 * 
 */
package com.birdeye.social.sro;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.birdeye.social.constant.GMBLocationJobStatus;

/**
 * <AUTHOR>
 *
 */

public class GMBLocationRequestDTO implements Serializable {

	private static final long serialVersionUID = -8753658016142218573L;

	private String	accountId;
	
	private String	locationId;
	
	private String	locationState;
	
	private String	serviceArea;

	private Integer	isSelected;
	
	private Integer	isValid;
	
	private Integer	refreshTokenId;

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getLocationState() {
		return locationState;
	}

	public void setLocationState(String locationState) {
		this.locationState = locationState;
	}

	public String getServiceArea() {
		return serviceArea;
	}

	public void setServiceArea(String serviceArea) {
		this.serviceArea = serviceArea;
	}

	public Integer getIsSelected() {
		return isSelected;
	}

	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Integer getRefreshTokenId() {
		return refreshTokenId;
	}

	public void setRefreshTokenId(Integer refreshTokenId) {
		this.refreshTokenId = refreshTokenId;
	}

	@Override
	public String toString() {
		return "GMBLocationRequestDTO [accountId=" + accountId + ", locationId=" + locationId + ", locationState="
				+ locationState + ", serviceArea=" + serviceArea + ", isSelected=" + isSelected + ", isValid=" + isValid
				+ ", refreshTokenId=" + refreshTokenId + "]";
	}
	
	

}
