/**
 * 
 */
package com.birdeye.social.sro;

import java.io.Serializable;

import com.birdeye.social.constant.AppleInsightReportTypesEnum;
import com.birdeye.social.constant.Constants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class AppleInsightsConnectRequest implements Serializable {
	private static final long serialVersionUID = 1L;
	
	@JsonIgnore
	private String companyId;

	@Builder.Default
	private String resourceType = Constants.APPLE_REPORT_RESOURCE_TYPE;

	private String resourceId;

	private AppleInsightReportTypesEnum reportName;

	private String startDate;

	private String endDate;

	private boolean useLocalTime;

	private String timeGranularity;

}
