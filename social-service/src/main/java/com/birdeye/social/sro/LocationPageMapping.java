/**
 * 
 */
package com.birdeye.social.sro;

import java.util.List;

import com.birdeye.social.model.ChannelLocationInfo;

/**
 * <AUTHOR>
 *
 */
public class LocationPageMapping extends SocialResponse {

	private static final long serialVersionUID = -6907633152029035057L;
	
	private Integer unmapped;
	private Integer totalLocations;
	private List<ChannelLocationInfo> locationList;
	private Integer disconnectedCount;
	private Boolean allPagesMapped;
	private Integer permissionIssuePageCount;
	private Integer totalCount;

	public Integer getPermissionIssuePageCount() {
		return permissionIssuePageCount;
	}

	public void setPermissionIssuePageCount(Integer permissionIssuePageCount) {
		this.permissionIssuePageCount = permissionIssuePageCount;
	}

	public Boolean getAllPagesMapped() {
		return allPagesMapped;
	}

	public void setAllPagesMapped(Boolean allPagesMapped) {
		this.allPagesMapped = allPagesMapped;
	}

	public Integer getDisconnectedCount() {
		return disconnectedCount;
	}

	public void setDisconnectedCount(Integer disconnectedCount) {
		this.disconnectedCount = disconnectedCount;
	}

	/**
	 * @return the unmapped
	 */
	public Integer getUnmapped() {
		return unmapped;
	}
	/**
	 * @param unmapped the unmapped to set
	 */
	public void setUnmapped(Integer unmapped) {
		this.unmapped = unmapped;
	}
	/**
	 * @return the totalLocations
	 */
	public Integer getTotalLocations() {
		return totalLocations;
	}
	/**
	 * @param totalLocations the totalLocations to set
	 */
	public void setTotalLocations(Integer totalLocations) {
		this.totalLocations = totalLocations;
	}
	/**
	 * @return the locationList
	 */
	public List<ChannelLocationInfo> getLocationList() {
		return locationList;
	}
	/**
	 * @param locationList the locationList to set
	 */
	public void setLocationList(List<ChannelLocationInfo> locationList) {
		this.locationList = locationList;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	@Override
	public String toString() {
		return "LocationPageMapping [unmapped=" + unmapped + ", totalLocations=" + totalLocations + ", locationList="
				+ locationList + ", disconnectedCount=" + disconnectedCount + ", allPagesMapped=" + allPagesMapped
				+ ", permissionIssuePageCount=" + permissionIssuePageCount + ", totalCount=" + totalCount + "]";
	}
	
}