package com.birdeye.social.sro.socialReseller;

import lombok.ToString;

import javax.validation.constraints.NotNull;

@ToString
public class SocialResellerDoupRequestDTO {
    @NotNull
    private String channel;
    @NotNull
    private Long resellerId;

    private String processIdentifier;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Long getResellerId() {
        return resellerId;
    }

    public void setResellerId(Long resellerId) {
        this.resellerId = resellerId;
    }

    public String getProcessIdentifier() {
        return processIdentifier;
    }

    public void setProcessIdentifier(String processIdentifier) {
        this.processIdentifier = processIdentifier;
    }
}
