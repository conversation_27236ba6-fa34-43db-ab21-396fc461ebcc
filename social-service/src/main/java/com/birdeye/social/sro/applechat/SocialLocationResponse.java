package com.birdeye.social.sro.applechat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialLocationResponse implements Serializable {

    List<AppleDoupDTO> data;

    public List<AppleDoupDTO> getData() {
        return data;
    }

    public void setData(List<AppleDoupDTO> data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "SocialLocationResponse{" +
                "data=" + data +
                '}';
    }
}
