package com.birdeye.social.sro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 30/11/23
 */

@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppleShowcaseCreativeDTO implements Serializable {

    private static final long serialVersionUID = 7960039570782981340L;


    private String companyId;
    private String businessId;
    private String id;
    private String createdDate;
    private String updatedDate;
    private String state;
    private String etag;
    private ShowcaseCreativeDetails showcaseCreativeDetails;


    @Data
    @Builder
    @Slf4j
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ShowcaseCreativeDetails implements Serializable{

        private static final long serialVersionUID = 1430479656473243406L;

        private List<Content> contents;
        private String callToAction;
        private Photo photo;


        @Data
        @Builder
        @Slf4j
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Content implements Serializable {

            private static final long serialVersionUID = 5687852028845163245L;

            private String placement;
            private List<Description> descriptions;


            @Data
            @Builder
            @Slf4j
            @NoArgsConstructor
            @AllArgsConstructor
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class Description implements Serializable {

                private static final long serialVersionUID = -7606188005624093017L;

                private String locale;
                private String text;

            }
        }

        @Data
        @Builder
        @Slf4j
        @NoArgsConstructor
        @AllArgsConstructor
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Photo implements Serializable{

            private static final long serialVersionUID = 8613290302789839332L;

            private String id;
            private List<Caption> captions;

            @Data
            @Builder
            @Slf4j
            @NoArgsConstructor
            @AllArgsConstructor
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class Caption implements Serializable{

                private static final long serialVersionUID = -8958229915319276848L;

                private String altText;
                private String locale;

            }
        }
    }
}
