package com.birdeye.social.sro;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleExistingLocationRequest {
    @NonNull
    private Long beEnterpriseId;
    private List<BusinessList> businessList;

    @Getter
    @Setter
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BusinessList {
        private String businessName;
        private String companyId;
        private String businessId;
    }
}
