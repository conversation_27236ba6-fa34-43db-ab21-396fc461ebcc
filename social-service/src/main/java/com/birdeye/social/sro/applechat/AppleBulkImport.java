package com.birdeye.social.sro.applechat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleBulkImport implements Serializable {

    private long eventId;
    private DoupMetaData metaData;
    private AppleDoupDTO data;

    public long getEventId() {
        return eventId;
    }

    public void setEventId(long eventId) {
        this.eventId = eventId;
    }

    public DoupMetaData getMetaData() {
        return metaData;
    }

    public void setMetaData(DoupMetaData metaData) {
        this.metaData = metaData;
    }

    public AppleDoupDTO getData() {
        return data;
    }

    public void setData(AppleDoupDTO data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "AppleBulkImport{" +
                "eventId=" + eventId +
                ", metaData=" + metaData +
                ", data=" + data +
                '}';
    }
}
