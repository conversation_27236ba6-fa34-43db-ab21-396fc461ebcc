/**
 * 
 */
package com.birdeye.social.sro;

import java.io.Serializable;
import java.util.List;

import com.birdeye.social.constant.AppleInsightReportTypesEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = Include.NON_NULL)
public class AppleInsightsConnectResponse implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String companyId;

	private Object request;

	private Object meta;

	private AppleInsightsData data;

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class AppleInsightsData implements Serializable {
		private static final long serialVersionUID = 1L;

		private List<AppleInsightTuple> tuples;

	}

	@Data
	@AllArgsConstructor
	@NoArgsConstructor
	@JsonIgnoreProperties(ignoreUnknown = true)
	@JsonInclude(value = Include.NON_NULL)
	public static class AppleInsightTuple implements Serializable {
		private static final long serialVersionUID = 1L;
		
		private List<String> dimensions;

		private List<String> metrics;

	}

}
