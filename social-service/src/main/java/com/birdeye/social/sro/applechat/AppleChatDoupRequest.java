package com.birdeye.social.sro.applechat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleChatDoupRequest implements Serializable {

    @NotNull
    Long businessNumber;

    public Long getBusinessNumber() {
        return businessNumber;
    }

    public void setBusinessNumber(Long businessNumber) {
        this.businessNumber = businessNumber;
    }

    @Override
    public String toString() {
        return "AppleChatDoupRequest{" +
                "businessNumber=" + businessNumber +
                '}';
    }
}
