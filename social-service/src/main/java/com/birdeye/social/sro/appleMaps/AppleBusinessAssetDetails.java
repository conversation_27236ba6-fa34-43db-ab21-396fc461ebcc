package com.birdeye.social.sro.appleMaps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppleBusinessAssetDetails implements Serializable {

    private static final long serialVersionUID = 3783783983L;

    private String imageId;
    private String partnersAssetId;
    private String intent;

}
