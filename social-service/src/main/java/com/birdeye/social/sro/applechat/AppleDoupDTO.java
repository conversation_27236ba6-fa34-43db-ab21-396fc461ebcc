package com.birdeye.social.sro.applechat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AppleDoupDTO implements Serializable {

    Long locationId;
    String businessLocationName;
    String appleLocationName;
    String appleLocationAddress;
    String intentId;
    String mapUrl;

    public Long getLocationId() {
        return locationId;
    }

    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    public String getBusinessLocationName() {
        return businessLocationName;
    }

    public void setBusinessLocationName(String businessLocationName) {
        this.businessLocationName = businessLocationName;
    }

    public String getAppleLocationName() {
        return appleLocationName;
    }

    public void setAppleLocationName(String appleLocationName) {
        this.appleLocationName = appleLocationName;
    }

    public String getAppleLocationAddress() {
        return appleLocationAddress;
    }

    public void setAppleLocationAddress(String appleLocationAddress) {
        this.appleLocationAddress = appleLocationAddress;
    }

    public String getIntentId() {
        return intentId;
    }

    public void setIntentId(String intentId) {
        this.intentId = intentId;
    }

    public String getMapUrl() {
        return mapUrl;
    }

    public void setMapUrl(String mapUrl) {
        this.mapUrl = mapUrl;
    }

    @Override
    public String toString() {
        return "AppleDoupDTO{" +
                "enterpriseId=" + locationId +
                ", businessLocationName='" + businessLocationName + '\'' +
                ", appleLocationName='" + appleLocationName + '\'' +
                ", appleLocationAddress='" + appleLocationAddress + '\'' +
                ", intentId='" + intentId + '\'' +
                ", mapUrl='" + mapUrl + '\'' +
                '}';
    }
}
