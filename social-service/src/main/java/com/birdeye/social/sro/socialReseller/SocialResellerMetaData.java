package com.birdeye.social.sro.socialReseller;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialResellerMetaData {
    private long fileId;
    private String fileName;
    private long createdAt;
    private long userId;

    //business id
    private Integer accountId;

    // enterpriseId
    private long businessId;

    private SocialResellerMetaDataExtraParamsDTO extraParams;

    private String businessType;

    public long getFileId() {
        return fileId;
    }

    public void setFileId(long fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(long businessId) {
        this.businessId = businessId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public SocialResellerMetaDataExtraParamsDTO getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(SocialResellerMetaDataExtraParamsDTO extraParams) {
        this.extraParams = extraParams;
    }

    @Override
    public String toString() {
        return "SocialResellerMetaData{" +
                "fileId=" + fileId +
                ", fileName='" + fileName + '\'' +
                ", createdAt=" + createdAt +
                ", userId=" + userId +
                ", accountId=" + accountId +
                ", businessId=" + businessId +
                ", extraParams=" + extraParams +
                ", businessType='" + businessType + '\'' +
                '}';
    }
}
