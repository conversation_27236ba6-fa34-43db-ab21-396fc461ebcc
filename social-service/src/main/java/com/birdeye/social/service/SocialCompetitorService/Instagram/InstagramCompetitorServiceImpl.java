package com.birdeye.social.service.SocialCompetitorService.Instagram;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialReservedAccountsRepo;
import com.birdeye.social.dao.competitor.CompetitorPostsRepo;
import com.birdeye.social.dao.competitor.CompetitorProfileStatusInfoRepo;
import com.birdeye.social.dao.competitor.InstagramCompetitorInfoRepo;
import com.birdeye.social.dao.competitor.InstagramCompetitorMappingRepo;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.MediaAsset;
import com.birdeye.social.entities.SocialReservedAccounts;
import com.birdeye.social.entities.competitor.CompetitorPosts;
import com.birdeye.social.entities.competitor.CompetitorProfileStatusInfo;
import com.birdeye.social.entities.competitor.InstagramCompetitorInfo;
import com.birdeye.social.entities.competitor.InstagramCompetitorMapping;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.instagram.response.*;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.service.MediaAssetRepoService;
import com.birdeye.social.service.SocialCompetitorService.SocialCompetitorHelperService;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InstagramCompetitorServiceImpl implements InstagramCompetitorService{

    @Autowired
    private InstagramCompetitorInfoRepo competitorInfoRepo;
    @Autowired
    private InstagramCompetitorMappingRepo competitorMappingRepo;
    @Autowired
    private CompetitorPostsRepo competitorPostsRepo;
    @Autowired
    private KafkaProducerService kafkaService;
    @Autowired
    private IInstagramService instagramService;
    @Autowired
    private SocialReservedAccountsRepo reservedAccountsRepo;
    @Autowired
    private CompetitorProfileStatusInfoRepo competitorProfileStatusInfoRepo;

    @Autowired
    private SocialCompetitorHelperService socialCompetitorHelperService;

    @Autowired
    private MediaAssetRepoService mediaAssetRepoService;

    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;

    private static final Logger LOG = LoggerFactory.getLogger(InstagramCompetitorServiceImpl.class);



    private String getInsightData(InstagramTimelineResponse response) {
        CompetitorInsightDTO competitorInsightDTO = CompetitorInsightDTO.builder()
                .likeCount(response.getLike_count())
                .commentCount(response.getComments_count())
                .build();

        return JSONUtils.toJSON(competitorInsightDTO);
    }

    private CompetitorPosts convertToCompetitorPost(InstagramTimelineResponse data, CompetitorRequestDTO competitorRequestDTO) {
        List<String> imageUrls = new ArrayList<>();
        List<String> videoUrls = new ArrayList<>();
        if("IMAGE".equals(data.getMedia_type())) {
            imageUrls.add(data.getMedia_url());
        } else if("VIDEO".equals(data.getMedia_type())) {
            videoUrls.add(data.getMedia_url());
        } else if("CAROUSEL_ALBUM".equals(data.getMedia_type()) && Objects.nonNull(data.getChildren())){
            InstagramChildrenData childrenData = data.getChildren();
            if(CollectionUtils.isNotEmpty(childrenData.getData())) {
                childrenData.getData().forEach(childrenMedia -> {
                    if("IMAGE".equals(childrenMedia.getMedia_type())) {
                        imageUrls.add(childrenMedia.getMedia_url());
                    } else if("VIDEO".equals(childrenMedia.getMedia_type())) {
                        videoUrls.add(childrenMedia.getMedia_url());
                    }
                });
            }
        }

        return CompetitorPosts.builder()
                .rawCompetitorId(competitorRequestDTO.getRawId())
                .postId(data.getId())
                .sourceId(competitorRequestDTO.getSourceId())
                .postUrl(data.getPermalink())
                .postText(data.getCaption())
                .imageUrls(imageUrls)
                .videoUrls(videoUrls)
                .userName(competitorRequestDTO.getUserName())
                .publishDate(data.getTimestamp())
                .insightData(getInsightData(data))
                .build();
    }

    // TODO: add dedupe for competitorPosts and use pagination on IG api istead of getting all 500 at once | cache result of SocialReservedAccounts
    @Override
    public void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) {
        LOG.info("[IG competitor posts] started processing for IG username: {}",competitorRequestDTO.getUserName());
        String igAccountId = null;
        String accessToken = null;
        Pair<String, String> pageAndTokenPair = getAccessToken(competitorRequestDTO);
        if(Objects.nonNull(pageAndTokenPair)) {
            igAccountId = pageAndTokenPair.getKey();
            accessToken = pageAndTokenPair.getValue();
        }
        InstagramCompetitorProfile profileResponse = null;
        CompetitorProfileStatusEnum status = CompetitorProfileStatusEnum.FETCHING_MEDIA_DATA;
        Integer daysSub = 0;
        String nextToken = null;
        if(Objects.nonNull(competitorRequestDTO) && Objects.nonNull(competitorRequestDTO.getIsDailyScan()) && competitorRequestDTO.getIsDailyScan()) {
            daysSub = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getInstagramCompPostLimitDayForJob();
        } else {
            daysSub = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getInstagramCompPostLimitDay();
        }
        Date thresholdDate = DateUtils.addDays(new Date(), 0-daysSub);
        competitorProfileStatusInfoRepo.updateStatus(competitorRequestDTO.getUserName(), status);
        do {
            try {
                if (StringUtils.isNotEmpty(igAccountId) && StringUtils.isNotEmpty(accessToken)) {
                    profileResponse = instagramService.getCompetitorProfileData(igAccountId, accessToken, competitorRequestDTO.getUserName(), true, nextToken);
                }
                if (Objects.nonNull(profileResponse) && Objects.nonNull(profileResponse.getBusiness_discovery()) &&
                        Objects.nonNull(profileResponse.getBusiness_discovery().getMedia()) && CollectionUtils.isNotEmpty(profileResponse.getBusiness_discovery().getMedia().getData())) {

                    InstagramTimelineResponseList timelineResponseList = profileResponse.getBusiness_discovery().getMedia();
                    boolean ifStopFetch = stopFetchingPosts(timelineResponseList, thresholdDate);
                    if(CollectionUtils.isNotEmpty(timelineResponseList.getData())) {
                        List<CompetitorPosts> competitorPostsList = new ArrayList<>();
                        timelineResponseList.getData().forEach(data -> competitorPostsList.add(convertToCompetitorPost(data, competitorRequestDTO)));
                        upsertCompetitorEntries(competitorPostsList, competitorRequestDTO.getSourceId());
                    } else {
                        break;
                    }

                    if(ifStopFetch) {
                        LOG.info("threshold reached breaking the loop");
                        break;
                    }
                    status = CompetitorProfileStatusEnum.SUCCESS;
                    if(Objects.nonNull(timelineResponseList.getPaging()) &&
                            Objects.nonNull(timelineResponseList.getPaging().getCursors()) &&
                            StringUtils.isNotEmpty(timelineResponseList.getPaging().getCursors().getAfter())) {
                        nextToken = timelineResponseList.getPaging().getCursors().getAfter();
                    } else {
                        nextToken = null;
                    }
                } else {
                    status = CompetitorProfileStatusEnum.MEDIA_DATA_NOT_FOUND;
                    LOG.info("No post data found for user: {}", competitorRequestDTO.getUserName());
                    nextToken = null;
                }
            }catch(Exception e) {
                status = CompetitorProfileStatusEnum.MEDIA_FETCH_FAILED;
                LOG.error("Exception while fetching posts for user: {}", competitorRequestDTO.getUserName(), e);
                nextToken = null;
            }
        } while (StringUtils.isNotEmpty(nextToken));
        competitorProfileStatusInfoRepo.updateStatus(competitorRequestDTO.getUserName(), status);
        updateCompPageScannedOnce(competitorRequestDTO);
    }

    private Pair<String, String> getAccessToken(CompetitorRequestDTO competitorRequestDTO) {
        if(Objects.isNull(competitorRequestDTO)) return null;

        String accessToken = null;
        String igPageId = null;
        if(Objects.nonNull(competitorRequestDTO.getIsDailyScan()) && competitorRequestDTO.getIsDailyScan()) {
            List<InstagramCompetitorMapping> instagramCompetitorMappingList =  competitorMappingRepo.findByRawCompetitorId(competitorRequestDTO.getRawId());

            if(CollectionUtils.isNotEmpty(instagramCompetitorMappingList)) {
                List<Long> businessNumberList = instagramCompetitorMappingList.stream().map(InstagramCompetitorMapping::getEnterpriseId).collect(Collectors.toList());
                List<BusinessInstagramAccount> instagramAccounts =
                        businessInstagramAccountRepository.findByEnterpriseIdInAndIsValidAndBusinessIdIsNotNull(businessNumberList, ValidTypeEnum.VALID.getId());
                if(CollectionUtils.isNotEmpty(instagramAccounts)) {
                    accessToken = instagramAccounts.get(0).getPageAccessToken();
                    igPageId = instagramAccounts.get(0).getInstagramAccountId();
                }
            }
        } else {
            List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceId(SocialChannel.INSTAGRAM.getId());
            if(CollectionUtils.isNotEmpty(reservedAccounts)) {
                SocialReservedAccounts socialReservedAccount = reservedAccounts.get(0);
                igPageId = socialReservedAccount.getPageId();
                accessToken = socialReservedAccount.getAccessToken();
            }
        }
        return new Pair<>(igPageId, accessToken);
    }

    private void updateCompPageScannedOnce(CompetitorRequestDTO competitorRequestDTO) {
        competitorInfoRepo.updateScannedOnce(1, competitorRequestDTO.getRawId());
    }

    private boolean stopFetchingPosts(InstagramTimelineResponseList media, Date thresholdDate) {
        try {
            List<InstagramTimelineResponse> instagramTimelineResponseList = media.getData();
            if(CollectionUtils.isEmpty(instagramTimelineResponseList)) return true;
            int i = 0;
            while(i < instagramTimelineResponseList.size()) {
                InstagramTimelineResponse competitorPosts = instagramTimelineResponseList.get(i);
                if(thresholdDate.after(competitorPosts.getTimestamp())) {
                    break;
                }
                i++;
            }
            if(i == instagramTimelineResponseList.size()) return false;
            else {
                media.setData(instagramTimelineResponseList.subList(0, i));
                return true;
            }
        } catch (Exception e) {
            LOG.info("exception occurred while checking fetch posts stop: {}", e.getMessage());
            return true;
        }
    }

    private void upsertCompetitorEntries(List<CompetitorPosts> competitorPostsList, Integer sourceId) {
        if(CollectionUtils.isEmpty(competitorPostsList)) return;
        List<String> postIds = competitorPostsList.stream().map(CompetitorPosts::getPostId).collect(Collectors.toList());
        List<CompetitorPosts> competitorPostsInDB = competitorPostsRepo.findByPostIdInAndSourceId(postIds, sourceId);
        Map<String, CompetitorPosts> postIdVsCompPosts = new HashMap<>();
        if(CollectionUtils.isNotEmpty(competitorPostsInDB)) {
            postIdVsCompPosts = competitorPostsInDB.stream().collect(Collectors.toMap(s->s.getPostId(), s->s));
        }
        List<CompetitorPosts> competitorPostsUpdatedList = new ArrayList<>();
        for(CompetitorPosts competitorPosts: competitorPostsList) {
            if(postIdVsCompPosts.containsKey(competitorPosts.getPostId())) {
                CompetitorPosts competitorPostsFromDB = postIdVsCompPosts.get(competitorPosts.getPostId());
                competitorPosts.setId(competitorPostsFromDB.getId());
            }
            competitorPosts.setMetaData(socialCompetitorHelperService.getCompetitorPostMetaData(competitorPosts));
            competitorPostsUpdatedList.add(competitorPosts);
        }
        competitorPostsRepo.save(competitorPostsUpdatedList);
        competitorPostsRepo.flush();

        if(CollectionUtils.isNotEmpty(competitorPostsUpdatedList)) {
            competitorPostsUpdatedList.forEach(competitorEntity -> {
                socialCompetitorHelperService.sendEsSyncEvent(competitorEntity);
                socialCompetitorHelperService.sendEventToCallPicturesQueue(competitorEntity.getId(), MediaAssetEntityType.COMPETITOR_POST, null, null);
            });
        }
    }

    private InstagramCompetitorInfo createProfileInfoEntity(BusinessDiscovery profileData, String username) {
        return InstagramCompetitorInfo.builder()
                .fullName(StringUtils.isEmpty(profileData.getName()) ? username : profileData.getName())
                .pageId(profileData.getId())
                .userName(username)
                .pageUrl("https://www.instagram.com/"+username)
                .profilePictureUrl(profileData.getProfile_picture_url())
                .description(profileData.getBiography())
                .scannedOnce(0)
                .build();
    }

    private void pushDtoForFetchingPosts(InstagramCompetitorInfo instagramCompetitorInfo) {
        CompetitorRequestDTO competitorRequestDTO = CompetitorRequestDTO.builder()
                .rawId(instagramCompetitorInfo.getId())
                .sourceId(SocialChannel.INSTAGRAM.getId())
                .pageId(instagramCompetitorInfo.getPageId())
                .userName(instagramCompetitorInfo.getUserName())
                .build();
        kafkaService.sendObjectV1(KafkaTopicEnum.INSTAGRAM_FETCH_COMP_POSTS.getName(), competitorRequestDTO);
    }

    private InstagramCompetitorMapping createCompMappingEntity(InstagramCompetitorInfo instagramCompetitorInfo, Long enterpriseId) {
        return InstagramCompetitorMapping.builder()
                .competitorId(instagramCompetitorInfo.getPageId())
                .enterpriseId(enterpriseId)
                .rawCompetitorId(instagramCompetitorInfo.getId())
                .build();
    }

    // TODO: add dedupe for competitorInfo, competitorMapping and competitorPostStatusInfo table
    @Override
    public void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) {
        LOG.info("Processing IG account info for competitors: {}",accountIdentifier.size());
        String igAccountId = null;
        String accessToken = null;
        List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceId(SocialChannel.INSTAGRAM.getId());
        if(CollectionUtils.isNotEmpty(reservedAccounts)) {
            SocialReservedAccounts socialReservedAccount = reservedAccounts.get(0);
            igAccountId = socialReservedAccount.getPageId();
            accessToken = socialReservedAccount.getAccessToken();
        }
        if(StringUtils.isEmpty(igAccountId) && StringUtils.isEmpty(accessToken)) {
            LOG.info("Not able to get igAccountId and accessToken from SocialReservedAccounts, exiting the flow");
            return;
        }
        String igAccountIdFinal = igAccountId;
        String accessTokenFinal = accessToken;
        accountIdentifier.forEach(userName -> {
            CompetitorProfileStatusEnum status = CompetitorProfileStatusEnum.INIT;
            competitorProfileStatusInfoRepo.save(new CompetitorProfileStatusInfo(SocialChannelIcons.INSTAGRAM.getId(), userName, status));
            try {
                LOG.info("Processing IG account with userName: {}",userName);
                InstagramCompetitorProfile profileResponse = instagramService.getCompetitorProfileData(igAccountIdFinal,accessTokenFinal,userName,false, null);
                if(Objects.nonNull(profileResponse) && Objects.nonNull(profileResponse.getBusiness_discovery())) {
                    InstagramCompetitorInfo instagramCompetitorInfo = createProfileInfoEntity(profileResponse.getBusiness_discovery(),userName);
                    createAndSaveCompetitorInfoAndMapping(instagramCompetitorInfo, enterpriseId);
                    pushDtoForFetchingPosts(instagramCompetitorInfo);
                    socialCompetitorHelperService.sendEventToCallPicturesQueue(instagramCompetitorInfo.getId(), MediaAssetEntityType.COMPETITOR_PAGE, SocialChannel.INSTAGRAM, null);
                    status = CompetitorProfileStatusEnum.PROFILE_DATA_FETCHED;
                    LOG.info("Profile data fetched for user: {}", userName);
                } else {
                    status = CompetitorProfileStatusEnum.PROFILE_DATA_NOT_FOUND;
                    LOG.info("Profile data not found for user: {}", userName);
                }
            } catch (Exception e) {
                status = CompetitorProfileStatusEnum.PROFILE_FETCH_FAILED;
                LOG.info("Exception while getting account info for IG account: {}",userName, e);
                throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
            }finally {
                competitorProfileStatusInfoRepo.updateStatus(userName, status);
            }
        });
    }

    public void createAndSaveCompetitorInfoAndMapping(InstagramCompetitorInfo instagramCompetitorInfo, Long enterpriseId) {
        InstagramCompetitorInfo instagramCompetitorInfoFromDB = competitorInfoRepo.findByPageId(instagramCompetitorInfo.getPageId());

        if(Objects.nonNull(instagramCompetitorInfoFromDB)) {
            instagramCompetitorInfo.setId(instagramCompetitorInfoFromDB.getId());
            instagramCompetitorInfo.setLastScanDate(new Date());
            instagramCompetitorInfo.setScannedOnce(instagramCompetitorInfoFromDB.getScannedOnce());
        }
        competitorInfoRepo.saveAndFlush(instagramCompetitorInfo);

        InstagramCompetitorMapping competitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(instagramCompetitorInfo.getPageId(), enterpriseId);
        if(Objects.isNull(competitorMapping)) {
            competitorMappingRepo.saveAndFlush(createCompMappingEntity(instagramCompetitorInfo, enterpriseId));
        }
        socialCompetitorHelperService.clearCache(SocialChannel.INSTAGRAM, enterpriseId);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.INSTAGRAM, enterpriseId);
    }

    @Override
    public void updateCompCache(SocialChannel channel, Long businessNumber) {
        List<InstagramCompetitorMapping> mappings = competitorMappingRepo.findByEnterpriseId(businessNumber);

        if(CollectionUtils.isEmpty(mappings)) {
            socialCompetitorHelperService.clearCache(channel, businessNumber);
            return;
        }

        List<String> pageIds = mappings.stream().map(InstagramCompetitorMapping::getCompetitorId) .collect(Collectors.toList());
        List<InstagramCompetitorInfo> instagramCompetitorInfos = competitorInfoRepo.findByPageIdIn(pageIds);

        if(CollectionUtils.isEmpty(instagramCompetitorInfos)) {
            socialCompetitorHelperService.clearCache(channel, businessNumber);
            return;
        }

        socialCompetitorHelperService.updateInstagramCache(channel, businessNumber, instagramCompetitorInfos);
    }

    @Override
    public CompetitorListResponse getCompetitorList(Long businessNumber) {
        Object listObject = socialCompetitorHelperService.getCompetitorListFromCache(SocialChannel.INSTAGRAM, businessNumber);
        CompetitorListResponse competitorListResponse = new CompetitorListResponse();
        try {
            if(Objects.nonNull(listObject)) {
                List<InstagramCompetitorInfo> instagramCompetitorInfoFromCache = (List<InstagramCompetitorInfo>) listObject;
                competitorListResponse.setSocialCompetitorDataList(createCompetitorResponseList(instagramCompetitorInfoFromCache));
            } else {
                List<InstagramCompetitorMapping> instagramCompetitorMappings = competitorMappingRepo.findByEnterpriseId(businessNumber);
                if(CollectionUtils.isEmpty(instagramCompetitorMappings)) {
                    LOG.info("empty mapping for channel instagram and business: {}", businessNumber);
                    return competitorListResponse;
                }

                List<String> pageIds = instagramCompetitorMappings.stream()
                        .map(InstagramCompetitorMapping::getCompetitorId).collect(Collectors.toList());

                List<InstagramCompetitorInfo> instagramCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
                if(CollectionUtils.isEmpty(instagramCompetitorInfoList)) {
                    LOG.info("no pages found for channel instagram and business: {}", businessNumber);
                    return competitorListResponse;
                }

                competitorListResponse.setSocialCompetitorDataList(createCompetitorResponseList(instagramCompetitorInfoList));
            }

            return competitorListResponse;
        } catch (Exception e) {
            LOG.info("error occured while fetching pages for business: {}, error: {}", businessNumber, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    private List<CompetitorBasicDetail> createCompetitorResponseList(List<InstagramCompetitorInfo> instagramCompetitorInfoList) {
        if(CollectionUtils.isEmpty(instagramCompetitorInfoList))
            return new ArrayList<>();

        List<CompetitorBasicDetail> competitorBasicDetailList = new ArrayList<>();
        for(InstagramCompetitorInfo instagramCompetitorInfo: instagramCompetitorInfoList) {
            Boolean isVerified = null;
            if(Objects.nonNull(instagramCompetitorInfo.getVerified())) {
                isVerified = instagramCompetitorInfo.getVerified()==1?true:false;
            }
            CompetitorBasicDetail competitorBasicDetail = CompetitorBasicDetail.builder()
                    .id(instagramCompetitorInfo.getPageId())
                    .name(instagramCompetitorInfo.getFullName())
                    .userName(instagramCompetitorInfo.getUserName())
                    .pageLink(instagramCompetitorInfo.getPageUrl())
                    .profilePictureUrl(instagramCompetitorInfo.getProfilePictureUrl())
                    .isVerified(isVerified)
                    .build();

            competitorBasicDetailList.add(competitorBasicDetail);
        }

        return competitorBasicDetailList;
    }

    @Override
    @Transactional
    public void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId) {
        if (Objects.isNull(deleteRequest) || StringUtils.isEmpty(deleteRequest.getPageId())) {
            LOG.info("invalid request payload for enterprise id: {}", enterpriseId);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "Invalid Request Payload");
        }

        String pageId = deleteRequest.getPageId();

        InstagramCompetitorInfo instagramCompetitorInfo = competitorInfoRepo.findByPageId(pageId);

        if (Objects.isNull(instagramCompetitorInfo)) {
            LOG.info("No page found with page id: {}", pageId);
            socialCompetitorHelperService.clearCache(SocialChannel.INSTAGRAM, enterpriseId);
            socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.INSTAGRAM, enterpriseId);
            return;
        }
        InstagramCompetitorMapping instagramCompetitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(pageId, enterpriseId);

        if (Objects.nonNull(instagramCompetitorMapping)) {
            competitorMappingRepo.delete(instagramCompetitorMapping);
        }

//      competitorInfoRepo.delete(instagramCompetitorInfo);
        socialCompetitorHelperService.clearCache(SocialChannel.INSTAGRAM, enterpriseId);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.INSTAGRAM, enterpriseId);
    }


//    private boolean isCompetitorDeletedAll(Integer rawCompetitorId) {
//        return CollectionUtils.isEmpty(competitorMappingRepo.findByRawCompetitorId(rawCompetitorId));
//    }

    @Override
    public String getPageIdOnCompId(Integer rawCompId) {
        InstagramCompetitorInfo instagramCompetitorInfo = competitorInfoRepo.findOne(rawCompId);
        if(Objects.isNull(instagramCompetitorInfo)) {
            return null;
        }
        return instagramCompetitorInfo.getPageId();
    }

    @Override
    public void scanPages() {
        Integer numberOfRecords = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCompetitorPageScanCount();
        Date previousDayDate = DateUtils.addDays(new Date(), -1);
        List<InstagramCompetitorInfo> instagramCompetitorInfoList = competitorInfoRepo
                .findRecordsByLastScanDate(previousDayDate, numberOfRecords);

        if(CollectionUtils.isEmpty(instagramCompetitorInfoList)) {
            LOG.info("no records to scan for instagram");
            return;
        }

        List<Integer> ids = new ArrayList<>();

        instagramCompetitorInfoList.forEach(instagramCompetitorInfo -> {
            CompetitorRequestDTO competitorRequestDTO = CompetitorRequestDTO.builder()
                    .rawId(instagramCompetitorInfo.getId())
                    .pageId(instagramCompetitorInfo.getPageId())
                    .sourceId(SocialChannel.INSTAGRAM.getId())
                    .userName(instagramCompetitorInfo.getUserName())
                    .isDailyScan(true)
                    .build();
            kafkaService.sendObjectV1(KafkaTopicEnum.INSTAGRAM_FETCH_COMP_POSTS.getName(), competitorRequestDTO);
            ids.add(instagramCompetitorInfo.getId());
        });

        competitorInfoRepo.updateLastScanDate(new Date(), ids);
    }

    @Override
    public List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds) {
        List<InstagramCompetitorInfo> instagramCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);

        List<CompetitorBasicDetail> competitorBasicDetailList = new ArrayList<>();
        if(CollectionUtils.isEmpty(instagramCompetitorInfoList)) {
            return competitorBasicDetailList;
        }

        instagramCompetitorInfoList.forEach(instagramCompetitorInfo -> {
            Boolean isVerified = null;
            if(Objects.nonNull(instagramCompetitorInfo.getVerified())) {
                isVerified = instagramCompetitorInfo.getVerified() == 1?true:false;
            }

            CompetitorBasicDetail competitorBasicDetail = CompetitorBasicDetail.builder()
                    .id(instagramCompetitorInfo.getPageId())
                    .name(instagramCompetitorInfo.getFullName())
                    .userName(instagramCompetitorInfo.getUserName())
                    .profilePictureUrl(instagramCompetitorInfo.getProfilePictureUrl())
                    .scannedOnce(instagramCompetitorInfo.getScannedOnce() == 1?true:false)
                    .isVerified(isVerified)
                    .build();
            competitorBasicDetailList.add(competitorBasicDetail);
        });
        return competitorBasicDetailList;
    }

    @Override
    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId) {
        if(Objects.isNull(picturesqueMediaCallback) || Objects.isNull(rawCompId)) {
            LOG.info("invalid upload profile picture request");
            return;
        }

        InstagramCompetitorInfo InstagramCompetitorInfo = competitorInfoRepo.findOne(rawCompId);

        if(Objects.isNull(InstagramCompetitorInfo)) {
            LOG.info("no page found with id: {}", rawCompId);
            return;
        }

        InstagramCompetitorInfo.setProfilePictureUrl(picturesqueMediaCallback.getMediaUrl());
        competitorInfoRepo.saveAndFlush(InstagramCompetitorInfo);

        List<InstagramCompetitorMapping> competitorMappings = competitorMappingRepo.findByRawCompetitorId(rawCompId);

        if(CollectionUtils.isNotEmpty(competitorMappings)) {
            for(InstagramCompetitorMapping mapping: competitorMappings) {
                socialCompetitorHelperService.clearCache(SocialChannel.INSTAGRAM, mapping.getEnterpriseId());
                socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.INSTAGRAM, mapping.getEnterpriseId());
            }
        }
    }

    @Override
    public void callPicturesQueForPage(PicturesqueCompRequest request) {
        if(Objects.isNull(request) || Objects.isNull(request.getId())) {
            LOG.info("invalid upload profile picture request");
            return;
        }

        InstagramCompetitorInfo InstagramCompetitorInfo = competitorInfoRepo.findOne(request.getId());

        if(Objects.isNull(InstagramCompetitorInfo) || StringUtils.isEmpty(InstagramCompetitorInfo.getProfilePictureUrl())) {
            LOG.info("no page found with id: {}", request.getId());
            return;
        }
        MediaAsset mediaAsset = MediaAsset.builder()
                .mediaUrl(InstagramCompetitorInfo.getProfilePictureUrl())
                .mediaType(MediaAssetMediaType.IMAGE.getName())
                .entityId(InstagramCompetitorInfo.getId()+"_"+SocialChannel.INSTAGRAM.getId())
                .entityType(MediaAssetEntityType.COMPETITOR_PAGE.getName())
                .build();
        mediaAssetRepoService.saveAndFlush(mediaAsset);
        String callBackUrl =  CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getUploadPicturesqueMediaUploadCallBackUrl();
        String businessNumber = Objects.isNull(request.getBusinessNumber())?"na":Long.toString(request.getBusinessNumber());
        socialCompetitorHelperService.picturesqueService(InstagramCompetitorInfo.getProfilePictureUrl(), businessNumber, mediaAsset.getId(), callBackUrl);
    }

    @Override
    public CompetitorPageDetailResponse getPageSummary(String userName, Long businessNumber) {
        if(StringUtils.isEmpty(userName)) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
        }

        try {
            String igAccountId = null;
            String accessToken = null;
            List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceId(SocialChannel.INSTAGRAM.getId());
            if(CollectionUtils.isNotEmpty(reservedAccounts)) {
                SocialReservedAccounts socialReservedAccount = reservedAccounts.get(0);
                igAccountId = socialReservedAccount.getPageId();
                accessToken = socialReservedAccount.getAccessToken();
            }
            if(StringUtils.isEmpty(igAccountId) && StringUtils.isEmpty(accessToken)) {
                LOG.info("Not able to get igAccountId and accessToken from SocialReservedAccounts, exiting the flow");
                return new CompetitorPageDetailResponse();
            }
            String igAccountIdFinal = igAccountId;
            String accessTokenFinal = accessToken;

            InstagramCompetitorProfile profileResponse = instagramService.getCompetitorProfileData(igAccountIdFinal,accessTokenFinal,userName,false, null);
            BusinessDiscovery discovery = profileResponse.getBusiness_discovery();
            if(Objects.isNull(discovery)) {
                throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "no user details found");
            }

            InstagramCompetitorMapping instagramCompetitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(discovery.getId(), businessNumber);

            CompetitorPageDetailResponse response = CompetitorPageDetailResponse.builder()
                    .pageName(discovery.getName())
                    .pageLink("https://www.instagram.com/"+discovery.getUsername())
                    .followerCount(discovery.getFollowers_count())
                    .followingCount(discovery.getFollows_count())
                    .profileImageUrl(discovery.getProfile_picture_url())
                    .description(discovery.getBiography())
                    .website(discovery.getWebsite())
                    .pagePresent(Objects.nonNull(instagramCompetitorMapping))
                    .build();

            return response;

        } catch (Exception e) {
            LOG.info("exception occurred while getting page summary for id: {}, error: {}", userName, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public void proceedToUnmapPage(Long businessNumber) {
        List<InstagramCompetitorMapping> instagramCompetitorMappingList = competitorMappingRepo.findByEnterpriseId(businessNumber);
        if(CollectionUtils.isEmpty(instagramCompetitorMappingList)) {
            LOG.info("no instagram mapping found for : {}", businessNumber);
            return;
        }
        competitorMappingRepo.delete(instagramCompetitorMappingList);

        socialCompetitorHelperService.clearCache(SocialChannel.INSTAGRAM, businessNumber);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.INSTAGRAM, businessNumber);
    }

    @Override
    public Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO) {
        LOG.info("Processing IG account info for competitors profile fetch: {}", competitorRequestDTO);
        String igAccountId = null;
        String accessToken = null;
        List<SocialReservedAccounts> reservedAccounts = reservedAccountsRepo.findBySourceId(SocialChannel.INSTAGRAM.getId());
        if(CollectionUtils.isNotEmpty(reservedAccounts)) {
            SocialReservedAccounts socialReservedAccount = reservedAccounts.get(0);
            igAccountId = socialReservedAccount.getPageId();
            accessToken = socialReservedAccount.getAccessToken();
        }
        if(StringUtils.isEmpty(igAccountId) && StringUtils.isEmpty(accessToken)) {
            LOG.info("Not able to get igAccountId and accessToken from SocialReservedAccounts, exiting the flow");
            return Optional.empty();
        }
        try {
            LOG.info("Processing IG page with pageIdentifier: {}", competitorRequestDTO.getUserName());
            InstagramCompetitorProfile profileResponse = instagramService.getCompetitorProfileData(igAccountId,accessToken, competitorRequestDTO.getUserName(),false, null);
            if(Objects.nonNull(profileResponse) && Objects.nonNull(profileResponse.getBusiness_discovery())) {
                return Optional.of(profileResponse.getBusiness_discovery().getFollowers_count());
            } else {
                LOG.info("Page data not found for user: {}", competitorRequestDTO.getUserName());
            }
        } catch (Exception e) {
            LOG.info("Exception while getting account info for IG page: {}", competitorRequestDTO.getUserName(), e);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return Optional.empty();
    }
    public Map<String, CompetitorPageDetails> getPageNameByPageId(List<String> pageIds){
        Map<String, CompetitorPageDetails> pageIdVsPageName = new HashMap<>();
        try {
            List<InstagramCompetitorInfo> instagramCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
            // Mapping pageIds with pageName
            instagramCompetitorInfoList.forEach(instagramCompetitorInfo ->
                    pageIdVsPageName.put(instagramCompetitorInfo.getPageId(),
                            CompetitorPageDetails.builder()
                                    .pageName(StringUtils.isEmpty(instagramCompetitorInfo.getFullName()) ? instagramCompetitorInfo.getUserName() : instagramCompetitorInfo.getFullName())
                                    .profilePictureUrl(instagramCompetitorInfo.getProfilePictureUrl())
                                    .pageLink(instagramCompetitorInfo.getPageUrl())
                                    .channel(SocialChannel.INSTAGRAM.getName())
                                    .pageId(instagramCompetitorInfo.getPageId())
                                    .build()));
        }catch (Exception e){
            LOG.info("Exception while getting page name for pageIds: {} {}", pageIds, e.getMessage());
        }
        return pageIdVsPageName;
    }

    @Override
    public List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds) {
        List<CompetitorPageDetailsDTO> response = new ArrayList<>();


        List<InstagramCompetitorInfo> instagramCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
        if(CollectionUtils.isEmpty(instagramCompetitorInfoList)) {
            return new ArrayList<>();
        }

        for(InstagramCompetitorInfo instagramCompetitorInfo: instagramCompetitorInfoList) {
            CompetitorPageDetailsDTO data =  CompetitorPageDetailsDTO.builder()
                    .pageName(instagramCompetitorInfo.getFullName())
                    .pageId(instagramCompetitorInfo.getPageId())
                    .pageProfileUrl(instagramCompetitorInfo.getProfilePictureUrl())
                    .build();
            response.add(data);
        }
        return response;
    }

    @Override
    public Integer getCompetitorAccounts(Long businessNumber) {
        Object listObject = socialCompetitorHelperService.getCompetitorListFromCache(SocialChannel.INSTAGRAM, businessNumber);
        try {
            if (Objects.nonNull(listObject)) {
                List<InstagramCompetitorMapping> igCompetitorInfoFromCache = (List<InstagramCompetitorMapping>) listObject;
                return igCompetitorInfoFromCache.size();
            } else {
                List<InstagramCompetitorMapping> igCompetitorMappings = competitorMappingRepo.findByEnterpriseId(businessNumber);
                if (CollectionUtils.isEmpty(igCompetitorMappings)) {
                    LOG.info("empty mapping for channel instagram and business: {}", businessNumber);
                    return 0;
                }
                return igCompetitorMappings.size();

            }
        } catch (Exception e) {
            LOG.info("error occurred while fetching pages for business: {}, error: {}", businessNumber, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }
}
