package com.birdeye.social.service.tiktok.impl;

import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.model.SocialPostSchedulerMetadata;
import com.birdeye.social.model.TiktokPostMetadataRequest;
import com.birdeye.social.model.tiktok.TiktokPostInfo;
import com.birdeye.social.model.tiktok.TiktokSourceInfo;
import com.birdeye.social.model.tiktok.TiktokVideoPostingRequest;
import com.birdeye.social.model.tiktok.TiktokVideoResponseError;
import com.birdeye.social.service.ISocialPostsAssetService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialTikTikHelperService {


    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    private static final String PULL_FROM_URL = "PULL_FROM_URL";

    private static final Logger LOG	= LoggerFactory.getLogger(SocialTikTikHelperService.class);

    public TiktokVideoPostingRequest prepareTikTokVideoRequest(SocialPost socialPost, Long businessNumber,String profileId) {
        if(Objects.isNull(socialPost)) {
            return null;
        }
        // Create and populate TiktokPostInfo
        TiktokPostInfo tiktokPostInfo = new TiktokPostInfo();
        populateTiktokPostInfo(socialPost, tiktokPostInfo);
        // Create and populate TiktokVideoPostingRequest
        TiktokVideoPostingRequest tiktokVideoPostingRequest = new TiktokVideoPostingRequest();
        tiktokVideoPostingRequest.setPostInfo(tiktokPostInfo);
        tiktokVideoPostingRequest.setBusinessId(profileId);
        populateVideoUrls(socialPost, businessNumber, tiktokVideoPostingRequest);
        return tiktokVideoPostingRequest;
    }

    private void populateTiktokPostInfo(SocialPost socialPost, TiktokPostInfo tiktokPostInfo) {
        if (StringUtils.isNotEmpty(socialPost.getPostText())) {
            tiktokPostInfo.setCaption(socialPost.getPostText());
        }
        if (StringUtils.isNotEmpty(socialPost.getPostMetadata())) {
            try {
                SocialPostSchedulerMetadata schedulerMetadata =
                        JSONUtils.fromJSON(socialPost.getPostMetadata(), SocialPostSchedulerMetadata.class);
                String tiktokMetaDataString = (Objects.isNull(schedulerMetadata)
                        || StringUtils.isEmpty(schedulerMetadata.getTiktokPostMetaData()))
                        ? null : schedulerMetadata.getTiktokPostMetaData();
                TiktokPostMetadataRequest tiktokPostMetadataRequest =
                        JSONUtils.fromJSON(tiktokMetaDataString, TiktokPostMetadataRequest.class);
                if (Objects.nonNull(tiktokPostMetadataRequest)) {
                    tiktokPostInfo.setDisableDuet(tiktokPostMetadataRequest.getDisableDuet());
                    tiktokPostInfo.setDisableComment(tiktokPostMetadataRequest.getDisableComment());
                    tiktokPostInfo.setDisableStitch(tiktokPostMetadataRequest.getDisableStitch());
                }
            } catch (Exception e) {
                LOG.info("exception while converting social post metadata to json in tik tok: {}", e.getMessage());
            }
        }
    }

    private void populateVideoUrls(SocialPost socialPost, Long businessNumber,
                                   TiktokVideoPostingRequest tiktokVideoPostingRequest) {
        List<Integer> videos = new ArrayList<>();
        if (StringUtils.isNotEmpty(socialPost.getVideoIds())) {
            String[] videoIds = socialPost.getVideoIds().split(",");
            if (videoIds.length > 0) {
                try {
                    videos = Arrays.stream(videoIds).map(Integer::parseInt).collect(Collectors.toList());
                } catch (Exception e) {
                    LOG.info("error while converting videos");
                }
            }
            if (CollectionUtils.isNotEmpty(videos)) {
                List<String> videoUrls =
                        socialPostsAssetService.findVideoUrlsByIds(videos, Long.toString(businessNumber));
                if (CollectionUtils.isNotEmpty(videoUrls)) {
                    tiktokVideoPostingRequest.setVideoUrl(videoUrls.get(0));
                }
            }
        }
    }


    public String getErrorMessageFromTiktokError(TiktokVideoResponseError error) {
        if(Objects.isNull(error) || StringUtils.isEmpty(error.getCode()) || error.getCode().equals("OK")) {
            return null;
        }
        //TODO add using publish info
        return error.getMessage();
    }
}
