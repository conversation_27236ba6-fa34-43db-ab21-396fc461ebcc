package com.birdeye.social.service.Youtube;

import com.birdeye.social.dto.TokensAndUrlAuthData;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.google.api.services.youtube.model.Channel;

import java.io.File;
import java.util.List;

public interface YoutubeVideoUpload {

    void postVideo(SocialPostPublishInfo publishInfo, File videoFile);

    boolean deleteVideo(SocialPostPublishInfo publishInfo);
    void editYoutubeVideoText(SocialPostPublishInfo publishInfo);

    void markYoutubePageInvalid(BusinessYoutubeChannel businessYoutubeChannel);

    List<YoutubeCategory> getYoutubeCategories(String pageId) throws Exception;

    List<YoutubePlaylist> getPlaylistForChannel(String pageId) throws Exception;

    List<Channel> getChannel(TokensAndUrlAuthData tokensAndUrlAuthData);
}
