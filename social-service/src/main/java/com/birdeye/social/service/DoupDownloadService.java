package com.birdeye.social.service;

import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.model.CalendarPostsExportRequest;
import com.birdeye.social.response.DoupDownloadResponse;
import com.birdeye.social.response.PdfDataDownloadResponse;
import com.birdeye.social.trends.SLAExcelResponse;
import com.birdeye.social.trends.TrendsReportRequest;

import java.util.List;
import java.util.Map;

public interface DoupDownloadService {

    DoupDownloadResponse<? extends PageInsightDataPoint> downloadExcelReportForExcel(InsightsRequest insightsRequest,
                                                                                     String channel,
                                                                                     String sortBy,
                                                                                     String sortOrder) throws Exception;

    PdfDataDownloadResponse<? extends PageInsightsResponse> downloadExcelReportForPdf(InsightsRequest insightsRequest,
                                                                                      String channel,
                                                                                      String sortBy,
                                                                                      String sortOrder,
                                                                                      Integer pageSize,
                                                                                      Integer startIndex) throws Exception;


    DoupDownloadResponse<? extends ProfilePerformanceExcelResponse> downloadExcelReportForPerformance(InsightsRequest insightsRequest,
                                                                                                      String sortBy,
                                                                                                      String sortOrder) throws Exception;

    DoupDownloadResponse<? extends AnalyzeTabDataResponse> downloadExcelAnalyzeData(InsightsRequest insightsRequest, String channel,
                                                                                    String sortBy, String sortOrder, Integer pageSize, Integer startIndex);

    DoupDownloadResponse<? extends ExecutiveSummaryResponse> downloadExcelReportForExecutiveSummary(ExecutiveSummaryRequest executiveSummaryRequest);

    DoupDownloadResponse<? extends LeadershipByPostsResponseWrapper> downloadExcelReportForLocationSummary(LocationReportRequest insightsRequest,
                                                                                                           String sortBy, String sortOrder, Integer pageSize,
                                                                                                           Integer startIndex);
    DoupDownloadResponse<? extends Object> downloadExcelReportForSLA(TrendsReportRequest insightsRequest,
                                                                     Integer pageSize, Integer startIndex,
                                                                     String sortBy, String sortOrder) throws Exception;

    List<ProfilePerformanceExcelResponse> downloadExcelReportForSpecificTab(InsightsRequest insightsRequest,
                                                                            String channel) throws Exception;

    Map<String, Object> downloadPdfCalendarReport(CalendarPostsExportRequest exportFilter, Integer userId, String timeZone,
                                                  Integer startIndex, String sortOrder, Integer pageSize, Integer accountId);
}
