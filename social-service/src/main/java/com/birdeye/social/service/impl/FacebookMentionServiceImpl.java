/**
 * 
 */
package com.birdeye.social.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.BusinessKeywordMappingRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessKeywordMapping;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.FacebookPostInput;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.facebook.PostInsightItem;
import com.birdeye.social.model.GNIPActivity;
import com.birdeye.social.model.GnipActivityDTO;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.FacebookAliasInfoRepo;
import com.birdeye.social.platform.dao.GnipActivityRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.FacebookAliasInfo;
import com.birdeye.social.platform.entities.GnipActivity;
import com.birdeye.social.service.FacebookMentionService;
import com.birdeye.social.utils.BusinessUtilsService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 *
 */
@Service
public class FacebookMentionServiceImpl implements FacebookMentionService {
	
	private static final Logger		LOGGER	= LoggerFactory.getLogger(FacebookMentionServiceImpl.class);
	
	@Autowired
	private BusinessUtilsService	businessUtilService;
	
	@Autowired
	private FacebookAliasInfoRepo	aliasRepo;

	@Autowired
	private FacebookService			fbService;
	
	@Autowired
	private GnipActivityRepository	gnipActRepo;
	
	@Autowired
	private ObjectMapper			mapper;
	
	@Autowired
	private KafkaProducerService	producer;
	
	@Autowired
	private BusinessKeywordMappingRepository keywordRepo;
	
	@Autowired
	private BusinessRepository businessRepo;
	
	@Autowired
	private SocialFBPageRepository socialFBpageRepo;
	
	@Autowired
	private BusinessFacebookPageRepository businessFacebookPageRepo;
	
	@Override
	public void getActivities() {
		LOGGER.info("Request received to get facebook mentions");//NOSONAR
		List<BusinessKeywordMapping> businessKeywords = keywordRepo.findByStatusAndOrderByIsExcludedDesc("active");
		LOGGER.info("Count of Active Business gnip rules received for facebook is {}", businessKeywords.size());
		Map<Integer, List<String>> exclusionKeywordsDetails = businessUtilService.getExclusionKeywordsDetails(businessKeywords);
		LOGGER.info("Count of excluded keywords received for facebook is {}", exclusionKeywordsDetails.size());
		Map<Integer, List<BusinessKeywordMapping>> keywordByBusinessId = businessUtilService.getGnipRulesByBusinessId(businessKeywords);
		LOGGER.info("Count of gnipRules by business id for facebook is {}", keywordByBusinessId.size());
		List<FacebookAliasInfo> facebookAliases = aliasRepo.findAll();
		LOGGER.info("Count of facebook aliases is {}", keywordByBusinessId.size());
		
		facebookAliases.parallelStream().forEach(alias -> {
			LOGGER.info("Start Facebook Mentions processing for alias : {}", alias.getFacebookPageAlias());
			List<BusinessKeywordMapping> aliasLinkedRules = keywordByBusinessId.get(alias.getBusinessId());
			List<String> exclusionKeywords = exclusionKeywordsDetails.get(alias.getBusinessId());
			if (aliasLinkedRules != null) {
				try {
					startFacebookActivitiesAggre(alias, aliasLinkedRules, exclusionKeywords);
				} catch (Exception e) {
					LOGGER.warn("Exception {} occurred in startFacebookActivitiesAggre", e);
				}
			} else {
				LOGGER.info("No alias linked rules found for alias : {}", alias);
			}
		});
		
	}
	
    private void startFacebookActivitiesAggre(FacebookAliasInfo aliasInfo, List<BusinessKeywordMapping> aliasLinkedRules, List<String> exclusionKeywords) {
		
		String fbSessionToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFBAccessToken();
		LOGGER.info("FB Session Token received is : {}", fbSessionToken);
		if (!StringUtils.isEmpty(fbSessionToken)) {
			FacebookPostInput input = new FacebookPostInput(aliasInfo.getFacebookPageAlias(), fbSessionToken);
			try {
				List<PostInsightItem> postEntities = fbService.getPost(input);
				if (CollectionUtils.isNotEmpty(postEntities)) {
					LOGGER.info("Count of Posts found from facebook : {}", postEntities.size());
					// Iterating over individual rule entry
					matchAndStorePosts(postEntities, aliasLinkedRules, exclusionKeywords);
				} else {
					LOGGER.info("No Posts found on facebook for handle: {}", input.getHandler());
				}
			} catch (Exception ex) {
				LOGGER.warn("Exception {} occurred in matchAndStorePosts", ex);
			}
		} else {
			throw new BirdeyeSocialException(ErrorCodes.FACEBOOK_SESSION_TOKEN_IS_NULL);
		}
	}
	
	@Override
	public void getNewActivities() {
		
		LOGGER.info("Request received to get new facebook mentions");
		List<Integer> businessId = keywordRepo.findByStatus("active");
		List<Integer> disableBusinessIds=CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDisableMentionsBusinessId();
		LOGGER.info("Count of Active Business rules received for new facebook is {}", businessId.size());

		businessId.removeIf(data->disableBusinessIds.contains(data));
		LOGGER.info("Count of Filtered Business rules received for new facebook is {}", businessId.size());
		List<Business> business=businessRepo.findByIdIn(businessId);		

		Map<Long, Integer> businessIdMap = business.stream().collect( Collectors.toMap(Business::getBusinessId, Business::getId,(key1, key2) -> key1));
        List<BusinessFBPage> businessFBPage=socialFBpageRepo.findByEnterpriseIdInAndIsValid(businessIdMap.keySet(),1);
	    
	    Map<String,BusinessFBPage> businessFbPageIdMap=businessFBPage.stream().collect(Collectors.toMap(BusinessFBPage::getFacebookPageId, Function.identity(),(key1, key2) -> key1));
        List<BusinessFBPage> businessFacebookPage= businessFBPage.stream().filter(fbPage -> Objects.nonNull(fbPage.getBusinessId())).collect(Collectors.toList());
	    
        for(BusinessFBPage b: businessFacebookPage)
	    {
		   BusinessFBPage businessFbPage=businessFbPageIdMap.containsKey(b.getFacebookPageId())?businessFbPageIdMap.get(b.getFacebookPageId()):null;
		   if(businessFbPage!=null)
		   {
			   try
			   {
			       Integer shortBusinessId=businessIdMap.get(businessFbPage.getEnterpriseId());
		           FacebookPostInput facebookPostInput=new FacebookPostInput(businessFbPage.getFacebookPageId(),businessFbPage.getPageAccessToken(),businessFbPage.getEnterpriseId(),shortBusinessId);
		           producer.sendObject(Constants.FB_MENTION_TOPIC, facebookPostInput);
			   }
			   catch(Exception e)
			   {
				   LOGGER.warn("Exception occurred while processing Enterprise Id : {} and Facebook page Id : {}",businessFbPage.getEnterpriseId(),businessFbPage.getFacebookPageId()); 
			   }
		   }
	    }
	}
	
	@Override
	public  void processFacebookMention(FacebookPostInput facebookPostInput)
	{
		LOGGER.info("Request received is : {}", facebookPostInput);
		if (!StringUtils.isEmpty(facebookPostInput.getAccessToken())) {
			
			List<BusinessKeywordMapping> businessKeywords = keywordRepo.findByStatusAndBusinessIdAndOrderByIsExcludedDesc("active",facebookPostInput.getShortBusinessId());
			LOGGER.info("Count of Active Business gnip rules received for facebook is {}", businessKeywords.size());
			
			Map<Boolean, List<BusinessKeywordMapping>> partitions = businessKeywords.stream().collect(Collectors.partitioningBy(keyword -> keyword.getIsExcluded()==0));
			List<BusinessKeywordMapping> keywordByBusinessId= partitions.get(true);
			LOGGER.info("Count of gnipRules by business id for facebook is {}", keywordByBusinessId.size());
			List<BusinessKeywordMapping> exKeywordByBusinessId= partitions.get(false);
			LOGGER.info("Count of exclude gnipRules by business id for facebook is {}", keywordByBusinessId.size());
			final List<String> exclusionKeywordsDetails=new ArrayList<>();
			if(CollectionUtils.isNotEmpty(exKeywordByBusinessId))
			{
				exKeywordByBusinessId.stream().forEach(keyword->exclusionKeywordsDetails.add(keyword.getKeyword()));
			}			
			LOGGER.info("Count of excluded keywords received for facebook is {}", exclusionKeywordsDetails.size());		
			try {
				List<PostInsightItem> postEntities = fbService.getPost(facebookPostInput);
				if (CollectionUtils.isNotEmpty(postEntities)) {
					LOGGER.info("Count of Posts found from facebook : {}", postEntities.size());
			
					matchAndStorePosts(postEntities, keywordByBusinessId, exclusionKeywordsDetails);
				} else {
					LOGGER.info("No Posts found on facebook for handle: {}", facebookPostInput.getPageId());
				}
			} catch (Exception ex) {
				LOGGER.warn("Exception {} occurred in matchAndStorePosts", ex);
			}
		} else {
			throw new BirdeyeSocialException(ErrorCodes.FACEBOOK_SESSION_TOKEN_IS_NULL);
		}
	}
	

	
	private void matchAndStorePosts(List<PostInsightItem> postEntities, List<BusinessKeywordMapping> aliasLinkedRules, List<String> exclusionKeywords) {
		if (!CollectionUtils.isEmpty(postEntities)) {
		
			Map<String, PostInsightItem> idToPostMap = postEntities.stream().filter(distinctByKey(PostInsightItem::getId)).collect(Collectors.toMap(PostInsightItem::getId, Function.identity()));
			List<String> existingActivities = gnipActRepo.findByActivityIdInAndSourceId(new ArrayList<>(idToPostMap.keySet()), 110);
			postEntities.parallelStream().filter(Objects::nonNull).forEach(post -> {
				Optional<BusinessKeywordMapping> matched = aliasLinkedRules.stream()
						.filter(rule -> businessUtilService.isRuleFulfilled(rule.getKeyword(), exclusionKeywords, post.getMessage(), (post.getFrom() != null) ? post.getFrom().getId() : ""))
						.findFirst();
				if (matched.isPresent()) {
					LOGGER.info("Rule Matched, id: {} keyword: {}", matched.get().getId(), matched.get().getKeyword());
					if (!existingActivities.contains(post.getId())) {
						// Post not present, Saving New Post in gnip activity
						LOGGER.info("Creating new activity for post id {}", post.getId());
						try {
							GnipActivity activity = new GnipActivity(post.getId(), 110, mapper.writeValueAsString(post), new Date());
							gnipActRepo.saveAndFlush(activity);
							GNIPActivity message = new GNIPActivity("facebook", post, matched.get().getId(), new GnipActivityDTO(activity));
							// pushing activity to kafka
							producer.send(Constants.FB_GP_YTUBE_MENTION_TOPIC,activity.getId(), message);
							LOGGER.info("Processed Facebook post id: {} and gnip activity id is: {}", post.getId(),activity.getId());
						} catch (Exception e) {
							LOGGER.warn("Exception occurred while processing Facebook post : {}", post.getId());
						}
					} else {
						LOGGER.info("Ignoring the Post as it is already present in existing activity. Post id {}", post.getId());
					}
				} else {
					LOGGER.info("No rule statisfied by post : {}", post.getId());
				}
			});
		}
	}

	public static <T> java.util.function.Predicate<T> distinctByKey(java.util.function.Function<? super T, Object> keyExtractor) {
		java.util.Map<Object, Boolean> seen = new java.util.HashMap<>();
		return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
	}
	
}
