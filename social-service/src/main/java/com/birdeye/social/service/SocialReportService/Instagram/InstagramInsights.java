package com.birdeye.social.service.SocialReportService.Instagram;

import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.PageInsightV2EsData;
import com.birdeye.social.insights.ES.PageReportEsData;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramAccountDataResponse;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import org.elasticsearch.search.sort.SortOrder;

public interface InstagramInsights {
    PageInsightsResponse getInstagramInsightsForPage(InsightsRequest insights) throws Exception;

    PostDataAndInsightResponse getInstagramInsightsForPost(InsightsRequest insights,int startIndex, int pageSize, String sortParam,
                                                           String sortOrder, boolean excelDownload);

    void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest);

    void postInstagramPageInsightToES(PageInsights pageInsights);

    void postInstagramPostInsightsToEs(PostData postData);

    void updateInstagramPageInsightsPostCount(PageInsights pageInsights);

    void startScanForPosts(String pageId);

    void saveCDNPostToES(BusinessPosts businessPosts);

    PageInsightV2EsData getPageInsightsESData(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getInstagramInsightsForMessageSent(InsightsRequest insights) throws Exception;

    PageInsightV2EsData getInstagramInsightsForPublishPost(InsightsRequest insights) throws Exception;

    PerformanceSummaryResponse getInstagramPerformanceData(InsightsRequest insights);

    boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights);
    PageReportEsData getInstagramInsightsReportData(InsightsRequest insights) throws Exception;

    PageReportEsData getMessageVolumeInsightsReportData(InsightsRequest insights) throws Exception;

    PostData createPostData(BusinessPosts businessPosts, PostInsightDTO postInsightDTO, InstagramAccountDataResponse engagementResponse) throws Exception;

    void backfillEngagmentBreakDown(BackfillInsightReq backfillInsightReq);


    PostDataAndInsightResponse getInstagramInsightsForTopPost(InsightsRequest insights,int startIndex, int pageSize, String sortParam,
                                                           String sortOrder, boolean excelDownload);


    LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                        ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                        Integer startIndex, Integer pageSize);

}
