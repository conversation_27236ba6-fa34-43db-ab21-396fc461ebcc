package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.SocialBusinessPropertyRepo;
import com.birdeye.social.entities.SocialBusinessProperty;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SocialBusinessPropertyServiceImpl implements SocialBusinessPropertyService{


    @Autowired
    SocialBusinessPropertyRepo socialBusinessPropertyRepo;
    private static final Logger logger	= LoggerFactory.getLogger(GoogleSocialAccountServiceImpl.class);


    @Override
    @Cacheable(value = Constants.SOCIAL_BUSINESS_PROPERTY, key="#enterpriseId.toString()", unless = "#result == null")
    public boolean findAutoLaunchEnabledByEnterpriseId(Long enterpriseId) {
        List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(enterpriseId);
        if(CollectionUtils.isEmpty(socialBusinessPropertyList)){
            logger.info("Not able to get property from table for ent id : {}",enterpriseId);
            return false;
        }
        SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyList.get(0);
        if(Objects.isNull(socialBusinessProperty.getAutoLaunchedEnable())){
            return false;
        }
        return Objects.equals(socialBusinessProperty.getAutoLaunchedEnable(), 1);
    }

    @Override
    public SocialBusinessProperty findByEnterpriseId(Long enterpriseId) {
        List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(enterpriseId);
        if(CollectionUtils.isEmpty(socialBusinessPropertyList)){
            logger.info("Not able to get property from table for ent id : {}",enterpriseId);
            return null;
        }
        return socialBusinessPropertyList.get(0);
    }

    @Override
    public void save(SocialBusinessProperty socialBusinessProperty) {
        socialBusinessPropertyRepo.save(socialBusinessProperty);
    }


    @Override
    @Cacheable(value = Constants.SOCIAL_BUSINESS_PROPERTY, key="#businessNumber.toString().concat('_posting_reporting_enable')", unless = "#result == null")
    public boolean isSocialPostingAndReportingEnabled(Long businessNumber) {
        logger.info("Business number to check reporting enabled : {}",businessNumber);
        List<SocialBusinessProperty> socialBusinessPropertyList =
                socialBusinessPropertyRepo.findByEnterpriseId(businessNumber);
        if(CollectionUtils.isEmpty(socialBusinessPropertyList)){
            logger.info("No business property list found with business number {}",businessNumber);
            return false;
        }
        List<Integer> socialPostingAndReportingEnableList =
                CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialPostingAndReportingEnableValues();
        return socialPostingAndReportingEnableList.contains(socialBusinessPropertyList.get(0).getSocialEnabled());
    }

    @Override
    @CacheEvict(value = Constants.SOCIAL_BUSINESS_PROPERTY, key="#businessNumber.toString().concat('_posting_reporting_enable')")
    public void evictCache(Long businessNumber) {
        logger.info("Evict cache :{}",businessNumber);
    }
}
