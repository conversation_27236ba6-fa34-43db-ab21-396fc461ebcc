package com.birdeye.social.service.SocialUnSubscribeService;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dao.SocialSetupAuditRepository;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.SocialSetupAudit;
import com.birdeye.social.service.ISocialAppService;
import com.birdeye.social.service.SocialAppCredsInfo;
import com.birdeye.social.twitter.TwitterCreds;
import com.birdeye.social.twitter.TwitterService;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TwitterUnSubscribe implements SocialUnSubscribe {

    @Autowired
    private ISocialAppService socialAppService;
    @Autowired
    private TwitterService twitterService;
    @Autowired
    private SocialSetupAuditRepository auditRepository;
    @Autowired
    private SocialTwitterAccountRepository twitterAccountRepository;
    private static final Logger LOGGER = LoggerFactory.getLogger(TwitterUnSubscribe.class);
    @Override
    public String channelName() {
        return SocialChannel.TWITTER.getName();
    }

    private TwitterCreds twitterCredsV2(BusinessTwitterAccounts twitterAccounts){
        SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettingsV2();
        return new TwitterCreds(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret(), twitterAccounts.getAccessToken(), twitterAccounts.getAccessSecret());
    }
    @Override
    public void unsubscribeNotification(SocialSetupAudit socialSetupAudit) throws Exception {
        BusinessTwitterAccounts twitterAccount = JSONUtils.fromJSON(socialSetupAudit.getEntity(),BusinessTwitterAccounts.class);
        if(Objects.nonNull(twitterAccount)){
            twitterAccount.setProfileId(Long.parseLong(socialSetupAudit.getIntegrationId()));
            TwitterCreds creds = twitterCredsV2(twitterAccount);
            String privateKey = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getTwitterWebhookKey();
            LOGGER.info("Private key for twitter : {} and page id :{}",privateKey,twitterAccount.getProfileId());
            twitterService.unSubscribeWebhook(creds,privateKey, String.valueOf(twitterAccount.getProfileId()));
            LOGGER.info("Twitter account unsubscribed for profileId: {}",twitterAccount.getProfileId());
        }
    }

    @Override
    public List<SocialSetupAudit> getSocialSetupAuditIds(Date fromDate, SocialSetupAuditEnum action) {
        List<SocialSetupAudit> auditList = auditRepository.findByChannelAndActionAndCreatedGreaterThanEqualOrderByIdDesc(SocialChannel.TWITTER.getName(), action.name(),fromDate);
        LOGGER.info("Entries found in Social setup audit: {}",auditList.size());
        if(CollectionUtils.isNotEmpty(auditList)) {
            List<String> pageIds = auditList.stream().map(SocialSetupAudit::getIntegrationId).collect(Collectors.toList());
            List<Long> profileIdsLong = pageIds.stream().map(Long::parseLong).collect(Collectors.toList());
            List<BusinessTwitterAccounts> twitterAccounts = new ArrayList<>();
            if(action.equals(SocialSetupAuditEnum.REMOVE_PAGE)) {
                twitterAccounts = twitterAccountRepository.findByProfileIdIn(profileIdsLong);
            } else if(action.equals(SocialSetupAuditEnum.REMOVE_MAPPING)) {
                twitterAccounts = twitterAccountRepository.findByProfileIdInAndBusinessIdIsNotNull(profileIdsLong);
            }
            Set<String> existingPageIds = twitterAccounts.stream().map(account -> String.valueOf(account.getProfileId())).collect(Collectors.toSet());
            LOGGER.info("Existing page count: {}", existingPageIds.size());
            auditList.removeIf(audit -> existingPageIds.contains(audit.getIntegrationId()));
            LOGGER.info("Final setup audit list size: {}", auditList.size());
        }
        return auditList;
    }
}
