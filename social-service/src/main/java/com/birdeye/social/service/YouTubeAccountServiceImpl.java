package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dao.GoogleRefreshTokenRepo;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.request.nexus.CheckStatusRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.googleplus.GooglePlusService;
import com.birdeye.social.googleplus.GoogleProfileResponse;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.service.Youtube.YoutubeService;
import com.birdeye.social.service.Youtube.YoutubeVideoUpload;
import com.birdeye.social.specification.YouTubeSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.YoutubeUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.services.youtube.model.Channel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.StrBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static java.util.Comparator.nullsFirst;

@Service
public class YouTubeAccountServiceImpl extends SocialAccountSetupCommonService implements YouTubeAccountService {

    private static final String CONNECT = "connect";
    private static final String RECONNECT = "reconnect";
    private static final String AUTHORIZATION_CODE = "authorization_code";
    public final String REDIRECT_URI_SETUP_YOUTUBE = "%s/social/youtube/callback";
    private static final String MYSQL_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";

    private static final String YOUTUBE_DP_SYNC_TOPIC= "youtube-dp-sync";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);



    private static Logger logger = LoggerFactory.getLogger(YouTubeAccountServiceImpl.class);

    @Autowired
    private NexusService nexusService;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private BusinessRepository businessRepo;

    @Autowired
    private GoogleAuthenticationService	googleAuthenticationService;

    @Autowired
    private GooglePlusService googlePlusService;

    @Autowired
    private SocialPostGooglePlusService	socialPostGooglePlusService;

    @Autowired
    private GoogleRefreshTokenRepo googleRefreshTokenRepo;

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    @Autowired
    private YoutubeVideoUpload youtubeService;

    @Autowired
    private BusinessYoutubeChannelRepository youtubeChannelRepository;

    @Autowired
    private KafkaProducerService kafkaProducer;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private BusinessUtilsService businessUtilService;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private YoutubeService youtubeExternalService;
    
    @Autowired
    private YouTubeSpecification youTubeSpecification;
    
    @Autowired
	private IBusinessCoreService iBusinessCoreService;

    @Autowired
    private BusinessService coreBusinessService;

    @Override
    public void submitChannelRequest(ChannelAuthRequest authRequest, String type) {
        Long parentId  = authRequest.getBusinessId();
        String key = SocialChannel.YOUTUBE.getName().concat(String.valueOf(parentId));
        boolean lock = redisService.tryToAcquireLock(key);
        logger.info("[Redis Lock] Lock status : {}",lock);
        try {
            BusinessGetPageRequest request = (type.equalsIgnoreCase(ENTERPRISE))
                    ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.YOUTUBE.getName(),CONNECT)
                    : businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.YOUTUBE.getName(),CONNECT);

            if(lock) {
                List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
                if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
                    pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),CONNECT,Status.INITIAL.getName(),parentId);
                    String youtubeRedirectUri = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYoutubeRedirectUri();
                    String redirectUri = String.valueOf(new StrBuilder(String.format(REDIRECT_URI_SETUP_YOUTUBE,youtubeRedirectUri)));
                    GoogleAuthToken googleAuthToken = getGoogleToken(parentId, authRequest.getAuthCode(),redirectUri);
                    logger.info("generated  GoogleAuthToken = {}", googleAuthToken);
                    BusinessGetPageRequest businessGetPageRequest = submitYoutubeRequest(parentId, authRequest.getBirdeyeUserId(), googleAuthToken, Status.INITIAL.getName(), type);
                    TokensAndUrlAuthData tokensAndUrlAuthData  = new TokensAndUrlAuthData();
                    tokensAndUrlAuthData.setAccessToken(googleAuthToken.getAccess_token());
                    List<Channel> youtubeChannelDetails = youtubeService.getChannel(tokensAndUrlAuthData);
                    if(CollectionUtils.isEmpty(youtubeChannelDetails)){
                        businessGetPageRequest.setStatus(Status.NO_PAGES_FOUND.getName());
                        redisService.release(key);
                    }else {
                        businessGetPageRequest.setStatus(Status.FETCHED.getName());
                        savePageData(youtubeChannelDetails, businessGetPageRequest, googleAuthToken,type);
                    }
                    businessGetPageReqRepo.save(businessGetPageRequest);
                    pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(), businessGetPageRequest.getRequestType(), businessGetPageRequest.getStatus(), parentId);
                }else{
                    logger.info("[Youtube] BusinessGetPageRequest found with status init or fetched for account id {}",parentId);
                    pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),request.getRequestType(),request.getStatus(),parentId);
                    redisService.release(key);
                }
            } else {
                handleFailureLock(request,SocialChannel.YOUTUBE.getName(), key,parentId,CONNECT);
            }
        } catch (Exception ex) {
            redisService.release(key);
            logger.error("[Redis Lock] (YouTube) Lock released for business {} exception {}", parentId, ex);
            pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
            throw new BirdeyeSocialException(ErrorCodes.YOUTUBE_CONNECT_ERROR, ex.getMessage() != null ? ex.getMessage() : "Error while connecting to Youtube");
           // throw new BirdeyeSocialException("Error while saving channel");
        }
    }

    @Override
    public Map<String, List<ChannelAccountInfo>> getIntegratedChannels(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        logger.info("Get all pages for request id : {}",businessGetPageRequest.getId());
        Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
        List<ChannelAccountInfo> managed = new ArrayList<>();
        List<BusinessYoutubeChannel> fetchedChannel = youtubeChannelRepository.findByRequestId(businessGetPageRequest.getId().toString());
        if (CollectionUtils.isNotEmpty(fetchedChannel)) {
            fetchedChannel.forEach(channel -> managed.add(channelAccountInfo(channel, enterpriseId)));
            if (CollectionUtils.isNotEmpty(managed))
                pageType.put(SocialChannel.YOUTUBE.getName(), managed);
        }
        return pageType;
    }

    @Override
    public ChannelPageInfo connectPages(List<String> channelIds , Long enterpriseId,Boolean selectAll, String searchStr,String type,Integer accountId) throws Exception {
        logger.info("[Youtube Setup] connect youtubePages channel ids : {}", channelIds);
        ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
        List<BusinessGetPageRequest> request = getRequestForBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.YOUTUBE.getName(), CONNECT,type);
        if (CollectionUtils.isEmpty(request)) {
            logger.error("[Youtube Setup] seems status has already changed");
            throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems status has already changed");
        } else if (request.size() > 1) {
            logger.error("[Youtube Setup] multiple fetched rows are not possible for any business");
            throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status");
        } else {
            BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
            checkForUnMappedSmbPages(business);
            BusinessGetPageRequest req = request.get(0);
            Long parentId= RESELLER.equals(type)?req.getResellerId():req.getEnterpriseId();
            req.setStatus(Status.COMPLETE.getName());
            req.setUpdated(new Date());
            businessGetPageReqRepo.saveAndFlush(req);
            pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),req.getRequestType(),Status.COMPLETE.getName(), parentId);
			if(Constants.RESELLER.equals(type) && BooleanUtils.isTrue(selectAll)
					&& StringUtils.isNotEmpty(searchStr)) {
				channelIds = youtubeChannelRepository.findAllByYoutubeChannelName(searchStr,req.getId().toString());
			}
			else if(Constants.RESELLER.equals(type) && BooleanUtils.isTrue(selectAll)){
				channelIds = youtubeChannelRepository.findChannelIdsByRequestId(req.getId().toString());
			}

            List<BusinessYoutubeChannel> ytChannels = getYoutubeAccountsList(channelIds);
            ytChannels.forEach(channel -> {
                channel.setIsSelected(1);
                if(RESELLER.equals(type)) {
                	channel.setResellerId(enterpriseId);
                }else {
                	channel.setEnterpriseId(enterpriseId);
                }
                channel.setAccountId(accountId);
                youtubeChannelRepository.saveAndFlush(channel);
            });
            List<BusinessYoutubeChannel> youtubeChannelList =  youtubeChannelRepository.findByBusinessId(business.getBusinessId());
            if (checkBusinessSMB(business) && youtubeChannelList.isEmpty()) {
                ytChannels.forEach(y -> {
                    y.setBusinessId(business.getBusinessId());
                    y.setCreatedBy(req.getBirdeyeUserId());
                    y.setUpdatedBy(req.getBirdeyeUserId());
                    youtubeChannelRepository.saveAndFlush(y);
                });
            }else{
                SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(channelIds,SocialChannel.YOUTUBE.getName());
                kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
            }
            releaseLock(req.getChannel(),parentId);
            Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();

            List<ChannelAccountInfo> youtubeChannelInfo = getYoutubeChannelInfo(getYoutubeChannelList(channelIds, enterpriseId, type));
            if (CollectionUtils.isNotEmpty(youtubeChannelInfo)) {
                youtubeChannelInfo.forEach(channel -> channel.setDisabled(null));
            }

            accountMap.put(SocialChannel.YOUTUBE.getName(), youtubeChannelInfo);

            channelAccountInfo.setPageTypes(accountMap);
        }
        return channelAccountInfo;
    }

    @Override
    public void saveLocationPageMapping(Integer locationId, String channelId, Integer userId,String type, Long resellerId) throws Exception {
        logger.info("Youtube channel id {} mapping with location Id {}", channelId, locationId);

        BusinessYoutubeChannel youtubeChannel = youtubeChannelRepository.findByChannelId(channelId);
        if (Objects.isNull(youtubeChannel)) {
            logger.error("For Youtube channelId {} no data found ", channelId);
            throw new BirdeyeSocialException(ErrorCodes.FB_PAGE_NOT_FOUND, "youtube channel data not found");
        }
        if(youtubeChannelRepository.existsByBusinessId(locationId)){
            throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS,MAPPING_ALREADY_EXIST_ERROR);
        }
        if(Objects.nonNull(resellerId)){
            commonService.checkRequestFromAuthorizedSourceUsingLongResellerID(youtubeChannel.getResellerId(), resellerId);
        }
        updateYTChannelForReseller(youtubeChannel, type, locationId);
        youtubeChannel.setBusinessId(locationId);
//        youtubeChannel.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
        youtubeChannelRepository.saveAndFlush(youtubeChannel);
        commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(youtubeChannel), userId.toString(), locationId,Constants.ENTERPRISE.equals(type)?youtubeChannel.getEnterpriseId():youtubeChannel.getResellerId());

    }

    @Override
    public void removeLocationChannelMapping(List<LocationPageMappingRequest> locationPageMappings,String type, boolean unlink) throws Exception {
        logger.info("Remove youtube channel mappings with input {}", locationPageMappings);
        List<String> channelIds = locationPageMappings.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByChannelIdIn(channelIds);

        if(Objects.nonNull(locationPageMappings.get(0)) && Objects.nonNull(locationPageMappings.get(0).getResellerId())){
            List<Long> storedResellerIds = youtubeChannels.stream().map(BusinessYoutubeChannel::getResellerId).collect(Collectors.toList());
            commonService.checkRequestFromAuthorizedSourceUsingResellerIdsList(storedResellerIds, locationPageMappings.get(0).getResellerId());
        }
        youtubeChannels.forEach(channel -> {
            commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(), Arrays.asList(channel), null, channel.getBusinessId(),Constants.ENTERPRISE.equals(type)?channel.getEnterpriseId():channel.getResellerId());
            channel.setBusinessId(null);
            if(Objects.nonNull(channel.getResellerId())){
                channel.setEnterpriseId(null);
                channel.setAccountId(null);
            }
            if(unlink) {
                channel.setIsSelected(0);
                channel.setEnterpriseId(null);
                channel.setResellerId(null);
            }
        });
        youtubeChannelRepository.save(youtubeChannels);
        kafkaProducer.sendObjectV1(KafkaTopicEnum.YOUTUBE_REMOVE_MAPPING.getName(), locationPageMappings);
        logger.info("Youtube channel mappings removed with input {}", locationPageMappings);
    }

    @Override
    public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List<Integer> businessIds, Set<String> status,
                                                       Integer page, Integer size, String search, List<String> includeModules) throws Exception {
        logger.info("getLocationMappingPages: enterpriseId {} userId {} status {}", enterpriseId, userId, status);
        if ( Objects.isNull(enterpriseId) || Objects.isNull(userId) || CollectionUtils.isEmpty(businessIds) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid value for enterpriseId/userId/businessIds");
        }
        LocationPageMapping response = new LocationPageMapping();
        response.setTotalLocations(businessIds.size());

        List<Integer> totalEnterpriseBusinessIds = new ArrayList<>();
        if(CollectionUtils.isEmpty(businessIds)) {
            BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
            businessLiteRequest.setKey("businessNumber");
            businessLiteRequest.setValue(enterpriseId);
            BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
            totalEnterpriseBusinessIds = businessUtilService.getBusinessLocationsForEnterpriseUsingLite(business, null);
            businessIds.addAll(totalEnterpriseBusinessIds);
        }

        if (CollectionUtils.isNotEmpty(totalEnterpriseBusinessIds) || CollectionUtils.isNotEmpty(businessIds)) {
            response.setTotalLocations(businessIds.size());
            if(CollectionUtils.isEmpty(status)) {
                logger.info("[Youtube] blank status received hence returning for business {}",enterpriseId);
                response.setLocationList(new ArrayList<>());
                response.setDisconnectedCount(0);
                response.setUnmapped(0);
                response.setAllPagesMapped(false);
                response.setPermissionIssuePageCount(0);
                return response;
            }
            Boolean toSearch = Objects.nonNull(search) && !search.isEmpty();
            if (CollectionUtils.isNotEmpty(businessIds)){
                List<BusinessYoutubeChannel> businessYoutubeChannel = youtubeChannelRepository.findByBusinessIdIn(businessIds);
                response.setUnmapped(businessIds.size() - businessYoutubeChannel.size());
                if(status.size() > 1) {
                    prepareLocationYoutubePageMapping(businessIds, response, status, enterpriseId,page,size,search,toSearch,businessYoutubeChannel,includeModules);
                } else{
                    prepareMappedOrUnmappedData(businessIds,status,response,enterpriseId,page,size,search,toSearch,businessYoutubeChannel,includeModules);
                }

            }else {
                logger.error("[youtube] No business ids found for userId: {} and enterpriseIds: {}", userId, totalEnterpriseBusinessIds);
                throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found.");
            }
        }
        return response;
    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
        List<BusinessYoutubeChannel> youtubeLocations = youtubeChannelRepository.findByEnterpriseId(enterpriseId);
        if (youtubeLocations.isEmpty()) {
            return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
        } else {
            Optional<BusinessYoutubeChannel> result = youtubeLocations.stream()
                    .filter(location -> (location.getIsValid() != 1))
                    .findAny();

            if (result.isPresent()) {
                return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
            }
        }
        return ChannelSetupStatus.PageSetupStatus.OK;
    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
        List<BusinessYoutubeChannel> youtubeLocations = youtubeChannelRepository.findByEnterpriseId(enterpriseId);
        if (youtubeLocations.isEmpty()) {
            return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
        } else {
            Optional<BusinessYoutubeChannel> result = youtubeLocations.stream()
                    .filter(location -> (Objects.nonNull(location.getBusinessId()) && location.getIsValid() == 1))
                    .findAny();

            if (result.isPresent()) {
                return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
            }
            return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
        }
    }

    @Override
    public boolean checkIfAccountExistsByAccountId(Long accountId) {
        return youtubeChannelRepository.existsByEnterpriseIdAndIsSelected(accountId,1);
    }

    @Override
    public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
        logger.info("[Youtube Setup] Request received to cancel youtube request for business {}", businessId);
        BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.YOUTUBE.getName(),CONNECT);
        if(Objects.isNull(req)) {
            logger.error("No record found in business get page request for businessId: {}", businessId);
            return;
        }
        if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
        }
        req.setStatus(Status.CANCEL.getName());
        req.setUpdated(new Date());
        businessGetPageReqRepo.saveAndFlush(req);
        pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),req.getRequestType(),Status.COMPLETE.getName(), req.getEnterpriseId());
        releaseLock(req.getChannel(), req.getEnterpriseId());

    }

    @Override
    public List<SocialPageListInfo> getUnmappedYoutubeChannelsByEnterpriseId(Long enterpriseId) {
        logger.info("[Youtube Setup]: Request received to get unmapped pages for business {}", enterpriseId);
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
        if (CollectionUtils.isEmpty(youtubeChannels)) {
            return Collections.emptyList();
        }
        List<SocialPageListInfo> socialPageListInfos = new ArrayList<>();
        youtubeChannels.forEach(youtubeChannel -> {
            SocialPageListInfo listInfo = convertBusinessYoutubeChannelToLocationPageListInfo(youtubeChannel);
            socialPageListInfos.add(listInfo);
        });
        socialPageListInfos.sort(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder())));
        return socialPageListInfos;
    }

    @Override
    public ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus) {
        ConnectedPages connectedPage = new ConnectedPages();
        List<BusinessYoutubeChannel> connectedAccounts = youtubeChannelRepository.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
        ChannelPageDetails channelPageDetails = getPageDetailsFromAccount(connectedAccounts, pageConnectionStatus);
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
        pageTypes.put(SocialChannel.YOUTUBE.getName(), channelPageDetails);
        connectedPage.setPageTypes(pageTypes);
        return connectedPage;
    }

    @Override
    public ConnectedPages getPagesForPostReconnect(PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request) {
        ConnectedPages connectedPage = new ConnectedPages();
        List<BusinessYoutubeChannel> connectedAccounts = youtubeChannelRepository.findByChannelIdInAndIsSelected(request.getPageIds(), 1);
        ChannelPageDetails channelPageDetails = getPageDetailsFromAccount(connectedAccounts, pageConnectionStatus);
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
        pageTypes.put(SocialChannel.YOUTUBE.getName(), channelPageDetails);
        connectedPage.setPageTypes(pageTypes);
        return connectedPage;
    }

    @Override
    public void removeYoutubeChannel(List<String> pageIds,String type) {
        logger.info("[Youtube Setup] Remove youtube channel Ids {} ", pageIds);
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByChannelIdIn(pageIds);
        removeYoutubeChannels(youtubeChannels,type);
    }

    private void removeYoutubeChannels(List<BusinessYoutubeChannel> youtubeChannels,String type) {
        removeYoutubeChannelAndAudit(youtubeChannels,type);
        kafkaProducer.sendObject(Constants.SOCIAL_PAGE_REMOVED,
                youtubeChannels.stream().map(rawPage -> new ChannelPageRemoved(SocialChannel.YOUTUBE.getName(), rawPage.getChannelId(),
                        rawPage.getChannelName(), rawPage.getBusinessId(),Constants.ENTERPRISE.equals(type)?rawPage.getEnterpriseId():rawPage.getResellerId(), null, null,rawPage.getId())).collect(Collectors.toList()));
    }

    private void removeYoutubeChannelAndAudit(List<BusinessYoutubeChannel> youtubeChannels, String type) {
        youtubeChannels.forEach(rawPage -> {
            logger.info("Remove Page for page id :{}",rawPage.getChannelId());
            commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(),Collections.singletonList(rawPage),
                    null, rawPage.getBusinessId(),Constants.ENTERPRISE.equals(type)?rawPage.getEnterpriseId():rawPage.getResellerId());
            youtubeChannelRepository.delete(rawPage.getId());
        });
    }

    public void pushToKafkaForValidity(String channel, Collection<String> channelIds) {
        if(CollectionUtils.isEmpty(channelIds)) {
            return;
        }
        ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
        validityRequestDTO.setChannel(channel);
        validityRequestDTO.setYoutubeChannelIds(channelIds);
        kafkaProducer.sendObject(Constants.CHECK_VALIDITY,validityRequestDTO);
    }

    @Override
    public List<SocialPageListInfo> findPagesSocialList(Long businessId) {
        List<BusinessYoutubeChannel> youtubeBusinessLocations = youtubeChannelRepository.findByEnterpriseId(businessId);
        return youtubeBusinessLocations.stream()
                .map(this::convertBusinessYoutubeChannelToLocationPageListInfo)
                .sorted(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    @Override
    public void reconnectYoutubeChannel(Long parentId, List<String> channelIds, String accessToken, String redirectUri,
                                        Integer userId,String type) throws Exception {
        String key = SocialChannel.YOUTUBE.getName().concat(String.valueOf(parentId));
        boolean lock = redisService.tryToAcquireLock(key);
        logger.info("[Redis Lock] Lock status : {}",lock);
        logger.info("pageIds : {}",channelIds);
        if (lock) {
            try {
                // Check if another connect request is present in INIT or FETCHED state
                BusinessGetPageRequest existingInProgressReq =  Constants.RESELLER.equals(type)?
                        businessGetPageReqRepo.findFirstByResellerIdAndChannelAndStatusIn(parentId, SocialChannel.YOUTUBE.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name())):
                        businessGetPageReqRepo.findFirstByEnterpriseIdAndChannelAndStatusIn(parentId, SocialChannel.YOUTUBE.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));;

                if (existingInProgressReq != null) {
                    logger.info("reconnect youtube: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}", existingInProgressReq);
                    throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
                }
                pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(), RECONNECT, Status.INITIAL.getName(),parentId);
                GoogleAuthToken gmbAuth = getGoogleToken(parentId, accessToken, redirectUri);
                BusinessGetPageRequest request = new BusinessGetPageRequest();
                request.setBirdeyeUserId(userId);
                request.setSocialUserId(gmbAuth.getUserId());
                request.setChannel(SocialChannel.YOUTUBE.getName());
                if(type.equalsIgnoreCase(Constants.RESELLER)){
                    request.setResellerId(parentId);
                }else{
                    request.setEnterpriseId(parentId);
                }
                request.setPageCount(0);
                request.setStatus(Status.INITIAL.getName());
                request.setRequestType(RECONNECT);
                request.setEmail(gmbAuth.getEmail());
                businessGetPageReqRepo.saveAndFlush(request);
                submitReconnectFetchPageRequest(gmbAuth,channelIds,request,userId,parentId,type);
            } catch (Exception e) {
                // Cleanup redis cache for error cases.
                redisService.release(key);
                logger.error("[Redis Lock] (Youtube Reconnect) Lock released for business {}", parentId);
                if (e instanceof BirdeyeSocialException) {
                    BirdeyeSocialException beExp = (BirdeyeSocialException) e;
                    if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
                        throw beExp;
                }
                //Cleanup business get pages request table
                BusinessGetPageRequest req = checkInProgressRequestsGMB(parentId, RECONNECT, type);
                if (req != null) {
                    req.setStatus(Status.CANCEL.getName());
                    businessGetPageReqRepo.saveAndFlush(req);
                }
                pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(), RECONNECT, Status.COMPLETE.getName(),parentId,true);
            }
        } else {
            logger.info("[Redis Lock] (Youtube Reconnect) Lock is already acquired for business {}", parentId);
            throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
        }
    }

    private BusinessGetPageRequest checkInProgressRequestsGMB(Long businessId, String requestType, String type) {
        logger.info("Exception occurred in connect youtube, Checking for in progress request for business id {}", businessId);
        List<BusinessGetPageRequest> underProcessRequests = Constants.RESELLER.equals(type)?businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(businessId, Status.INITIAL.getName(),SocialChannel.YOUTUBE.getName(),requestType)
       : businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, Status.INITIAL.getName(),SocialChannel.YOUTUBE.getName(),requestType);

        if (CollectionUtils.isNotEmpty(underProcessRequests)) {
            return underProcessRequests.get(0);
        }
        return null;
    }

    private void submitReconnectFetchPageRequest(GoogleAuthToken gmbAuth, List<String> channelIds, BusinessGetPageRequest request, Integer userId,Long parentId,String type){
        String key = SocialChannel.YOUTUBE.getName().concat(String.valueOf(parentId));
        try {
            List<BusinessYoutubeChannel> businessYoutubeChannelsList = new ArrayList<>();
            TokensAndUrlAuthData tokensAndUrlAuthData = new TokensAndUrlAuthData();
            tokensAndUrlAuthData.setAccessToken(gmbAuth.getAccess_token());
            List<Channel> channelList = youtubeService.getChannel(tokensAndUrlAuthData);
            if (CollectionUtils.isNotEmpty(channelList)) {
                List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByChannelIdIn(channelIds);

                if(CollectionUtils.isEmpty(youtubeChannels)) {
                    return;
                }
                for(Channel c : channelList) {
                    Optional<BusinessYoutubeChannel> businessYoutubeChannel = youtubeChannels.stream().filter(v -> v.getChannelId().equals(c.getId())).findFirst();
                    if(businessYoutubeChannel.isPresent()) {
                        BusinessYoutubeChannel ytChannel = businessYoutubeChannel.get();
                        prepareBusinessYoutubeChannel(ytChannel, c, request, gmbAuth);
                        businessYoutubeChannelsList.add(ytChannel);
                        commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(ytChannel),
                                userId.toString(), ytChannel.getBusinessId(), Constants.ENTERPRISE.equals(type)?ytChannel.getEnterpriseId():ytChannel.getResellerId());
                    }
                }
                logger.info("Youtube channel to be updated are: {}", businessYoutubeChannelsList);
                youtubeChannelRepository.save(businessYoutubeChannelsList);
                businessYoutubeChannelsList.forEach(businessYoutubeChannel -> commonService.uploadPageImageToCDN(businessYoutubeChannel));
                // 1. release key 2.update firebase 3. update business request
                redisService.release(key);
                request.setStatus(Status.COMPLETE.getName());
                pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(), request.getRequestType(),request.getStatus(),parentId);
                businessGetPageService.saveAndFlush(request);

            }else{
                logger.info("No account received from youtube for businessId and requestId {} {}",parentId,request.getId());
                redisService.release(key);
                request.setStatus(Status.COMPLETE.getName());
                request.setErrorLog("No account received from youtube");
                businessGetPageService.saveAndFlush(request);
                CheckStatusRequest checkStatusRequest = CheckStatusRequest.toBuilder().with($->{
                    $.status = Status.COMPLETE.getName();
                    $.statusType = request.getRequestType();
                    $.error = false;
                }).build();
                pushCheckStatusInFirebase(SocialChannel.GMB.getName(),parentId,checkStatusRequest);
            }
        } catch (Exception exe) {
            redisService.release(key);
            request.setStatus(Status.CANCEL.getName());
            request.setErrorLog("No account received from youtube.");
            businessGetPageService.saveAndFlush(request);
            logger.error("Error while pushing youtube account location data", exe.getLocalizedMessage());
            pushCheckStatusInFirebase(SocialChannel.YOUTUBE.getName(),request.getRequestType(),Status.COMPLETE.getName(),parentId,true);
        }
    }

    private ChannelPageDetails getPageDetailsFromAccount(List<BusinessYoutubeChannel> connectedAccounts, PageConnectionStatus pageConnectionStatus) {
        //logger.info("[Youtube Setup] Get pages status and channel details for enterprise id: {}",connectedAccounts.get(0).getEnterpriseId());
        List<ChannelPages> pageInfo = new ArrayList<>();
        connectedAccounts.forEach(account -> {
            ChannelPages completePageInfo = getAccountInfo(account);
            if ( pageConnectionStatus == PageConnectionStatus.CONNECTED &&
                    completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName()) ) {
                pageInfo.add(completePageInfo);
            } else if ( pageConnectionStatus == PageConnectionStatus.DISCONNECTED && (
                    completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName()) ||
                            completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())
            ) ) {
                pageInfo.add(completePageInfo);
            } else if ( pageConnectionStatus == PageConnectionStatus.ALL ) {
                pageInfo.add(completePageInfo);
            }
        });
        return filterInvalidAndValidPage(pageInfo);
    }

    private ChannelPages getAccountInfo(BusinessYoutubeChannel page) {
        logger.info("[Youtube Setup] Convert channel pages info for page : {}",page.getChannelId());
        ChannelPages pageInfo = new ChannelPages();
        pageInfo.setId(page.getChannelId());
        pageInfo.setImage(page.getPictureUrl());
        pageInfo.setPageName(page.getChannelName());
        pageInfo.setLink(page.getChannelLink());
        Validity validity = fetchValidityAndErrorMessage(page);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setAddress(page.getCountryCode());
        return pageInfo;
    }

    private ChannelPageDetails filterInvalidAndValidPage(List<ChannelPages> pageInfo) {
        ChannelPageDetails channelPageDetails = new ChannelPageDetails();
        List<ChannelPages> invalidPages = new ArrayList<>();
        List<ChannelPages> validPages = new ArrayList<>();
        for(ChannelPages page: pageInfo) {
            if(page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
                validPages.add(page);
            } else {
                invalidPages.add(page);
            }
        }
        channelPageDetails.setDisconnected(invalidPages.size());
        invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
        validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
        invalidPages.addAll(validPages);
        channelPageDetails.setPages(invalidPages);
        return channelPageDetails;
    }

    private SocialPageListInfo convertBusinessYoutubeChannelToLocationPageListInfo(BusinessYoutubeChannel channel){
        SocialPageListInfo locationPageListInfo = new SocialPageListInfo();
        locationPageListInfo.setId(channel.getChannelId());
        locationPageListInfo.setPageName(channel.getChannelName());
        locationPageListInfo.setMapped(channel.getBusinessId() != null ? Boolean.TRUE : Boolean.FALSE);
        locationPageListInfo.setLink(channel.getChannelLink());
        locationPageListInfo.setImage(channel.getPictureUrl());
        Validity validity = fetchValidityAndErrorMessage(channel);
        locationPageListInfo.setValidType(validity.getValidType());
        locationPageListInfo.setErrorCode(validity.getErrorCode());
        locationPageListInfo.setErrorMessage(validity.getErrorMessage());
        locationPageListInfo.setConnectedInReseller(Objects.isNull(channel.getResellerId())?false:true);
        return locationPageListInfo;
    }

    private void prepareMappedOrUnmappedData(List<Integer> businessIds, Set<String> status, LocationPageMapping response, Long enterpriseId, Integer page,
                                             Integer size,String search,Boolean toSearch,List<BusinessYoutubeChannel> businessYoutubeChannels,List<String> includeModules) {
        Map<Integer,BusinessYoutubeChannel> mappedYtChannelsMap = businessYoutubeChannels.stream().collect(Collectors.toMap(BusinessYoutubeChannel::getBusinessId, Function.identity()));
        List<Integer> ytBusinessIds = new LinkedList<>();
        if(Objects.nonNull(mappedYtChannelsMap)){
            ytBusinessIds.addAll(mappedYtChannelsMap.keySet());
        }
        Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap;
        businessIds.removeAll(ytBusinessIds);
        if(status.contains(LocationStatusEnum.UNMAPPED.getName())){
            if(toSearch){
                businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
            }else{
                businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
            }
        }else {
            if(toSearch){
                businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(ytBusinessIds,page,size,search,response);
            }else{
                businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMapPaginated(ytBusinessIds,page,size);
            }
        }
        List<ChannelLocationInfo> locationList = new ArrayList<>();
        if(Objects.nonNull(businessIdLocationMap)){
            locationList = getChannelLocationInfoList(businessIdLocationMap,mappedYtChannelsMap,response,status,enterpriseId,includeModules);
        }
        locationList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
        response.setLocationList(locationList);
        response.setAllPagesMapped(businessIds.size() <= 0);
    }

    public void prepareLocationYoutubePageMapping(List<Integer> businessIds, LocationPageMapping response, Set<String> status, Long enterpriseId,
                                                  Integer page, Integer size, String search, Boolean toSearch, List<BusinessYoutubeChannel> pages,List<String> includeModules) {
        Map<Integer, BusinessLocationLiteEntity> mapPaginated;
        if(toSearch){
            mapPaginated = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
        }else{
            mapPaginated = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
        }

        logger.info("prepareLocationFBPageMapping: Total businessLocations fetched {}", mapPaginated.size());
        List<BusinessYoutubeChannel> businessYoutubeChannels = pages.stream().filter(p -> mapPaginated.containsKey(p.getBusinessId())).collect(Collectors.toList());
        Map<Integer, BusinessYoutubeChannel> mappedFbPagesMap = new HashMap<>();
        if(Objects.nonNull(businessYoutubeChannels)) {
            mappedFbPagesMap = businessYoutubeChannels.stream().collect(Collectors.toMap(BusinessYoutubeChannel::getBusinessId, Function.identity()));
        }
        List<ChannelLocationInfo> locationList = getChannelLocationInfoList(mapPaginated, mappedFbPagesMap, response, status, enterpriseId,includeModules);
        List<ChannelLocationInfo> locationListWithoutMapping = new LinkedList<>();
        List<ChannelLocationInfo> locationListWithMapping = new LinkedList<>();
        for (ChannelLocationInfo ll : locationList) {
            if (ll.getPageData() != null) {
                locationListWithMapping.add(ll);
            } else {
                locationListWithoutMapping.add(ll);
            }
        }

        locationListWithoutMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
        locationListWithMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));

        List<ChannelLocationInfo> finalList = new LinkedList<>();
        if(status.contains(LocationStatusEnum.UNMAPPED.getName())) {
            finalList.addAll(locationListWithoutMapping);
        }
        if(status.contains(LocationStatusEnum.MAPPED.getName())) {
            finalList.addAll(locationListWithMapping);
        }
        response.setLocationList(finalList);
    }

    private List<ChannelLocationInfo> getChannelLocationInfoList(Map<Integer, BusinessLocationLiteEntity> businessLocationsMap, Map<Integer, BusinessYoutubeChannel> mappedYtChannelMap,
                                                                 LocationPageMapping response, Set<String> status,Long enterpriseId,List<String> includeModules) {
        logger.info("Get channel location info for youtube enterprise id :{} ",enterpriseId);
        Integer disconnectedCount = 0;
        int permissionIssuePageCount = 0;
        List<ChannelLocationInfo> locationList = new ArrayList<>();
        if(Objects.isNull(businessLocationsMap)){
            return  locationList;
        }
        for (Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocationsMap.entrySet()) {
            ChannelLocationInfo locInfo = new ChannelLocationInfo();
            locInfo.setLocationId(entry.getKey());
            locInfo.setLocationName(entry.getValue().getAlias1() != null ? entry.getValue().getAlias1() : entry.getValue().getName());

            if (Objects.nonNull(mappedYtChannelMap) && mappedYtChannelMap.get(entry.getKey()) != null) {
                Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
                LocationPageListInfo locationPageListInfo = preparePageData(mappedYtChannelMap.get(entry.getKey()),includeModules);
                if(locationPageListInfo.getValidType().equals(ValidTypeEnum.INVALID.getName()) ||
                        locationPageListInfo.getValidType().equals(ValidTypeEnum.PARTIAL_VALID.getName()) ) {
                    disconnectedCount++;
                }
                pageInfoMap.put(SocialChannel.YOUTUBE.getName(), locationPageListInfo);
                locInfo.setPageData(pageInfoMap);
            }
            locationList.add(locInfo);
        }
        if(!(status.size() <= 1 && status.contains(LocationStatusEnum.UNMAPPED.getName()))) {
            response.setDisconnectedCount(disconnectedCount);
        }
        response.setPermissionIssuePageCount(permissionIssuePageCount);

        logger.info("Completed channel location info for youtube enterprise id :{} ",enterpriseId);
        return locationList;
    }

    private LocationPageListInfo preparePageData(BusinessYoutubeChannel businessYoutubeChannel,List<String> includeModules) {
        LocationPageListInfo pageInfo = new LocationPageListInfo();
        pageInfo.setId(String.valueOf(businessYoutubeChannel.getChannelId()));
        pageInfo.setImage(businessYoutubeChannel.getPictureUrl());
        pageInfo.setLink(businessYoutubeChannel.getChannelLink());
        pageInfo.setPageName(businessYoutubeChannel.getChannelName());
//        pageInfo.setHandle(businessYoutubeChannel.getHandle());
//        pageInfo.setAddress(businessYoutubeChannel.getLocation());
        Validity validity = fetchValidityAndErrorMessage(businessYoutubeChannel);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        if (CollectionUtils.isNotEmpty(includeModules)) {
            Map<String, PermissionDTO> permissionsMap = new HashMap<>();
            for (String module : includeModules) {
                if(ValidTypeEnum.VALID.getName().equalsIgnoreCase(pageInfo.getValidType())) {
                    permissionsMap.put(module, new PermissionDTO(true));
                } else {
                    permissionsMap.put(module, new PermissionDTO(false));
                }
            }
            pageInfo.setModulePermission(permissionsMap);
        }
        pageInfo.setConnectedInReseller(Objects.isNull(businessYoutubeChannel.getResellerId())?false:true);
        return pageInfo;
    }

    @Override
    public Validity fetchValidityAndErrorMessage(BusinessYoutubeChannel page) {
        // only for prod sanity, delete once testing is done.
        String reportingEnabledBusinessNumber = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.REPORTING_ENABLED_BUSINESS_NUMBER);

        Validity validity = new Validity();
        if(StringUtils.isNotEmpty(page.getPermissions()) && page.getIsValid()!=null && page.getIsValid()==1) {
            List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndModuleAndPermissionNameNotNull(
                    SocialChannel.YOUTUBE.getName(), INTEGRATION_MODULE);
            logger.info("Permission mapping for youtube is {}", permissionMappings);
            logger.info("Permission of BusinessYoutubeChannel is {}", page.getPermissions());
            String finalErrorMessage = DEFAULT_ERROR_MESSAGE_YOUTUBE_START;
            int numberOfErrors = 0;
            HashSet<String> permissions = new HashSet<>(Arrays.asList(page.getPermissions().split(" ")));
            for(PermissionMapping permissionMapping: permissionMappings) {
                if(!permissions.contains(permissionMapping.getPermissionName())) {
                    if(numberOfErrors == 0) {
                        finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
                    } else if(numberOfErrors > 0) {
                        finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                    }
                    numberOfErrors++;
                }
            }
            if (StringUtils.equalsIgnoreCase(reportingEnabledBusinessNumber,"all")
                    || reportingEnabledBusinessNumber.contains(page.getEnterpriseId()+"")) {
                PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                        SocialChannel.YOUTUBE.getName(),Constants.REPORT);
                if(!page.getPermissions().contains(permissionMapping.getPermissionName())) {
                    finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                    numberOfErrors++;
                }
            }
            logger.info("finalErrorMessage {} numberOfErrors {}", finalErrorMessage,numberOfErrors);
            if(numberOfErrors>0) {
                finalErrorMessage = finalErrorMessage.concat(". "+DEFAULT_ERROR_MESSAGE_YOUTUBE_END);
                validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
                validity.setErrorCode(PERMISSION_MISSING);
                validity.setErrorMessage(finalErrorMessage);
            } else {
                validity.setValidType(ValidTypeEnum.VALID.getName());
            }
        }else {
            validity.setValidType(ValidTypeEnum.INVALID.getName());
            validity.setErrorCode(Constants.INTEGRATION);
            PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                    SocialChannel.YOUTUBE.getName(),Constants.INTEGRATION);
            validity.setErrorMessage(permissionMapping.getErrorMessage());
        }
        return validity;
    }

    private Validity createValidityOnValidType(BusinessYoutubeChannel page) {

        String reportingEnabledBusinessNumber = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.REPORTING_ENABLED_BUSINESS_NUMBER);
        Validity validity = new Validity();

        if(ValidTypeEnum.VALID.getId().equals(page.getValidType())) {
            validity.setValidType(ValidTypeEnum.VALID.getName());
        } else if(ValidTypeEnum.INVALID.getId().equals(page.getValidType())) {
            validity.setValidType(ValidTypeEnum.INVALID.getName());
            validity.setErrorCode(Constants.INTEGRATION);
            PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                    SocialChannel.YOUTUBE.getName(),Constants.INTEGRATION);
            validity.setErrorMessage(permissionMapping.getErrorMessage());
        } else {
            validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
            if(StringUtils.isNotEmpty(page.getPermissions()) && page.getIsValid()!=null && page.getIsValid()==1) {
                List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannelAndModuleAndPermissionNameNotNull(
                        SocialChannel.YOUTUBE.getName(), INTEGRATION_MODULE);
                logger.info("Permission mapping for youtube is {}", permissionMappings);
                logger.info("Permission of BusinessYoutubeChannel is {}", page.getPermissions());
                String finalErrorMessage = DEFAULT_ERROR_MESSAGE_YOUTUBE_START;
                int numberOfErrors = 0;
                HashSet<String> permissions = new HashSet<>(Arrays.asList(page.getPermissions().split(" ")));
                for(PermissionMapping permissionMapping: permissionMappings) {
                    if(!permissions.contains(permissionMapping.getPermissionName())) {
                        if(numberOfErrors == 0) {
                            finalErrorMessage = finalErrorMessage.concat(permissionMapping.getErrorMessage());
                        } else if(numberOfErrors > 0) {
                            finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                        }
                        numberOfErrors++;
                    }
                }
                if (StringUtils.equalsIgnoreCase(reportingEnabledBusinessNumber,"all")
                        || reportingEnabledBusinessNumber.contains(page.getEnterpriseId()+"")) {
                    PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                            SocialChannel.YOUTUBE.getName(),Constants.REPORT);
                    if(!page.getPermissions().contains(permissionMapping.getPermissionName())) {
                        finalErrorMessage = finalErrorMessage.concat(", ").concat(permissionMapping.getErrorMessage());
                        numberOfErrors++;
                    }
                }
                logger.info("finalErrorMessage {} numberOfErrors {}", finalErrorMessage,numberOfErrors);
                finalErrorMessage = finalErrorMessage.concat(". "+DEFAULT_ERROR_MESSAGE_YOUTUBE_END);
                if(numberOfErrors == 0) {
                    finalErrorMessage = GENERIC_PERMISSION_MISSING_MESSAGE;
                }
                validity.setErrorCode(PERMISSION_MISSING);
                validity.setErrorMessage(finalErrorMessage);
            }
        }

        return validity;
    }

    private List<ChannelAccountInfo> getYoutubeChannelInfo(List<BusinessYoutubeChannel> channelList) {
        List<ChannelAccountInfo> accountInfo = new ArrayList<>();
        channelList.stream().forEach(page -> accountInfo.add(channelAccountInfo(page, null)));
        return accountInfo;
    }

    private void releaseLock(String channel, Long enterpriseId) {
        redisService.release(channel.concat(String.valueOf(enterpriseId)));
    }

    private List<BusinessYoutubeChannel> getYoutubeAccountsList(List<String> channelIds) {
        return youtubeChannelRepository.findByChannelIdIn(channelIds);
    }

    private void checkForUnMappedSmbPages(BusinessLiteDTO business) {
        if (checkBusinessSMB(business)) {
            List<BusinessYoutubeChannel> existingPages = youtubeChannelRepository.findByAccountId(business.getBusinessId());
            List<BusinessYoutubeChannel> mappedPage = existingPages.stream().filter(yt -> Objects.nonNull(yt.getBusinessId())).collect(Collectors.toList());
            List<String> mappedProfileIds = mappedPage.stream().map(BusinessYoutubeChannel::getChannelId).collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(mappedProfileIds)) {
                youtubeChannelRepository.deleteByAccountIdAndChannelIdNotIn(business.getBusinessId(),mappedProfileIds);
            } else {
                youtubeChannelRepository.deleteByAccountId(business.getBusinessId());
            }
        }
    }

    private List<BusinessYoutubeChannel> getYoutubeChannelList(List<String> channelIds, Long enterpriseId, String type) {
        return RESELLER.equals(type)?youtubeChannelRepository.findByResellerIdAndChannelIdIn(enterpriseId, channelIds)
				:youtubeChannelRepository.findByEnterpriseIdAndChannelIdIn(enterpriseId, channelIds);
    }



    private List<BusinessGetPageRequest> getRequestForBusiness(Long businessId, String status, String channel, String requestType, String type) {
    	return ENTERPRISE.equals(type)?businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, channel, requestType)
				:businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(businessId, status, channel, requestType);
    }

    @Override
    public ChannelAccountInfo channelAccountInfo(BusinessYoutubeChannel channel, Long enterpriseId) {
        logger.info("Create ChannelAccountInfo object for channel : {}",channel.getChannelId());
        ChannelAccountInfo accountInfo = new ChannelAccountInfo();
        accountInfo.setId(channel.getChannelId());
        accountInfo.setPageName(channel.getChannelName());
        accountInfo.setLink(channel.getChannelLink());
        accountInfo.setImage(channel.getPictureUrl());
        Validity validity = fetchValidityAndErrorMessage(channel);
        accountInfo.setValidType(validity.getValidType());
        accountInfo.setErrorCode(validity.getErrorCode());
        accountInfo.setErrorMessage(validity.getErrorMessage());
        accountInfo.setDisabled((channel.getIsSelected() != null && channel.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
        if(Objects.nonNull(channel.getEnterpriseId()) && Objects.nonNull(channel.getIsSelected())
                && channel.getIsSelected() == 1
                && Objects.nonNull(enterpriseId)) {
            commonService.setCommonChannelAccountInfo(accountInfo, channel.getBusinessId(), channel.getIsSelected(), enterpriseId, channel.getEnterpriseId());
        }
        if (Objects.nonNull(channel.getEnterpriseId()) && Objects.nonNull(enterpriseId)) {
            accountInfo.setSameAccountConnections(channel.getEnterpriseId().equals(enterpriseId));
            accountInfo.setDiffAccountConnections(!channel.getEnterpriseId().equals(enterpriseId));
        }
        return accountInfo;
    }

    private void savePageData(List<Channel> channelList, BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken,String type) {
        Map<String, Channel> channels = channelList.stream().collect(Collectors.toMap(Channel::getId,Function.identity()));
        Long accountId= Constants.ENTERPRISE.equals(type)?businessGetPageRequest.getEnterpriseId():businessGetPageRequest.getResellerId();
        logger.info("Youtube channels save for account id :{} with size :{}",accountId,channels.size());
        List<BusinessYoutubeChannel> existingChannels = youtubeChannelRepository.findByChannelIdIn(new ArrayList<>(channels.keySet()));
        Map<String, BusinessYoutubeChannel> existingChannelMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(existingChannels)) {
            existingChannelMap = existingChannels.stream().collect(Collectors.toMap(BusinessYoutubeChannel::getChannelId, Function.identity()));
            updateExistingYTChannels(existingChannels,channels,businessGetPageRequest, googleAuthToken,type);
        }
        if(existingChannelMap.size() == channels.size()){
            logger.info("No new channel found to add in account id :{} with size :{}",accountId,channels.size());
            return;
        }
        List<Channel> newChannels = new ArrayList<>();
        for(Map.Entry<String,Channel> entry : channels.entrySet()){
            if(!existingChannelMap.containsKey(entry.getKey())){
                newChannels.add(entry.getValue());
            }
        }
        logger.info("New channels found for account id :{} with size :{}",accountId,newChannels.size());
        if(CollectionUtils.isNotEmpty(newChannels)) {
            List<String> newChannelIds = new ArrayList<>();
            logger.info("Adding new channels for account id : {}",accountId);
            for (Channel channel : newChannels) {
                try {
                    BusinessYoutubeChannel ytChannel = new BusinessYoutubeChannel();
                    prepareBusinessYoutubeChannel(ytChannel, channel, businessGetPageRequest, googleAuthToken);
                    newChannelIds.add(ytChannel.getChannelId());
                    youtubeChannelRepository.saveAndFlush(ytChannel);
                    commonService.uploadPageImageToCDN(ytChannel);
                    commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(ytChannel),
                            String.valueOf(businessGetPageRequest.getBirdeyeUserId()), ytChannel.getBusinessId(), accountId);
                } catch (Exception exe) {
                    logger.error("For page id {} Error while saving the data {}", channel.getId(), exe.getMessage());
                }
            }
            pushToKafkaForValidity(YOUTUBE, newChannelIds);
        }
    }

    private void updateExistingYTChannels(List<BusinessYoutubeChannel> existingChannels, Map<String, Channel> channels,BusinessGetPageRequest request, GoogleAuthToken googleAuthToken, String type) {
        logger.info("Updating existing youtube channels for enterprise id : {} and reseller id: {}",request.getEnterpriseId(),request.getResellerId());
        List<String> updatedChannelIds = new ArrayList<>();
        Set<String> oldRequestIds = new HashSet<>();
        existingChannels.forEach(ytChannel -> {
            if((Constants.ENTERPRISE.equals(type) && Objects.isNull(ytChannel.getEnterpriseId())) || (Constants.RESELLER.equals(type) && Objects.isNull(ytChannel.getResellerId()))) {
                Channel channel = channels.get(ytChannel.getChannelId());
                ytChannel.setChannelDescription(channel.getSnippet().getDescription());
                ytChannel.setCountryCode(channel.getSnippet().getCountry());
                ytChannel.setChannelLink(Constants.YOUTUBE_CHANNEL_URL + channel.getId());
                ytChannel.setChannelStatus(channel.getStatus().getLongUploadsStatus());
                ytChannel.setPictureUrl(channel.getSnippet().getThumbnails().getDefault().getUrl());
                ytChannel.setChannelName(channel.getBrandingSettings().getChannel().getTitle());
                ytChannel.setUpdatedBy(request.getBirdeyeUserId());
                ytChannel.setUpdatedAt(new Date());
                ytChannel.setPermissions(YoutubeUtils.trimYoutubePermission(googleAuthToken.getScope()));
                updatedChannelIds.add(ytChannel.getChannelId());
            }
            oldRequestIds.add(ytChannel.getRequestId());
            ytChannel.setRequestId(request.getId().toString());
            commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(ytChannel),
                    request.getBirdeyeUserId().toString(), ytChannel.getBusinessId(), Constants.ENTERPRISE.equals(type)?ytChannel.getEnterpriseId():ytChannel.getResellerId());
        });
        pushToKafkaForValidity(YOUTUBE, updatedChannelIds);
        youtubeChannelRepository.save(existingChannels);
        existingChannels.forEach(page->commonService.uploadPageImageToCDN(page));
        youtubeChannelRepository.flush();
        if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
            kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(YOUTUBE, oldRequestIds));
        }
    }

    private void prepareBusinessYoutubeChannel(BusinessYoutubeChannel ytChannel, Channel channel, BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken) {
        logger.info("Prepare Youtube info for channel id {}",channel.getId());
        ytChannel.setChannelId(channel.getId());
        ytChannel.setEmailId(googleAuthToken.getEmail());
        ytChannel.setChannelDescription(channel.getSnippet().getDescription());
        ytChannel.setCountryCode(channel.getSnippet().getCountry());
        ytChannel.setChannelLink(Constants.YOUTUBE_CHANNEL_URL+channel.getId());
        ytChannel.setChannelStatus(channel.getStatus().getLongUploadsStatus());
        ytChannel.setPictureUrl(channel.getSnippet().getThumbnails().getDefault().getUrl());
        ytChannel.setChannelName(channel.getBrandingSettings().getChannel().getTitle());
        ytChannel.setRefreshTokenId(googleAuthToken.getRefreshTokenId());
        ytChannel.setRequestId(businessGetPageRequest.getId().toString());
        ytChannel.setCreatedBy(businessGetPageRequest.getBirdeyeUserId());
        ytChannel.setIsValid(1);
        ytChannel.setPermissions(YoutubeUtils.trimYoutubePermission(googleAuthToken.getScope()));
    }

    private BusinessGetPageRequest submitYoutubeRequest(Long businessId, Integer birdeyeUserId, GoogleAuthToken token, String status, String type) {
        BusinessGetPageRequest youtubeRequest = new BusinessGetPageRequest();
        youtubeRequest.setBirdeyeUserId(birdeyeUserId);
        youtubeRequest.setSocialUserId(token.getUserId());
        youtubeRequest.setChannel(SocialChannel.YOUTUBE.getName());
        if(type.equalsIgnoreCase(Constants.RESELLER)){
            youtubeRequest.setResellerId(businessId);
        }else{
            youtubeRequest.setEnterpriseId(businessId);
        }
        youtubeRequest.setStatus(status);
        youtubeRequest.setPageCount(0);
        youtubeRequest.setRequestType(CONNECT);
        youtubeRequest.setEmail(token.getEmail());
        return businessGetPageReqRepo.saveAndFlush(youtubeRequest);
    }

    private GoogleAuthToken getGoogleToken(Long businessId, String code, String redirectUri) throws Exception {
        Business business = businessRepo.findByBusinessId(businessId);
        SocialAppCredsInfo domainYoutubeInfo = getYoutubeCreds();

        GoogleAuthToken token = googleAuthenticationService.generateGoogleTokensUsingCode(domainYoutubeInfo.getChannelClientId(), domainYoutubeInfo.getChannelClientSecret(), code, redirectUri);
        logger.info(" For business {} GoogleAuthToken is {}", businessId, token);
        if (token.getAccess_token() == null) {
            logger.error("[GooglePlus Social] Failed to generated access token for business: {}", business.getBusinessId());
            throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, "Exception in generating access token.");
        }
        GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(token.getAccess_token());
        Integer refreshTokenId = null;
        logger.info("[GooglePlus Reconnect] User id for Google plus: {}, business id: {}", user.getId(), business.getId());
        if (StringUtils.isNotBlank(token.getRefresh_token())) {
            GoogleRefreshToken refreshToken = new GoogleRefreshToken();
            refreshToken.setRefreshToken(token.getRefresh_token());
            // setting the channel name to youtube
            refreshToken.setChannel(SocialChannel.YOUTUBE.getName().toLowerCase());
            refreshTokenId = socialPostGooglePlusService.saveGoogleAuthRefeshTokenDetails(refreshToken, user, business.getId(), Integer.parseInt(domainYoutubeInfo.getSocialCredsId()));
            token = googleAuthenticationService.getYoutubeAuthTokens(refreshTokenId);
            token.setRefreshTokenId(refreshTokenId);
        } else {
            token = getExistingValidToken(user.getId(), domainYoutubeInfo,token.getAccess_token());
            if (token == null) {
                logger.error("[GooglePlus Social] Failed to get refersh token business: {}", business.getBusinessId());
                throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, "Exception in generating access token.");
            }
        }
        token.setUserId(user.getId());
        token.setUserName(user.getName());
        token.setEmail(user.getEmail());
        return token;
    }

    private GoogleAuthToken getExistingValidToken(String userId, SocialAppCredsInfo domainGoogleInfo, String accessToken) {
        GoogleAuthToken token = null;
        /*
         * Google refresh token is generated once for each user. The next time user logs into same app, response will not have refresh token but only the
         * access token.
         */
        List<GoogleRefreshToken> savedRefreshTokens = googleRefreshTokenRepo.getByUserIdAndValidAndTypeAndChannel(userId, "social","youtube");
        logger.info("For user {} valid social saved Refresh Tokens {}", userId, (savedRefreshTokens != null ? savedRefreshTokens.size() : 0));

        // Still have this check for tokens if for that user we don't have any valid
        if (CollectionUtils.isEmpty(savedRefreshTokens)) {
            logger.error("[GooglePlus Social] no token with isValid = 1");
            List<GoogleRefreshToken> refreshTokens = googleRefreshTokenRepo.findByGoogleUserIdAndTypeAndChannel(userId, "social","youtube");
            logger.info("For user {} social Refresh Tokens {}", userId, (refreshTokens != null ? refreshTokens.size() : 0));
            if (CollectionUtils.isNotEmpty(refreshTokens)) {
                for (GoogleRefreshToken refToken : refreshTokens) {
                    try {
                        token = googleAuthenticationService.generateGoogleTokensUsingRefreshToken(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), refToken.getRefreshToken());
                        if (token != null) {
                            logger.info("For user {} got the Tokens {}", userId, token);
                            refToken.setIsValid(1);
                            if(Objects.isNull(refToken.getChannel())) {
                                refToken.setChannel(SocialChannel.YOUTUBE.getName().toLowerCase());
                            }
                            GoogleRefreshToken saveToken = googleRefreshTokenRepo.saveAndFlush(refToken);
                            token.setRefreshTokenId(saveToken.getId());
                            break;
                        }
                    } catch (Exception e) {
                        logger.info("[GooglePlus Social] could not create access token for refresh token id {}", refToken.getId());
                    }
                }
            } else {
                logger.info("Revoking the access token for user: {}",userId);
                googleAuthenticationService.revokeGoogleToken(accessToken);
                throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, "Exception in generating refresh token.");
            }
        } else {
            Integer refreshTokenId = null;
            for (GoogleRefreshToken savedRefToken : savedRefreshTokens) {
                try {
                    token = googleAuthenticationService.generateGoogleTokensUsingRefreshToken(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), savedRefToken.getRefreshToken());
                    if (token != null) {
                        logger.info("For user {} got the Tokens {}", userId, token);
                        refreshTokenId = savedRefToken.getId();
                        break;
                    }
                    logger.info("For user {} valid Token {} found ", userId, token);
                } catch (Exception e) {
                    logger.info("[GooglePlus Social] could not create access token for refresh token id {}", savedRefToken.getId());
                    // savedRefToken.setIsValid(0);
                    // googleRefreshTokenRepository.saveAndFlush(savedRefToken);
                }
            }
            if (refreshTokenId != null) {
                token.setRefreshTokenId(refreshTokenId);
            }
        }
        logger.info("in getExistingValidToken For user {} GoogleAuthToken is : {} ", userId, token);
        return token;
    }

    public void pushCheckStatusInFirebase(String channel,String requestType,String status,Long businessId) {
        CheckStatusRequest checkStatusRequest = new CheckStatusRequest(status,requestType);
        nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialAccountCheckStatusTopic(channel,businessId),checkStatusRequest, businessId.toString());
        logger.info("pushed check status in firebase for business with requestType, status for channel :{} {} {} {}",businessId,requestType,status,channel);
    }

    public void pushCheckStatusInFirebase(String channel,String requestType,String status,Long businessId,boolean error) {
        CheckStatusRequest checkStatusRequest = new CheckStatusRequest(status,requestType,error);
        nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialAccountCheckStatusTopic(channel,businessId),checkStatusRequest, businessId.toString());

        logger.info("pushed check status in firebase for business with requestType, status , error for channel :{} {} {} {} {}",businessId,requestType,status,error,channel);
    }

    @Override
    public boolean getYoutubePermission(Integer accountId, List<String> modules) {
        logger.info("Check Youtube post permissions for account id : {}",accountId);
        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByAccountIdAndBusinessIdNotNull(accountId);
        return checkPermission(businessYoutubeChannels,modules);
    }

    @Override
    public boolean getYoutubePostPermission(List<BusinessYoutubeChannel> youtubeChannels, List<String> modules) {
        return checkPermission(youtubeChannels,modules);
    }

    @Override
    public void updatePageDetails() {
        try {
            List<BusinessYoutubeChannel> businessYoutubeChannelsList = new ArrayList<>();

            int size = getDefaultListFetchSize();
            Date currentDate = simpleDateFormat.parse(simpleDateFormat.format(new Date()));

            Page<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository
                    .findByIsValidAndRefreshDataSyncDateIsLessThanEqualOrderByIdDesc(1, currentDate, new PageRequest(0, size));

            if(CollectionUtils.isEmpty(businessYoutubeChannels.getContent())) {
                logger.info("No valid youtube channels found for updating data");
                return;
            }

            businessYoutubeChannels.getContent().stream().forEach(yt -> {
                try {
                    logger.info("Analysing the channel details for {}", yt);
                    GoogleAuthToken gmbAuth = googleAuthenticationService.getYoutubeAuthTokens(yt.getRefreshTokenId());
                    if(Objects.isNull(gmbAuth)) {
                        logger.info("No auth details found for refresh token id {}", yt.getRefreshTokenId());
                        return;
                    }
                    TokensAndUrlAuthData tokensAndUrlAuthData = new TokensAndUrlAuthData();
                    tokensAndUrlAuthData.setAccessToken(gmbAuth.getAccess_token());
                    List<Channel> channelList = youtubeService.getChannel(tokensAndUrlAuthData);
                    if(CollectionUtils.isEmpty(channelList)) {
                        logger.info("No channel found for YT channel {}", yt);
                        return;
                    }

                    Optional<Channel> youtubeChannelDetails =  channelList.stream().filter(v -> v.getId().equals(yt.getChannelId())).findFirst();
                    if (youtubeChannelDetails.isPresent()) {
                        Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextMonthScanDate(currentDate)));

                        Channel channel = youtubeChannelDetails.get();
                        updateBusinessYoutubeChannelDetails(yt, channel, gmbAuth);
                        yt.setRefreshDataSyncDate(nextDate);
                        businessYoutubeChannelsList.add(yt);
                        commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(yt), null, yt.getBusinessId(), yt.getEnterpriseId());
                    }
                } catch (BirdeyeSocialException ex) {
                     if(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value() == ex.getCode()) {
                         logger.info("token is invalid");
                         yt.setIsValid(0);
                         businessYoutubeChannelsList.add(yt);
                         commonService.sendPageDisconnectAuditEvent(yt, null);
                     }
                } catch (Exception e) {
                    logger.info("Something went wrong while parsing data for channel {} with error {}",yt ,e.getMessage());
                }
            });

            logger.info("Channels updated list {}", businessYoutubeChannelsList);
            youtubeChannelRepository.save(businessYoutubeChannelsList);

        } catch (Exception e) {
            logger.info("Something went wrong while updating channel details with error {}", e.getMessage());
            throw new BirdeyeSocialException(e.getMessage());
        }
    }

    private static Date nextMonthScanDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, 30);
        try {
            return simpleDateFormat.parse(simpleDateFormat.format(c.getTime()));
        } catch (ParseException e) {
            logger.info("Not able parse date {}", e.getMessage());
            e.printStackTrace();
        }
        return date;
    }

    public int getDefaultListFetchSize() {
        return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty("youtube.month.scheduler.pagesize", SystemPropertiesCache.YOUTUBE_PAGE_SIZE_DEFAULT);
    }


    private void updateBusinessYoutubeChannelDetails(BusinessYoutubeChannel ytChannel, Channel channel, GoogleAuthToken googleAuthToken) {
        logger.info("Prepare to update Youtube info for channel ", channel);
        ytChannel.setChannelId(channel.getId());
//        ytChannel.setEmailId(googleAuthToken.getEmail());
        ytChannel.setChannelDescription(channel.getSnippet().getDescription());
        ytChannel.setCountryCode(channel.getSnippet().getCountry());
        ytChannel.setChannelLink(Constants.YOUTUBE_CHANNEL_URL+channel.getId());
        ytChannel.setChannelStatus(channel.getStatus().getLongUploadsStatus());
        ytChannel.setPictureUrl(channel.getSnippet().getThumbnails().getDefault().getUrl());
        ytChannel.setChannelName(channel.getBrandingSettings().getChannel().getTitle());
        ytChannel.setIsValid(1);
    }

    private boolean checkPermission(List<BusinessYoutubeChannel> businessYoutubeChannels, List<String> modules) {
        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
            logger.info("businessYoutubeChannels list is empty");
            return true;
        }
        for(BusinessYoutubeChannel youtubeChannel : businessYoutubeChannels) {
            if (youtubeChannel.getIsValid() == 0) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void validateSocialServiceTokens(SocialTokenValidationDTO payload) {
        GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(payload.getId());
        if (refreshToken == null || StringUtils.isEmpty(refreshToken.getRefreshToken())) {
            return;
        }
        logger.info("[Youtube Job] Initiating Youtube validation for: {}", refreshToken.getRefreshToken());
        refreshToken.setLastScannedOn(new Date());
        GMBPageDTO valid = socialPostGooglePlusService.validateYTRefreshToken(refreshToken);
        if (!valid.isValid()) {
            refreshToken.setIsValid(0);
            logger.info("[Youtube Job] GooglePlus Refresh token is invalid: {}", refreshToken);
        }
        List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByRefreshTokenId(refreshToken.getId());
        if(CollectionUtils.isEmpty(youtubeChannels)) {
            return;
        }
        logger.info("[Youtube Job] pages picked for validation check are {}", youtubeChannels);
        for(BusinessYoutubeChannel page: youtubeChannels) {
            if (!valid.isValid()) {
                page.setIsValid(0);
                commonService.sendPageDisconnectAuditEvent(youtubeChannels, null);
            }
        }
        youtubeChannelRepository.save(youtubeChannels);
        googleRefreshTokenRepo.saveAndFlush(refreshToken);
        List<String> channelIds = youtubeChannels.stream().map(BusinessYoutubeChannel::getChannelId).collect(Collectors.toList());
        pushToKafkaForValidity(YOUTUBE, channelIds);
    }

    @Override
    public void initiateDPSync() {
        logger.info("Request received to initiate profile pic sync for youtube accounts");
        try {
            List<BusinessYoutubeChannel> youtubeChannels = youtubeChannelRepository.findByIsValidAndEnterpriseIdNotNull(1);
            youtubeChannels.forEach(youtubeChannel -> {
                DpSyncRequest dpSyncRequest = new DpSyncRequest();
                dpSyncRequest.setId(youtubeChannel.getId());
                kafkaProducer.sendObjectV1(YOUTUBE_DP_SYNC_TOPIC,dpSyncRequest);
            });

        } catch (Exception e) {
            logger.info("Exception while initiating youtube Dp sync request: ",e);
        }
    }

    @Override
    public void syncYoutubeDP(DpSyncRequest youtubeDpSyncRequest) {
        logger.info("Request received to sync dp for youtube account with id: {}",youtubeDpSyncRequest.getId());
        try {
            BusinessYoutubeChannel youtubeChannel = youtubeChannelRepository.findById(youtubeDpSyncRequest.getId());

            if(Objects.isNull(youtubeChannel)) {
                logger.info("no page found");
                return;
            }

            Map<?,?> channelDetailsMap = youtubeExternalService.getChannelInformation(youtubeChannel.getChannelId());

            String profilePicUrl = getProfilePicUrl(channelDetailsMap);

            if(StringUtils.isNotEmpty(profilePicUrl)) {
                youtubeChannel.setPictureUrl(profilePicUrl);
                youtubeChannelRepository.save(youtubeChannel);
                commonService.uploadPageImageToCDN(youtubeChannel);
            }

        } catch (Exception e) {
            logger.info("Exception while syncing dp for youtube account with id: {}",youtubeDpSyncRequest.getId(),e);
        }
    }

    @Override
    public void removeYoutubeChannelByEnterprise(String name, Long enterpriseId) {
        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByEnterpriseId(enterpriseId);
        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
            logger.info("No YT channel found for enterpriseId :{}",enterpriseId);
            return;
        }
        businessYoutubeChannels.forEach(page ->{
            logger.info("Remove Page for id :{}",page.getChannelId());
            DeleteEventRequest request = DeleteEventRequest.builder()
                    .channel(SocialChannel.YOUTUBE.getName())
                    .pagesIds(Collections.singletonList(page.getChannelId()))
                    .build();
            kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
        });
    }

    @Override
    public void removeYoutubeChannelByBusinessId(String name, Integer businessId) {
        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByBusinessId(businessId);
        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
            logger.info("No YT channel found for business Id  :{}",businessId);
            return;
        }
        businessYoutubeChannels.forEach(page ->{
            logger.info("Remove Page for id :{}",page.getChannelId());
            DeleteEventRequest request = DeleteEventRequest.builder()
                    .channel(SocialChannel.YOUTUBE.getName())
                    .pagesIds(Collections.singletonList(page.getChannelId()))
                    .build();
            kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
        });
    }

    @Override
    public void removeUnmappedByEnterprise(Long enterpriseId, Integer businessId) {
        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByEnterpriseId(enterpriseId);
        if (CollectionUtils.isEmpty(businessYoutubeChannels)) {
            logger.info("No YT channel found for business Id  :{}", businessId);
            return;
        }
        businessYoutubeChannels.forEach(page -> {
            if (!Objects.equals(page.getBusinessId(), businessId)) {
                logger.info("Remove Page for page id :{}", page.getChannelId());
                commonService.deletePage(SocialChannel.YOUTUBE.getName(), page.getChannelId());
            }
        });
    }

    @Override
    public List<ApprovalPageInfo> findByYoutubeChannelId(String pageId) {
        return youtubeChannelRepository.findByChannelIdLite(pageId);
    }

    @Override
    public Map<String, Boolean> getYoutubePostPermissionsPageWise(List<BusinessYoutubeChannel> youtubeChannels, List<String> engage) {
        Map<String, Boolean> permissionMap = new HashMap<>();
        if(CollectionUtils.isEmpty(youtubeChannels)){
            logger.info("BusinessYoutubeChannel list is empty");
            return permissionMap;
        }
        for(BusinessYoutubeChannel youtubeChannel : youtubeChannels) {
            permissionMap.put(youtubeChannel.getChannelId(), true);
            if (youtubeChannel.getIsValid() == 0) {
                permissionMap.put(youtubeChannel.getChannelId(), false);
            }
        }
        return permissionMap;
    }

    @Override
    public List<String> findByBusinessIdIn(List<Integer> businessIds) {
        return youtubeChannelRepository.findDistinctPageIdByBusinessIdIn(businessIds);
    }

    @Override
    public void removePageMap(Integer businessId) {
        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByBusinessId(businessId);
        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
            logger.info("No page found to update youtube channel : {}",businessId);
            return;
        }
        businessYoutubeChannels.forEach(page -> {
            logger.info("Remove mapping for channel id :{}",page.getChannelId());
            commonService.removeMapping(page.getBusinessId(),page.getChannelId(),SocialChannel.YOUTUBE.getName(), null);
        });
    }

    @Override
    public void moveYoutubeAccountLocation(Long sourceBusinessAccountNumber, Long targetBusinessAccountNumber,
                                           Integer businessId, boolean isMultiLocationSource,Integer accountId) {
        List<BusinessYoutubeChannel> pageData = isMultiLocationSource
                ? youtubeChannelRepository.findByBusinessId(businessId)
                : youtubeChannelRepository.findByEnterpriseId(sourceBusinessAccountNumber);
        if(CollectionUtils.isEmpty(pageData)){
            logger.info("Unable to get page for business id:{} or enterprise id  :{}",businessId,sourceBusinessAccountNumber);
            return;
        }
        updateMappingForYoutubePages(sourceBusinessAccountNumber,targetBusinessAccountNumber,pageData);
    }

    @Override
    public void removeYoutubeChannelByPageIds(List<String> pagesIds) {
        List<BusinessYoutubeChannel> channels = youtubeChannelRepository.findByChannelIdIn(pagesIds);
        if(CollectionUtils.isEmpty(channels)){
            logger.info("No yt page found for page ids:{}",pagesIds);
            return;
        }
        removeYoutubeChannels(channels,Constants.ENTERPRISE);
    }

    private void updateMappingForYoutubePages(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, List<BusinessYoutubeChannel> mappingData) {
        mappingData.forEach(page-> {
            logger.info("Updated Youtube enterpriseId from {} to {}  for channel id {}", sourceEnterpriseNumber, targetEnterpriseNumber, page.getChannelId());
            commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.UPDATE_MAPPING.name(),
                    Collections.singletonList(page), null, page.getBusinessId(),page.getEnterpriseId());
            page.setEnterpriseId(targetEnterpriseNumber);
            youtubeChannelRepository.save(page);
        });
    }

    @Override
    public List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds) {
        return youtubeChannelRepository.findSocialBusinessPageInfoByBusinessIdIn(businessIds);
    }

    @Override
    public void updateYoutubeAccountIsValidStatus(String pageId, Integer isValid) {
        logger.info("Marking Youtube page invalid for pageId {}", pageId);
        BusinessYoutubeChannel businessYoutubeChannel = youtubeChannelRepository.findByChannelId(pageId);
        if(businessYoutubeChannel !=null){
            businessYoutubeChannel.setIsValid(isValid);
            youtubeChannelRepository.saveAndFlush(businessYoutubeChannel);
            commonService.sendYoutubeSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessYoutubeChannel),
                    null, businessYoutubeChannel.getBusinessId(), businessYoutubeChannel.getEnterpriseId());
            logger.info("Marked Youtube page invalid for pageId {}", pageId);
        }else{
            logger.info("No Youtube page found for pageId {}", pageId);
        }
    }

    private String getProfilePicUrl(Map<?,?> channelDetailsMap) {
        ObjectMapper mapper =  new ObjectMapper();
        JsonNode node = mapper.valueToTree(channelDetailsMap);

        if(Objects.nonNull(node)) {
            if(Objects.nonNull(node.get("thumbnails")) && Objects.nonNull(node.get("thumbnails").get("default")) &&
                    Objects.nonNull(node.get("thumbnails").get("default").get("url"))) {
                return node.get("thumbnails").get("default").get("url").asText();
            }
        }
        return null;
    }

	@Override
	public PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search,
                                                    ResellerSearchType searchType, PageSortDirection sortDirection,
                                                    ResellerSortType sortType, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                                    Boolean locationFilterSelected) {

		PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

		Long getInvalidPageIds = youtubeChannelRepository.findCountByResellerIdAndValidityType(resellerId,
                Arrays.asList(ValidTypeEnum.PARTIAL_VALID.getId(), ValidTypeEnum.INVALID.getId()));
		Page<BusinessYoutubeChannel> connectedPages  ;
		connectedPages = searchSortAndPaginate(search, resellerId, locationIds, pageConnectionStatus, userIds, 1, mappingStatus,
                page, size, sortDirection, sortType, locationFilterSelected);
		logger.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));
		if (CollectionUtils.isNotEmpty(connectedPages.getContent())) {
			connectedPages.getContent().stream().forEach(x -> {
				if (x.getChannelId() == null) {
					logger.error("YT channel Id is null for resellerId: {}, Data cleanup is required.", x.getResellerId());
				}
			});
		}
		try {
            ChannelPageDetails accountInfo = getResellerPageInfo(connectedPages.getContent());
            accountInfo.setDisconnected(Math.toIntExact(getInvalidPageIds));
            pageTypes.put(SocialChannel.YOUTUBE.getName(), accountInfo);

            connectedPage.setPageTypes(pageTypes);
            connectedPage.setPageCount(connectedPages.getTotalPages());
            connectedPage.setTotalCount(connectedPages.getTotalElements());
            return connectedPage;
        } catch (Exception e) {
            logger.info("exception occred while setting page details error: {}",e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
	}
	private Page<BusinessYoutubeChannel> search(String search, PageRequest pageRequest, Long resellerId) {
		Specification<BusinessYoutubeChannel> ytChannel = Specifications.where((youTubeSpecification.hasPageName(search))).
				and(youTubeSpecification.isSelected(1)).
				and(youTubeSpecification.hasResellerId(resellerId));

		return youtubeChannelRepository.findAll(ytChannel,pageRequest);
	}

    public Page<BusinessYoutubeChannel> searchSortAndPaginate(String search, Long resellerId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                                List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
                                                                PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected) {
        Specification<BusinessYoutubeChannel> spec = Specifications.where((youTubeSpecification.hasResellerId(resellerId)));
        if(Objects.nonNull(search)) {
            spec = Specifications.where(spec).and(youTubeSpecification.hasPageName(search));
        }
        if(CollectionUtils.isNotEmpty(businessIds)) {
            if(locationFilterSelected) {
                if(MappingStatus.UNMAPPED.equals(mappingStatus)) return new PageImpl<>(new ArrayList<>());
                else spec = Specifications.where(spec).and(youTubeSpecification.inBusinessIds(businessIds));
            } else {
                if(MappingStatus.MAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(youTubeSpecification.inBusinessIds(businessIds));
                } else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(youTubeSpecification.hasBusinessIdNullOrNotNull(true));
                } else {
                    Specification<BusinessYoutubeChannel> orSpec = Specifications.where(youTubeSpecification.inBusinessIds(businessIds));
                    orSpec = Specifications.where(orSpec).or(youTubeSpecification.hasBusinessIdNullOrNotNull(true));
                    spec = Specifications.where(spec).and(orSpec);
                }
            }
        } else {
            if(MappingStatus.MAPPED.equals(mappingStatus)) {
                return new PageImpl<>(new ArrayList<>());
            } else {
                spec = Specifications.where(spec).and(youTubeSpecification.hasBusinessIdNullOrNotNull(true));
            }
        }
        if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(youTubeSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.VALID.getId())));
            if(Objects.nonNull(isSelected)) {
                spec = Specifications.where(spec).and(youTubeSpecification.isSelected(isSelected));
            }
        } else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(youTubeSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.INVALID.getId(),
                    ValidTypeEnum.PARTIAL_VALID.getId())));
        } else {
            spec = Specifications.where(spec).and(youTubeSpecification.isSelected(isSelected));
        }
        if(CollectionUtils.isNotEmpty(createdByIds)) {
            spec = Specifications.where(spec).and(youTubeSpecification.inCreatedByIds(createdByIds));
        }
        PageRequest pageRequest = null;
        if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
            pageRequest = new PageRequest(page, size,
                    new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "channelName"));
        } else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            spec = Specifications.where(spec).and(youTubeSpecification.sortValidTypesInGroup(sortDirection));
            pageRequest = new PageRequest(page, size);
        } else {
            spec = Specifications.where(spec).and(youTubeSpecification.sortBusinessIdNullsFirst());
            pageRequest = new PageRequest(page, size);
        }

        return youtubeChannelRepository.findAll(spec, pageRequest);
    }
	
	private ChannelPageDetails getResellerPageInfo(List<BusinessYoutubeChannel> ytChannels) throws Exception {
			List<ChannelPages> pageInfo = new ArrayList<>();
			// Process for non empty connected X accounts.
			if (!ytChannels.isEmpty()) {
                List<Integer> businessIds = new ArrayList<>();
                List<Integer> userIds = new ArrayList<>();
                ytChannels.forEach(x->{
                    if(Objects.nonNull(x.getBusinessId())) businessIds.add(x.getBusinessId());
                    if(Objects.nonNull(x.getCreatedBy())) userIds.add(x.getCreatedBy());
                });
                Map<String, Object> businessLocations = null;
                CompletableFuture<Map<String, Object>> businessLocationsFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        return businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,true);
                    } catch (Exception e) {
                        logger.info("exception while executing business location future, error: {}", e.getMessage());
                        return new HashMap<>();
                    }
                });
                CompletableFuture<Map<Integer, BusinessCoreUser>> userDetailsFuture = CompletableFuture.supplyAsync(() -> {
                    try {
                        return coreBusinessService.getBusinessUserForUserId(userIds);
                    } catch (Exception e) {
                        logger.info("exception while executing user details future, error: {}", e.getMessage());
                        return new HashMap<>();
                    }
                });
                CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(businessLocationsFuture, userDetailsFuture);
                allCompletableFuture.get(10, TimeUnit.SECONDS);
                businessLocations = businessLocationsFuture.get();
                Map<Integer, BusinessCoreUser> userIdVsInfoMap= userDetailsFuture.get();
                Map<String, Object> finalBusinessLocations = businessLocations;
				ytChannels.stream().forEach(channel -> {
					BusinessLocationLiteEntity locationLite = null;
					if(Objects.nonNull(finalBusinessLocations) && Objects.nonNull(channel.getBusinessId())){
						logger.info("Prepare data for mapped account :{}",channel);
						Map<String ,Object> locationData = (Map<String, Object>) finalBusinessLocations.get(channel.getBusinessId().toString());
						locationLite = commonService.getMappedLocationInfo(locationData, channel.getBusinessId(), channel.getChannelName());
					}

                    BusinessCoreUser userDetail = null;
                    if(Objects.nonNull(channel.getCreatedBy()) && MapUtils.isNotEmpty(userIdVsInfoMap) &&
                            userIdVsInfoMap.containsKey(channel.getCreatedBy())) {
                        userDetail = userIdVsInfoMap.get(channel.getCreatedBy());
                    }

					ChannelPages completePageInfo = getResellerPageInfo(channel, locationLite, userDetail);
					pageInfo.add(completePageInfo);
				});
			}
			return commonService.sortInvalidAndValidPage(pageInfo);
		}

		private ChannelPages getResellerPageInfo(BusinessYoutubeChannel page,
				BusinessLocationLiteEntity locationDetails, BusinessCoreUser userDetail) {
			ChannelPages pageInfo = new ChannelPages();
			pageInfo.setId(page.getChannelId());
			pageInfo.setImage(page.getPictureUrl());
			pageInfo.setPageName(page.getChannelName());
			pageInfo.setLink(page.getChannelLink());
			pageInfo.setUserId(page.getEmailId());
			Validity validity = createValidityOnValidType(page);
			pageInfo.setValidType(validity.getValidType());
			pageInfo.setErrorCode(validity.getErrorCode());
			pageInfo.setErrorMessage(validity.getErrorMessage());
			pageInfo.setAddress(page.getCountryCode());
            pageInfo.setAddedBy(Objects.nonNull(userDetail)?businessCoreService.getFullUsername(userDetail):null);
			if (Objects.nonNull(locationDetails)) {
				pageInfo.setLocationId(locationDetails.getId());
				pageInfo.setLocationName(
						locationDetails.getAlias1() != null ? locationDetails.getAlias1() : locationDetails.getName());
				pageInfo.setLocationAddress(commonService.prepareBusinessAddress(locationDetails));
                pageInfo.setParentName(locationDetails.getAccountName());
			}
			return pageInfo;
		}

		@Override
		public void removeYoutubeChannel(List<String> pageIds, Integer limit) {
			 List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByChannelIdInWithLimit(pageIds,new org.springframework.data.domain.PageRequest(0,limit));
		        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
		            logger.info("No YT channels found for channel ids : {}",pageIds);
		            return;
		        }
		        List<String> channels=businessYoutubeChannels.stream().map(channel->channel.getChannelId()).collect(Collectors.toList());
		        removeYoutubeChannel(channels, RESELLER);
		}

		@Override
		public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber,
				Integer size, String search) {
			Map<String, Object> pageData = new HashMap<>();
			Map<String, List<ChannelAccountInfoLite>> pageType = new HashMap<>();
			List<ChannelAccountInfoLite> managed = new ArrayList<>();

			// fetchedPages =  defined pageNumber and pageSize to get pagableResult
			YoutubeLiteDto fetchedPages = com.birdeye.social.utils.StringUtils.isNotEmpty(search) ?
					convertToYoutubeObject(search,new org.springframework.data.domain.PageRequest(pageNumber, size, Sort.Direction.ASC, IS_SELECTED),
							businessGetPageRequest.getId().toString()):
					findByRequestIdOrderByCreatedAt(businessGetPageRequest.getId().toString(),new org.springframework.data.domain.PageRequest(pageNumber, size,Sort.Direction.ASC, IS_SELECTED));
			if (CollectionUtils.isNotEmpty(fetchedPages.getPageLites())) {
				fetchedPages.getPageLites().forEach(page -> managed.add(getYoutubeAcntInfoLite(page,businessGetPageRequest.getEmail())));
				if (CollectionUtils.isNotEmpty(managed))
					pageType.put(SocialChannel.YOUTUBE.getName(), managed);
			}
			pageData.put("pageType", pageType);
			pageData.put("totalCount", fetchedPages.getTotalElements());
			pageData.put("pageCount", fetchedPages.getTotalPages());
			return pageData;
		}

		private ChannelAccountInfoLite getYoutubeAcntInfoLite(BusinessYoutubeChannel channel, String userEmail) {
			ChannelAccountInfoLite accountInfo= new ChannelAccountInfoLite();
			accountInfo.setId(channel.getChannelId());
			accountInfo.setImage(channel.getPictureUrl());
			accountInfo.setPageName(channel.getChannelName());
			accountInfo.setLink(channel.getChannelLink());
	        Validity validity = fetchValidityAndErrorMessage(channel);
            accountInfo.setDisabled((channel.getIsSelected() != null && channel.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
            accountInfo.setValidType(validity.getValidType());
	        accountInfo.setErrorCode(validity.getErrorCode());
	        accountInfo.setErrorMessage(validity.getErrorMessage());
	        accountInfo.setAddress(channel.getCountryCode());
	        return accountInfo;
		}
		
		private YoutubeLiteDto convertToYoutubeObject(String search, org.springframework.data.domain.PageRequest pageRequest, String requestId) {
			Page<BusinessYoutubeChannel> page = searchWithRequestId(search,pageRequest,requestId);
			return getYouTubeLiteDto(page);
		}

		private YoutubeLiteDto getYouTubeLiteDto(Page<BusinessYoutubeChannel> page) {
			YoutubeLiteDto youtubeLiteDto = new YoutubeLiteDto();
			if(Objects.nonNull(page)) {
				youtubeLiteDto.setPageLites(page.getContent());
				youtubeLiteDto.setTotalElements(page.getTotalElements());
				youtubeLiteDto.setTotalPages(page.getTotalPages());
			}
			return youtubeLiteDto;
		}

		private Page<BusinessYoutubeChannel> searchWithRequestId(String search, org.springframework.data.domain.PageRequest pageRequest,
																  String requestId) {
			Specification<BusinessYoutubeChannel> twitterSpec = Specifications.where((youTubeSpecification.hasPageName(search))).
					and(youTubeSpecification.hasRequestId(requestId));

			return youtubeChannelRepository.findAll(twitterSpec,pageRequest);
		}
		
		public YoutubeLiteDto findByRequestIdOrderByCreatedAt(String requestId, org.springframework.data.domain.PageRequest pageRequest) {
			Page<BusinessYoutubeChannel> page = youtubeChannelRepository.findByRequestId(requestId,pageRequest);
			return getYouTubeLiteDto(page);
		}
		private void updateYTChannelForReseller(BusinessYoutubeChannel ytChannel, String type, Integer locationId) {
		    if (Constants.RESELLER.equals(type)) {
		        BusinessLiteDTO businessLiteDTO = iBusinessCoreService.getBusinessLite(locationId, false);
		        ytChannel.setAccountId(businessLiteDTO.getAccountId());

		        if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
		            ytChannel.setEnterpriseId(businessLiteDTO.getBusinessNumber());
		        } else {
		            ytChannel.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
		        }
		    }
		}

    @Override
    public void validityCheckForYoutube(Collection<String> youtubeChannelIds) {
        if(CollectionUtils.isEmpty(youtubeChannelIds)){
            logger.error("linkedin account ids can not be null");
            return;
        }
        logger.info("Request received to update validity column for youtube accountIds: {}", youtubeChannelIds);
        List<String> channelIds = (List<String>) youtubeChannelIds;

        List<BusinessYoutubeChannel> businessYoutubeChannels = youtubeChannelRepository.findByChannelIdIn(channelIds);
        if(CollectionUtils.isEmpty(businessYoutubeChannels)){
            logger.info("No pages found for account ids : {}", channelIds);
            return;
        }
        businessYoutubeChannels.forEach(account -> {
            account.setCanPost(getYoutubePostPermission(Collections.singletonList(account),
                    Collections.singletonList(SocialMessageModule.PUBLISH.name())) ? 1 : 0);
            updateYoutubeValidityType(account);
        });
        youtubeChannelRepository.save(businessYoutubeChannels);
        logger.info("Successfully saved to db");
    }

    @Override
    public void updateYoutubeValidityType(BusinessYoutubeChannel channel) {
        Validity validity = fetchValidityAndErrorMessage(channel);
        if(Objects.nonNull(validity.getValidType()) ) {
            if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())){
                channel.setValidType(ValidTypeEnum.PARTIAL_VALID.getId());
            } else if(validity.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())){
                channel.setValidType(ValidTypeEnum.VALID.getId());
            } else {
                channel.setValidType(ValidTypeEnum.INVALID.getId());
            }
        }
    }


    @Override
    public List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds) {
        if(CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
            return youtubeChannelRepository.findAllIdByBusinessIdIn(resellerLeafLocationIds);
        }
        return Collections.emptyList();
    }

    /**
     * @param requestIds
     */
    @Override
    public List<String> getMappedRequestIds(Set<String> requestIds) {
        return youtubeChannelRepository.findDistinctRequestIdByRequestIdIn(requestIds);
    }

}