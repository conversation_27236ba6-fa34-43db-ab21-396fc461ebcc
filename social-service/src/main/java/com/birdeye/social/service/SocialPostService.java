package com.birdeye.social.service;

import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.dto.SocialPostLite;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.request.google.GMBAnswerDTO;
import com.birdeye.social.external.request.google.GMBAnswerDTO.Answer;
import com.birdeye.social.external.request.google.GMBQuestionDTO;
import com.birdeye.social.external.request.google.GMBQuestionDTO.QuestionDTO;
import com.birdeye.social.external.request.linkedin.TargetAudienceResponse;
import com.birdeye.social.external.request.posting.*;
import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.model.*;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.model.approval_workflow.ApprovalWorkFlowData;
import com.birdeye.social.model.approval_workflow.InboxRequestEvent;
import com.birdeye.social.model.bulk.posting.SocialUploadData;
import com.birdeye.social.model.es.SocialPostCalendarMessage;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.platform.entities.User;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.sro.SocialTagMappingOperationRequest;
import com.birdeye.social.sro.applePost.AppleLocationStatusResponse;
import com.birdeye.social.sro.bulkupload.BulkUploadDraftRequest;
import com.birdeye.social.sro.bulkupload.PostInfoRequest;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SocialPostService {
	
	 void postOnSocialSites(Integer businessId,Integer userId, SocialPostInputMessage socialPost, String requestSource)
			throws Exception;

	void postScheduleAcknowledgment(SamayAcknowledgementRequest data);

	void scheduledPostOnSocialSites(SocialPostInputMessageRequest socialPost, Integer masterPostId,
									Set<SocialTagMappingOperationRequest> tagMappings, Long businessNumber, boolean isReseller,
									String requestSource) throws Exception;


	CreatePostResponse scheduledMasterPostOnSocialSites(SocialMasterPostInputMessages socialPosts, Integer businessId, Long businessNumber, boolean isReseller, String requestSource)
			throws Exception;

	SocialMasterPost getSocialMasterPostObjectNew(SocialPostInputMessageRequest socialPost, User user) throws JsonProcessingException;
	List<Integer> getAssetIds (List < MediaData > socialAssetsData, String type, String videoThumbnailUrl, Integer businessId);

	SocialPost getSocialPostObjectNew (SocialPostInputMessageRequest socialPost, User user, Boolean
			isQuotedTweet, Integer quotedPostId) throws Exception;

	void immediatePostOnSocialSites(SocialPostInputMessageRequest socialPost, Integer masterPostId,
									Set<SocialTagMappingOperationRequest> tagMappings, Long businessNumber, boolean isReseller, String requestSource) throws Exception;

	CreatePostResponse immediateMasterPostOnSocialSites(SocialMasterPostInputMessages socialPosts, Long businessNumber, boolean isReseller, String requestSource) throws Exception;


	void processDataForScheduledPosts(SocialPostScheduleIdsRequest request, Boolean logActivity, List<SocialPostCalendarMessage> socialPostCalendarMessageList)
			throws Exception;

	void postDataOnSocialSites(SocialPostPublishInfoRequest data)
			throws Exception;

	void postDataOnSocialSitesPreProd(SocialPostPublishInfoRequest data)
			throws Exception;

	void removeSchedulerData(Integer postId, Integer userId, String activity, Integer businessId, boolean isBackupRequired);

	void removeSchedulerDataV2(AccessibleLocationIdsInput accessibleLocationIdsInput, Integer postId, Integer userId, String activity, Integer businessId);

	void processingFailedPostNotification(SocialFailedPostIdsRequest socialFailedPostIdsRequest);


	List<Integer> getBusinessIdFromPageId(Integer sourceId, List<String> pageIds);

	List<LocationPagePair> getBusinessIdPageIdPairFromPageId(Integer sourceId, List<String> pageIds,
															 Map<String, Boolean> pagePermissionMap);

	List<LocationPagePair> getBusinessIdPageIdPairFromBusinessId(List<Integer> sourceId, List<Integer> businessIds);

	List<String> getPageIdFromBusinessId(Integer sourceId, List<Integer> businessIds);

	SocialPostPermissionStatusResponse getPermissionStatus
			(List<Integer> accessibleLocationIds, SocialSchedulePostMessage
					postMessage, List<SocialPostScheduleInfo> scheduleInfos);

	List<Integer> getAllSourceIdList();

	EditPostResponse editSchedulerPostData(SocialPostInputMessageRequest data, Set<SocialTagMappingOperationRequest> tagMappings, Long businessNumber, boolean isReseller, String requestSource) throws ParseException;

	EditMasterPostResponse editSchedulerMasterPostData(SocialMasterPostInputMessages data, Long businessNumber, boolean isReseller, String requestSource, Integer businessId) throws Exception;

	void schedulerPostNowData(Integer postId, Integer businessId, String activity, Integer userId) throws Exception;

	void schedulerPostNowDataV2(AccessibleLocationIdsInput input, Integer postId, Integer businessId, String activity, Integer userId, Long enterpriseId, String requestSource) throws Exception;

	void draftPostNowData(Integer postId, Integer businessId, String activity, Integer userId, boolean isReseller, Long businessNumber, String requestSource) throws Exception;

	SocialScheduleEditPostResponse getSchedulerPostData(Integer postId, String type, Integer srcId, BusinessIdRequest businessIdRequest) throws Exception;

	Date convertScheduleDateToTimezone(String date) throws ParseException;

	public void updatePost(Integer businessId, Integer userId, SocialPostInputMessage socialPost, String requestSource) throws Exception;
	
	public void deletePost(Integer postId) throws Exception;
	
	public PostMessageOutput getAllPosts(Integer businessId, Integer userId, GlobalFilterCriteriaMessage filter, Integer count) throws Exception;
	
	public Map<String,List<SocialPageInfo>> getConfig(Integer businessId, Integer userId) throws Exception;
	
	public SocialPostAssets getAssets(Integer businessId) throws Exception;
	
	public List<InstalledSocialAccounts> getAllAddedAccounts(Integer businessId, Integer userId) throws Exception;
	
	public void deleteAccount(Integer businessId, String id, String channel) throws Exception;
	
	public void setAccountConfigurations(Integer businessId, String pageId, Integer minRating, boolean isEnabled, String channel);

	public ActivityResponse shareReview(SocialPostInputMessage socialPost);

	SocialNotificationResponse getNotifications(Integer businessId, Integer pageNo, Integer pageSize);

	boolean toggleInstagramPublishingStatus(Integer postId, Integer status);

	Map<String, List<SocialPageInfo>> getConfig(String sessionToken, boolean validPages, boolean getGmb, boolean twitterHandle);

	/**
	 * Fetch metatags of a URL
	 *
	 * @param url
	 * @return
	 */
	MetatagsResponseDTO getMetatagsOfAUrl(String url);

	/**
	 * update social page info
	 *
	 * @param channel
	 * @param businessId
	 * @throws IOException
	 */
	Object updateSocialPageInfo(String channel,Integer businessId,LocationInfoMessage locationInfoMessage,boolean update) throws Exception;

	/**
	 * create Gallery media
	 *
	 * @param channel
	 * @param businessId
	 * @param locationInfoMessage
	 * @throws Exception
	 */

	Object createSocialPageMedia(String channel,Integer businessId,LocationInfoMessage locationInfoMessage) throws Exception;

	/**
	 * delete Gallery media
	 *
	 * @param channel
	 * @param businessId
	 * @param gmbImageId
	 * @throws Exception
	 */
	Object deleteSocialPageMedia(String channel,Integer businessId,String gmbImageId) throws Exception;

	/**
	 * delete Gallery media
	 *
	 * @param channel
	 * @param businessId
	 * @param locationInfoMessage
	 * @throws Exception
	 */
	Object updateSocialMediaCategory(String channel, Integer businessId, LocationInfoMessage locationInfoMessage) throws Exception;

	SocialPostPageStatusData fetchPagesForPost(SocialPostStatusRequest socialPostStatusRequest, Integer accountId, boolean isReseller);

	Map<Integer, List<CalendarViewPagePostInsightsData>> getInsightDataBulk(List<Integer> bePostIds, List<String> pageIds);

	List<Integer> fetchlocationsByScheduleCondition(SocialPostScheduleInfo fetchedSchedule);

	List<Integer> fetchlocationsByScheduleCondition(SocialPostCalendarMessage socialPostCalendarMessage);

	SocialPostScheduleInfo savePostScheduleInfo(Integer enterpriseId, Integer postId, Date publishDate,
												Integer aggSourceId, PostingPageScheduler postingPage, List<String> pageIds,
												int isPublished, CreatePostGroupDetails postGroupDetails, boolean isReseller, String requestSource);

	void checkForGMBPostStatus();

	void checkForTiktokPostStatus();

	void checkForGMBPostStatusForFailedPosts();

	void retryPosts(SocialPostRetryRequest data);

	SocialGenericResponse saveDraft(SocialPostInputMessageRequest postDraftInputMessageRequest, boolean isReseller);

	SocialGenericResponse editDraft(SocialPostInputMessageRequest postDraftInputMessageRequest);

	SocialDraftEditPostResponse getDraftData(Integer id, Integer accountId, boolean isReseller) throws Exception;

	SocialGenericResponse deleteDraft(Integer id);

	SocialDraftPostListResponse draftListData(Integer businessId, BusinessIdRequest businessIdRequest);

	SocialDraftPostCountResponse draftListCount(Integer businessId, BusinessIdRequest businessIdRequest);

	String convertPublishedStateToStatus(Integer publishState, Integer bucket);

	void retryProcessingPosts(String channel);

	List<TargetAudienceResponse> getTargetAudienceList(String pageId, String category);

	SocialPostPermissionStatusResponse postPermissionStatus(Integer businessId, List<String> modules);

	SocialMentionsData searchMentionData(String channel, String search, Integer businessId, Long enterpriseId, String nextToken);

	List<YoutubeCategory> getYoutubeCategories(String channelId) throws Exception;

	SocialPostInputMessageRequest createScheduleInfoRequest(SocialPostInputMessagePublicRequest publicPostingRequest, List<String> previewUrlText);

	List<YoutubePlaylist> getPlaylistForChannel(String channelId) throws Exception;

	SocialLocationsTagData searchLocationData(String channel, String search, Integer businessId);

	Integer updateFailureReason(String errorCode, String description);

	SocialPublicResponsePayload createPublicSchedule(SocialPostInputMessagePublicRequest socialPostInputMessagePublicRequest, Long businessNumber,String requestSource);

	SocialPublicTrackingPayload trackPostInfo(String trackingId, Integer businessId);

	SocialGenericResponse disableFailedPost(Integer postId, Integer userId, Integer isDisabled, Integer srcId, String activity, Integer businessId, boolean isReseller);

	FailedPostListResponse getAllFailedPosts(Integer businessId, FailedPostFilter filter, boolean isReseller, String requestSource) throws Exception;

	SocialPostPageStatusDataSMB fetchPagesForPostSMB(SocialPostStatusRequestSMB socialPostStatusRequest);

	GetPageDetailForPostResponse getPageDetailsForPosts(String type, GetPageDetailForPostRequest request, Integer accountId, boolean isReseller) throws Exception;

	GetPageCountForPostResponse getPageCountForPosts(GetPageCountForPostRequest request) throws Exception;

	SocialGenericResponse deletePublishedPosts(DeletePublishedRequest deletePublishedRequest, Integer businessId, Integer postId, Integer userId, boolean isReseller);

	EditMasterPostResponse editPublishedMasterPostData(SocialMasterPostInputMessages data, Long businessNumber, String requestSource, Integer businessId) throws Exception;
	List <ChannelLocationInfo> getPublishedPagesList(Integer postId, LocationMappingRequest request, Integer businessId) throws Exception;
	void updateDataOnSocialSites(SocialPostPublishInfoRequest data) throws Exception;

	TweetDetailsResponse getTwitterDetails(String postId, Boolean isParent,String source);

	FailedPostResponse getFailedPosts(FailedPostIdFilter filter);
	//void retryPostsLoop(List<SocialPostRetryRequest> data);

	void postBulkDraft(BulkUploadDraftRequest request);

	void prepareMasterPostRequest(PostInfoRequest request, String requestSource);

	SocialPost findByPostId(Integer postId);

	String findThumbnailById(String videoIds);

	void updateFailedPostNotification(List<Integer> publishInfoIds);

	void samayNotificationSchedule(SocialPostPublishInfo socialPostPublishInfo);

	void pickFailedPost();

	void postOnSocialSitesDirect(SocialPostInputMessage socialPost)
			throws Exception;
	List<AggregationSourceMessage> getAllSocialSources(Integer businessId);

	CalendarEventResponse getCalendarEvents(GetCalendarEventRequest request);

	CalendarEventResponse getCalendarEventsWithBusinessId(GetCalendarEventRequest request, Integer businessId);

	void saveCalendarEvents(SaveCalendarEventRequest request);

	CalenderEventCountyListResponse getEventCountryList(Long enterpriseId);

	SocialGenericResponse updateEventLocations(Long enterpriseId, UpdateLocationEventRequest request);

	Map<String, Integer> getIgPostLimit(IgPostLimitRequest request);

	SocialGenericResponse saveMasterDraft(SocialMasterPostInputMessages socialMasterPostInputMessages, Long businessNumber, boolean isReseller, String requestSource);

	SocialGenericResponse editMasterDraft(SocialMasterPostInputMessages socialMasterPostInputMessages, boolean isReseller, String requestSource);

	SocialMasterDraftEditPostResponse getMasterDraftData(Integer id, Integer accountId, boolean isReseller) throws Exception;

	SocialGenericResponse deleteMasterDraft(Integer id, Integer businessId, boolean isReseller);

	SocialDraftPostListResponse masterDraftListData(Integer businessId, BusinessIdRequest businessIdRequest, boolean isReseller);

	void migrateMasterPostScheduledData(PostMigrationRequest postMigrationRequest) throws Exception;

	void migrateMasterPostDraftData(PostMigrationRequest postMigrationRequest) throws Exception;

	void migrateMasterPostPublishedData(PostMigrationRequest postMigrationRequest) throws Exception;

	void migratePostData(PostMigrationRequest postMigrationRequest, String type) throws Exception;


	QuestionDTO createGMBQuestion(Integer businessId, QuestionDTO question)throws Exception;

	Answer upsertGmbAnswer(Integer businessId, GMBAnswerDTO answer)throws Exception;

	QuestionDTO updateGMBQuestion(Integer businessId, QuestionDTO question)throws Exception;

	GMBQuestionDTO returnGMBQuestionsList(Integer businessId, boolean checkNext)throws Exception;

	GMBAnswerDTO returnGMBAnswersList(Integer businessId, GMBAnswerDTO answer)throws Exception;

	Map<String, Object> deleteGmbAnswer(Integer businessId, GMBAnswerDTO answer)throws Exception;

	Map<String, Object> deleteGmbQuestion(Integer businessId, QuestionDTO question)throws Exception;

	Object updateGMBAttributes(String channel, Integer businessId, LocationInfoMessage locationInfoMessage,
			boolean update)throws Exception;
			
	void movePostScheduleData(Integer sourceBusinessId,Integer sourceEnterpriseId, Integer targetBusinessAccountId);

	void updateSocialPostScheduleInfo(LocationMovementPostDTO request);

	void removeScheduledPosts(Integer enterpriseId, Integer businessId, boolean isBackupRequired);


	void saveOrUpdateSocialPostOnEs(SocialPostEsSyncRequest socialPostEsSyncRequest) throws Exception;

	List<ApprovalWorkFlowData> getPostDetailsFromPostId(Integer userId, Integer businessId, Long enterpriseId, String postId, boolean isPublicPost, String timeZone);

  	void saveApprovalUpdate(ApprovalWorkflowEvent approvalWorkflowEvent) throws Exception;

	void saveApprovalActivity(ApprovalActivity approvalActivity);

    void savePostDetails(SocialPost socialPost);

	void updateConversation(InboxRequestEvent requestEvent);

	void setAppleMetaData(SocialSchedulePostMessage socialSchedulePostMessage, Integer socialPostId);

	List<SocialPost> getSocialPost(Set<Integer> distinctPostIds, List<String> approvalState, List<Integer> approveWorkflowIds, List<Integer> creatorIds,
								   List<FilterPostType> postType, List<FilterPostType> postContent);

	abstract Map<Integer, SocialPost> generateMapForIdAndSocialPost(List<SocialPost> socialPostList);

	List<String> getMediaSequence(SocialPost socialPost, Integer enterpriseId) throws Exception;

	List<String> getMediaSequence(SocialPostCalendarMessage socialPostCalendarMessage, Integer enterpriseId) throws Exception;

	List<MediaData> getMediaData (String imageIds, Integer parentId, String type);

	AppleShowcaseLocationResponse getAppleShowcase(AppleLocationStatusResponse request);

	Date convertTimeStampToDate(Long scheduleTimestamp);
	String convertEpochToDate (Long scheduleTimestamp);
	Map<String, PostingPageScheduler> getPostingSiteData (SocialPostInputMessagePublicRequest input,String accountPostingKey,String postType);

	void saveAndPostActivity (SocialPostInputMessageRequest socialPost, Integer masterPostId, boolean isReseller,
								 String requestSource, List<SocialPostCalendarMessage> socialPostCalendarMessageList) throws Exception ;

	void uploadSocialPostDocument(SocialDocumentRequest document);

	Object getSocialPostDocument(String documentId);

	SocialPostPageAndActivityData getPagesAndPostActivityCombine(Integer postId, SocialPostStatusRequest socialPostStatusRequest, Integer enterpriseId);

	SocialSchedulePostMobileResponse getAllScheduledPostsForMobile(GlobalFilterCriteriaSchedulePostMessage filter);

	void updateTagInApprovalPostEs(SocialTagEntityMappingActionEvent tagUpdateEvent);
	Integer findMasterPostIdByPostId(Integer socialPostId);
	SocialSchedulePostResponse getAllScheduledPostsForReseller(GlobalFilterCriteriaSchedulePostMessage filter);

	boolean isPostedFromReseller(SocialPostScheduleInfo socialPostScheduled);

	boolean isPostedFromReseller(String postMethod);

	List<PageDetail> getPageDetailsFromPageIds (Integer sourceId, List < String > pageIds);

	List<String> getMediaSequenceV2 (PostLib postLib, Long businessNumber) throws Exception;

	List<String> getMediaSequenceV2(PostLibMaster postLibMaster, Long businessNumber) throws Exception;

	List<String> getMediaSequenceV2 (String metadata, Long businessNumber) throws Exception;
	LocationTagMetaDataRequest locationMetaData (String postMetadata);
	Set<String> getPageIdsFromSourceAndBusinessId(Integer sourceId, List<Integer> businessIds);
    List<SocialPostLite> findIdByMasterPostIdIn(List<Integer> masterPostIds);

	PostAssetsData setSocialPostsAssetsDataV2(List<SocialPostsAssets> socialPostAssets, Long parentId, String type);

	List<SocialPostsAssets> getPostsAssetsById(Map<Integer, SocialPostsAssets> postAssetsMap, String imageIds);

	List<MediaData> getMediaDataV2(List<SocialPostsAssets> postAssets, Long enterpriseLongId, String type);

	List<String> getMediaRequestV2(List<SocialPostsAssets> postsAssets, Long enterpriseBizNum, String type);

	List<String> getMediaUrl (List < String > mediaUrl, Long businessNumber);

	List<SocialPostInputMessageRequest> getSeparatePostRequest(SocialPostInputMessageRequest socialPostRequest);
	Map<String, PostingPageScheduler> getPostingSiteDataForBulkSchedule (SocialPostInputMessagePublicRequest input, String postingKey, SocialUploadData socialUploadData);

	void removePageFormScheduleInfo(String channel, String pageId);

	void deleteScheduledPosts(Integer enterpriseId, boolean isBackupRequired);

	FacebookMetadataResponse fetchFacebookMetadata(FacebookMetadataRequest request);

	void checkIgContainerStatus(IgContainerCheckDTO igContainerCheckDTO, boolean markFailed) throws BirdeyeSocialException;
	void updatePostApprovalExpiryStatus(SocialPostDetailsRequest data);
	List<SocialPost> findByPostIds(List<Integer> postIds);

    void checkForGMBPostStatusForPostsInList(List<Integer> publishInfoIds);

	void syncBusinessPostsAndSaveInES(SocialPostPublishInfoRequest request);
	SocialReportSummaryResponse getPostReportSummary(SocialReportSummaryRequest request) throws Exception;

    void restorePosts(Long enterpriseId);

	void processPendingPosts(Integer processingPostId, String channel);

	List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String channel, String keyword, Integer businessId) throws Exception;
}