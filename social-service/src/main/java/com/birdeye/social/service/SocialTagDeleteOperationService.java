package com.birdeye.social.service;

import com.birdeye.social.AbstractSocialTagOperationService;
import com.birdeye.social.constant.SocialTagOperation;
import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.entities.SocialTag;
import com.birdeye.social.sro.AbstractSocialTagOperation;
import com.birdeye.social.sro.SocialTagOperationResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 22/12/23
 */
@Service
@Slf4j
public class SocialTagDeleteOperationService extends AbstractSocialTagOperationService {

    @Autowired
    private SocialTagDBService socialTagDBService;

    @Override
    public SocialTagOperationResponse performOperation(Integer accountId, Long accountNum, Long userId,
                                                       Set<AbstractSocialTagOperation.SocialTagOperationDetail> tagOperationDetails, boolean throwError) {
        Map<Long, SocialTag> tagIdToTagMap = socialTagDBService.getTagIdToSocialTagMap(accountId);
        SocialTagOperationResponse tagOperationResponse = new SocialTagOperationResponse(SocialTagOperation.DELETE);
        Set<AbstractSocialTagOperation.SocialTagOperationDetail> tags = new HashSet<>();

        // Delete only eligible tagIds
        Set<Long> validTagIds = tagOperationDetails.stream().filter(tag -> tagIdToTagMap.containsKey(tag.getId()))
                                                   .map(AbstractSocialTagOperation.SocialTagOperationDetail::getId).collect(Collectors.toSet());
        boolean isOperationExecuted = false;
        if (CollectionUtils.isNotEmpty(validTagIds)) {
            isOperationExecuted = true;
            List<SocialTagMappingInfo> socialTagMappingInfos = socialTagDBService.findDistinctEntityIdAndEntityTypeMappedToTagIds(validTagIds);
            socialTagDBService.deleteSocialTagsAndMappingsByAccountIdAndTagIds(accountId, validTagIds);
            log.info("[SocialTagDeleteOperationService] performOperation tags:{} deleted by user:{} for account:{}", validTagIds, userId, accountId);
            validTagIds.forEach(tagId -> tags.add(new AbstractSocialTagOperation.SocialTagOperationDetail(tagId)));

            // This is done so that all the thus deleted tag(s) can be updated on
            // respective entity mappings as well
            if (CollectionUtils.isNotEmpty(socialTagMappingInfos)) {
                publishTagEntityMappingActionEvent(accountId, userId, socialTagMappingInfos, accountNum);
            }
        }
        tagOperationResponse.setTags(tags);
        tagOperationResponse.setIsOperationExecuted(isOperationExecuted);

        return tagOperationResponse;
    }
}
