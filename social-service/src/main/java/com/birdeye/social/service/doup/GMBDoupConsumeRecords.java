package com.birdeye.social.service.doup;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.GoogleMessagesAgentRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.GoogleMessagesAgent;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.BAMUpdateRequest;
import com.birdeye.social.model.GoogleLocationStatus;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.GoogleSocialAccountService;
import com.birdeye.social.service.SocialErrorMessagePageService;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.socialReseller.*;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkImportDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkStatusDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseReportUploadDTO;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseReportUnmappedGMBAccounts;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
@Service
public class GMBDoupConsumeRecords implements DoupConsumeRecords{
    private static final Logger LOGGER	= LoggerFactory.getLogger(GMBDoupConsumeRecords.class);
    @Autowired
    private CommonService commonService;
    @Autowired
    private BusinessGMBLocationRawRepository socialGMBRepo;
    @Autowired
    private SocialErrorMessagePageService socialErrorMessageService;
    @Autowired
    private GoogleMessagesAgentRepository agentRepo;
    @Autowired
    private KafkaProducerService kafkaProducer;

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Override
    public String channelName() {
        return SocialChannel.GMB.getName();
    }

    @Override
    public SocialResellerReportUploadDTO processMappingIntegrationReport(Long resellerId, Integer size, Integer page) {
        try {
            SocialResellerReportUploadDTO unmappedGmbPages =  new SocialResellerReportUploadDTO();
            Page<BusinessGoogleMyBusinessLocation> unmappedBusinessGMBPage = socialGMBRepo.findByResellerIdAndBusinessIdIsNull(resellerId, new org.springframework.data.domain.PageRequest(page, size));
            if (Objects.isNull(unmappedBusinessGMBPage) ) {
                return null;
            }

            List<SocialResellerReportUploadGMBDatum> socialResellerReportUploadGMBDatumList =
                    unmappedBusinessGMBPage.getContent().stream().map(data -> {
                        SocialResellerReportUploadGMBDatum conf = new SocialResellerReportUploadGMBDatum();
                        conf.setGmbListingID(data.getLocationId());
                        conf.setGmbListingPlaceId(data.getPlaceId());
                        conf.setGmbListingName(data.getLocationName());
                        conf.setGmbListingAddress(data.getSingleLineAddress());
                        return conf;
                    } ).collect(Collectors.toList());

            unmappedGmbPages.setData(socialResellerReportUploadGMBDatumList);
            unmappedGmbPages.setPageCount(unmappedBusinessGMBPage.getTotalPages());
            unmappedGmbPages.setTotalCount(unmappedBusinessGMBPage.getTotalElements());

            return unmappedGmbPages;

        } catch (Exception e) {
            LOGGER.error("something went wrong while fetching the location mapping data for resellerId {}", resellerId);
            return null;
        }
    }

    @Override
    public List<Integer> processLocationUnMappingIntegration( Long resellerId)  {
               return socialGMBRepo.findByResellerIdAndBusinessIdIsNotNull(resellerId);
    }


    @Override
    public SocialResellerBulkStatusDTO mapPageWithLocation(SocialResellerBulkImportDTO data, Long resellerId, BusinessLiteDTO businessDetails,
                                                           Long enterpriseId, Integer accountId) {
        SocialResellerBulkStatusDTO socialResellerBulkStatus = new SocialResellerBulkStatusDTO();
        try {
            socialResellerBulkStatus.setEventId(data.getEventId());
            socialResellerBulkStatus.setOperation("ADD");

            String locationId= data.getData().getSocialPageId();
            Integer businessId = businessDetails.getBusinessId();

            List<BusinessGoogleMyBusinessLocation> pageDetails = socialGMBRepo.findByLocationId(locationId);
            BusinessGoogleMyBusinessLocation businessGMBLocation = pageDetails.isEmpty() ? null : pageDetails.get(0);

            if(Objects.isNull(businessGMBLocation)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_NOT_FOUND.name(),SocialChannel.GOOGLE.getLabel());
                return createGMBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if(Objects.nonNull(businessGMBLocation.getBusinessId()) ) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_ALREADY_MAPPED_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return createGMBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.DUPLICATE.name());
            } else if (!businessGMBLocation.getResellerId().equals(resellerId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.ACCOUNT_ACCESS_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return createGMBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if (socialGMBRepo.existsByBusinessId(businessId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BUSINESS_MAPPED_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return createGMBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            }

            businessGMBLocation.setBusinessId(businessId);
            businessGMBLocation.setEnterpriseId(enterpriseId);
            businessGMBLocation.setShortAccountId(accountId);

            List<GoogleMessagesAgent> googleMessagesAgents = agentRepo.findByEnterpriseIdIn(enterpriseId);
            Map<Integer,GoogleMessagesAgent> googleMessagesAgentMap = googleMessagesAgents.stream().collect(Collectors.toMap(GoogleMessagesAgent::getId, Function.identity()));
            if(Objects.nonNull(businessGMBLocation.getAgentId())){
                googleSocialAccountService.initSetupLocation(businessGMBLocation.getEnterpriseId(),googleMessagesAgentMap.get(businessGMBLocation.getAgentId()), Collections.singletonList(businessGMBLocation),false);
            }else if((CollectionUtils.isNotEmpty(googleMessagesAgents) && googleMessagesAgents.size() == 1)
                    && (StringUtils.isEmpty(businessGMBLocation.getgMsgLocationStatus())
                    || (StringUtils.isNotEmpty(businessGMBLocation.getgMsgLocationStatus()) && !businessGMBLocation.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())))
                    && businessGMBLocation.getPermissions() != null && businessGMBLocation.getPermissions().contains(Constants.BUSINESS_MESSAGING)
                    && businessGMBLocation.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)){
                businessGMBLocation.setAgentId(googleMessagesAgents.get(0).getId());
                googleSocialAccountService.initSetupLocation(businessGMBLocation.getEnterpriseId(),googleMessagesAgents.get(0), Collections.singletonList(businessGMBLocation),false);
            }
            socialGMBRepo.saveAndFlush(businessGMBLocation);

            socialResellerBulkStatus.setStatus(ResellerMappingStatusEnum.SUCCESS.name());
            socialResellerBulkStatus.setErrorMessage("");
            BAMUpdateRequest payload =
                    new BAMUpdateRequest("gmb", businessGMBLocation.getBusinessId(), businessGMBLocation.getLocationMapUrl(),businessGMBLocation.getLocationId(), businessGMBLocation.getPlaceId());

            kafkaProducer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(locationId), payload);

            kafkaProducer.sendObject(Constants.GMB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessGMBLocation.getBusinessId(), businessGMBLocation.getLocationId()));


            commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(businessGMBLocation),
                    null, businessGMBLocation.getBusinessId(), businessGMBLocation.getEnterpriseId());
            return socialResellerBulkStatus;
        } catch (Exception e) {
            LOGGER.error("RESELLER_MAPPING_EVENT:For GMB, cannot map location with reseller {} {}", resellerId, e.toString() );
            String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.GOOGLE.getLabel());
            return createGMBDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());

        }
    }

    private SocialResellerBulkStatusDTO createGMBDoupErrorResponse(SocialResellerBulkStatusDTO socialResellerBulkStatus, long eventId,
                                                                  String errMessage, String status) {
        socialResellerBulkStatus.setEventId(eventId);
        socialResellerBulkStatus.setStatus(status);
        socialResellerBulkStatus.setOperation(Constants.REJECTED);
        socialResellerBulkStatus.setErrorMessage(errMessage);
        return socialResellerBulkStatus;
    }

	@Override
	public List<Integer> getMappedLocations(List<Long> bizHierarchyList) {
        return socialGMBRepo.findByEnterpriseIdInAndBusinessIdIsNotNull(bizHierarchyList);
	}

    @Override
    public List<Integer> getMappedRecordsByLocationId(List<Integer> locationsIds) {
        return socialGMBRepo.findByBusinessIds(locationsIds);
    }

    @Override
    public SocialEnterpriseReportUploadDTO processEnterpriseMappingIntegrationReport(Long enterpriseId, Integer size, Integer page) {
        try {
            SocialEnterpriseReportUploadDTO unmappedGmbPages =  new SocialEnterpriseReportUploadDTO();
            Page<BusinessGoogleMyBusinessLocation> unmappedBusinessGMBPage = socialGMBRepo.findByEnterpriseIdAndBusinessIdIsNull(enterpriseId, new org.springframework.data.domain.PageRequest(page, size));
            if (Objects.isNull(unmappedBusinessGMBPage) ) {
                return null;
            }
            List<SocialEnterpriseReportUnmappedGMBAccounts> socialResellerReportUploadGMBDatumList =
                    unmappedBusinessGMBPage.getContent().stream().map(data -> {
                        SocialEnterpriseReportUnmappedGMBAccounts conf = new SocialEnterpriseReportUnmappedGMBAccounts();
                        conf.setGmbListingID(data.getLocationId());
                        conf.setGmbListingPlaceId(data.getPlaceId());
                        conf.setGmbListingName(data.getLocationName());
                        conf.setGmbListingAddress(data.getSingleLineAddress());
                        return conf;
                    } ).collect(Collectors.toList());

            unmappedGmbPages.setData(socialResellerReportUploadGMBDatumList);
            unmappedGmbPages.setPageCount(unmappedBusinessGMBPage.getTotalPages());
            unmappedGmbPages.setTotalCount(unmappedBusinessGMBPage.getTotalElements());

            return unmappedGmbPages;

        } catch (Exception e) {
            LOGGER.error("something went wrong while fetching the location mapping data for enterprise Id {}", enterpriseId);
            return null;
        }
    }

    @Override
    public SocialEnterpriseBulkStatusDTO mapPageWithEnterpriseLocation(SocialEnterpriseBulkImportDTO data, BusinessLiteDTO businessDetails, Long enterpriseId, Integer accountId) {
        SocialEnterpriseBulkStatusDTO socialEnterpriseBulkStatusDto = new SocialEnterpriseBulkStatusDTO();
        try {
            socialEnterpriseBulkStatusDto.setEventId(data.getEventId());
            socialEnterpriseBulkStatusDto.setOperation("ADD");
            String locationId= data.getData().getSocialPageId();
            Integer businessId = businessDetails.getBusinessId();
            List<BusinessGoogleMyBusinessLocation> pageDetails = socialGMBRepo.findByLocationId(locationId);
            BusinessGoogleMyBusinessLocation businessGMBLocation = pageDetails.isEmpty() ? null : pageDetails.get(0);

            if(Objects.isNull(businessGMBLocation)) {
                String errMessage = socialErrorMessageService.getEnterpriseErrorMessage(SocialErrorMessagesEnum.PAGE_NOT_FOUND.name(),SocialChannel.GOOGLE.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatusDto,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if(Objects.nonNull(businessGMBLocation.getBusinessId()) ) {
                String errMessage = socialErrorMessageService.getEnterpriseErrorMessage(SocialErrorMessagesEnum.PAGE_ALREADY_MAPPED_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatusDto,data.getEventId(),errMessage,ResellerMappingStatusEnum.DUPLICATE.name());
            } else if (!businessGMBLocation.getEnterpriseId().equals(enterpriseId)) {
                String errMessage = socialErrorMessageService.getEnterpriseErrorMessage(SocialErrorMessagesEnum.ACCOUNT_ACCESS_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatusDto,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if (socialGMBRepo.existsByBusinessId(businessId)) {
                String errMessage = socialErrorMessageService.getEnterpriseErrorMessage(SocialErrorMessagesEnum.BUSINESS_MAPPED_ERR.name(),SocialChannel.GOOGLE.getLabel());
                return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatusDto,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            }
            businessGMBLocation.setBusinessId(businessId);
            businessGMBLocation.setEnterpriseId(enterpriseId);
            businessGMBLocation.setShortAccountId(accountId);

            List<GoogleMessagesAgent> googleMessagesAgents = agentRepo.findByEnterpriseIdIn(enterpriseId);
            Map<Integer,GoogleMessagesAgent> googleMessagesAgentMap = googleMessagesAgents.stream().collect(Collectors.toMap(GoogleMessagesAgent::getId, Function.identity()));
            if(Objects.nonNull(businessGMBLocation.getAgentId())){
                googleSocialAccountService.initSetupLocation(businessGMBLocation.getEnterpriseId(),googleMessagesAgentMap.get(businessGMBLocation.getAgentId()), Collections.singletonList(businessGMBLocation),false);
            }else if((CollectionUtils.isNotEmpty(googleMessagesAgents) && googleMessagesAgents.size() == 1)
                    && (StringUtils.isEmpty(businessGMBLocation.getgMsgLocationStatus())
                    || (StringUtils.isNotEmpty(businessGMBLocation.getgMsgLocationStatus()) && !businessGMBLocation.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())))
                    && businessGMBLocation.getPermissions() != null && businessGMBLocation.getPermissions().contains(Constants.BUSINESS_MESSAGING)
                    && businessGMBLocation.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)){
                businessGMBLocation.setAgentId(googleMessagesAgents.get(0).getId());
                googleSocialAccountService.initSetupLocation(businessGMBLocation.getEnterpriseId(),googleMessagesAgents.get(0), Collections.singletonList(businessGMBLocation),false);
            }
            socialGMBRepo.saveAndFlush(businessGMBLocation);

            socialEnterpriseBulkStatusDto.setStatus(ResellerMappingStatusEnum.SUCCESS.name());
            socialEnterpriseBulkStatusDto.setErrorMessage("");
            BAMUpdateRequest payload =
                    new BAMUpdateRequest("gmb", businessGMBLocation.getBusinessId(), businessGMBLocation.getLocationMapUrl(),businessGMBLocation.getLocationId(), businessGMBLocation.getPlaceId());

            kafkaProducer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(locationId), payload);

            kafkaProducer.sendObject(Constants.GMB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessGMBLocation.getBusinessId(), businessGMBLocation.getLocationId()));

            commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Collections.singletonList(businessGMBLocation),
                    null, businessGMBLocation.getBusinessId(), businessGMBLocation.getEnterpriseId());
            return socialEnterpriseBulkStatusDto;
        } catch (Exception e) {
            LOGGER.error("ENTERPRISE_MAPPING_EVENT:For GMB, cannot map location with reseller {} {}", enterpriseId, e.toString() );
            String errMessage = socialErrorMessageService.getEnterpriseErrorMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.GOOGLE.getLabel());
            return commonService.createEnterpriseDoupErrorResponse(socialEnterpriseBulkStatusDto,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
        }
    }

}