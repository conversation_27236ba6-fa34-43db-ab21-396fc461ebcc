package com.birdeye.social.service.ratelimit.impl;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SocialAPIRateLimitMaster;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.RateLimitLevel;
import com.birdeye.social.constant.RateLimitingConstants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.service.IRedisExternalService;
import com.birdeye.social.service.ratelimit.ChannelRateLimitService;
import com.birdeye.social.service.ratelimit.ChannelRateLimitingFactory;
import com.birdeye.social.service.ratelimit.RateLimitService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.RateLimitingMapper;
import io.github.bucket4j.*;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.net.URI;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

@Service
public class RateLimitServiceImpl  implements RateLimitService {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitServiceImpl.class);

    @Autowired
    IRedisExternalService redisExternalService;

    @Autowired
    RateLimitAuditRepo rateLimitAuditRepo;

    @Autowired
    private APIRateLimitRepository apiRateLimitRepository;

    @Autowired
    private SocialRateLimitingMasterRepo socialRateLimitingMasterRepo;

    @Autowired
    private RateLimitingMapper rateLimitingMapper;

    @Autowired
    private ProxyManager proxyManager;

    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private SocialRateLimitingDomainRepo socialRateLimitingDomainRepo;
    @Autowired
    private RateLimitSharedUsageAuditRepo rateLimitSharedUsageAuditRepo;



    private static final String UPDATE_OPERATION_NOT_ALLOWED_LOG = "Update operation cannot be performed on :{}";
    public static final String COMMENTS_THREADS_URL = "youtube/v3/commentThreads";
    public static final String YT_VIDEO_URL = "youtube/v3/videos";

    @Override
    public void publishToKafkaForRateLimitAudit(String sourceUrl, String methodType, String breachUrl, String breachUrlType, String groupIdentifier, String channel, long secondsToReset) {
        kafkaProducerService.sendObjectV1(Constants.RATE_LIMIT_EXCEEDED_AUDIT_TOPIC,
                new RateLimitAuditRequest(sourceUrl, methodType, breachUrl, breachUrlType, groupIdentifier, channel, secondsToReset));
    }

    @Override
    public void publishToKafkaForSharedRateLimitAudit(String sourceUrl, String methodType, String breachUrl, String breachUrlType, String groupIdentifier,
                                                      String channel) {
        kafkaProducerService.sendObjectV1(Constants.RATE_LIMIT_SHARED_AUDIT_TOPIC,
                new RateLimitAuditRequest(sourceUrl, methodType, breachUrl, breachUrlType, groupIdentifier, channel,null));
    }

    @Override
    public void auditRateLimit(RateLimitAuditRequest request) {
        logger.info("Inside audit table for sourceUrl :{} and type :{}",
                request.getSourceUrl(), request.getMethodType());
        List<String> data= redisExternalService.popFromQueue("RL#*");
        if (CollectionUtils.isEmpty(data)) {
            logger.info("No data identified during pop operation");
            return;
        }

        String concatenatedString = convertToString(data);

        SocialRateLimitAudit audit = new SocialRateLimitAudit();
        audit.setChannel(request.getChannel());
        audit.setApiSource(request.getSourceUrl());
        audit.setApiType(request.getMethodType());
        audit.setQuotaBreachUrl(request.getBreachUrl());
        audit.setQuotaBreachUrlType(request.getBreachUrlType());
        audit.setGroupIdentifier(request.getGroupIdentifier());
        audit.setRateLimitData(concatenatedString);
        audit.setCreatedAt(new Date());
        audit.setUpdatedAt(new Date());
        audit.setSecondsToReset(request.getSecondsToReset());
        rateLimitAuditRepo.saveAndFlush(audit);
    }

    public static String convertToString(List<String> list) {
        String delimiter = ", ";
        return String.join(delimiter, list);
    }


    @Override
    public void enqueueKeyRedis(String key, long remainingTokens, long seconds, Boolean isSharedKey) {
        if(Boolean.TRUE.equals(isSharedKey))
            redisExternalService.setWithExipry("RL#SHARED_"+key, String.valueOf(remainingTokens),seconds);

        else{
            redisExternalService.setWithExipry("RL#"+key, String.valueOf(remainingTokens),seconds);
        }

    }

    @Override
    public void refreshLinkedinRates() {
        List<APIRateLimitSettings> rateLimitData = apiRateLimitRepository.findAll();
        rateLimitData.stream()
                .filter(data -> Objects.nonNull(data.getApiUrl()) && SocialChannel.LINKEDIN.name().equalsIgnoreCase(data.getChannelName()))
                .forEach(param -> {
                    String cacheKey = param.getApiMethod() + "_" + param.getServiceName();
                    RateLimitingDTO rateLimitingDTO = redisExternalService.getRateLimitingDto(cacheKey);

                    if (Objects.isNull(rateLimitingDTO)) {
                        return;
                    }
                    long limit = Long.parseLong(rateLimitingDTO.getServiceLimitSync());
                    Refill refill = Refill.intervally(limit, Duration.ofMinutes(rateLimitingDTO.getRefillTime()));
                    Bandwidth limitBandwidth = Bandwidth.classic(limit, refill);

                    proxyManager.builder().build(cacheKey.getBytes(UTF_8), () -> (BucketConfiguration.builder()
                            .addLimit(limitBandwidth)
                            .build())).reset();
                });
    }

    @Override
    public void removeRatesForKeys(HttpMethod method, URI uri) {
        String sourceHeader = MDC.get("source");
        String channel = MDC.get("channel");
        String pageId = MDC.get("pageId");

        // isSync is true when sync_api is null or true
        boolean isSync = Objects.isNull(MDC.get(RateLimitingConstants.SYNC_API)) || BooleanUtils.isTrue(Boolean.valueOf(MDC.get(RateLimitingConstants.SYNC_API)));

        RateLimitingDTO bucketConfig = getRateLimitingDto(uri.toString(), sourceHeader, channel, method);
        if (Objects.isNull(bucketConfig)) {
            return;
        }

        String cacheKey = getCacheKey(sourceHeader, pageId, bucketConfig, isSync);
        String sharedKey = getSharedCacheKey(pageId, bucketConfig, sourceHeader);

        long syncDataLimit = Long.parseLong(Objects.requireNonNull(bucketConfig).getServiceLimitSync());
        long asyncDataLimit = Long.parseLong(Optional.ofNullable(bucketConfig.getServiceLimitAsync()).orElse("0"));
        long dataLimit = isSync ? syncDataLimit : asyncDataLimit;

        emptyBucket(cacheKey, dataLimit, bucketConfig.getRefillTime(), bucketConfig.getTimeUnit());
        emptyBucket(sharedKey, Long.parseLong(bucketConfig.getSharedLimit()), bucketConfig.getRefillTime(), bucketConfig.getTimeUnit());
    }

    private void emptyBucket(String key, long limit, Integer refillTime, ChronoUnit timeUnit) {
        if (Objects.nonNull(key) && limit > 0) {
            resolveBucket(key, limit, refillTime, timeUnit).tryConsumeAsMuchAsPossible();
        }
    }

    @Override
    public RateLimitingDTO getRateLimitingDto(String requestUrl, String header, String channelName, HttpMethod httpMethod) {
        SocialChannel socialChannelEnum = SocialChannel.getSocialChannelByName(channelName);
        ChannelRateLimitService execute = ChannelRateLimitingFactory.getService(socialChannelEnum);

        String url = execute.getCacheUrl(requestUrl, httpMethod.name());

        if (Objects.isNull(url)) {
            return null;
        }
        RateLimitingDTO data = redisExternalService.getRateLimitingDto(url + "_" + header);
        return ObjectUtils.isEmpty(data) ? null : data;
    }

    @Override
    public String getCacheKey(String header, String pageId, RateLimitingDTO data, boolean isSync) {
        if (Objects.isNull(data)) {
            return null;
        }
        StringBuilder key = new StringBuilder(data.getApiMethod())
                .append("_")
                .append(header);
        key.append("_").append(isSync ? RateLimitingConstants.SYNC : RateLimitingConstants.ASYNC);
        if (Objects.equals(data.getLevel(), RateLimitLevel.PAGE)) {
            key.append("_").append(pageId);
        }
        return key.toString();
    }

    @Override
    public String getSharedCacheKey(String pageId, RateLimitingDTO data, String header) {
        if (ObjectUtils.isEmpty(data)) {
            return null;
        }

        StringBuilder sharedKey = new StringBuilder(data.getApiIdentifier())
                .append("_")
                .append("SHARED");
        if (Objects.equals(data.getLevel(), RateLimitLevel.PAGE)) {
            sharedKey.append("_").append(pageId);
        }

        return sharedKey.toString();
    }

    @Override
    public void testing() {
        logger.info("Testing function");
    }

    private void updateSharedLimitQuota(List<APIRateLimitSettings> rateLimits, int sharedLimit, String apiIdentifier) {
        logger.info("Updating shared limit as {} for api identifier : {}", sharedLimit, apiIdentifier);
        rateLimits.forEach(rateLimit -> rateLimit.setSharedLimit(String.valueOf(sharedLimit)));
    }

    @Override
    public void updateBucketConfigInRedis(RateLimitingDTO dto) {
        redisExternalService.set(dto.getApiUrl() + "_" + dto.getServiceName(),
                JSONUtils.toJSON(dto));
    }

    private void removeLimitsFromListIndividually(List<APIRateLimitSettings> rateLimits, int count, String apiIdentifier) {
        logger.info("Removing {} rate limits from sync and async for api identifier : {}", count, apiIdentifier);
        while (true) {
            for (APIRateLimitSettings rateLimit : rateLimits) {
                if (decrementSyncLimitAndReturnWhetherDecremented(rateLimit)) {
                    count--;
                }
                if (0 == count) {
                    return;
                }

                if (decrementASyncLimitAndReturnWhetherDecremented(rateLimit)) {
                    count--;
                }
                if (0 == count) {
                    return;
                }
            }
        }
    }

    private boolean decrementSyncLimitAndReturnWhetherDecremented(APIRateLimitSettings rateLimit) {
        int limit = Integer.parseInt(Optional.ofNullable(rateLimit.getServiceLimitSync()).orElse("0"));
        if (limit == 0) {
            return false;
        }

        rateLimit.setServiceLimitSync(Integer.toString(limit - 1));
        return true;
    }

    private boolean decrementASyncLimitAndReturnWhetherDecremented(APIRateLimitSettings rateLimit) {
        int limit = Integer.parseInt(Optional.ofNullable(rateLimit.getServiceLimitAsync()).orElse("0"));
        if (limit == 0) {
            return false;
        }

        rateLimit.setServiceLimitAsync(Integer.toString(limit - 1));
        return true;
    }

    public void modifyRateLimitData(String tableName, RateLimitUpdateRequest updateRequest) {
       // validateTableNameForRateLimitingUpdate(tableName);
        boolean distributeQuotas = true;

        switch (updateRequest.getRequestType().toLowerCase()) {
            case "insert":
                insertRateLimitData(tableName,updateRequest);
                break;
            case "update":
                updateRateLimitData(tableName,updateRequest);
                break;
            case "delete":
                deleteRateLimitData(tableName,updateRequest);
                break;
            default:
                distributeQuotas = false;
                logger.info("Invalid Request");
        }

        if (RateLimitingConstants.RATE_LIMIT_TABLE.equalsIgnoreCase(tableName) && distributeQuotas) {
            distributeRateLimitQuotas();
        }
    }

    @Override
    public void updateGroupQuota(APIRateLimit apiRateLimit) {
        List<APIRateLimitSettings> rateLimitsWithIdentifier =
                apiRateLimitRepository.findByApiIdentifier(apiRateLimit.getApiIdentifier());

        updateRateLimitsWithGroupQuotaAndStoreInRedis(rateLimitsWithIdentifier,
                Integer.parseInt(apiRateLimit.getGroupQuota()), apiRateLimit.getApiIdentifier());
        apiRateLimitRepository.save(rateLimitsWithIdentifier);
    }

    @Override
    public void updateRateLimitSocialApiInfoInRedis(RateLimitingMasterDTO dto) {
        redisExternalService.set(dto.getApiUrl() + "##" + dto.getApiType(), JSONUtils.toJSON(dto));
    }

    @Override
    public void loadRateLimitCache() {
        logger.info("STARTED loading rate limit cache from social_api_rate_limit table.");
        List<APIRateLimitSettings> rateLimitData = apiRateLimitRepository.findAll();
        rateLimitData.parallelStream().forEach(param -> {
            RateLimitingDTO rateLimitingDTO = rateLimitingMapper.mapToDTO(param);
            updateBucketConfigInRedis(rateLimitingDTO);
        });
        logger.info("ENDED loading loadRateLimitCache");
    }

    @Override
    public void loadRateLimitMaster() {
        logger.info("STARTED loading rate limit cache from social_rate_limit_api_info table.");
        SocialAPIRateLimitMaster socialAPIRateLimitMaster = new SocialAPIRateLimitMaster();
        List<RateLimitingMaster> rateLimitData=socialRateLimitingMasterRepo.findAll();
        rateLimitData.parallelStream().forEach(param -> {
            RateLimitingMasterDTO rateLimitingMasterDTO = rateLimitingMapper.mapMasterToDTO(param);
            socialAPIRateLimitMaster.addLimitByType(param.getApiUrl()+"##"+param.getApiType(),rateLimitingMasterDTO);
        });
        CacheManager.getInstance().setCache(socialAPIRateLimitMaster);
        logger.info("ENDED loading loadRateLimitMaster");
    }

    @Override
    public boolean validateAndEnqueueRateLimitsPresent(boolean isSync, HttpRequest request, String sourceHeader,
                                                       String channel, String pageId) {
        RateLimitingDTO bucketConfig = getRateLimitingDto(request.getURI().toString(), sourceHeader, channel, request.getMethod());
        String cacheKey = getCacheKey(sourceHeader, pageId, bucketConfig, isSync);
        String sharedKey = getSharedCacheKey(pageId, bucketConfig, sourceHeader);

        if (StringUtils.isEmpty(cacheKey)) {
            logger.info("Empty cache key for bucket config {}", bucketConfig);
            return true;
        }

        long syncDataLimit = Long.parseLong(Objects.requireNonNull(bucketConfig).getServiceLimitSync());
        long asyncDataLimit = Long.parseLong(Optional.ofNullable(bucketConfig.getServiceLimitAsync()).orElse("0"));

        long dataLimit = isSync ? syncDataLimit : asyncDataLimit;
        if (dataLimit == 0) {
            logger.info("No sync limit available for key :{}", cacheKey);
            if ("Y".equalsIgnoreCase(bucketConfig.getSharedLimitAccess())) {
                return validateRateLimitForSharedKey(sharedKey, bucketConfig, request, null);
            } else {
                logger.info("No sync limit available and no access to shared limit for key :{}", cacheKey);
                return false;
            }
        } else {
            Bucket tokenBucketIndividual = resolveBucket(cacheKey, dataLimit,
                    bucketConfig.getRefillTime(), bucketConfig.getTimeUnit());
            ConsumptionProbe probe = tokenBucketIndividual.tryConsumeAndReturnRemaining(1);

            long secondsToRefresh = TimeUnit.NANOSECONDS.toSeconds(probe.getNanosToWaitForReset());
            enqueueKeyRedis(cacheKey, probe.getRemainingTokens(), secondsToRefresh,false);
            if (probe.isConsumed()) {
                // The limit is not exceeded.
                logger.info("Consuming individual quota for key :{} and API :{} with remaining quota :{}", cacheKey, request.getURI(), probe.getRemainingTokens());
                return true;
            } else {
                if ("Y".equalsIgnoreCase(bucketConfig.getSharedLimitAccess())) {
                    return validateRateLimitForSharedKey(sharedKey, bucketConfig, request, probe);
                } else {
                    if(Boolean.FALSE.equals(isSync)){
                       /* RetryRateRequest retryRateRequest=  RetryRateRequest.builder().retryAfterSeconds(e.getRetryAfterSeconds()).
                                key(categoriesRequest.getUuid()).build();
                        LOGGER.info("retryRateRequest: {}", JSONUtils.toJSON(retryRateRequest));
                        kafkaProducerService.sendObjectV1("rate-limit-retry",  retryRateRequest);

                        retryAsyncRateLimitingFlow(secondsToRefresh,);*/
                    }
                    throw new TooManyRequestException(ErrorCodes.TOO_MANY_REQUESTS,
                            RateLimitingConstants.RATE_LIMIT_EXCEEDED, secondsToRefresh, bucketConfig,probe.getRemainingTokens());
                }
            }
        }
    }

    @Override
    public boolean validateAndEnqueueSharedRateLimitsPresent(HttpRequest request) {
        RateLimitDomainInfoDTO data= redisExternalService.getRateLimitingSocialDomainDto(request.getURI().getHost());
        String sourceHeader="SOCIAL";
        String channel= data.getSource();
        String pageId = MDC.get("pageId");
        RateLimitingDTO bucketConfig = getRateLimitingDto(request.getURI().toString(), sourceHeader, channel, request.getMethod());
        String sharedKey = getSharedCacheKey(pageId, bucketConfig, sourceHeader);
        if (StringUtils.isEmpty(sharedKey)) {
            logger.info("Empty shared cache key for bucket config {}", bucketConfig);
            return true;
        }

        return validateRateLimitForSharedKey(sharedKey, bucketConfig, request, null);
    }


    private boolean validateRateLimitForSharedKey(String sharedKey, RateLimitingDTO bucketConfig, HttpRequest request,
                                                  ConsumptionProbe probeOfNonSharedLimits) {
        Long sharedLimit = Long.valueOf(bucketConfig.getSharedLimit());
        if (sharedLimit.equals(0L)) {
            if (Objects.nonNull(probeOfNonSharedLimits)) {
                long secondsToRefresh = TimeUnit.NANOSECONDS.toSeconds(probeOfNonSharedLimits.getNanosToWaitForReset());
                throw new TooManyRequestException(ErrorCodes.TOO_MANY_REQUESTS, RateLimitingConstants.RATE_LIMIT_EXCEEDED,
                        secondsToRefresh, bucketConfig,probeOfNonSharedLimits.getRemainingTokens());
            }
            throw new BirdeyeSocialException("Shared Limit and dedicated limit cannot be zero");
        }

        Bucket sharedBucket = resolveBucket(sharedKey, sharedLimit,
                bucketConfig.getRefillTime(), bucketConfig.getTimeUnit());
        ConsumptionProbe sharedProbe = sharedBucket.tryConsumeAndReturnRemaining(1);

        long secondsToRefresh = TimeUnit.NANOSECONDS.toSeconds(sharedProbe.getNanosToWaitForReset());
        enqueueKeyRedis(sharedKey, sharedProbe.getRemainingTokens(),secondsToRefresh,true);
        logger.info("Consuming shared quota for key :{} and API :{} with remaining quota :{}", sharedKey, request.getURI(), sharedProbe.getRemainingTokens());
        if (sharedProbe.isConsumed()) {
            //push to kafka once percentage quota is reached
            if(RC_ALERT_ENABLE) {
                long usedTokens = sharedLimit - sharedProbe.getRemainingTokens();
                if (usedTokens >= PERCENTAGE * sharedLimit) {
                    // Push to Kafka once percentage of the shared probe is used
                    publishToKafkaForSharedRateLimitAudit(MDC.get("uri"), MDC.get("uriType"),
                            String.valueOf(request.getURI()), request.getMethod().name(), bucketConfig.getApiIdentifier(),
                            bucketConfig.getChannelName());
                }
            }
            return true;
        } else {
            // The limit is exceeded.
            throw new TooManyRequestException(ErrorCodes.TOO_MANY_REQUESTS, RateLimitingConstants.RATE_LIMIT_EXCEEDED,
                    secondsToRefresh, bucketConfig,sharedProbe.getRemainingTokens());
        }
    }

    private void validateTableNameForRateLimitingUpdate(String tableName) {
        logger.info("Validating table name : {}", tableName);
        if (!Arrays.asList(RateLimitingConstants.RATE_LIMIT_API_INFO_TABLE,
                RateLimitingConstants.RATE_LIMIT_TABLE).contains(tableName.toLowerCase())) {
            throw new BirdeyeSocialException("Incorrect table name");
        }
    }

    private void deleteRateLimitData(String tableName, RateLimitUpdateRequest updateRequest) {
        logger.info("deleting request");
        switch (tableName.toLowerCase()) {
            case RateLimitingConstants.RATE_LIMIT_TABLE:
                deleteRateLimitAPIData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_API_INFO_TABLE:
                deleteRateLimitAPIInfoData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_DOMAIN_INFO_TABLE:
                deleteDomainInfoData(updateRequest);
                break;
            default:
                logger.info("Invalid Request");
        }
    }

    private void deleteRateLimitAPIInfoData(RateLimitUpdateRequest updateRequest) {
        List<RateLimitingMaster> rateLimitingMasterList =
                updateRequest.getMasterData().stream().filter(data -> Objects.nonNull(data.getId()))
                        .map(temp -> {
                            RateLimitingMaster rateLimitingMaster1 = new RateLimitingMaster();
                            rateLimitingMaster1.setId(temp.getId());
                            return rateLimitingMaster1;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rateLimitingMasterList)) {
            socialRateLimitingMasterRepo.deleteInBatch(rateLimitingMasterList);
        } else {
            logger.info("No delete operation performed in social_rate_limit_api_info table");
        }
    }

    private void deleteRateLimitAPIData(RateLimitUpdateRequest updateRequest) {
        List<APIRateLimitSettings> apiRateLimitSettingsList =
                updateRequest.getApiData().stream().filter(data -> Objects.nonNull(data.getId()))
                        .map(temp -> {
                            APIRateLimitSettings apiRateLimitSettings1 = new APIRateLimitSettings();
                            apiRateLimitSettings1.setId(temp.getId());
                            return apiRateLimitSettings1;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(apiRateLimitSettingsList)) {
            apiRateLimitRepository.deleteInBatch(apiRateLimitSettingsList);
        } else {
            logger.info("No delete operation performed in social_api_rate_limit table");
        }
    }

    private void deleteDomainInfoData(RateLimitUpdateRequest updateRequest) {
        List<RateLimitDomainInfo> rateLimitDomainInfoList =
                updateRequest.getRateLimitDomainInfo().stream().filter(data -> Objects.nonNull(data.getId()))
                        .map(temp -> {
                            RateLimitDomainInfo domainInfo = new RateLimitDomainInfo();
                            domainInfo.setId(temp.getId());
                            return domainInfo;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rateLimitDomainInfoList)) {
            socialRateLimitingDomainRepo.deleteInBatch(rateLimitDomainInfoList);
        } else {
            logger.info("No delete operation performed in rate_limit_domain_info table");
        }
    }


    private void updateRateLimitData(String rateLimitIdentifier, RateLimitUpdateRequest updateRequest) {
        logger.info("updating request");
        switch (rateLimitIdentifier.toLowerCase()) {
            case RateLimitingConstants.RATE_LIMIT_TABLE:
                updateRateLimitAPIData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_API_INFO_TABLE:
                updateRateLimitAPIInfoData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_DOMAIN_INFO_TABLE:
                updateDomainInfoData(updateRequest);
                break;
            default:
                logger.info("Invalid Request");
        }
    }

    private void updateRateLimitAPIInfoData(RateLimitUpdateRequest updateRequest) {
        updateRequest.getMasterData().forEach(data -> {
            if (Objects.isNull(data.getId())) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data.getId());
                return;
            }
            RateLimitingMaster pm = socialRateLimitingMasterRepo.findOne(data.getId());

            if (Objects.isNull(pm)) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data.getId());
                return;
            }

            if (Objects.nonNull(data.getRateLimitIdentifier())) {
                pm.setRateLimitIdentifier(data.getRateLimitIdentifier());
            }
            if (Objects.nonNull(data.getApiType())) {
                pm.setApiType(data.getApiType());
            }
            if (Objects.nonNull(data.getApiUrl())) {
                pm.setApiUrl(data.getApiUrl());
            }

            socialRateLimitingMasterRepo.save(pm);
        });
        socialRateLimitingMasterRepo.flush();
    }

    private void updateRateLimitAPIData(RateLimitUpdateRequest updateRequest) {
        updateRequest.getApiData().forEach(data -> {
            if (Objects.isNull(data.getId())) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data);
                return;
            }
            APIRateLimitSettings pm = apiRateLimitRepository.findOne(data.getId());

            if (Objects.isNull(pm)) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data.getId());
                return;
            }
            updateRateLimitSettings(pm, data);
            apiRateLimitRepository.save(pm);
        });
        apiRateLimitRepository.flush();
    }

    private void updateRateLimitSettings(APIRateLimitSettings pm, APIRateLimit data) {
        Optional.ofNullable(data.getSharedLimit()).ifPresent(pm::setSharedLimit);
        Optional.ofNullable(data.getServiceName()).ifPresent(pm::setServiceName);
        Optional.ofNullable(data.getServiceLimitSync()).ifPresent(pm::setServiceLimitSync);
        Optional.ofNullable(data.getApiMethod()).ifPresent(pm::setApiMethod);
        Optional.ofNullable(data.getApiUrl()).ifPresent(pm::setApiUrl);
        Optional.ofNullable(data.getSharedLimitAccess()).ifPresent(pm::setSharedLimitAccess);
        Optional.ofNullable(data.getServiceLimitAsync()).ifPresent(pm::setServiceLimitAsync);
        Optional.ofNullable(data.getRefillTime()).ifPresent(pm::setRefillTime);
        Optional.ofNullable(data.getTimeUnit()).ifPresent(pm::setTimeUnit);
    }

    private void updateDomainInfoData(RateLimitUpdateRequest updateRequest) {
        updateRequest.getRateLimitDomainInfo().forEach(data -> {
            if (Objects.isNull(data.getId())) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data.getId());
                return;
            }
            RateLimitDomainInfo pm = socialRateLimitingDomainRepo.findOne(data.getId());

            if (Objects.isNull(pm)) {
                logger.error(UPDATE_OPERATION_NOT_ALLOWED_LOG, data.getId());
                return;
            }

            if (Objects.nonNull(data.getDomain())) {
                pm.setDomain(data.getDomain());
            }
            if (Objects.nonNull(data.getEnabled())) {
                pm.setEnabled(data.getEnabled());
            }
            if (Objects.nonNull(data.getSource())) {
                pm.setSource(data.getSource());
            }

            socialRateLimitingDomainRepo.save(pm);
        });
    }


    private void insertRateLimitData(String rateLimitIdentifier, RateLimitUpdateRequest updateRequest) {
        logger.info("inserting request");
        switch (rateLimitIdentifier.toLowerCase()) {
            case RateLimitingConstants.RATE_LIMIT_TABLE:
                insertRateLimitAPIData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_API_INFO_TABLE:
                insertRateLimitAPIInfoData(updateRequest);
                break;
            case RateLimitingConstants.RATE_LIMIT_DOMAIN_INFO_TABLE:
                insertRateLimitDomainData(updateRequest);
                break;
            default:
                logger.info("Invalid Request");
        }
    }

    private void insertRateLimitAPIInfoData(RateLimitUpdateRequest updateRequest) {
        List<RateLimitingMaster> rateLimitingMasters =
                updateRequest.getMasterData().stream().filter(data -> StringUtils.isNotEmpty(data.getApiUrl()))
                        .map(temp -> {
                            RateLimitingMaster rateLimitingMaster1 = new RateLimitingMaster();
                            rateLimitingMaster1.setApiType(temp.getApiType());
                            rateLimitingMaster1.setRateLimitIdentifier(temp.getRateLimitIdentifier());
                            rateLimitingMaster1.setSource(temp.getSource());
                            rateLimitingMaster1.setApiUrl(temp.getApiUrl());

                            return rateLimitingMaster1;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rateLimitingMasters)) {
            socialRateLimitingMasterRepo.save(rateLimitingMasters);
        } else {
            logger.info("No insert operation performed in social_rate_limit_api_info table");
        }
    }

    private void insertRateLimitAPIData(RateLimitUpdateRequest updateRequest) {
        List<APIRateLimitSettings> rateLimitSettings =
                updateRequest.getApiData().stream().filter(data -> StringUtils.isNotEmpty(data.getApiMethod()))
                        .map(temp -> {
                            APIRateLimitSettings apiRateLimitSettings1 = new APIRateLimitSettings();
                            apiRateLimitSettings1.setSharedLimitAccess(temp.getSharedLimitAccess());
                            apiRateLimitSettings1.setSharedLimit(temp.getSharedLimit());
                            apiRateLimitSettings1.setApiMethod(temp.getApiMethod());
                            apiRateLimitSettings1.setApiUrl(temp.getApiUrl());
                            apiRateLimitSettings1.setChannelName(temp.getChannelName());
                            apiRateLimitSettings1.setServiceName(temp.getServiceName());
                            apiRateLimitSettings1.setApiIdentifier(temp.getApiIdentifier());
                            apiRateLimitSettings1.setGroupQuota(temp.getGroupQuota());
                            apiRateLimitSettings1.setServiceLimitSync(temp.getServiceLimitSync());
                            apiRateLimitSettings1.setCreatedAt(new Date());
                            apiRateLimitSettings1.setUpdatedAt(new Date());
                            apiRateLimitSettings1.setServiceLimitAsync(temp.getServiceLimitAsync());
                            apiRateLimitSettings1.setRefillTime(temp.getRefillTime());
                            apiRateLimitSettings1.setLevel(temp.getLevel());
                            apiRateLimitSettings1.setTimeUnit(temp.getTimeUnit());
                            return apiRateLimitSettings1;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rateLimitSettings)) {
            apiRateLimitRepository.save(rateLimitSettings);
        } else {
            logger.info("No insert operation performed in social_api_rate_limit table");
        }
    }

    private void insertRateLimitDomainData(RateLimitUpdateRequest updateRequest) {
        List<RateLimitDomainInfo> rateLimitDomainInfoList =
                updateRequest.getRateLimitDomainInfo().stream().filter(data -> StringUtils.isNotEmpty(data.getDomain()))
                        .map(temp -> {
                            RateLimitDomainInfo domainInfo = new RateLimitDomainInfo();
                            domainInfo.setDomain(temp.getDomain());
                            domainInfo.setSource(temp.getSource());
                            domainInfo.setEnabled(temp.getEnabled());

                            return domainInfo;
                        })
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rateLimitDomainInfoList)) {
            socialRateLimitingDomainRepo.save(rateLimitDomainInfoList);
        } else {
            logger.info("No insert operation performed in rate_limit_domain_info table");
        }
    }

    private void distributeRateLimitQuotas() {
        logger.info("starting distribution of rate limit quotas");
        List<APIRateLimitSettings> apiRateLimitSettingsList = apiRateLimitRepository.findAll();

        Map<String, List<APIRateLimitSettings>> rateLimitSettingsMap =
                apiRateLimitSettingsList.stream().collect(Collectors.groupingBy(APIRateLimitSettings::getApiIdentifier));

        rateLimitSettingsMap.forEach((apiIdentifier, rateLimits) -> {
            int groupQuota = Integer.parseInt(Optional.ofNullable(rateLimits.get(0).getGroupQuota()).orElse("0"));

            updateRateLimitsWithGroupQuotaAndStoreInRedis(rateLimits, groupQuota, apiIdentifier);
            apiRateLimitRepository.save(rateLimits);
        });
        logger.info("distribution of rate limit quotas is done");
        apiRateLimitRepository.flush();
    }

    private void updateRateLimitsWithGroupQuotaAndStoreInRedis(List<APIRateLimitSettings> rateLimits, int groupQuota, String apiIdentifier) {
        logger.info("Updating rate limit setting table with apiIdentifier : {} and groupQuota : {}", apiIdentifier, groupQuota);
        int totalLimit = 0;

        for (APIRateLimitSettings rateLimit : rateLimits) {
            rateLimit.setGroupQuota(String.valueOf(groupQuota));
            totalLimit += (Integer.parseInt(Optional.ofNullable(rateLimit.getServiceLimitSync()).orElse("0"))
                    + Integer.parseInt(Optional.ofNullable(rateLimit.getServiceLimitAsync()).orElse("0")));
        }

        if (totalLimit > groupQuota) {
            removeLimitsFromListIndividually(rateLimits, totalLimit - groupQuota, apiIdentifier);
            updateSharedLimitQuota(rateLimits, 0, apiIdentifier);
        } else if (totalLimit < groupQuota) {
            updateSharedLimitQuota(rateLimits, groupQuota - totalLimit, apiIdentifier);
        }
        updateRateLimitsInRedis(rateLimits);
    }

    private void updateRateLimitsInRedis(List<APIRateLimitSettings> rateLimitSettings) {
        rateLimitSettings.forEach(rateLimit -> {
            RateLimitingDTO dto = rateLimitingMapper.mapToDTO(rateLimit);
            updateBucketConfigInRedis(dto);
        });
    }

    public Bucket resolveBucket(String key, Long limit, Integer refillTime, ChronoUnit timeUnit) {
        Supplier<BucketConfiguration> configSupplier = getConfigSupplierForUser(limit, refillTime, timeUnit);
        // Does not always create a new bucket, but instead returns the existing one if it exists.
        Bucket bucket = proxyManager.builder().build(key.getBytes(UTF_8), configSupplier);
        bucket.replaceConfiguration(configSupplier.get(), TokensInheritanceStrategy.PROPORTIONALLY);

        return bucket;
    }

    private Supplier<BucketConfiguration> getConfigSupplierForUser(Long limit, Integer refillTime, ChronoUnit timeUnit) {
        Refill refill = Refill.intervally(limit, Duration.of(refillTime, timeUnit));
        Bandwidth limitBandwidth = Bandwidth.classic(limit, refill);
        return () -> (BucketConfiguration.builder()
                .addLimit(limitBandwidth)
                .build());
    }

    @Override
    public void loadRateLimitDomainInfo() {
        logger.info("STARTED loading rate limit cache from rate_limit_domain_info table.");
        List<RateLimitDomainInfo> rateLimitData = socialRateLimitingDomainRepo.findAll();
        rateLimitData.parallelStream().forEach(param -> {
            RateLimitDomainInfoDTO rateLimitDomainInfoDTO = rateLimitingMapper.mapDomainToDTO(param);
            updateRateLimitSocialDomainInfoInRedis(rateLimitDomainInfoDTO);
        });
        logger.info("ENDED loading loadRateLimitDomainInfo");
    }
   @Override
    public void updateRateLimitSocialDomainInfoInRedis(RateLimitDomainInfoDTO dto) {
       redisExternalService.set(dto.getDomain() ,JSONUtils.toJSON(dto));
    }

    @Override
    public void enableRateLimit(String rateLimitEnabledFlag) {
        redisExternalService.set("social.api.rate.limiting.enabled", rateLimitEnabledFlag);
    }

    @Override
    public void sharedAuditRateLimit(RateLimitAuditRequest request) {
        logger.info("Inside shared audit table for sourceUrl :{} and type :{}",
                request.getSourceUrl(), request.getMethodType());
        List<String> data= redisExternalService.popFromQueue("RL#SHARED*");
        if (CollectionUtils.isEmpty(data)) {
            logger.info("No data identified during pop operation");
            return;
        }

        String concatenatedString = convertToString(data);

        SocialRateLimitAlertAudit audit = new SocialRateLimitAlertAudit();
        audit.setChannel(request.getChannel());
        audit.setApiSource(request.getSourceUrl());
        audit.setApiType(request.getMethodType());
        audit.setQuotaBreachUrl(request.getBreachUrl());
        audit.setQuotaBreachUrlType(request.getBreachUrlType());
        audit.setGroupIdentifier(request.getGroupIdentifier());
        audit.setRateLimitData(concatenatedString);
        audit.setCreatedAt(new Date());
        audit.setUpdatedAt(new Date());
        rateLimitSharedUsageAuditRepo.saveAndFlush(audit);
    }
}
