package com.birdeye.social.service.tiktok.impl;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessTiktokAccountsRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.entities.SocialModulePermission;
import com.birdeye.social.service.ISocialModulePermissionService;
import com.birdeye.social.service.impl.FacebookSocialServiceImpl;
import com.birdeye.social.service.tiktok.TiktokAccountService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TiktokAccountServiceImpl implements TiktokAccountService {

    @Autowired
    BusinessTiktokAccountsRepository tiktokAccountsRepository;

    @Autowired
    private ISocialModulePermissionService socialModulePermissionService;

    private static final Logger log	= LoggerFactory.getLogger(TiktokAccountServiceImpl.class);

    @Override
    public List<String> findByBusinessIdIn(List<Integer> businessIds) {
        return tiktokAccountsRepository.findProfileIdByBusinessIdIn(businessIds);
    }

    @Override
    public Map<String, Boolean> getTiktokPostPermissionPageWise(List<BusinessTiktokAccounts> businessTiktokAccounts, List<String> modules) {
        List<String> tiktokScopes = new ArrayList<>();
        Map<String, Boolean> permissionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(modules)) {
            for (String module : modules) {
                List<String> modulePermissions = getModulePermissions(SocialChannel.TIKTOK.getId(), module);
                tiktokScopes.addAll(modulePermissions);
            }
            for(BusinessTiktokAccounts tiktokAccount : businessTiktokAccounts) {
                boolean flag = true;
                if (tiktokAccount.getIsValid() == 0 || Objects.isNull(tiktokAccount.getScope())) {
                    flag = false;
                }
                List<String> permissions = Arrays.stream(tiktokAccount.getScope().split(",")).collect(Collectors.toList());
                if(flag && !new HashSet<>(permissions).containsAll(tiktokScopes)){
                    flag = false;
                }
                permissionMap.put(tiktokAccount.getProfileId(), flag);
            }
        }
        return permissionMap;
    }

    @Cacheable(value = "modulePermissions", key = "#sourceId+'_'+#module", unless = "#result == null")
    private List<String> getModulePermissions(Integer sourceId, String module) {
        log.info("Fetching Tiktok module permissions from DB for :{}", module);
        SocialModulePermission socialModulePermission = socialModulePermissionService
                .getPermissionsForChannelAndModule(sourceId, module);
        if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
            return Arrays
                    .asList(socialModulePermission.getPermissionsNeeded().split(","));
        }
        return new ArrayList<>();
    }
}
