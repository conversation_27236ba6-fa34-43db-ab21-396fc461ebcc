package com.birdeye.social.service.SocialReportService.Apple;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dao.applechat.BusinessAppleAccountRepository;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dao.reports.PostInsightAuditRepo;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.dto.PostInsightDTO;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.entities.report.PostInsightAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.insights.constants.InsightsConstants;
import com.birdeye.social.service.CacheService;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialBusinessPropertyService;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.service.SocialReportService.LinkedIn.LinkedInInsightsImpl;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.TopPostsReportUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Functions;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static com.birdeye.social.constant.Constants.BATCH_SIZE_FOR_TOP_POSTS;
import static com.birdeye.social.constant.Constants.THREADPOOL_SIZE_FOR_TOP_POSTS;

/**
 * <AUTHOR> on 20/11/23
 */

@Service
public class AppleInsightsImpl implements AppleInsights {

    @Autowired
    private ReportDataConverter reportDataConverter;

    @Autowired
    private ReportsEsService reportsEsService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private BusinessAppleAccountRepository businessAppleAccountRepository;

    @Autowired
    private BusinessPostsRepository businessPostsRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    @Autowired
    private PostInsightAuditRepo postInsightAuditRepo;

    @Autowired
    private BusinessAppleLocationRepo businessAppleLocationRepo;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private SocialBusinessPropertyService socialBusinessPropertyService;

    @Autowired
    private IBusinessCoreService coreService;

    @Autowired
    private SocialPostRepository socialPostRepository;


    @Value("${apple.client.id}") private String clientId;
    @Value("${apple.client.secret}") private String clientSecret;

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    private static final Logger log = LoggerFactory.getLogger(LinkedInInsightsImpl.class);

    @Override
    public PostDataAndInsightResponse getAppleInsightsForPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam,
                                                              String sortOrder, boolean excelDownload) {
        log.info("Request to get post insight with enterprise id: {}", insightsRequest.getEnterpriseId());
        PostDataAndInsightResponse response = new PostDataAndInsightResponse();
        InsightsPostRequest insightsPostRequest = new InsightsPostRequest(insightsRequest,startIndex,pageSize,
                sortParam,sortOrder,Integer.toString(SocialChannel.APPLE_CONNECT.getId()), getPageIdsByBids(insightsRequest.getBusinessIds()));
        InsightsESRequest request = reportDataConverter.createESRequestForPost(insightsPostRequest, ElasticConstants.POST_INSIGHTS.getName());
        log.info("InsightsESRequest with page ids : {}",request.getDataModel());
        response = reportsEsService.getPostDataFromEs(request, excelDownload);
        return response;
    }

    private List<String> getPageIdsByBids(List<Integer> businessIds){
        return Lists.transform(businessAppleLocationRepo.findDistinctAppleLocationIdByBusinessIdIn(businessIds),
                Functions.toStringFunction());
    }

    @Override
    public void getPostInsights(BusinessPosts businessPosts, Boolean isFreshRequest) {
        BusinessAppleLocation businessAppleLocation = businessAppleLocationRepo.findFirstByAppleLocationId(businessPosts.getExternalPageId());
        if(Objects.isNull(businessAppleLocation)) {
            log.info("No business found for page id: {}",businessPosts.getExternalPageId());
            return;
        }
        if(Objects.nonNull(businessAppleLocation.getEnterpriseId()) && !socialBusinessPropertyService.isSocialPostingAndReportingEnabled(businessAppleLocation.getEnterpriseId())){
            log.info("Social reporting is not enabled for enterprise id : {}", businessAppleLocation.getEnterpriseId());
            return;
        }
        try {
            AppleShowcaseInsightsDTO postInsightEngagementResponse = socialProxyHandler.runWithRetryableBirdeyeException( () ->
                    getPostInsightResponse(businessAppleLocation, Constants.APPLE_ENGAGEMENT_METRIC,businessPosts.getPublishDate())
            );

            AppleShowcaseInsightsDTO postInsightImpressionsResponse = socialProxyHandler.runWithRetryableBirdeyeException( () ->
                    getPostInsightResponse(businessAppleLocation, Constants.APPLE_IMPRESSION_METRIC,businessPosts.getPublishDate())
            );
            log.info("postInsightEngagementResponse: {}", postInsightEngagementResponse);
            Map<String, Integer> insightData = new HashMap<>();
            Integer metrics = 0;
            if(Objects.nonNull(postInsightEngagementResponse) && Objects.nonNull(postInsightEngagementResponse.getData()))
                for (Tuples tuple : postInsightEngagementResponse.getData().getTuples()){
                    metrics += Integer.parseInt(tuple.getMetrics().get(0));
                }
            insightData.put(InsightsConstants.POST_ENGAGEMENT_METRIC, metrics);

            log.info("postInsightImpressionsResponse: {}", postInsightImpressionsResponse);
            metrics = 0;
            if(Objects.nonNull(postInsightImpressionsResponse) && Objects.nonNull(postInsightImpressionsResponse.getData()))
                for (Tuples tuple : postInsightImpressionsResponse.getData().getTuples()){
                    metrics += Integer.valueOf(tuple.getMetrics().get(0));
                }
            insightData.put(InsightsConstants.POST_IMPRESSIONS_METRIC, metrics);
            log.info("map: {}", insightData);
            PostData postData = createPostData(businessPosts, insightData);
            log.info("Post data: {}", postData);
            PostInsightDTO postInsightDTO = getPostInsightInfo(insightData);
            ObjectMapper objectMapper = new ObjectMapper();
            String postInsightJson = objectMapper.writeValueAsString(postInsightDTO);
            businessPosts.setResponse(postInsightJson);
            businessPostsRepository.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, false);
            if(!isFreshRequest || (CollectionUtils.isEmpty(postData.getImages()) && CollectionUtils.isEmpty(postData.getVideos()))) {
                sendPostDataToKafka(postData);
            } else {
                commonService.uploadMediaToPicturesque(postData, businessPosts);
                saveCDNPostToES(businessPosts);
            }
        }catch (Exception e) {
            businessPosts.setError(e.getMessage());
            businessPostsRepository.saveAndFlush(businessPosts);
            auditPostInsight(businessPosts, true);
            log.info("Exception occurred while getting data for postId: {} of Apple Location: {}",businessPosts.getPostId(),businessPosts.getExternalPageId());
        }

    }

    private AppleShowcaseInsightsDTO getPostInsightResponse(BusinessAppleLocation page, String insightType, Date publishDate) {
        String uri = "";
        AppleShowcaseInsightsDTO appleShowcaseInsightsDTOResponse;

        try {
            String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV3();
            ResponseEntity<AppleShowcaseInsightsDTO> response;
            uri = baseUrl.concat("/companies/").concat(page.getAppleCompanyId()).concat("/reports/quick-report");

            Map<String, Object> requestBody = getRequestBodyMap(page, insightType,publishDate);
            log.info("uri to getPostInsightResponse {}",uri );

            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(requestBody, null), AppleShowcaseInsightsDTO.class);
            appleShowcaseInsightsDTOResponse = response.getBody();
        } catch (HttpStatusCodeException e) {
            log.info("HttpStatusCodeException while getting apple location info for url {}: {}", uri, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            log.info("Exception while getting apple location info for url: {} :", uri, e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return appleShowcaseInsightsDTOResponse;
    }

    @NotNull
    private Map<String, Object> getRequestBodyMap(BusinessAppleLocation page, String insightType, Date publishDate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // Prepare request body
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("resourceType", "LOCATION");
        requestBody.put("resourceId", page.getAppleLocationId());
        requestBody.put("reportName", insightType);
        requestBody.put("startDate", dateFormat.format(publishDate));
        requestBody.put("endDate", dateFormat.format(new Date()));
        requestBody.put("useLocalTime", false);
        requestBody.put("timeGranularity", "DAY");
        return requestBody;
    }

    private <T> HttpEntity<T> getHttpEntity(T body, final Map<String, String> extraHeaders){
        return new HttpEntity<T>(body, getHttpHeaders(extraHeaders));
    }

    private HttpHeaders getHttpHeaders(final Map<String, String> providedHeaders){
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set(HttpHeaders.AUTHORIZATION, this.generateBearerToken());
        if(MapUtils.isNotEmpty(providedHeaders)){
            providedHeaders.keySet().forEach(h -> httpHeaders.set(h, providedHeaders.get(h)));
        }
        return httpHeaders;
    }

    @Autowired
    CacheService cacheService;
    private String generateBearerToken(){
        String access_token = (String) this.cacheService.get(Constants.APPLE_CONNECT_TOKEN);
        if(access_token != null){
            return access_token;
        }
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrl();
        String uri = baseUrl.concat("/oauth2/token");
        AppleCredsDto credentials = AppleCredsDto.builder().client_id(this.clientId).client_secret(this.clientSecret).grant_type(Constants.APPLE_GRANT_TYPE).scope(Constants.APPLE_SCOPE).build();
        ResponseEntity<AppleCredsDto> response;
        try {
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(credentials, null), AppleCredsDto.class);
        } catch (HttpStatusCodeException e) {
            log.info("HttpStatusCodeException while getting apple token: {}",e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            log.info("Exception while getting apple token:",e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        AppleCredsDto tokenDto = response.getBody();
        access_token =  tokenDto.getToken_type().concat(" ").concat(tokenDto.getAccess_token());
        this.cacheService.set(Constants.APPLE_CONNECT_TOKEN, access_token, 50 * 60);
        return access_token;
    }

    @Override
    public PostData createPostData(BusinessPosts businessPosts,Map<String,Integer> insightData) throws Exception {
        List<String> media;
        PostData postData = new PostData();
        postData.setId(businessPosts.getId());
        postData.setBePostId(businessPosts.getBePostId()!=null?businessPosts.getBePostId().toString()
                :null);
        postData.setPostId(businessPosts.getPostId());
        postData.setSourceId(businessPosts.getSourceId());
        postData.setPageName(businessPosts.getPageName());
        postData.setPageId(businessPosts.getExternalPageId());
        postData.setBusinessId(businessPosts.getBusinessId());
        postData.setEnterpriseId(businessPosts.getEnterpriseId());
        postData.setPostUrl(businessPosts.getPostUrl());
        postData.setPostText(businessPosts.getPostText());
        postData.setPostingDate(businessPosts.getPublishDate());
        postData.setPostEndDate(businessPosts.getPostEndDate());
        postData.setPostType(businessPosts.getPostType());
        if(StringUtils.isNotEmpty(businessPosts.getImageUrls())) {
            String imageUrl = businessPosts.getImageUrls();
            // imageUrl = imageUrl.substring(1, imageUrl.length() - 1);
            media = Arrays.asList(imageUrl.split(","));
            postData.setImages(media);
        }
        if(StringUtils.isNotEmpty(businessPosts.getVideoUrls())) {
            String videoUrl = businessPosts.getVideoUrls();
            // videoUrl = videoUrl.substring(1, videoUrl.length() - 1);
            media = Arrays.asList(videoUrl.split(","));
            postData.setVideos(media);
        }
        postData.setEngagement(insightData.get(InsightsConstants.POST_ENGAGEMENT_METRIC));
        postData.setImpression(insightData.get(InsightsConstants.POST_IMPRESSIONS_METRIC));
        if(Objects.nonNull(postData.getEngagement()) && Objects.nonNull(postData.getImpression()) && postData.getImpression()!=0) {
            postData.setEngagementRate((double)(postData.getEngagement()*100)/postData.getImpression());
        }
        if(Objects.nonNull(businessPosts.getBePostId())) {
            postData.setTagIds(socialTagService.getTagIdsFromEntityId(Long.valueOf(businessPosts.getBePostId()), SocialTagEntityType.POST));
        }
        // add post author details to be shown on analyze tab
        addPostAuthorDetails(businessPosts, postData);
        return postData;
    }

    private void addPostAuthorDetails(BusinessPosts businessPosts, PostData postData) {
        boolean isBePost = Objects.equals(postData.getSourceId(), SocialChannel.APPLE_CONNECT.getId()) ||
                Objects.nonNull(postData.getBePostId()) && !Objects.equals(postData.getPostId(), postData.getBePostId());
        // Check if the author details are missing in BusinessPosts
        boolean isAuthorDetailsMissing = Objects.isNull(businessPosts.getCreatedByBeUserId())
                || Objects.isNull(businessPosts.getCreatedByBeUserName())
                || Objects.isNull(businessPosts.getCreatedByBeUserEmail());

        BusinessCoreUser businessCoreUser = null;

        if (isBePost && isAuthorDetailsMissing) {
            businessCoreUser = getBusinessCoreUser(businessPosts);
        }

        // Update BusinessPosts with the author details
        if (isAuthorDetailsMissing && Objects.nonNull(businessCoreUser)) {
            businessPosts.setCreatedByBeUserId(businessCoreUser.getId());
            businessPosts.setCreatedByBeUserName(businessCoreUser.getName());
            businessPosts.setCreatedByBeUserEmail(businessCoreUser.getEmailId());
        }

        // Set author details in postData which is later updated in ES
        postData.setPublisherId(Objects.nonNull(businessCoreUser) ? businessCoreUser.getId() : businessPosts.getCreatedByBeUserId());
        postData.setPublisherName(Objects.nonNull(businessCoreUser) ? businessCoreUser.getName() : businessPosts.getCreatedByBeUserName());
        postData.setPublisherEmail(Objects.nonNull(businessCoreUser) ? businessCoreUser.getEmailId() : businessPosts.getCreatedByBeUserEmail());
    }

    private BusinessCoreUser getBusinessCoreUser(BusinessPosts businessPosts) {
        Integer createdById = getCreatedById(businessPosts);
        // createdById is 0 for PUBLIC-POSTING
        if (Objects.nonNull(createdById) && createdById != 0) {
            return coreService.getUserInfo(createdById);
        }
        return null;
    }

    private Integer getCreatedById(BusinessPosts businessPosts) {
        if (Objects.isNull(businessPosts.getCreatedByBeUserId())) {
            SocialPost socialPost = socialPostRepository.findById(businessPosts.getBePostId());
            if (Objects.nonNull(socialPost) && Objects.nonNull(socialPost.getCreatedBy())) {
                return socialPost.getCreatedBy();
            }
        }
        return businessPosts.getCreatedByBeUserId();
    }

    private PostInsightDTO getPostInsightInfo(Map<String,Integer> insightData) {
        PostInsightDTO postInsightDTO = new PostInsightDTO();
        postInsightDTO.setEngagement(insightData.get(InsightsConstants.POST_ENGAGEMENT_METRIC));
        postInsightDTO.setImpression(insightData.get(InsightsConstants.POST_IMPRESSIONS_METRIC));
        if(Objects.nonNull(postInsightDTO.getEngagement()) && Objects.nonNull(postInsightDTO.getImpression()) && postInsightDTO.getImpression()!=0) {
            postInsightDTO.setEngagementRate((double)(postInsightDTO.getEngagement()*100)/postInsightDTO.getImpression());

        }
        return postInsightDTO;
    }

    private void sendPostDataToKafka(PostData postData) {
        String key = postData.getPostId().concat("_").concat(postData.getPostingDate().toString());
        kafkaProducerService.sendObjectV1(InsightsConstants.APPLE_POST_INSIGHT_ES, postData);
    }

    private void auditPostInsight(BusinessPosts businessPosts, Boolean isFailed) {
        PostInsightAudit postInsightAudit = new PostInsightAudit();
        postInsightAudit.setPostId(businessPosts.getPostId());
        postInsightAudit.setSourceId(businessPosts.getSourceId());
        postInsightAudit.setPageId(businessPosts.getExternalPageId());
        if(isFailed) {
            postInsightAudit.setResponse(businessPosts.getError());
        } else {
            postInsightAudit.setResponse(businessPosts.getResponse());
        }
        postInsightAuditRepo.saveAndFlush(postInsightAudit);
    }



    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData, BusinessPosts businessPosts) throws IOException {

    }

    @Override
    public void startScanForPosts(String pageId) {
    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {

    }

    @Async
    @Override
    public void postApplePostInsightsToEs(PostData postData) {
        String index = InsightsConstants.POST_INSIGHT;
        List<EsPostDataPoint> esPostDataPoints = reportDataConverter.preparePostEsDTO(postData);
        reportsEsService.bulkPostPagePostDataToES(esPostDataPoints,index);
    }

    @Override
    public void postApplePageInsightToES(PageInsights pageInsights) {

    }

    @Override
    public PageInsightsResponse getAppleInsightsForPage(InsightsRequest insights) throws Exception {
        return null;
    }

    @Override
    public PostDataAndInsightResponse getAppleInsightsForTopPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        log.info("Request to get top post insight with enterprise id : {} for Apple", insightsRequest.getEnterpriseId());
        List<List<Integer>> batches = TopPostsReportUtils.splitListIntoBatches(insightsRequest.getBusinessIds(), BATCH_SIZE_FOR_TOP_POSTS);
        ExecutorService executorService = Executors.newFixedThreadPool(THREADPOOL_SIZE_FOR_TOP_POSTS);
        List<Future<PostDataAndInsightResponse>> futures = new ArrayList<>();
        for (List<Integer> batch : batches) {
            List<String> pageIds = getPageIdsByBids(batch);
            InsightsPostRequest insightsPostRequest = new InsightsPostRequest(insightsRequest, startIndex, pageSize,
                    sortParam, sortOrder, Integer.toString(SocialChannel.APPLE_CONNECT.getId()), pageIds);
            futures.add(executorService.submit(() -> {
                InsightsESRequest request = reportDataConverter.createESRequestForTopPosts(insightsPostRequest, ElasticConstants.POST_INSIGHTS.getName());
                return reportsEsService.getPostDataFromEs(request, excelDownload);
            }));
        }
        List<PostDataAndInsightResponse> combinedResponses = new ArrayList<>();
        try {
            for (Future<PostDataAndInsightResponse> future : futures) {
                combinedResponses.add(future.get());
            }
        } catch (InterruptedException | ExecutionException e) {
            log.error("Async Exception occurred in [getAppleInsightsForTopPost] for enterpriseId : {} exception :  {}", insightsRequest.getEnterpriseId(), e.getMessage());
        } finally {
            executorService.shutdown();
        }
        return TopPostsReportUtils.getSortedInsightResponse(combinedResponses, pageSize, sortParam, sortOrder);
    }
}
