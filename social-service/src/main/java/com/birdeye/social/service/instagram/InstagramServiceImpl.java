package com.birdeye.social.service.instagram;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialStreams;
import com.birdeye.social.external.response.facebook.CursorDTO;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.birdeye.social.facebook.response.FacebookPostResponse;
import com.birdeye.social.instagram.*;
import com.birdeye.social.instagram.response.InstagramBaseResponse;
import com.birdeye.social.instagram.response.InstagramCompetitorProfile;
import com.birdeye.social.instagram.response.InstagramTimelineResponse;
import com.birdeye.social.instagram.response.InstagramTimelineResponseList;
import com.birdeye.social.model.Feed;
import com.birdeye.social.model.SocialTimeline;
import com.birdeye.social.service.KafkaExternalService;
import com.birdeye.social.utils.FacebookUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import com.birdeye.social.aspect.Profiled;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.utils.instagram.InstagramUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.net.URI;
import java.util.*;

/**
 * <AUTHOR>
 *
 */

@Service("instagramService")
@Profiled
public class InstagramServiceImpl implements IInstagramService {

	private final Logger LOGGER = LoggerFactory.getLogger(InstagramServiceImpl.class);
	private static final String FEED_DATE_FORMAT = "yyyy-MM-dd'T'hh:mm:ssZ";
	private static final String INSTAGRAM_CLIENT_ID_KEY = "client_id";
	private static final String INSTAGRAM_CLIENT_SECRET_KEY = "client_secret";
	private static final String INSTAGRAM_REDIRECT_URI_KEY = "redirect_uri";

	//TODO
	private static final String INSTAGRAM_REDIRECT_URI_DOMAIN_PREFIX = "https://";
	private static final String INSTAGRAM_REDIRECT_URI_DOMAIN_SUFFIX = "/dashboard/setup/social?active=instagram";
	private static final String INSTAGRAM_AUTH_CODE_SCOPES = "&response_type=code&scope=public_content+comments+follower_list+likes+basic+relationships";

	private static final String EQUALS = "=";
	private static final String AND = "&";

	private static final String COMMENT_ERROR_MESSAGE= "Cannot post comment due to some error.";
	private static final String MENTIONS_NOT_ALLOWED_ERROR_MESSAGE= "Mentions are not allowed as per the target user's settings.";
	private static final String PERMISSION_MISSING_ERROR_MESSAGE= "Comment reply not allowed due to missing permissions.";

	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate socialRestTemplate;

	@Autowired
	private SocialProxyHandler socialProxyHandler;

	@Autowired
	private KafkaExternalService kafkaExternalService;


	@Override
	public String getInstagramAuthorizationCodeURL(String clientId, String domainUrl) {
		StringBuilder sb = new StringBuilder(InstagramUtils.getInstagramAuthBaseUrl());
		
		sb.append(INSTAGRAM_CLIENT_ID_KEY).append(EQUALS)
				.append(clientId).append(AND).append(INSTAGRAM_REDIRECT_URI_KEY).append(EQUALS)
				.append(getInstagramRedirectUri(domainUrl)).append(INSTAGRAM_AUTH_CODE_SCOPES);
		return sb.toString();
		
	}
	
	@Override
	public InstagramGenerateAccessTokenResponse getAccessToken(String authCode, String clientId, String clientSecret, String domainUrl) {
		String instagramAccessTokenApiUrl = InstagramUtils.getInstagramAccessTokenGenerationBaseUrl();
		MultiValueMap<String, String> parametersMap = getAccessTokenGenerationParameters(authCode, clientId, clientSecret, domainUrl);
		InstagramGenerateAccessTokenResponse response = null;
		try {
			LOGGER.info("url for getAccessToken {} ",instagramAccessTokenApiUrl);
			response = socialRestTemplate.postForObject(instagramAccessTokenApiUrl, parametersMap, InstagramGenerateAccessTokenResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("Exception while generating access token for instagram is  {} for request :: {} ",
					e.getResponseBodyAsString(), parametersMap, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_ACCESS_TOKEN, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while generating access token for instagram  for request :: {} ", parametersMap,
					e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_ACCESS_TOKEN, e.getMessage());
		}
		LOGGER.info("Instagram response for getAccessToken is {}", response);
		return response;
	}
	@Override
	public InstagramTimelineResponseList getInstagramMyPostsFeeed(String accessToken, String instagramUserId, Integer count, String nextToken) {

		String instagramStreamMyPostsUrl = InstagramUtils.getInstagramMyPostsUrl();

		String childFields = "{id,media_type,media_url}";
		StringBuilder sb = new StringBuilder();
		sb.append(instagramStreamMyPostsUrl).append(instagramUserId).append("/media").append("?")
				.append("fields=caption,comments_count,id,is_comment_enabled" +
						",media_product_type,ig_id,is_shared_to_feed,media_type,like_count,owner,thumbnail_url," +
						"timestamp,username,permalink,shortcode,media_url,children{id,media_type,media_url}")
				.append("&access_token=").append(accessToken).append("&limit=").append(count);
		if (Objects.nonNull(nextToken)) {
			sb.append("&after=").append(nextToken);
		}

		InstagramTimelineResponseList response = null;
		try {
			LOGGER.info("url for getInstagramMyPostsFeeed {} ",sb.toString());
			response = socialRestTemplate.getForObject(sb.toString(), InstagramTimelineResponseList.class, childFields);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while generating instagram feed for request {} is {}. Detailed Stacktrace is {} ",
					instagramStreamMyPostsUrl, e.getResponseBodyAsString(), e.getStackTrace());
			InstagramBaseResponse ex = null;
			try {
				ex = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), InstagramBaseResponse.class);
			} catch (IOException exc) {
				LOGGER.error("Exception while fetching instagram feeds {}", exc.getMessage());
			}

			if(Objects.nonNull(ex) && Objects.nonNull(ex.getError()) && Objects.nonNull(ex.getError().getCode()) && ex.getError().getCode().equals(190)) {
				if(Objects.nonNull(ex.getError().getError_subcode())
						&& ex.getError().getError_subcode().equals(460)) {
					kafkaExternalService.markPageInvalid(SocialChannel.INSTAGRAM.getName(), instagramUserId);
				}
			}
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while generating instagram feed for request {}. Detailed Stacktrace is {} ",
					instagramStreamMyPostsUrl, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getInstagramMyPostsFeeed is {}", response);
		return response;
	}

	@Override
	public InstagramTimelineResponseList getInstagramMyPostsFeedEngagement(String accessToken, String instagramUserId, Integer count, String nextToken) {
		String instagramStreamMyPostsUrl = InstagramUtils.getInstagramMyPostsUrl();
		InstagramTimelineResponseList completeResponse = new InstagramTimelineResponseList();
		List<InstagramTimelineResponse> postData = new ArrayList<>();
		String childFields = "{id,media_type,media_url}";
		boolean hasMore = true;
		// We sync posts engagement from last 30 days
		Long maxEpochDate = DateUtils.addDays(new Date(), -30).getTime() / 1000;
		do {
			StringBuilder sb = new StringBuilder();
			sb.append(instagramStreamMyPostsUrl).append(instagramUserId).append("/media").append("?")
					.append("fields=comments_count,id,like_count,timestamp")
					.append("&since=").append(maxEpochDate)
					.append("&access_token=").append(accessToken).append("&limit=").append(count);
			if (Objects.nonNull(nextToken)) {
				sb.append("&after=").append(nextToken);
			}

			InstagramTimelineResponseList response = null;
			try {
				LOGGER.info("url for getInstagramMyPostsFeedEngagement {} ",sb.toString());
				response = socialRestTemplate.getForObject(sb.toString(), InstagramTimelineResponseList.class, childFields);
				if(response!=null){
					postData.addAll(response.getData());
					if(response.getPaging()!=null && response.getPaging().getCursors()!=null && response.getPaging().getCursors().getAfter()!=null){
						nextToken = response.getPaging().getCursors().getAfter();
					}else{
						hasMore = false;
					}
				}
			} catch (RestClientResponseException e) {
				LOGGER.error("RestClientResponseException while generating instagram feed for request {} is {}. Detailed Stacktrace is {} ",
						instagramStreamMyPostsUrl, e.getResponseBodyAsString(), e.getStackTrace());
				throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
			} catch (Exception e) {
				LOGGER.error("Exception while generating instagram feed for request {}. Detailed Stacktrace is {} ",
						instagramStreamMyPostsUrl, e.getStackTrace());
				throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
			}
			LOGGER.info("Instagram response for getInstagramMyPostsFeeed is {}", response);
		}while (hasMore && nextToken!=null);

		completeResponse.setData(postData);
		return completeResponse;
	}

	private InstagramTimelineResponseList getInstagramStories(String accessToken, String instagramUserId, Integer count, String nextToken) {

		String instagramStreamMyPostsUrl = InstagramUtils.getInstagramMyPostsUrl();

		String childFields = "{id,media_type,media_url}";
		StringBuilder sb = new StringBuilder();
		sb.append(instagramStreamMyPostsUrl).append(instagramUserId).append("/stories").append("?")
				.append("fields=caption,id" +
						",media_product_type,ig_id,is_shared_to_feed,media_type,like_count,owner,thumbnail_url," +
						"timestamp,username,permalink,shortcode,media_url,children{id,media_type,media_url}")
				.append("&access_token=").append(accessToken).append("&limit=").append(count);
		if (Objects.nonNull(nextToken)) {
			sb.append("&after=").append(nextToken);
		}

		InstagramTimelineResponseList response = null;
		try {
			LOGGER.info("url for getInstagramStories {} ",sb.toString());
			response = socialRestTemplate.getForObject(sb.toString(), InstagramTimelineResponseList.class, childFields);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while generating instagram stories for request {} is {}. Detailed Stacktrace is {} ",
					instagramStreamMyPostsUrl, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while generating instagram stories for request {}. Detailed Stacktrace is {} ",
					instagramStreamMyPostsUrl, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getInstagramStories is {}", response);
		return response;
	}

	@Override
	public SocialTimeline getInstagramTimeline(BusinessInstagramAccount instagramAccount, String type, String nextToken, Date maxDate,
											   boolean parseInRelativeDate, int limit, boolean isStream) {
		SocialTimeline socialTimeline = new SocialTimeline();
		InstagramTimelineResponseList igPostList = new InstagramTimelineResponseList();
		try {
			boolean maxDateExceeded = false;
			InstagramTimelineResponseList response = null;
			socialTimeline.setChannel("instagram");
			socialTimeline.setStreamName(instagramAccount.getInstagramAccountName());
			socialTimeline.setStreamImage(instagramAccount.getInstagramAccountPictureUrl());
			socialTimeline.setPageId(instagramAccount.getId());
			socialTimeline.setBusinessId(instagramAccount.getBusinessId());
			socialTimeline.setStreamType(type.toLowerCase());
			if (SocialStreams.StreamType.MY_POSTS.getType().equalsIgnoreCase(type)) {
				String finalNextToken = nextToken;
				response = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
						() -> getInstagramMyPostsFeeed(instagramAccount.getPageAccessToken(),
								instagramAccount.getInstagramAccountId(), limit, finalNextToken)
				);
			}
			if(CollectionUtils.isEmpty(response.getData())) {
				return socialTimeline;
			}

			igPostList.setData(response.getData());
			Date postDate = response.getData().get(response.getData().size() - 1).getTimestamp();

			while(response!=null && !CollectionUtils.isEmpty(response.getData()) && response.getPaging()!=null && response.getPaging().getNext()!=null
					 && (Objects.isNull(maxDate) || postDate.after(maxDate)) && !isStream) {
				CursorDTO cursorDTO = response.getPaging().getCursors();
				if(cursorDTO == null) break;
				nextToken = cursorDTO.getAfter();
				String finalNextToken1 = nextToken;
				response = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
						() -> getInstagramMyPostsFeeed(instagramAccount.getPageAccessToken(),
								instagramAccount.getInstagramAccountId(), limit, finalNextToken1)
				);

				postDate = response.getData().get(response.getData().size() - 1).getTimestamp();
				if(response!=null && !CollectionUtils.isEmpty(response.getData())) {
					igPostList.setData(response.getData());
				}

			}
			LOGGER.info("Instagram External Response for getInstagramTimeline type {} is : {}", type, response);
			if (response == null) {
				LOGGER.error("Instagram External Response for getInstagramTimeline type {} is null", type);
				throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, "No stream data found");
			}
			socialTimeline.setFeeds(InstagramUtils.getInstagramFeeds(igPostList, instagramAccount, maxDate, parseInRelativeDate, maxDateExceeded));
			socialTimeline.setNextUrl(maxDateExceeded ? null : response.getPaging().getCursors().getAfter());
		} catch (BirdeyeSocialException e) {
			LOGGER.info("Error occured while fetching socialTimeline for instagram account {} with error", instagramAccount, e);
		} catch (Exception e) {
			LOGGER.info("Error occured while fetching socialTimeline for instagram account {} with error", instagramAccount, e);
			return null;
		}
		return socialTimeline;
	}

	@Override
	public InstagramStreamResponse getInstagramLocationsFeed(String accessToken, String locationId, Integer count, String nextMaxId) {
		String instagramStreamLocationsUrl = InstagramUtils.getInstagramLocationsUrl(accessToken, locationId, count, nextMaxId);
		InstagramStreamResponse response = null;
		try {
			LOGGER.info("url for getInstagramLocationsFeed {} ",instagramStreamLocationsUrl);
			response = socialRestTemplate.getForObject(instagramStreamLocationsUrl, InstagramStreamResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while generating instagram locations feed for request {} is {}. Detailed Stacktrace is {} ",
					instagramStreamLocationsUrl, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while generating instagram locations feed for request {}. Detailed Stacktrace is {} ",
					instagramStreamLocationsUrl, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getInstagramLocationsFeed is {}", response);
		return response;
	}
	
	
	//@Cacheable(value = "instagramLocationDetails", key = "#facebookPlacesId", unless = "#result == null")
	
	@Override
	public InstagramLocationResponse getInstagramLocationDetails(String accessToken, String facebookPlacesId) {
		String instagramGetLocationUrl = InstagramUtils.getInstagramGetLocationId(accessToken, facebookPlacesId);
		InstagramLocationResponse response = null;
		try {
			LOGGER.info("url for getInstagramLocationDetails {} ",instagramGetLocationUrl);
			response = socialRestTemplate.getForObject(instagramGetLocationUrl, InstagramLocationResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting instagram location id for request {} is {}. Detailed Stacktrace is {} ",
					instagramGetLocationUrl, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}catch (Exception e) {
			LOGGER.error("Exception while getting instagram location id for request {}. Detailed Stacktrace is {} ",
					instagramGetLocationUrl, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_FEED_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getInstagramLocationDetails is {}", response);
		return response;
	}

	public InstagramGetCommentsResponse getInstagramCommentsOfAPost(String accessToken, String postId) {
		return getInstagramCommentsOfAPost(accessToken, postId, null);
	}

	@Override
	public InstagramGetCommentsResponse getInstagramCommentsOfAPost(String accessToken, String postId, Integer limit) {
		InstagramGetCommentsResponse allCommentsResponse = new InstagramGetCommentsResponse();
		allCommentsResponse.setData(new ArrayList<>());

		String instagramCommentsUrl = InstagramUtils.getInstagramGetCommentsUrl() + postId + "/comments";

		// Build base URI with common query parameters
		UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromUriString(instagramCommentsUrl)
				.queryParam("fields", "like_count,replies{id,username,from,media,parent_id,text,like_count,timestamp}," +
						"text,username,user,media,timestamp,hidden,id,from,parent_id")
				.queryParam("access_token", accessToken);

		// Add 'limit' query parameter if provided
		if (Objects.nonNull(limit)) {
			uriBuilder.queryParam("limit", limit);
		}

		URI getCommentUri = uriBuilder.build().toUri();
		InstagramGetCommentsResponse response;
		LOGGER.info("url for getInstagramCommentsOfAPost {} ",getCommentUri);

		try {
			do {
				response = socialRestTemplate.getForObject(getCommentUri, InstagramGetCommentsResponse.class);
				allCommentsResponse.getData().addAll(response.getData());

				// Check for pagination
				if (Objects.isNull(response.getPaging()) || Objects.isNull(response.getPaging().getCursors())) {
					break;
				}

				// Update URI for next page of comments
				String afterCursor = response.getPaging().getCursors().getAfter();
				getCommentUri = uriBuilder.replaceQueryParam("after", afterCursor).build().toUri();

			} while (true);
		} catch (RestClientResponseException e) {
			handleRestClientResponseException(instagramCommentsUrl, e);
		} catch (Exception e) {
			handleGenericException(instagramCommentsUrl, e);
		}

		LOGGER.info("Instagram response for getInstagramCommentsOfAPost is {}", allCommentsResponse);
		return allCommentsResponse;
	}

	private void handleRestClientResponseException(String instagramCommentsUrl, RestClientResponseException e) {
		LOGGER.error("RestClientResponseException while getting instagram comments for request {} is {}. Detailed Stacktrace is {} ",
				instagramCommentsUrl, e.getResponseBodyAsString(), e.getStackTrace());
		throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
	}

	private void handleGenericException(String instagramCommentsUrl, Exception e) {
		LOGGER.error("Exception while getting instagram comments for request {}. Detailed Stacktrace is {} ",
				instagramCommentsUrl, e.getStackTrace());
		throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
	}


	@Override
	public InstagramPostCommentResponse postCommentOnAnInstagramPost(String accessToken, String postId,
			String comment) {
		/*
		 * curl -F 'access_token=6277192457.b53964e.10964875d2a841b4aea7648f0db96c03' \
		 * > -F 'text=This+is+my+comment' \ >
		 * https://api.instagram.com/v1/media/1638321070632369719_6277192457/comments
		 */

		String instagramPostCommentUrl = InstagramUtils.getInstagramPostCommentUrl(postId);
		MultiValueMap<String, String> parametersMap = getPostCommentParametersNew(accessToken, comment);
		InstagramPostCommentResponse response = null;
		LOGGER.info("Inside postCommentOnAnInstagramPost : comment : {} with url :{} and parametersMap:{}", comment,instagramPostCommentUrl,parametersMap);
		try {
			instagramPostCommentUrl = UriComponentsBuilder.fromHttpUrl(instagramPostCommentUrl).queryParams(parametersMap).build().encode().toUriString();
			response = socialRestTemplate.postForObject(instagramPostCommentUrl, parametersMap,
					InstagramPostCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while posting instagram comments for url {} parameters {} is {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, e.getMessage());
		} catch (Exception e) {
			LOGGER.error(
					"Exception while posting instagram comments for url {} parameters {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, e.getMessage());
		}
		LOGGER.info("Instagram response for postCommentOnAnInstagramPost is {}", response);
		return response;
	}

	@Override
	public InstagramPostCommentResponse postReplyOnAnInstagramPost(String accessToken, String postId,
																	 String comment) {
		/*
		 * curl -F 'access_token=6277192457.b53964e.10964875d2a841b4aea7648f0db96c03' \
		 * > -F 'text=This+is+my+comment' \ >
		 * https://api.instagram.com/v1/media/1638321070632369719_6277192457/comments
		 */
		String instagramPostCommentUrl = InstagramUtils.getInstagramPostCommentReplyUrl(postId );
		MultiValueMap<String, String> parametersMap = getPostCommentParametersNew(accessToken, comment);
		InstagramPostCommentResponse response = null;
		LOGGER.info("Inside postReplyOnAnInstagramPost : comment : {} with url :{} and parametersMap:{}", comment,instagramPostCommentUrl,parametersMap);

		try {
			instagramPostCommentUrl = UriComponentsBuilder.fromHttpUrl(instagramPostCommentUrl).queryParams(parametersMap).build().encode().toUriString();

			response = socialRestTemplate.postForObject(instagramPostCommentUrl, parametersMap,
					InstagramPostCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while posting instagram comments for url {} parameters {} is {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getResponseBodyAsString(), e.getStackTrace());
            InstagramBaseResponse ex = null;
            try {
                ex = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(e.getResponseBodyAsString(), InstagramBaseResponse.class);
            } catch (IOException exc) {
				LOGGER.error("Exception while posting instagram comments {}", exc.getMessage());
            }
            if(Objects.nonNull(ex) && Objects.nonNull(ex.getError())) {
				if(Objects.nonNull(ex.getError().getError_subcode())
					&& ex.getError().getError_subcode().equals(1772179)) {
					throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, MENTIONS_NOT_ALLOWED_ERROR_MESSAGE);
				} else if (Objects.nonNull(ex.getError().getCode())
						&& ex.getError().getCode().equals(100) && Objects.nonNull(ex.getError().getError_subcode())
						&& ex.getError().getError_subcode().equals(33)){
					throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, PERMISSION_MISSING_ERROR_MESSAGE);
				}
			}
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, e.getMessage());
		} catch (Exception e) {
			LOGGER.error(
					"Exception while posting instagram comments for url {} parameters {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, e.getMessage());
		}
		LOGGER.info("Instagram response for postCommentOnAnInstagramPost is {}", response);
		return response;
	}

	@Override
	public InstagramPostCommentResponse postReplyOnMentionPost(String accessToken, String pageId, String postId, String comment) {
		String instagramPostCommentUrl = InstagramUtils.getInstagramMentionReplyUrl(pageId);
		MultiValueMap<String, String> parametersMap = getMentionCommentParameters(accessToken, postId, null, comment);
		InstagramPostCommentResponse response = null;
		try {
			instagramPostCommentUrl = UriComponentsBuilder.fromHttpUrl(instagramPostCommentUrl).queryParams(parametersMap).build().encode().toUriString();
			LOGGER.info("Inside postReplyOnMentionPost with url :{} and parametersMap:{}",instagramPostCommentUrl,parametersMap);

			response = socialRestTemplate.postForObject(instagramPostCommentUrl, parametersMap,
					InstagramPostCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while posting instagram mention post comment for url {} parameters {} is {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, COMMENT_ERROR_MESSAGE);
		} catch (Exception e) {
			LOGGER.error(
					"Exception while posting instagram mention post comment for url {} parameters {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, COMMENT_ERROR_MESSAGE);
		}
		LOGGER.info("Instagram response for postReplyOnMentionPost is {}", response);
		return response;
	}


	@Override
	public InstagramPostCommentResponse postReplyOnMentionComment(String accessToken, String pageId, String postId, String commentId, String comment) {

		String instagramPostCommentUrl = InstagramUtils.getInstagramMentionReplyUrl(pageId);
		MultiValueMap<String, String> parametersMap = getMentionCommentParameters(accessToken, postId, commentId, comment);
		InstagramPostCommentResponse response = null;
		try {
			instagramPostCommentUrl = UriComponentsBuilder.fromHttpUrl(instagramPostCommentUrl).queryParams(parametersMap).build().encode().toUriString();
			LOGGER.info("Inside postReplyOnMentionComment with url :{} and parametersMap:{}",instagramPostCommentUrl,parametersMap);
			response = socialRestTemplate.postForObject(instagramPostCommentUrl, parametersMap,
					InstagramPostCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while posting instagram mention comments for url {} parameters {} is {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, COMMENT_ERROR_MESSAGE);
		} catch (Exception e) {
			LOGGER.error(
					"Exception while posting instagram mention comments for url {} parameters {}. Detailed Stacktrace is {} ",
					instagramPostCommentUrl, parametersMap, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_POST_COMMENT_FAILED, COMMENT_ERROR_MESSAGE);
		}
		LOGGER.info("Instagram response for postReplyOnMentionComment is {}", response);
		return response;
	}
	
	@Override
	public boolean deleteCommentOnAnInstagramPost(String accessToken,  String commentId) {
		/*
		 * curl -X DELETE
		 * https://api.instagram.com/v1/media/1638321070632369719_6277192457/comments/
		 * 17878572187163720?access_token=6277192457.b53964e.
		 * 10964875d2a841b4aea7648f0db96c03 {"data": null, "meta": {"code": 200}}
		 */
		String instagramDeleteCommentUrl = InstagramUtils.getInstagramDeleteCommentUrl(accessToken,  commentId);
		try {
			LOGGER.info("Inside deleteCommentOnAnInstagramPost with url :{}",instagramDeleteCommentUrl);
			socialRestTemplate.delete(instagramDeleteCommentUrl);
		} catch (RestClientResponseException e) {
			LOGGER.info(
					"RestClientResponseException while deleting instagram comments for url {} is {}",
					instagramDeleteCommentUrl, e.getResponseBodyAsString());
			return false;
		} catch (Exception e) {
			LOGGER.info("Exception while deleting instagram comments for url {}}. Detailed Stacktrace is {} ",
					instagramDeleteCommentUrl, e.getStackTrace());
			return false;
		}
		LOGGER.info("Instagram response for deleteCommentOnAnInstagramPost is {}", true);
		return true;
	}
	
	@Override
	public boolean likePost(String accessToken, String postId) {
		/*
		 * INSTAGRAM LIKE A POST curl -F
		 * 'access_token=6277192457.b53964e.10964875d2a841b4aea7648f0db96c03'
		 * https://api.instagram.com/v1/media/1638321070632369719_6277192457/likes
		 */
		String instagramLikeAPostUrl = InstagramUtils.getInstagramLikeAPostUrl(postId);
		MultiValueMap<String, String> parametersMap = getLikeAPostParameters(accessToken);
		InstagramGenericResponse response = null;
		try {
			LOGGER.info("Inside likePost with url :{} with parametersMap :{} ",instagramLikeAPostUrl,parametersMap);
			response = socialRestTemplate.postForObject(instagramLikeAPostUrl, parametersMap,
					InstagramGenericResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while liking instagram post for url {} parameters {} is {}. Detailed Stacktrace is {} ",
					instagramLikeAPostUrl, parametersMap, e.getResponseBodyAsString(), e.getStackTrace());
			return false;
		} catch (Exception e) {
			LOGGER.error("Exception while liking instagram post for url {} parameters {}. Detailed Stacktrace is {} ",
					instagramLikeAPostUrl, parametersMap, e.getStackTrace());
			return false;
		}
		LOGGER.info("Instagram response for likePost is {}", response);
		return true;
	}
	
	@Override
	public boolean unlikePost(String accessToken, String postId) {
		/*
		 * INSTAGRAM UNLIKE A POST curl -X DELETE
		 * https://api.instagram.com/v1/media/766634044231178278_744851973/likes?
		 * access_token=744851973.b53964e.03f94013776642f3bbfb47e0ff0c63de {"data":
		 * null, "meta": {"code": 200}}
		 */
		String instagramUnikeAPostUrl = InstagramUtils.getInstagramUnlikeAPostUrl(accessToken, postId);
		LOGGER.info("Inside unlikePost with url :{}",instagramUnikeAPostUrl);
		try {
			socialRestTemplate.delete(instagramUnikeAPostUrl);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while unliking instagram post for url {} is {}. Detailed Stacktrace is {} ",
					instagramUnikeAPostUrl, e.getResponseBodyAsString(), e.getStackTrace());
			return false;
		} catch (Exception e) {
			LOGGER.error("Exception while liking uninstagram post for url {}. Detailed Stacktrace is {} ",
					instagramUnikeAPostUrl, e.getStackTrace());
			return false;
		}
		LOGGER.info("Instagram response for likePost is {}", true);
		return true;
	}
	
	private MultiValueMap<String, String> getAccessTokenGenerationParameters(String authCode, String clientId, String clientSecret, String domainUrl) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		map.add(INSTAGRAM_CLIENT_ID_KEY, clientId);
		map.add(INSTAGRAM_CLIENT_SECRET_KEY, clientSecret);
		map.add(INSTAGRAM_REDIRECT_URI_KEY, getInstagramRedirectUri(domainUrl));
		map.add("grant_type", "authorization_code");
		map.add("code", authCode);
		return map;
	}
	
	private MultiValueMap<String, String> getPostCommentParameters(String accessToken){
		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		map.add("access_token", accessToken);
		return map;
	}
	private MultiValueMap<String, String> getPostCommentParametersNew(String accessToken, String comment){

		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		try{
			map.set("message", comment);
			map.set("access_token", accessToken);

		}catch (Exception e){
		}
		return map;

	}

	private MultiValueMap<String, String> getMentionCommentParameters(String accessToken, String postId, String commentId, String comment){

		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		try{
			map.set("media_id", postId);
			if(StringUtils.isNotEmpty(commentId)) {
				map.set("comment_id", commentId);
			}
			map.set("message", comment);
			map.set("access_token", accessToken);

		}catch (Exception e){
			LOGGER.info("Unable to create map. Error ", e );
		}
		return map;

	}


	
	private MultiValueMap<String, String> getLikeAPostParameters(String accessToken){
		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		map.add("access_token", accessToken);
		return map;
	}
	
	private String getInstagramRedirectUri(String domainUrl) {
		 return new StringBuilder().append(INSTAGRAM_REDIRECT_URI_DOMAIN_PREFIX).append(domainUrl)
				.append(INSTAGRAM_REDIRECT_URI_DOMAIN_SUFFIX).toString(); 
		//return "https://birdeye.com/";
	}

	@Override
	public Integer getIgAccPostingLimit(String accessToken, String accountId) {
		Integer remainingLimit = null;
		String instagramGetLocationUrl = InstagramUtils.getInstagramPostingLimitUrl(accessToken, accountId);
		InstagramPostingLimitResponse response = null;
		try {
			LOGGER.info("getIgAccPostingLimit with url :{}",instagramGetLocationUrl);
			response = socialRestTemplate.getForObject(instagramGetLocationUrl, InstagramPostingLimitResponse.class);
			if(Objects.nonNull(response) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(response.getData()) &&
					Objects.nonNull(response.getData().get(0).getConfig())) {
				InstagramPostingLimitData postingLimitData = response.getData().get(0);
				remainingLimit = postingLimitData.getConfig().getQuota_total() - postingLimitData.getQuota_usage();
			}
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting instagram posting limit for request {} is {}. Detailed Stacktrace is {} ",
					instagramGetLocationUrl, e.getResponseBodyAsString(), e.getStackTrace());
		}catch (Exception e) {
			LOGGER.error("Exception while getting instagram posting limit for request {}. Detailed Stacktrace is {} ",
					instagramGetLocationUrl, e.getStackTrace());
		}
		LOGGER.info("Instagram response for getIgAccPostingLimit is {}", response);
		return remainingLimit;
	}

	@Override
	public SocialTimeline getInstagramStories(BusinessInstagramAccount instagramAccount, SocialTimeline inputSocialTimeline) {
		InstagramTimelineResponseList igPostList = new InstagramTimelineResponseList();
		try {
			InstagramTimelineResponseList response = null;
			response = socialProxyHandler.runWithRetryableBirdeyeExceptionWithSingleRetry(
					() -> getInstagramStories(instagramAccount.getPageAccessToken(),
							instagramAccount.getInstagramAccountId(), 100, null)
			);
			if(response!=null && !CollectionUtils.isEmpty(response.getData())) {
				igPostList.setData(response.getData());
			}
			LOGGER.info("Instagram External Response for getInstagramStories is : {}", response);
			List<Feed> storyFeeds = InstagramUtils.getInstagramStories(igPostList, instagramAccount, false);
			if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(storyFeeds)) {
				if(Objects.isNull(inputSocialTimeline)) {
					inputSocialTimeline = new SocialTimeline();
					inputSocialTimeline.setFeeds(storyFeeds);
				} else {
					List<Feed> previousFeeds = org.apache.commons.collections4.CollectionUtils.isNotEmpty(inputSocialTimeline.getFeeds())?
							inputSocialTimeline.getFeeds():new ArrayList<>();
					previousFeeds.addAll(storyFeeds);
					inputSocialTimeline.setFeeds(previousFeeds);
				}
			}
		} catch (BirdeyeSocialException e) {
			LOGGER.info("BirdeyeSocialException occurred while fetching Stories for instagram account {} with error", instagramAccount, e);
		} catch (Exception e) {
			LOGGER.info("Error occurred while fetching Stories for instagram account {} with error", instagramAccount, e);
		}
		return inputSocialTimeline;
	}

	@Override
	public InstagramTimelineResponse getInstagramPostDetails(String pageAccessToken, String postId) {
		String url = StringUtils.join(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21, postId);
		String fields = "media_url,caption,id,like_count,comments_count,media_type,owner,permalink,shortcode,thumbnail_url," +
				"timestamp,username,children{media_url,media_type}";
		URI getPostDetailsUrl = UriComponentsBuilder.fromUriString(url)
				.queryParam("fields", fields)
				.queryParam("access_token", pageAccessToken).build().toUri();

		InstagramTimelineResponse response = null;
		try {
			LOGGER.info("url for getInstagramPostDetails {} ",getPostDetailsUrl);
			response = socialRestTemplate.getForObject(getPostDetailsUrl, InstagramTimelineResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while getting instagram post details for request {} is {}. Detailed Stacktrace is {} ",
					getPostDetailsUrl, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Exception while getting instagram post details for request {}. Detailed Stacktrace is {} ",
					getPostDetailsUrl, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getInstagramPostDetails is {}", response);
		return response;
	}

	@Override
	public InstagramCommentResponse getCommentDetails(String pageAccessToken, String commentId) {
		String url = StringUtils.join(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21, commentId);
		String fields = "parent_id,from,user,text,username,like_count,hidden,media,timestamp";
		URI getCommnetDetailsUri = UriComponentsBuilder.fromUriString(url)
				.queryParam("fields", fields)
				.queryParam("access_token", pageAccessToken).build().toUri();

		InstagramCommentResponse response = null;
		try {
			LOGGER.info("url for getCommentDetails {} ",getCommnetDetailsUri);
			response = socialRestTemplate.getForObject(getCommnetDetailsUri, InstagramCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while getting instagram comment details for request {} is {}. Detailed Stacktrace is {} ",
					getCommnetDetailsUri, e.getResponseBodyAsString(), e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Exception while getting instagram comment details for request {}. Detailed Stacktrace is {} ",
					getCommnetDetailsUri, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getCommentDetails is {}", response);
		return response;
	}

	@Override
	public boolean hideComment(String accessToken, String commentId, boolean hide) {
		String instagramLikeAPostUrl = InstagramUtils.getInstagramHideCommentUrl(commentId, accessToken, hide);
		InstagramGenericResponse response = null;
		try {
			MultiValueMap<String, String> parametersMap = getPostCommentParameters(accessToken);
			LOGGER.info("url for hideComment:{} with parameters :{}",instagramLikeAPostUrl,parametersMap);

			response = socialRestTemplate.postForObject(instagramLikeAPostUrl, parametersMap,
					InstagramGenericResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error(
					"RestClientResponseException while hiding instagram post for url {} is {}. Detailed Stacktrace is {} ",
					instagramLikeAPostUrl, e.getResponseBodyAsString(), e.getStackTrace());
			return false;
		} catch (Exception e) {
			LOGGER.error("Exception while liking instagram post for url {}. Detailed Stacktrace is {} ",
					instagramLikeAPostUrl, e.getStackTrace());
			return false;
		}
		LOGGER.info("Instagram response for likePost is {}", response);
		return true;
	}

	@Override
	public InstagramCompetitorProfile getCompetitorProfileData(String igAccountId, String accessToken, String userName, boolean isMediaReq, String afterToken) {
		String url = InstagramUtils.getInstagramCompProfileInfoUrl(igAccountId);
		String fields = "business_discovery.username(" + userName + "){biography,followers_count,follows_count,media_count,name,profile_picture_url,username,website";
		if(isMediaReq) {
			fields+=",media.limit(50)";
			if(StringUtils.isNotEmpty(afterToken)) {
				fields+=".after("+afterToken+")";
			}
			fields+="{comments_count,like_count,caption,media_type,permalink,timestamp,media_url,children{media_type,media_url}}}";
		} else {
			fields+="}";
		}
		URI competitorProfileDataUri = UriComponentsBuilder.fromUriString(url)
				.queryParam("fields", fields)
				.queryParam("access_token", accessToken).build().toUri();
		InstagramCompetitorProfile response = null;
		LOGGER.info("IG url to get competitor profile info: {}", competitorProfileDataUri);
		try {
			response = socialRestTemplate.getForObject(competitorProfileDataUri, InstagramCompetitorProfile.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("RestClientResponseException while getting competitor data request {} is {}",
					competitorProfileDataUri, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.IG_PUBLIC_PROFILE_ERROR, e.getResponseBodyAsString());
		}catch (Exception e) {
			LOGGER.error("Exception while getting competitor data for request {}. Detailed Stacktrace is {} ",
					competitorProfileDataUri, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.IG_PUBLIC_PROFILE_ERROR, e.getMessage());
		}
		LOGGER.info("Instagram response for competitor data is {}", response);
		return response;
	}

	private Map<String, Object> getExceptionDataMap(FacebookErrorResponse errorResponse) {
		Map<String, Object> map = new HashMap<>();
		if(errorResponse != null){
			Boolean markPageInactive = FacebookUtils.markPageInactive(errorResponse);
			map.put("mark_page_inactive", markPageInactive);
			map.put("errorCode", errorResponse.getCode());
			map.put("errorMessage", errorResponse.getMessage());
			map.put("type", errorResponse.getType());
			map.put("error_user_title", errorResponse.getError_user_title());
			map.put("error_user_msg", errorResponse.getError_user_msg());
			map.put("fbtrace_id", errorResponse.getFbtrace_id());
			map.put("message", errorResponse.getMessage());
		}
		return map;
	}

	private boolean checkIfDeletedCommentException(FacebookBaseResponse errorResponse, String commentId) {
		if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError()))
			return false;

		FacebookErrorResponse error = errorResponse.getError();
		if(Objects.nonNull(error.getCode()) && error.getCode().equals(100)
				&& Objects.nonNull(error.getError_subcode()) && error.getError_subcode().equals(33) &&
				Objects.nonNull(error.getMessage()) &&
				error.getMessage().contains("Unsupported get request. Object with ID '"+commentId+"' does not exist")) {
			return true;
		}

		return false;
	}

	@Override
	public Boolean getCommentDeleteDetails(String pageAccessToken, String commentId) {
		String url = StringUtils.join(FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21, commentId);
		URI getCommnetDetailsUri = UriComponentsBuilder.fromUriString(url)
				.queryParam("access_token", pageAccessToken).build().toUri();

		InstagramCommentResponse response = null;
		try {
			LOGGER.info("url for getCommentDeleteDetails:{}",getCommnetDetailsUri);
			response = socialRestTemplate.getForObject(getCommnetDetailsUri, InstagramCommentResponse.class);
		} catch (RestClientResponseException e) {
			LOGGER.error("HttpStatusCodeException while calling instagram comment API for URL {} :: {}", url, e.getResponseBodyAsString());
			FacebookPostResponse facebookPostResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), FacebookPostResponse.class);
			FacebookErrorResponse errorResponse = facebookPostResponse != null ? facebookPostResponse.getError() : new FacebookErrorResponse();
			Map<String, Object> exceptionDataMap = getExceptionDataMap(errorResponse);
			if(checkIfDeletedCommentException(facebookPostResponse,commentId)) {
				return true;
			}
			LOGGER.info("error map:{}",exceptionDataMap);
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Exception while getting instagram comment details for request {}. Detailed Stacktrace is {} ",
					getCommnetDetailsUri, e.getStackTrace());
			throw new BirdeyeSocialException(ErrorCodes.INSTAGRAM_INVALID_COMMENTS_DATA, e.getMessage());
		}
		LOGGER.info("Instagram response for getCommentDetails is {}", response);
		return false;
	}
}
