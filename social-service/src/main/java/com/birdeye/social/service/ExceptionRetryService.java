package com.birdeye.social.service;

import java.util.List;
import com.birdeye.social.entities.SocialRetryException;
import com.birdeye.social.model.SocialRetryMappingRequest;

public interface ExceptionRetryService {

    List<SocialRetryException> getDataBySourceIdAndSubErrorCodeAndErrorCodeAndErrorMessage(Integer sourceId, Integer subErrorCode,Integer errorCode, String errorMessage);
    List<SocialRetryException> getDataBySourceIdAndErrorCodeAndErrorMessage(Integer sourceId,Integer errorCode, String errorMessage);

    List<SocialRetryException> getDataBySourceIdAndErrorMessage(Integer sourceId, String errorMessage);

    void manualInsertUpdateDeleteRetry(SocialRetryMappingRequest request, String operation);
}
