package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.dto.BusinessBizLiteDto;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.Facebook.PagePostData;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.model.*;
import com.birdeye.social.response.DoupDownloadResponse;
import com.birdeye.social.response.PdfDataDownloadResponse;
import com.birdeye.social.service.SocialReportService.SocialReportService;
import com.birdeye.social.service.SocialTrendsReportService.SocialTrendsReportService;
import com.birdeye.social.trends.TrendsReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.birdeye.social.utils.ConversionUtils.getLeadershipByPostsResponseWrapper;

/**
 * The DoupDownloadServiceImpl fetches data from Social in the format desired by Doup
 *
 * <AUTHOR> Bihani
 */

@Service
@Slf4j
public class DoupDownloadServiceImpl implements DoupDownloadService{

    @Autowired
    private SocialReportService socialReportService;

    @Autowired
    private SocialTrendsReportService socialTrendsReportService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private SocialPostCalendarService socialPostCalendarService;

    @Autowired
    private DoupDownloadHelperService helperService;

    @Override
    public DoupDownloadResponse<? extends PageInsightDataPoint> downloadExcelReportForExcel(InsightsRequest insightsRequest, String channel, String sortBy, String sortOrder) throws Exception {
        PageInsightsResponse pageInsightsResponse = (PageInsightsResponse) socialReportService.getChannelPageInsights(insightsRequest, channel, null, null, null, null);
        log.info("[DoupDownloadServiceImpl] Size of Fetched PageInsightsResponse data points:{}",Objects.nonNull(pageInsightsResponse) && Objects.nonNull(pageInsightsResponse.getDataPoints())?pageInsightsResponse.getDataPoints().size():0);
        DoupDownloadResponse<PageInsightDataPoint> doupDownloadResponse = new DoupDownloadResponse<>(pageInsightsResponse.getDataPoints());
        return doupDownloadResponse;
    }

    @Override
    public PdfDataDownloadResponse<? extends PageInsightsResponse> downloadExcelReportForPdf(InsightsRequest insightsRequest, String channel, String sortBy, String sortOrder, Integer pageSize, Integer startIndex) throws Exception {
        PageInsightsResponse pageInsightsResponse = (PageInsightsResponse) socialReportService.getChannelPageInsights(insightsRequest, channel,null, null, null, null);
                log.info("[DoupDownloadServiceImpl] Size of Fetched PageInsightsResponse data points:{}",pageInsightsResponse.getDataPoints().size());
        PdfDataDownloadResponse<PageInsightsResponse> doupDownloadPdfResponse = new PdfDataDownloadResponse<>();
        doupDownloadPdfResponse.setPdfData(pageInsightsResponse);
        return doupDownloadPdfResponse;
    }

    @Override
    public DoupDownloadResponse<? extends ProfilePerformanceExcelResponse> downloadExcelReportForPerformance(InsightsRequest insightsRequest,
                                                                                                      String sortBy, String sortOrder) throws Exception {
        List<ProfilePerformanceExcelResponse> profilePerformanceExcelResponse = socialReportService.getChannelPageReportForAll(insightsRequest);
        DoupDownloadResponse<ProfilePerformanceExcelResponse> doupDownloadResponse = new DoupDownloadResponse<>(profilePerformanceExcelResponse);
        doupDownloadResponse.setData(profilePerformanceExcelResponse);
        return doupDownloadResponse;
    }

    @Override
    public DoupDownloadResponse<? extends AnalyzeTabDataResponse> downloadExcelAnalyzeData(InsightsRequest insightsRequest, String channel,
                                                                                 String sortBy, String sortOrder, Integer pageSize, Integer startIndex) {
        if(startIndex+pageSize > 10000) {
            //If start-index + page-size is greater than 10000 than return empty response
            return new DoupDownloadResponse<>(Collections.emptyList());
        }
        //If start-index is less than 0 than set it to 0 and than divide it by pageSize to get pageNumber
        Integer pageNumber = (((startIndex < 0) ? 0 : startIndex) > 0) ? (startIndex/pageSize) : startIndex;
        List<PagePostData> pagePostData = Collections.emptyList();
        PostDataAndInsightResponse postDataAndInsightResponse = (PostDataAndInsightResponse) socialReportService.getChannelPostInsights(insightsRequest, channel, pageNumber, pageSize, sortBy, sortOrder, true);
        List<BusinessBizLiteDto> businessBizLiteDtos =  businessCoreService.getBusinessLiteDtoByBusinessIds(insightsRequest.getBusinessIds());
        Map<Integer, BusinessBizLiteDto> businessBizLiteDtoMap = businessBizLiteDtos.stream()
                .collect(Collectors.toMap(BusinessBizLiteDto::getId, business -> business));
        if(Objects.nonNull(postDataAndInsightResponse)) {
            pagePostData = postDataAndInsightResponse.getPagePostData();
        }
        List<AnalyzeTabDataResponse> analyzeTabDataResponses = AnalyzeTabDataResponse.mapToAnalyzeTabDataResponse(pagePostData, channel, businessBizLiteDtoMap);
        return new DoupDownloadResponse<>(analyzeTabDataResponses, prepareMetadata(pagePostData));
    }

    @Override
    public DoupDownloadResponse<? extends ExecutiveSummaryResponse> downloadExcelReportForExecutiveSummary(ExecutiveSummaryRequest executiveSummaryRequest) {
        ExecutiveSummaryResponse response = socialReportService.getExecSummaryForAllChannel(executiveSummaryRequest);
        DoupDownloadResponse<ExecutiveSummaryResponse> doupDownloadResponse = new DoupDownloadResponse<>(Collections.singletonList(response));
        doupDownloadResponse.setData(Collections.singletonList(response));
        return doupDownloadResponse;
    }

    @Override
    public DoupDownloadResponse<? extends LeadershipByPostsResponseWrapper> downloadExcelReportForLocationSummary(
            LocationReportRequest insightsRequest, String sortBy, String sortOrder, Integer pageSize, Integer startIndex) {
        LeadershipByPostsDataPoints leadershipByPostsDataPoints = socialReportService.getPostLeadershipReport(insightsRequest,sortBy,sortOrder,startIndex,pageSize);
        if(Objects.isNull(leadershipByPostsDataPoints)) return new DoupDownloadResponse<>(null);
        List<LeadershipByPostsResponseWrapper> leadershipByPostsResponses = convertToWrapper(leadershipByPostsDataPoints.getDataPoints());
        DoupDownloadResponse<LeadershipByPostsResponseWrapper> doupDownloadResponse = new DoupDownloadResponse<>(leadershipByPostsResponses);
        doupDownloadResponse.setData(leadershipByPostsResponses);
        return doupDownloadResponse;
    }

    private List<LeadershipByPostsResponseWrapper> convertToWrapper(List<LeadershipByPostsResponse> dataPoints) {
        if(CollectionUtils.isEmpty(dataPoints)){
            return new ArrayList<>();
        }
        List<LeadershipByPostsResponseWrapper> wrapperList = new ArrayList<>();
        dataPoints.forEach(data -> wrapperList.add(getLeadershipByPostsResponseWrapper(data)));
        return wrapperList;
    }

    @Override
    public DoupDownloadResponse<? extends Object> downloadExcelReportForSLA(TrendsReportRequest insightsRequest, Integer pageSize, Integer startIndex, String sortBy, String sortOrder) throws Exception {

        List<Object> slaExcelResponse = socialTrendsReportService.getSLAExcelReport(insightsRequest);
        DoupDownloadResponse<Object> doupDownloadResponse = new DoupDownloadResponse<>(slaExcelResponse);
        doupDownloadResponse.setData(slaExcelResponse);
        return doupDownloadResponse;
    }

    public Map<String, String> prepareMetadata(List<PagePostData> pagePostDataList) {
        int totalEngagements = 0;
        int totalImpressions = 0;
        Set<Integer> uniquePublisherIds = new HashSet<>();

        for (PagePostData postData : pagePostDataList) {
            totalEngagements += postData.getEngagement();
            totalImpressions += postData.getImpression();

            Integer publisherId = postData.getPublisherId();
            if (Objects.nonNull(publisherId)) {
                uniquePublisherIds.add(publisherId);
            }
        }

        Map<String, String> metadata = new HashMap<>();
        metadata.put("totalPosts", String.valueOf(pagePostDataList.size()));
        metadata.put("totalEngagements", String.valueOf(totalEngagements));
        metadata.put("totalImpressions", String.valueOf(totalImpressions));
        metadata.put("numberOfUserPosted", String.valueOf(uniquePublisherIds.size()));
        return metadata;
    }

    //Tiktok
    @Override
    public List<ProfilePerformanceExcelResponse> downloadExcelReportForSpecificTab(InsightsRequest insightsRequest, String channel) throws Exception {
        return  socialReportService.downloadExcelReportForSpecificTab(insightsRequest, channel);
    }

    @Override
    public Map<String, Object> downloadPdfCalendarReport(CalendarPostsExportRequest exportFilter, Integer userId, String timeZone,
                                                                 Integer startIndex,String sortOrder,Integer pageSize, Integer accountId) {
        GlobalFilterCriteriaSchedulePostMessage calendarFilter = helperService.convertToGlobalFilterCriteria(exportFilter, startIndex, sortOrder, pageSize,  accountId);
        log.info("[DoupDownloadServiceImpl] filter: {}", calendarFilter);
        Map<String, Object> responseMap = new HashMap<>();
        CalendarPostsExportResponse calendarPostsExport = new CalendarPostsExportResponse();
        responseMap.put("pdfData", calendarPostsExport);
        try {
            CalendarExportPostPageStatusData postPageData = new CalendarExportPostPageStatusData();
            postPageData.setPageData(new HashMap<>());
            SocialSchedulePostResponse calendarPosts = socialPostCalendarService.getAllESScheduledPostsV2(calendarFilter,
                    userId, true, null, timeZone, postPageData, true);
            if(Objects.isNull(calendarPosts)) {
                log.info("[DoupDownloadServiceImpl] No data found for the given filter: {}", calendarFilter);
                return Collections.emptyMap();
            }
            calendarPostsExport.setAiPosts(calendarPosts.getAiPosts());

            CompletableFuture<Map<String, String>> locIdVsNameFuture = CompletableFuture.supplyAsync(() ->
                    helperService.getLocationIdVsLocationNameFromCore(calendarFilter.getAccessibleLocationIds()));

            CompletableFuture<Map<Integer, List<CalendarViewPagePostInsightsData>>> insightsDataFuture = CompletableFuture.supplyAsync(() ->
                    helperService.getBulkPostPageInsightsDataFromEs(postPageData));

            CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(locIdVsNameFuture, insightsDataFuture);
            allCompletableFuture.get(20, TimeUnit.SECONDS);

            Map<String, String> locIdVsName = locIdVsNameFuture.get();
            Map<Integer, List<CalendarViewPagePostInsightsData>> insightsData = insightsDataFuture.get();

            calendarPostsExport.setPosts(helperService.prepareCalendarPostsData(calendarPosts.getPosts()));

            helperService.preparePostPageAndInsightsData(calendarPostsExport, postPageData, locIdVsName, insightsData);

            calendarPostsExport.setFiltersSelected(helperService.getFiltersSelectedData(exportFilter, locIdVsName, exportFilter.getUserTimezoneId()));

            log.info("[DoupDownloadServiceImpl] Successfully fetched calendar posts export response");

            return responseMap;

        }catch (Exception e) {
            log.error("Error while fetching calendar posts export response: {}", e.getMessage(), e);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "Error while fetching calendar posts export response: " + e.getMessage());
        }
    }

}