/**
 * 
 */
package com.birdeye.social.service;

import java.util.Map;
import java.util.Set;

import com.birdeye.social.model.CheckIntegrationRequest.CheckIntegrationInputDTO;
import com.birdeye.social.sro.BusinessAggregationInfo;

/**
 * <AUTHOR>
 *
 */
public interface IBusinessAggregationCheckService {

	/**
	 * @param channel
	 * @param integrationId
	 * @param businessId 
	 * @param businessAggregationId 
	 * @return 
	 */
	BusinessAggregationInfo checkBusinessAggregation(String channel, String integrationId, Integer businessId, Integer businessAggregationId);

	/**
	 * Return a Map of <br><br>
	 * Key = Business Aggregation Id<br>
	 * Value = true (if valid integration exist) , false (if valid integration is not present)
	 * @param input
	 */
	Map<Integer, String> checkIntegration(Set<CheckIntegrationInputDTO> input);
	
}
