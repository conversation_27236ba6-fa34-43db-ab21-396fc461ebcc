package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialChannelPermissionRepository;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dto.report.MonthlyScanDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("FacebookAdminServiceImpl")
public class FacebookAdminServiceImpl implements ChannelsAdminService {

	private static final String FB_INSIGHTS_PERMISSION = "insightsApiPermission";

	private static final String MYSQL_DATE_FORMAT = "yyyy/MM/dd HH:mm:ss";
	SimpleDateFormat simpleDateFormat = new SimpleDateFormat(MYSQL_DATE_FORMAT);
	private static final long MILLIS_IN_A_DAY = 1000 * 60 * 60 * 24;


	@Autowired
	private SocialFBPageRepository socialFbRepo;

	@Autowired
	private SocialChannelPermissionRepository socialChannelPermissionRepository;

	private final static Logger log = LoggerFactory.getLogger(FacebookAdminServiceImpl.class);

	@Override
	public SocialChannel channelName() {
		return SocialChannel.FACEBOOK;
	}

	@Override
	public List<MonthlyScanDTO> fetchEligibleRecordsMonthly() throws ParseException {
		return null;
	}

	@Override
	public SocialScanEventDTO fetchChannelDetails(String channelId, Date date) throws ParseException {
		return null;
	}


	@Override
	public List<SocialScanEventDTO> fetchEligibleRecords(Integer count, Integer startId, Date date) throws ParseException {
		// @saurabh -fixed use slave db here & check for indexing
		// index is added
		Page<BusinessFBPage> pages = socialFbRepo.findByIsValidAndBusinessIdNotNullAndNextSyncDateIsLessThanEqualOrderByNextSyncDateAsc(1, date, new PageRequest(startId, count));
		log.info("Pages fetched to scan {}", pages);
		//remove pages with no insights permission from list
		// TODO @saurabh -answered verify that permission tab is always updated
		// this is dependent on page validation check job, if page is invalid, it will not be picked. for permission validation this can only be via graph API response.
		// TODO @saurabh -fixed read  FB_INSIGHTS_PERMISSION from database which khyati is working on <module to permission mapping>
		// fixed
		Date nextDate = simpleDateFormat.parse(simpleDateFormat.format(nextScanDate(date)));
		if(CollectionUtils.isNotEmpty(pages.getContent())) {
			List<Integer> eligibleIds = pages.getContent().stream().map(BusinessFBPage::getId).collect(Collectors.toList());
			updateNextSyncDate(eligibleIds, nextDate);
		}
//		SocialChannelPermission socialChannelPermission = socialChannelPermissionRepository.findByPermission(FB_INSIGHTS_PERMISSION); // TODO @saurabh this should be cached
		return conversionToScanEventDTO(getEligiblePageIds(pages.getContent()));
	}

	private static Date nextScanDate(Date date){
		return new Date(date.getTime() + MILLIS_IN_A_DAY);
	}

	private List<SocialScanEventDTO> conversionToScanEventDTO(List<BusinessFBPage> fbPages) {
		List<SocialScanEventDTO> scanEventList = new ArrayList<>();

		for(BusinessFBPage fbPage : fbPages) {
			SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
			scanEventDTO.setChannelPrimaryId(fbPage.getId());
			scanEventDTO.setBusinessId(fbPage.getBusinessId());
			scanEventDTO.setEnterpriseId(fbPage.getEnterpriseId());
			scanEventDTO.setExternalId(fbPage.getFacebookPageId());
			scanEventDTO.setPageName(fbPage.getFacebookPageName());
			scanEventDTO.setSourceName(SocialChannel.FACEBOOK.getName());
			scanEventDTO.setSourceId(SocialChannel.FACEBOOK.getId());
			scanEventDTO.setAccountId(fbPage.getAccountId());
			scanEventList.add(scanEventDTO);
		}
		log.info("Scan event dto ready for {}", scanEventList);
		return scanEventList;
	}

	private boolean permissionCheck(String permission) {
		boolean isValid = true;
		if(Objects.isNull(permission)) {
			return false;
		}
		List<String> requiredPermissionList = StringUtils.convertToList("read_insights");

		for(String requiredPermission:  requiredPermissionList) {
			// TODO: 17/11/22 @Saurabh  java.lang.NullPointerException: null
			//	at com.birdeye.social.service.FacebookAdminServiceImpl.permissionCheck(FacebookAdminServiceImpl.java:85)
			if (!permission.contains(requiredPermission)) {
				isValid = false;
				break;
			}
		}

		return isValid;
	}

	private List<BusinessFBPage> getEligiblePageIds(List<BusinessFBPage> pages) {
		// removed pages without insight permissions

		// TODO add more checks : can move this to common layer
		return pages.stream()
				.filter(page->  permissionCheck(page.getPagePermissions()))
				.collect(Collectors.toList());
	}

	@Override
	public void updateNextSyncDate(List<Integer> eligibleIds, Date nextDate) {
		socialFbRepo.updateNextSyncDate(nextDate, eligibleIds);
	}



}
