package com.birdeye.social.service.SocialReportService.GMB;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.constant.MetricEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.reports.GMBAnalyticsRepository;
import com.birdeye.social.dao.reports.GMBKeywordSyncRepository;
import com.birdeye.social.dao.reports.GMBPageAnalyticsFeedRepository;
import com.birdeye.social.dao.reports.GMBPageInsightRepo;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.GMBAnalytics;
import com.birdeye.social.entities.GMBKeywordSync;
import com.birdeye.social.entities.report.GMBPageInsight;
import com.birdeye.social.entities.report.GMBPageReportAnalyticsFeed;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.dao.reports.GMBPageAnalyticsFeedRepository;
import com.birdeye.social.dao.reports.GMBPageInsightRepo;
import com.birdeye.social.dto.ESGMBPageAnalyticsDTO;
import com.birdeye.social.dto.GMBAnalyticsDates;
import com.birdeye.social.dto.GMBAnayticsdatedValues;
import com.birdeye.social.dto.GMBPageAnalyticsRequestDTO;
import com.birdeye.social.dto.GMBReportAnalyticsResponse;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.ES.Request.DataModel;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PageLevelMetaData;
import com.birdeye.social.model.ESGMBAnalyticsKafkaRequest;
import com.birdeye.social.model.GMBReportInsightsRequest;
import com.birdeye.social.model.GMBReportInsightsResponse;
import com.birdeye.social.service.GMBLocationDetailService;
import com.birdeye.social.service.GMBPostService;
import com.birdeye.social.service.GoogleAccessTokenCache;
import com.birdeye.social.service.SocialReportService.Converter.DbDataConverter;
import com.birdeye.social.service.SocialReportService.Converter.ReportDataConverter;
import com.birdeye.social.service.SocialReportService.ES.ReportsEsService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.birdeye.social.utils.TimeZoneUtil;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigInteger;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("GmbReportServiceImpl")
public class GmbReportServiceImpl implements GmbReportService {

	private static final String GMB_PAGE_INSIGHTS = "gmb-page-insights";

	private static final String GMB_ANALYTICS_ES_SAVE = "gmb-analytics-es-save";

	@Autowired
	GMBPageInsightRepo gmbPageInsightRepo;

	@Autowired
	GMBPageAnalyticsFeedRepository analyticsFeedRepository;

	@Autowired
	GMBAnalyticsRepository analyticsRepository;

	@Autowired
	private BusinessGMBLocationRawRepository gmbRepo;

	@Autowired
	ReportDataConverter reportDataConverter;

	@Autowired
	private GMBLocationDetailService gmbLocationService;

	@Autowired
	private GMBPostService gmbPostService;

	@Autowired
	KafkaProducerService kafkaProducerService;

	@Autowired
	DbDataConverter dbDataConverter;

	@Autowired
	private ReportsEsService reportsEsService;

	@Autowired
	private GoogleAccessTokenCache gAccessToken;

	@Autowired
	private SocialProxyHandler socialProxyHandler;

	@Autowired
	private GMBKeywordSyncRepository gmbKeywordSyncRepository;

	private final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";

	private final String lastSyncGeneric = "1975-01-01 00:00:00.0";

	private static final Logger logger = LoggerFactory.getLogger(GmbReportServiceImpl.class);

	@Override
	public void getPageInsightsFromGMB(SocialScanEventDTO getPageInsights) {
		Date startDate, endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
		List<GMBPageInsight> businessPosts = gmbPageInsightRepo
				.findByLocationIdOrderByIdDesc(getPageInsights.getExternalId());
		if (CollectionUtils.isEmpty(businessPosts)) {
			startDate = DateUtils.addDays(endDate, -365);
		} else {
			GMBPageInsight facebookPageInsight = businessPosts.get(0);
			startDate = facebookPageInsight.getLastSyncDate();
		}
		BusinessGoogleMyBusinessLocation gmbPage = gmbRepo.findById(getPageInsights.getChannelPrimaryId());
		if (Objects.isNull(gmbPage) || startDate.after(endDate)) {
			return;
		}
		GMBReportInsightsRequest gmbInsightRequest;
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		gmbInsightRequest = reportDataConverter.createGmbLocationRequest(gmbPage.getLocationUrl(), df.format(startDate),
				df.format(endDate));

		DataModel dataModel = DataModel.builder().pageIds(gmbPage.getLocationId())
				.sourceIds(Integer.toString(SocialChannel.GMB.getId())).build();
		InsightsESRequest insightsESRequest = new InsightsESRequest(dataModel, SearchTemplate.POST_INSIGHT_POST_COUNT);
		insightsESRequest.setRouting(gmbPage.getEnterpriseId().toString());

//        SimpleDateFormat dateFormatter = new SimpleDateFormat(dateFormatterString);
//        Map<Date, Integer> postCountMap = reportsEsService.getPostCountForDates(insightsESRequest, dateFormatter.format(startDate), dateFormatter.format(endDate));

		pushDataToKafkaForEsUpdate(gmbPage, gmbInsightRequest);
	}

	@Override
	public void addInsightsToDB(PageInsights pageInsights) {
		GMBPageInsight gmbPageInsight = dbDataConverter.convertPageInsightForGmb(pageInsights);
		gmbPageInsightRepo.save(gmbPageInsight);
	}

	private void pushDataToKafkaForEsUpdate(BusinessGoogleMyBusinessLocation gmbPage,
			GMBReportInsightsRequest gmbReportInsightsRequest) {
		GMBReportInsightsResponse gmbReportInsightsResponse = socialProxyHandler
				.runWithRetryableBirdeyeExceptionWithSingleRetry(() -> gmbLocationService
						.getBusinessReportInsights(gmbPage.getBusinessId(), gmbReportInsightsRequest));
		List<PageLevelMetaData> pageLevelMetaDataList = reportDataConverter
				.prepareDayWiseDataForGmb(gmbReportInsightsResponse);

//        PageInsights pageInsightsPostCountUpdate = new PageInsights(gmbPage.getEnterpriseId(),gmbPage.getLocationId(),
//                gmbPage.getBusinessId(), SocialChannel.GMB.getName(), reportsEsService.mergeAndGetUpdatePageInsightList(pageLevelMetaDataList, postCountMap));
		PageInsights pageInsights = new PageInsights(gmbPage.getEnterpriseId(), gmbPage.getLocationId(),
				gmbPage.getBusinessId(), SocialChannel.GMB.getName(), pageLevelMetaDataList);
		kafkaProducerService.sendObjectV1(GMB_PAGE_INSIGHTS, pageInsights);
//        kafkaProducerService.sendObjectV1(Constants.PAGE_INSIGHTS_POSTCOUNT_UPDATE, pageInsightsPostCountUpdate);

		addInsightsToDB(pageInsights);
	}


	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId)throws Exception {
		Date dirReqstartDate = null, deskMapstartDate = null, mobMapstartDate = null, deskSearchstartDate = null,
				mobSearchstartDate = null, webClicksstartDate = null, callClickstartDate = null,
				endDate = Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
		List<GMBPageReportAnalyticsFeed> gmbPageAnalytics = analyticsFeedRepository.findByPageIdAndBusinessId(pageId,businessId);
		logger.info("GMBPageReportAnalyticsFeed : {} ", gmbPageAnalytics);
		// If not exists create with Default Entries.
		if (CollectionUtils.isEmpty(gmbPageAnalytics)) {
			try {
				gmbPageAnalytics = createDefaultGMBPageReportAnalytics(pageId, businessId);
			} catch (ParseException e) {
				logger.info("[GmbPageAnalytics is Null]");
				throw new BirdeyeSocialException("[GmbPageAnalytics is Null]");
			}
		}
		GMBAnalyticsDates lastSyncDate = createLastSyncDate(gmbPageAnalytics);
		logger.info("lastSyncDates  : {} ", lastSyncDate.toString());
		// if analytics entry not present we will fetch last 15 months insight from
		// Google.
		if (Objects.nonNull(gmbPageAnalytics)) {
			dirReqstartDate = lastSyncDate.getDirReqDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getDirReqDate(), -3);
			deskMapstartDate = lastSyncDate.getDeskMapsDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getDeskMapsDate(), -3);
			mobMapstartDate = lastSyncDate.getMobMapsDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getMobMapsDate(), -3);
			deskSearchstartDate = lastSyncDate.getDeskSearchDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getDeskSearchDate(), -3);
			mobSearchstartDate = lastSyncDate.getMobSearchDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getMobSearchDate(), -3);
			webClicksstartDate = lastSyncDate.getWebClicksDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getWebClicksDate(), -3);
			callClickstartDate = lastSyncDate.getCallClickDate().toString().equals(lastSyncGeneric)
					? DateUtils.addDays(endDate, -450)
					: DateUtils.addDays(lastSyncDate.getCallClickDate(), -3);
		}
		GMBAnalyticsDates analyticsStartDates = new GMBAnalyticsDates(deskMapstartDate, mobMapstartDate,
				deskSearchstartDate, mobSearchstartDate, webClicksstartDate, callClickstartDate, dirReqstartDate);
		logger.info("analyticsStartDates  : {} ", analyticsStartDates.toString());

		BusinessGoogleMyBusinessLocation gmbPage = gmbRepo.findByLocationIdAndBusinessIdAndIsValid(businessId, pageId).get(0);

		//comment to run on loccal
		if (Objects.isNull(gmbPage) || dirReqstartDate.after(endDate) || deskMapstartDate.after(endDate)
				|| mobMapstartDate.after(endDate) || deskSearchstartDate.after(endDate) || mobSearchstartDate.after(endDate)
				|| webClicksstartDate.after(endDate) || callClickstartDate.after(endDate)) {
			logger.info("date is after start date or gmbPage is null");
			throw new BirdeyeSocialException("[Start Date is after end date or gmbPage is null]");
		}
//		logger.info("gmbPage  : {} ", gmbPage.toString());
		// remove uncomment to run on local
//		gmbPage = new BusinessGoogleMyBusinessLocation();
//		gmbPage.setBusinessId(100055774);
//		gmbPage.setLocationId("17576097117847515997");
		// remove
		GmbPageAnalyticsEvent gmbPageAnalyticsEvent = GmbPageAnalyticsEvent.builder()
						.gmbPageId(gmbPage.getId())
						.startDates(analyticsStartDates)
						.endDate(endDate)
						.build();

		kafkaProducerService.sendObjectV1(KafkaTopicEnum.GMB_FETCH_PAGE_ANALYTICS.getName(), gmbPageAnalyticsEvent);
	}

	@Override
	public void fetchAndSaveGmbPageAnalytics(GmbPageAnalyticsEvent pageAnalyticsEvent) {
		BusinessGoogleMyBusinessLocation gmbLocation = gmbRepo.findById(pageAnalyticsEvent.getGmbPageId());
		if(Objects.isNull(gmbLocation)) {
			logger.info("gmb page with id: {} does not exists",pageAnalyticsEvent.getGmbPageId());
			return;
		}
		createGMBAnalyticsData(gmbLocation, pageAnalyticsEvent.getStartDates(), pageAnalyticsEvent.getEndDate());
	}

	@Override
	public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
		LocalDate date = LocalDate.now();
		GMBKeywordSync gmbKeywordSync = gmbKeywordSyncRepository.findByBusinessId(businessId);
		LocalDate nextSyncDate = null;
		if(Objects.isNull(gmbKeywordSync) || Objects.isNull(gmbKeywordSync.getNextSyncDate())) {
			nextSyncDate = LocalDate.parse(getStartingSyncDate());
		} else {
			nextSyncDate = gmbKeywordSync.getNextSyncDate().toInstant().atZone(ZoneId.systemDefault())
					.toLocalDate();
		}
		LocalDate syncDate = nextSyncDate.withDayOfMonth(getKeywordJobDay());
		if(date.isBefore(syncDate)) {
			return;
		}

		BusinessGoogleMyBusinessLocation gmbLocation = gmbRepo.findByBusinessIdAndIsValid(businessId, 1);
		if (Objects.isNull(gmbLocation)) {
			logger.error(
					"Error while fetching location report insights as gmb location not found/invalid for business id {}",
					businessId);
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND,
					"GMB location isn't integrated or inactive");
		}
		String pageId = gmbLocation.getLocationId();
		if(Objects.isNull(pageId)) {
			logger.info("page id is null for business_id:{} ", businessId);
			return ;
		}
		String locationName = gmbLocation.getLocationUrl();
		if (StringUtils.isEmpty(locationName)) {
			logger.error("getBusinessReportInsights Location name is null or empty for businessId {} ", businessId);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GET_LOCATION_NAME,
					"LocationName is null or empty for this businessId");
		}
		String accessToken = gAccessToken.getGoogleAccessToken(gmbLocation);

		if (accessToken == null) {
			logger.info("access token for business_id: {} is null", businessId);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB,
					"Exception occured while generating accessToken on gmb");
		}
		LocalDate updatedNextSyncDate = gmbLocationService.getGMBKeywordAnalytics(gmbLocation, date, syncDate,accessToken);
		updateLastSyncDate(gmbKeywordSync,businessId, updatedNextSyncDate);
 	}


	private void updateLastSyncDate(GMBKeywordSync gmbKeywordSync, Integer businessId, LocalDate date) {
		if(Objects.isNull(gmbKeywordSync)) {
			gmbKeywordSync = GMBKeywordSync.builder()
					.businessId(businessId)
					.nextSyncDate(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()))
					.created(new Date())
					.updated(new Date())
					.build();
		} else {
			gmbKeywordSync.setNextSyncDate(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
			gmbKeywordSync.setUpdated(new Date());
		}
		gmbKeywordSyncRepository.saveAndFlush(gmbKeywordSync);
	}


	private List<GMBPageReportAnalyticsFeed> createDefaultGMBPageReportAnalytics(String pageId, Integer businessId)
			throws ParseException {
		List<String> metrices = reportDataConverter.getAnalyticsMetricList();
		Date lastSyncDate = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss").parse("01/01/1975 00:00:00");
		metrices.forEach(metric -> {
			GMBPageReportAnalyticsFeed analytics = new GMBPageReportAnalyticsFeed(businessId, pageId, metric,
					lastSyncDate);
			logger.info("createDefaultGMBPageReportAnalytics with payload : {} ", analytics.toString());
			analyticsFeedRepository.save(analytics);
		});
		List<GMBPageReportAnalyticsFeed> gmbPageAnalytics = analyticsFeedRepository.findByPageIdAndBusinessId(pageId,businessId);
		return gmbPageAnalytics;
	}

	private GMBAnalyticsDates createLastSyncDate(List<GMBPageReportAnalyticsFeed> gmbPageAnalytics) {
		GMBAnalyticsDates gmbAnalyticsDates = new GMBAnalyticsDates();
		gmbPageAnalytics.forEach(obj -> {
			if (MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setDeskMapsDate(obj.getLastSyncDate());
			}
			if (MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setMobMapsDate(obj.getLastSyncDate());
			}
			if (MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setDeskSearchDate(obj.getLastSyncDate());
			}
			if (MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setMobSearchDate(obj.getLastSyncDate());
			}
			if (MetricEnum.BUSINESS_DIRECTION_REQUESTS.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setDirReqDate(obj.getLastSyncDate());
			}
			if (MetricEnum.WEBSITE_CLICKS.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setWebClicksDate(obj.getLastSyncDate());
			}
			if (MetricEnum.CALL_CLICKS.name().equals(obj.getMetric())) {
				gmbAnalyticsDates.setCallClickDate(obj.getLastSyncDate());
			}
		});
		return gmbAnalyticsDates;
	}

	private GMBReportAnalyticsResponse createGMBAnalyticsData(BusinessGoogleMyBusinessLocation gmbPage,
			GMBAnalyticsDates startDates, Date endDate) {
		Calendar endCal = Calendar.getInstance();
		endCal.setTime(endDate);
		logger.info("createGMBAnalyticsData  : endCal {} ", endCal.toString());

		List<GMBPageAnalyticsRequestDTO> gmbPageAnalyticsRequestDTOs = createAnalyticsRequestDTO(startDates, endCal,gmbPage);
		logger.info("gmbPageAnalyticsRequestDTOs  : {} ", gmbPageAnalyticsRequestDTOs.toString());
		try {
			executeFuturesforMetrices(gmbPageAnalyticsRequestDTOs, gmbPage, startDates, endDate);
		} catch (Exception e) {
			logger.info("Exception Occured]  : {} ", e.getMessage());
			throw new BirdeyeSocialException("[Exception Occured]",e.getCause());
		}
		return null;
	}

	private void executeFuturesforMetrices(List<GMBPageAnalyticsRequestDTO> gmbPageAnalyticsRequestDTOs,
			BusinessGoogleMyBusinessLocation gmbPage, GMBAnalyticsDates startDates, Date endDate) {
		try {
			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			Map<String, String> exceptionMetricsMap = new HashMap<>();
			List<String> completedMetrics = new ArrayList<>();
			String accessToken=gmbLocationService.generateAccessToken(gmbPage,gmbPage.getBusinessId());
			logger.info("Executing gmb analytics fetch......."); //print dates
			logger.info("startDates  : startDates {} ", startDates.toString());

			Date dirReqendDate = endDate, deskMapsendDate = endDate, mobMapendDate = endDate,
					deskSearchendDate = endDate, mobSearchendDate = endDate, webClickendDate = endDate,
					callClickendDate = endDate;


			logger.info("dirReqendDate  :  {} ", dirReqendDate.toString());
			logger.info("deskMapsendDate  :  {} ", deskMapsendDate.toString());
			logger.info("mobMapendDate  :  {} ", mobMapendDate.toString());
			logger.info("deskSearchendDate  :  {} ", deskSearchendDate.toString());
			logger.info("mobSearchendDate  :  {} ", mobSearchendDate.toString());
			logger.info("webClickendDate  :  {} ", webClickendDate.toString());
			logger.info("callClickendDate  :  {} ", callClickendDate.toString());

			Map<String, GMBReportAnalyticsResponse> mapGMBReportAnalyticsResponse = new HashMap<String, GMBReportAnalyticsResponse>();

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(
						gmbPageAnalyticsRequestDTOs.get(0), gmbPage.getBusinessId(),
						df.format(startDates.getDeskMapsDate()), df.format(deskMapsendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.businessImpDeskMaps, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name());
			} finally {
				completedMetrics.add(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(1),
						gmbPage.getBusinessId(), df.format(startDates.getMobMapsDate()), df.format(mobMapendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.businessImpMobMaps, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name());
			} finally {
				completedMetrics.add(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(2),
						gmbPage.getBusinessId(), df.format(startDates.getDeskSearchDate()), df.format(deskSearchendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.businessImpDeskSearch, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name());
			} finally {
				completedMetrics.add(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(3),
						gmbPage.getBusinessId(), df.format(startDates.getMobSearchDate()), df.format(mobSearchendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.businessImpMobSearch, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name());
			} finally {
				completedMetrics.add(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.WEBSITE_CLICKS.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(4),
						gmbPage.getBusinessId(), df.format(startDates.getWebClicksDate()), df.format(webClickendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.webClicks, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.WEBSITE_CLICKS.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.WEBSITE_CLICKS.name());
			} finally {
				completedMetrics.add(MetricEnum.WEBSITE_CLICKS.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.CALL_CLICKS.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(5),
						gmbPage.getBusinessId(), df.format(startDates.getCallClickDate()), df.format(callClickendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.callClicks, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.CALL_CLICKS.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.CALL_CLICKS.name());
			} finally {
				completedMetrics.add(MetricEnum.CALL_CLICKS.name());
			}

			try {
				logger.info("[Excecuting analytics fetch for]  metric: {} ", MetricEnum.BUSINESS_DIRECTION_REQUESTS.name());
				GMBReportAnalyticsResponse analyticsResponse = gmbLocationService.getGMBPageAnalytics(gmbPageAnalyticsRequestDTOs.get(6),
						gmbPage.getBusinessId(), df.format(startDates.getDirReqDate()), df.format(dirReqendDate),accessToken);
				mapGMBReportAnalyticsResponse.put(Constants.businessDirReq, analyticsResponse);
			} catch (Exception e) {
				exceptionMetricsMap.put(MetricEnum.BUSINESS_DIRECTION_REQUESTS.name(),
						e.getMessage());
				logger.info("[Exception Occured ] : {} for  metric: {} ", e.getMessage(),
						MetricEnum.BUSINESS_DIRECTION_REQUESTS.name());
			} finally {
				completedMetrics.add(MetricEnum.BUSINESS_DIRECTION_REQUESTS.name());
			}

			saveAndPrepareResponseKafka(mapGMBReportAnalyticsResponse, gmbPage,
					completedMetrics, exceptionMetricsMap);
		} catch (Exception e) {
			logger.info("Exception Occured]  : {} ", e.getMessage());
			throw new BirdeyeSocialException("[Exception Occured]", e.getCause());
		}
	}

	private void saveAndPrepareResponseKafka(
			Map<String, GMBReportAnalyticsResponse> mapGMBReportAnalyticsResponse,
			BusinessGoogleMyBusinessLocation gmbPage, List<String> completedMetrics, Map<String, String> exceptionMetricsMap) {
		try {

			// prepareResponseforNifi
			Map<String, ESGMBPageAnalyticsDTO> analyticsESResponse = prepareAnalyticsResponseforES(
					mapGMBReportAnalyticsResponse,gmbPage.getLocationId().toString(),gmbPage.getBusinessId());
			List<ESGMBPageAnalyticsDTO> analyticsESdatalist=analyticsESResponse.values().stream().collect(Collectors.toList());
			if (analyticsESResponse != null) {
				Date lastSyncDate = Date
						.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
				GMBAnalytics gmbAnalytics = new GMBAnalytics(gmbPage.getBusinessId(), gmbPage.getLocationId(),
						new ObjectMapper().writeValueAsString(analyticsESdatalist), lastSyncDate);
//				logger.info("save gmbAnalytics  : {} ", gmbAnalytics.toString());
				analyticsRepository.save(gmbAnalytics);
			}

			if (!exceptionMetricsMap.isEmpty()) {
				saveGMBPageAnalyticsErrorMap(exceptionMetricsMap, gmbPage.getBusinessId());
			}
			if (CollectionUtils.isNotEmpty(completedMetrics)) {
				saveGMBPageAnalyticslastSync(completedMetrics, gmbPage.getBusinessId(),analyticsESdatalist);
			}

			ESGMBAnalyticsKafkaRequest esgmbAnalyticsKafkaRequest = new ESGMBAnalyticsKafkaRequest(
					gmbPage.getBusinessId(), analyticsESdatalist);

			//Push to Kafka
			kafkaProducerService.sendObjectV1(GMB_ANALYTICS_ES_SAVE, esgmbAnalyticsKafkaRequest);
		}catch(Exception e) {
			logger.info("Exception Occured]  : {} ", e.getMessage());
			throw new BirdeyeSocialException("[Exception Occured]",e.getCause());
		}
	}

	private Map<String, ESGMBPageAnalyticsDTO> prepareAnalyticsResponseforES(
			Map<String, GMBReportAnalyticsResponse> mapGMBReportAnalyticsResponse,String pageId,Integer businessId) {
		Map<String, ESGMBPageAnalyticsDTO> analyticsESResponse = new HashMap<>();
		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.businessImpDeskMaps))) {
			List<GMBAnayticsdatedValues> businessImpDeskMapsdatedValues = mapGMBReportAnalyticsResponse
					.get(Constants.businessImpDeskMaps).getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(businessImpDeskMapsdatedValues))
				iterateAnalyticsReponse(analyticsESResponse, businessImpDeskMapsdatedValues, Constants.desk_map_vu_cnt,pageId,businessId);
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.businessImpMobMaps))) {
			List<GMBAnayticsdatedValues> businessImpMobMapsdatedValues = mapGMBReportAnalyticsResponse
					.get(Constants.businessImpMobMaps).getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(businessImpMobMapsdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, businessImpMobMapsdatedValues, Constants.mob_map_vu_cnt,pageId,businessId);
			}
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.businessImpDeskSearch))) {
			List<GMBAnayticsdatedValues> businessImpDeskSearchdatedValues = mapGMBReportAnalyticsResponse
					.get(Constants.businessImpDeskSearch).getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(businessImpDeskSearchdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, businessImpDeskSearchdatedValues, Constants.desk_srch_vu_cnt,pageId,businessId);
			}
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.businessImpMobSearch))) {
			List<GMBAnayticsdatedValues> businessImpMobSearchdatedValues = mapGMBReportAnalyticsResponse
					.get(Constants.businessImpMobSearch).getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(businessImpMobSearchdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, businessImpMobSearchdatedValues, Constants.mob_srch_vu_cnt,pageId,businessId);
			}
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.webClicks))) {
			List<GMBAnayticsdatedValues> webClicksdatedValues = mapGMBReportAnalyticsResponse.get(Constants.webClicks)
					.getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(webClicksdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, webClicksdatedValues, Constants.wsite_vst_cnt,pageId,businessId);
			}
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.callClicks))) {
			List<GMBAnayticsdatedValues> callClicksdatedValues = mapGMBReportAnalyticsResponse.get(Constants.callClicks)
					.getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(callClicksdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, callClicksdatedValues, Constants.cal_cnt,pageId,businessId);
			}
		}

		if (Objects.nonNull(mapGMBReportAnalyticsResponse.get(Constants.businessDirReq))) {
			List<GMBAnayticsdatedValues> businessDirReqdatedValues = mapGMBReportAnalyticsResponse.get(Constants.businessDirReq)
					.getTimeSeries().getDatedValues();
			if (CollectionUtils.isNotEmpty(businessDirReqdatedValues)) {
				iterateAnalyticsReponse(analyticsESResponse, businessDirReqdatedValues, Constants.dir_vst_cnt,pageId,businessId);
			}
		}
		return analyticsESResponse;
	}

	private void iterateAnalyticsReponse(Map<String, ESGMBPageAnalyticsDTO> analyticsESResponse,
			List<GMBAnayticsdatedValues> datedValuesList, String updateKey, String pageId, Integer businessId) {
		datedValuesList.forEach(valueObj -> {
			com.birdeye.social.external.request.google.Date date = valueObj.getDate();
			String dateString = date.getDay() + "/" + date.getMonth() + "/" + date.getYear();
			try {
				Date formattedDate = new SimpleDateFormat("dd/MM/yyyy").parse(dateString);
				Long value = valueObj.getValue();
				if (analyticsESResponse.containsKey(dateString)) {
					ESGMBPageAnalyticsDTO esgmbPageAnalyticsDTO = analyticsESResponse.get(dateString);
					switch (updateKey) {
					case Constants.desk_map_vu_cnt:
						esgmbPageAnalyticsDTO.setDesk_map_vu_cnt(value);
						break;
					case Constants.mob_map_vu_cnt:
						esgmbPageAnalyticsDTO.setMob_map_vu_cnt(value);
						break;
					case Constants.desk_srch_vu_cnt:
						esgmbPageAnalyticsDTO.setDesk_srch_vu_cnt(value);
						break;
					case Constants.mob_srch_vu_cnt:
						esgmbPageAnalyticsDTO.setMob_srch_vu_cnt(value);
						break;
					case Constants.wsite_vst_cnt:
						esgmbPageAnalyticsDTO.setWsite_vst_cnt(value);
						break;
					case Constants.cal_cnt:
						esgmbPageAnalyticsDTO.setCal_cnt(value);
						break;
					case Constants.dir_vst_cnt:
						esgmbPageAnalyticsDTO.setDir_vst_cnt(value);
						break;
					}

				} else {
					ESGMBPageAnalyticsDTO esgmbPageAnalyticsDTO = new ESGMBPageAnalyticsDTO();
					Long epochTime = formattedDate.getTime();
					esgmbPageAnalyticsDTO.setDay(epochTime.toString());
					Long createdEpoch = new Date().toInstant().toEpochMilli();
					esgmbPageAnalyticsDTO.setCreated(createdEpoch.toString());
					esgmbPageAnalyticsDTO.setPage_id(pageId);
					esgmbPageAnalyticsDTO.setB_id(businessId);
					switch (updateKey) {
					case Constants.desk_map_vu_cnt:
						esgmbPageAnalyticsDTO.setDesk_map_vu_cnt(value);
						break;
					case Constants.mob_map_vu_cnt:
						esgmbPageAnalyticsDTO.setMob_map_vu_cnt(value);
						break;
					case Constants.desk_srch_vu_cnt:
						esgmbPageAnalyticsDTO.setDesk_srch_vu_cnt(value);
						break;
					case Constants.mob_srch_vu_cnt:
						esgmbPageAnalyticsDTO.setMob_srch_vu_cnt(value);
						break;
					case Constants.wsite_vst_cnt:
						esgmbPageAnalyticsDTO.setWsite_vst_cnt(value);
						break;
					case Constants.cal_cnt:
						esgmbPageAnalyticsDTO.setCal_cnt(value);
						break;
					case Constants.dir_vst_cnt:
						esgmbPageAnalyticsDTO.setDir_vst_cnt(value);
						break;
					}
					analyticsESResponse.put(dateString, esgmbPageAnalyticsDTO);
				}
			} catch (ParseException e) {
				e.printStackTrace();
			}
		});
	}

	private void saveGMBPageAnalyticsErrorMap(Map<String, String> exceptionMetricsMap, Integer businessId) {
		exceptionMetricsMap.entrySet().stream().forEach(entry -> {
			StringBuilder errorMessage = new StringBuilder();
			if (StringUtils.isNotEmpty(entry.getValue())) {
				errorMessage.append(entry.getValue());
			} else {
				errorMessage.append("Future completed with Exception for : " + entry.getKey());
			}
			logger.info("save analyticsFeedRepository with error  : {} for metric : {}", errorMessage.toString(), entry.getKey());
			analyticsFeedRepository.updateErrorMessage(errorMessage.toString(), entry.getKey(), businessId);
		});
	}

	private void saveGMBPageAnalyticslastSync(List<String> metrices, Integer businessId,List<ESGMBPageAnalyticsDTO> analyticsESdatalist) {
		//Sort the list based on day
		Collections.sort(analyticsESdatalist, (o1, o2) -> {
		    return new BigInteger(o1.getDay()).compareTo(new BigInteger(o2.getDay()));
		});
		//filterout last non value and create lastsyncdate accordingly.
		Map<String,Date> lastSyncDateMetric=new HashMap<>();
		analyticsESdatalist.forEach(analyticsData -> {
			if (metrices.contains(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getDesk_map_vu_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getMob_map_vu_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getDesk_srch_vu_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getMob_srch_vu_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.WEBSITE_CLICKS.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getWsite_vst_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.WEBSITE_CLICKS.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.CALL_CLICKS.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getCal_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.CALL_CLICKS.name(), lastSyncdate);
				}
			}
			if (metrices.contains(MetricEnum.BUSINESS_DIRECTION_REQUESTS.name())) {
				List<ESGMBPageAnalyticsDTO> lasNonNullList = analyticsESdatalist.stream()
						.filter(obj -> obj.getDir_vst_cnt() != null).collect(Collectors.toList());
				if (CollectionUtils.isNotEmpty(lasNonNullList)) {
					ESGMBPageAnalyticsDTO analyticsDTO = lasNonNullList.get(lasNonNullList.size() - 1);
					Date lastSyncdate = new Date(Long.parseLong(analyticsDTO.getDay()));
					lastSyncDateMetric.put(MetricEnum.BUSINESS_DIRECTION_REQUESTS.name(), lastSyncdate);
				}
			}
		});
		
		lastSyncDateMetric.forEach((metric,date)->{
			String errorMessage = null;
			logger.info("save analyticsFeedRepository with lastSyncDate  : {} for metric : {}", date.toString(), metric);
			analyticsFeedRepository.updateLastSyncDate(date, errorMessage, metric, businessId);
		});

	}

	private List<GMBPageAnalyticsRequestDTO> createAnalyticsRequestDTO(
			GMBAnalyticsDates startDates, Calendar endCal,BusinessGoogleMyBusinessLocation gmbPage) {
		List<GMBPageAnalyticsRequestDTO> gmbPageAnalyticsRequestDTOs = null;
		Calendar businessImpDeskMapstartCal = Calendar.getInstance();
		businessImpDeskMapstartCal.setTime(startDates.getDeskMapsDate());
		Integer endMonth=endCal.get(Calendar.MONTH) + 1;
		Integer endyear=endCal.get(Calendar.YEAR);
		Integer endDay=endCal.get(Calendar.DAY_OF_MONTH);
		logger.info("createAnalyticsRequestDTO  : endMonth {} , endyear {} , endDay {}", endMonth,
				endyear, endDay);
		logger.info("createAnalyticsRequestDTO  : startDates {} ", startDates.toString());

		GMBPageAnalyticsRequestDTO businessImpDeskMapsanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				businessImpDeskMapstartCal.get(Calendar.YEAR), businessImpDeskMapstartCal.get(Calendar.MONTH) + 1,
				businessImpDeskMapstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth,endDay,
				MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_MAPS.name(), gmbPage.getLocationId());

		Calendar businessImpMobMapsstartCal = Calendar.getInstance();
		businessImpMobMapsstartCal.setTime(startDates.getMobMapsDate());
		GMBPageAnalyticsRequestDTO businessImpMobMapsanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				businessImpMobMapsstartCal.get(Calendar.YEAR), businessImpMobMapsstartCal.get(Calendar.MONTH) + 1,
				businessImpMobMapsstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth, endDay,
				MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_MAPS.name(),gmbPage.getLocationId());

		Calendar businessImpDeskSearchstartCal = Calendar.getInstance();
		businessImpDeskSearchstartCal.setTime(startDates.getDeskSearchDate());
		GMBPageAnalyticsRequestDTO businessImpDeskSearchanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				businessImpDeskSearchstartCal.get(Calendar.YEAR), businessImpDeskSearchstartCal.get(Calendar.MONTH) + 1,
				businessImpDeskSearchstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth, endDay,
				MetricEnum.BUSINESS_IMPRESSIONS_DESKTOP_SEARCH.name(), gmbPage.getLocationId());

		Calendar businessImpMobSearchstartCal = Calendar.getInstance();
		businessImpMobSearchstartCal.setTime(startDates.getMobSearchDate());
		GMBPageAnalyticsRequestDTO businessImpMobSearchanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				businessImpMobSearchstartCal.get(Calendar.YEAR), businessImpMobSearchstartCal.get(Calendar.MONTH) + 1,
				businessImpMobSearchstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth, endDay,
				MetricEnum.BUSINESS_IMPRESSIONS_MOBILE_SEARCH.name(), gmbPage.getLocationId());

		Calendar webClicksstartCal = Calendar.getInstance();
		webClicksstartCal.setTime(startDates.getWebClicksDate());
		GMBPageAnalyticsRequestDTO webClicksanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				webClicksstartCal.get(Calendar.YEAR), webClicksstartCal.get(Calendar.MONTH) + 1,
				webClicksstartCal.get(Calendar.DAY_OF_MONTH), endyear,endMonth,
				endDay, MetricEnum.WEBSITE_CLICKS.name(), gmbPage.getLocationId());

		Calendar callClicksstartCal = Calendar.getInstance();
		callClicksstartCal.setTime(startDates.getCallClickDate());
		GMBPageAnalyticsRequestDTO callClicksanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				callClicksstartCal.get(Calendar.YEAR), callClicksstartCal.get(Calendar.MONTH) + 1,
				callClicksstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth,endDay, MetricEnum.CALL_CLICKS.name(),
				gmbPage.getLocationId());

		Calendar businessDirReqstartCal = Calendar.getInstance();
		businessDirReqstartCal.setTime(startDates.getDirReqDate());
		GMBPageAnalyticsRequestDTO businessDirReqstartCalanalyticsRequestDTO = new GMBPageAnalyticsRequestDTO(
				businessDirReqstartCal.get(Calendar.YEAR), businessDirReqstartCal.get(Calendar.MONTH) + 1,
				businessDirReqstartCal.get(Calendar.DAY_OF_MONTH), endyear,
				endMonth, endDay,
				MetricEnum.BUSINESS_DIRECTION_REQUESTS.name(), gmbPage.getLocationId());

		gmbPageAnalyticsRequestDTOs = new ArrayList<>(Arrays.asList(businessImpDeskMapsanalyticsRequestDTO,
				businessImpMobMapsanalyticsRequestDTO, businessImpDeskSearchanalyticsRequestDTO,
				businessImpMobSearchanalyticsRequestDTO, webClicksanalyticsRequestDTO, callClicksanalyticsRequestDTO,
				businessDirReqstartCalanalyticsRequestDTO));
		return gmbPageAnalyticsRequestDTOs;
	}

	private Integer getKeywordJobDay() {
		return  CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("listing.keyword.job.monthly.day", 5);
	}

	private String getStartingSyncDate() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("listing.keyword.starting.sync.date", "2022-12-01");
	}
}
