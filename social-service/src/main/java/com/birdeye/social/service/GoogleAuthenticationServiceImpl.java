package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dao.GoogleRefreshTokenRepo;
import com.birdeye.social.entities.GoogleRefreshToken;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.JSONUtils;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.text.StrBuilder;
import org.apache.http.client.utils.URIBuilder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Service
public class GoogleAuthenticationServiceImpl implements GoogleAuthenticationService {

	private static final Logger LOGGER = LoggerFactory.getLogger(GoogleAuthenticationServiceImpl.class);

	@Autowired
	private GoogleRefreshTokenRepo googleRefreshTokenRepo;

	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate		googleRestTemplate;

	@Autowired
	private CommonService commonService;

	@Autowired
	private CacheService cacheService;

	@Override
	public String getAuthenticationUrlForGoogle(String websiteDomain, String clientId, Boolean redirectToSetup)
			throws URISyntaxException {
		URIBuilder authURL = new URIBuilder(GOOGLE_AUTHENTICATION_URL);
		StrBuilder redirectUri = null;
		if (redirectToSetup == null || redirectToSetup) {
			redirectUri = new StrBuilder(String.format(REDIRECT_URI_SETUP, websiteDomain));
		} else {
			redirectUri = new StrBuilder(String.format(REDIRECT_URI_GENERIC, websiteDomain));
		}
		List<String> scopes = getGoogleScopes();
		authURL.addParameter("redirect_uri", redirectUri.toString());
		authURL.addParameter("access_type", "offline");
		authURL.addParameter("approval_prompt", "force");
		authURL.addParameter("client_id", clientId);
		authURL.addParameter("scope", StringUtils.join(scopes, " "));
		authURL.addParameter("response_type", "code");
		return authURL.toString();
	}

	@NotNull
	private List<String> getGoogleScopes() {
		List<String> scopes = new ArrayList<>();
		scopes.add("https://www.googleapis.com/auth/plus.pages.manage");
		scopes.add("https://www.googleapis.com/auth/plus.stream.read");
		scopes.add("https://www.googleapis.com/auth/plus.stream.write");
		scopes.add("https://www.googleapis.com/auth/plus.media.readwrite");
		scopes.add("https://www.googleapis.com/auth/business.manage");
		scopes.add("https://www.googleapis.com/auth/userinfo.profile");
		scopes.add("https://www.googleapis.com/auth/userinfo.email");
		return scopes;
	}

	private String getGoogleRedirectUrl(String origin, Boolean isLogin) {
		List<String> googleRedirectUrlInfoList= commonService.getGoogleRedirectUrlInfoConfig();

		if(!CollectionUtils.isEmpty(googleRedirectUrlInfoList)){
			Optional<String> redirectUrl=googleRedirectUrlInfoList.stream()
					.filter(url -> url.contains(origin))
					.findFirst();
			if(redirectUrl.isPresent()) {
				return String.valueOf(new StrBuilder(String.format(isLogin?REDIRECT_URI_LOGIN:REDIRECT_URI_GENERIC_1, redirectUrl.get())));
			}
		}
		String googleRedirectUri = isLogin?CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGoogleRedirectUriForLogin():
				CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGoogleRedirectUri();
		return String.valueOf(new StrBuilder(String.format(isLogin?REDIRECT_URI_LOGIN:REDIRECT_URI_GENERIC,googleRedirectUri)));
	}

	@Override
	public String getAuthenticationUrlForGoogleV2(String origin, String clientId, Boolean isLogin) throws URISyntaxException {
		URIBuilder authURL = new URIBuilder(GOOGLE_AUTHENTICATION_URL);
		String googleRedirectUri=getGoogleRedirectUrl(origin, isLogin);
		List<String> scopes = getGoogleV2Scopes(isLogin);
		authURL.addParameter("redirect_uri", googleRedirectUri);
		authURL.addParameter("access_type", "offline");
		authURL.addParameter("prompt", "consent");
		authURL.addParameter("client_id", clientId);
		authURL.addParameter("scope", StringUtils.join(scopes, " "));
		authURL.addParameter("response_type", "code");
		authURL.addParameter("state", origin);
		return authURL.toString();

	}

	@NotNull
	private List<String> getGoogleV2Scopes(Boolean isLogin) {
		List<String> scopes = new ArrayList<>();
		if(!isLogin) {
			scopes.add("https://www.googleapis.com/auth/plus.pages.manage");
			scopes.add("https://www.googleapis.com/auth/plus.stream.read");
			scopes.add("https://www.googleapis.com/auth/plus.stream.write");
			scopes.add("https://www.googleapis.com/auth/plus.media.readwrite");
			scopes.add("https://www.googleapis.com/auth/business.manage");
		}
		scopes.add("https://www.googleapis.com/auth/userinfo.profile");
		scopes.add("https://www.googleapis.com/auth/userinfo.email");
		return scopes;
	}

	@Override
	public String getAuthenticationUrlForYoutube(String websiteDomain, String clientId, String origin, Boolean redirectToSetup
			, Long businessNumber) // remove this businessNumber after testing.
			throws URISyntaxException {
		URIBuilder authURL = new URIBuilder(GOOGLE_AUTHENTICATION_URL);
		String youtubeRedirectUri = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getYoutubeRedirectUri();
		String redirectUri = String.valueOf(new StrBuilder(String.format(REDIRECT_URI_SETUP_YOUTUBE,youtubeRedirectUri)));

		String reportingEnabledBusinessNumber = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.REPORTING_ENABLED_BUSINESS_NUMBER);
				List<String> scopes = getYoutubeScope(businessNumber, reportingEnabledBusinessNumber);

				authURL.addParameter("redirect_uri", redirectUri);
				authURL.addParameter("prompt", "consent");
		authURL.addParameter("access_type", "offline");
		authURL.addParameter("client_id", clientId);
		authURL.addParameter("scope", StringUtils.join(scopes, " "));
		authURL.addParameter("response_type", "code");
		authURL.addParameter("state", origin);
		return authURL.toString();
	}

	@NotNull
	private List<String> getYoutubeScope(Long businessNumber, String reportingEnabledBusinessNumber) {
		List<String> scopes = new ArrayList<>();
		scopes.add("https://www.googleapis.com/auth/youtube.upload");
		scopes.add("https://www.googleapis.com/auth/youtubepartner");
		scopes.add("https://www.googleapis.com/auth/youtube");
//		scopes.add("https://www.googleapis.com/auth/business.manage");
		scopes.add("https://www.googleapis.com/auth/userinfo.profile");
		scopes.add("https://www.googleapis.com/auth/userinfo.email");
		scopes.add("https://www.googleapis.com/auth/youtube.force-ssl");
		scopes.add("https://www.googleapis.com/auth/youtube.readonly");
		if(businessNumber != null && (StringUtils.equalsIgnoreCase(reportingEnabledBusinessNumber,"all")
				|| reportingEnabledBusinessNumber.contains(businessNumber+""))){
//			scopes.add("https://www.googleapis.com/auth/drive.file");
			scopes.add("https://www.googleapis.com/auth/youtubepartner-channel-audit");
			scopes.add("https://www.googleapis.com/auth/yt-analytics.readonly");
			scopes.add("https://www.googleapis.com/auth/yt-analytics-monetary.readonly");
		}
		return scopes;
	}

	@Override
	public String generateGoogleAccessToken(String clientId, String clientSecret, String code, Business business,
			GoogleRefreshToken googleRefreshToken, String websiteDomain, Boolean redirectToSetup)
					throws IOException {
		HttpClient httpClient = new HttpClient();
		PostMethod postRequest = createPostRequestForGoogleAuth(code, clientId, clientSecret, null, websiteDomain,
				redirectToSetup);
		httpClient.executeMethod(postRequest);
		if (200 == postRequest.getStatusCode()) {
			String response = postRequest.getResponseBodyAsString();
			JsonObject json = new JsonParser().parse(response.trim()).getAsJsonObject();
			if (json.has("access_token")) {
				String accessToken = json.get("access_token").getAsString();
				if (json.has("refresh_token")) {
					String refreshToken = json.get("refresh_token").getAsString();
					googleRefreshToken.setRefreshToken(refreshToken);
					googleRefreshToken.setType("social");
				}
				LOGGER.info("[GooglePlus Social] Generated access token business: {}", business.getBusinessId());
				return accessToken;
			}
		}
		LOGGER.error("[GooglePlus Social] Got a failure from google api: {}", postRequest.getResponseBodyAsString());
		return null;
	}

	private PostMethod createPostRequestForGoogleAuth(String code, String clientId, String clientSecret,
			String refreshToken, String websiteDomain, Boolean redirectToSetup) {
		PostMethod postRequest = new PostMethod(GOOGLE_ACCESS_TOKEN_URL);
		postRequest.addRequestHeader("Content-Type", "application/x-www-form-urlencoded");
		if (StringUtils.isBlank(refreshToken)) {
			postRequest.addParameter("code", code);
			postRequest.addParameter("grant_type", "authorization_code");
		} else {
			postRequest.addParameter("refresh_token", refreshToken);
			postRequest.addParameter("grant_type", "refresh_token");
		}
		postRequest.addParameter("client_id", clientId);
		postRequest.addParameter("client_secret", clientSecret);
		StrBuilder redirectUri;
		if (redirectToSetup == null || redirectToSetup) {
			redirectUri = new StrBuilder(String.format(REDIRECT_URI_SETUP, websiteDomain));
		} else {
			redirectUri = new StrBuilder(String.format(REDIRECT_URI_GENERIC, websiteDomain));
		}
		postRequest.addParameter("redirect_uri", redirectUri.toString());
		LOGGER.info("Redirect URL: {}", redirectUri.toString());
		return postRequest;
	}


	//TODO: Rest Template version should be preferred.
	@Override
	public String generateFreshAccessToken(String clientId, String clientSecret, String refreshToken,
			String websiteDomain) throws IOException {
		GoogleAuthToken generateGoogleTokensUsingRefreshToken = generateGoogleTokensUsingRefreshToken(clientId, clientSecret, refreshToken);
//		if (200 == postRequest.getStatusCode()) {
//			String response = postRequest.getResponseBodyAsString();
//			JsonObject json = new JsonParser().parse(response.trim()).getAsJsonObject();
//			if (json.has("access_token")) {
//				String accessToken = json.get("access_token").getAsString();
//				return accessToken;
//			}
//		} else if (400 == postRequest.getStatusCode() || 401 == postRequest.getStatusCode() || 403 == postRequest.getStatusCode()) {
//			LOGGER.info("[GooglePlus Social] Refresh token has expired! {}", postRequest.getResponseBodyAsString());
//			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, postRequest.getResponseBodyAsString());
//		}
		return generateGoogleTokensUsingRefreshToken.getAccess_token();
	}

	@Override
	public void revertAccessToGooglePlusAccount(String refreshToken) throws IOException {
		HttpClient httpClient = new HttpClient();
		GetMethod getMethod = new GetMethod(GOOGLE_REVERT_ACCESS_URL + refreshToken);
		getMethod.addRequestHeader("Content-Type", "application/x-www-form-urlencoded");
		httpClient.executeMethod(getMethod);
		if (200 != getMethod.getStatusCode()) {
			LOGGER.error("[GooglePlus Social] Response not success for reverting token: ERROR CODE {}, {}", getMethod.getStatusCode(),
					getMethod.getResponseBodyAsString());
			throw new BirdeyeSocialException("Could not revert access.");
		}
	}


	@Override
	public GoogleAuthToken generateGoogleTokensUsingRefreshToken(String clientId, String clientSecret, String refreshToken) throws BirdeyeSocialException {
		MultiValueMap<String, String> params = getAuthTokenParametersUsingRefreshToken(clientId, clientSecret, refreshToken);
		LOGGER.info("Params created to generateGoogleTokensUsingRefreshToken : {}  ", params);
		return generateGoogleTokens(params);
	}

	@Override
	public GoogleAuthToken generateGoogleTokensUsingCode(String clientId, String clientSecret, String code,
			String redirectUri) throws BirdeyeSocialException {
		MultiValueMap<String, String> params = getAuthTokenParamsUsingCode(clientId, clientSecret, code, redirectUri);
		LOGGER.info("Params created to generateGoogleTokensUsingCode : {}  ", params);
		return generateGoogleTokens(params);
	}

	@Override
	public GoogleAuthToken generateGoogleTokens(MultiValueMap<String, String> params) throws BirdeyeSocialException {
		GoogleAuthToken authToken = null;
		try {
			ResponseEntity<GoogleAuthToken> response = googleRestTemplate.postForEntity(GOOGLE_ACCESS_TOKEN_URL, params, GoogleAuthToken.class);
			if(response.getStatusCode().is2xxSuccessful()) {
				authToken = response.getBody();
				if (Objects.isNull(authToken) || Objects.isNull(authToken.getAccess_token())) {
					LOGGER.error("Failed to generated access token for params: {}", params);
					throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, "Null access token received from Google");
				}
			}
		}catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while calling Google API postText for URL {} and parameters {} :: {}", GOOGLE_ACCESS_TOKEN_URL, params, e.getResponseBodyAsString());
			handleGoogleErrors(e);
		} catch (Exception e) {
			LOGGER.error("Exception while calling Google API postText for URL {} and parameters {} :: {}", GOOGLE_ACCESS_TOKEN_URL, params, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_TOKEN_ON_GPlus, e.getMessage());
		}
		return authToken;
	}


	/**
	 * Prepare Google auth request params using grant type as refresh token.
	 * 
	 * @param clientId - Google Client App Id
	 * @param clientSecret - Google Client App secret
	 * @param refreshToken - Existing refresh Token
	 * @return
	 */
	private MultiValueMap<String, String> getAuthTokenParametersUsingRefreshToken(String clientId, String clientSecret, String refreshToken) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		map.add("client_id", clientId);
		map.add("client_secret", clientSecret);
		map.add("refresh_token", refreshToken);
		map.add("grant_type", "refresh_token");

		return map;
	}

	/**
	 * Prepare Google auth request params using grant type as authorization code.
	 * 
	 * @param clientId - Google Client App Id
	 * @param clientSecret - Google Client App secret
	 * @param code - authorization code received from Google
	 * @param redirectUri - redirectUri used by UI from authorization, google uses this for verification
	 * @return
	 */
	private MultiValueMap<String, String> getAuthTokenParamsUsingCode(String clientId, String clientSecret, String code, String redirectUri) {
		MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
		LOGGER.info("redirectUri for getAuthTokenParamsUsingCode {}:",redirectUri);
		map.add("client_id", clientId);
		map.add("client_secret", clientSecret);
		map.add("code", code);
		map.add("grant_type", "authorization_code");
		map.add("redirect_uri", redirectUri);
		return map;
	}

	//TODO: Unify json handling. Jackson vs Gson
	private static void handleGoogleErrors(HttpStatusCodeException e) {
		if (e.getStatusCode().is5xxServerError()) {
			LOGGER.error("InternalServerException while calling getGMBLocationDetails exception {}", e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB,e.getResponseBodyAsString());
		}
		if (e.getStatusCode().is4xxClientError()) {
			LOGGER.error("RestClientException while calling getGMBLocationDetails exception {}", e.getRawStatusCode(),e.getResponseBodyAsString());
			if(e.getRawStatusCode()==401 || e.getRawStatusCode()==403){
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, e.getResponseBodyAsString());
			} else if(e.getRawStatusCode()==400) {
				Map<String, Object> errorMap = null;
				try {
					errorMap = JSONUtils.fromJSON(e.getResponseBodyAsString(), Map.class);
				} catch (Exception ex) {
					LOGGER.info("error while parsing error: {} with error: {}", e.getResponseBodyAsString(), ex.getMessage());
				}

				if(MapUtils.isNotEmpty(errorMap) && errorMap.containsKey("error") && errorMap.containsKey("error_description")) {
					String error = (String) errorMap.get("error");
					String errorDescription = (String) errorMap.get("error_description");

					if(StringUtils.isNotEmpty(error) && StringUtils.isNotEmpty(errorDescription)
							&& error.equals("invalid_grant") &&
							(errorDescription.equals("Token has been expired or revoked.") || errorDescription.contains("Bad Request") || errorDescription.contains("Account has been deleted"))) {
						throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN, e.getResponseBodyAsString());
					}
				}
			}
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB,e.getResponseBodyAsString());
		}
	}

	/**
	 * Create Google Access token using business specific Google app settings.
	 * @param refreshTokenId - Refresh token id stored in db
	 * @return accesstoken string
	 * @throws Exception 
	 */
	@Override
	public GoogleAuthToken getGoogleAuthTokens(Integer refreshTokenId) {
		GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(refreshTokenId);
		return getGoogleAuthToken(refreshToken);
	}

	@Override
	public GoogleAuthToken getGoogleAuthToken(GoogleRefreshToken refreshToken) {
		SocialAppCredsInfo domainInfo = socialAppService.getGoogleAppSettings();
		GoogleAuthToken accessToken  =  new GoogleAuthToken();
		if(Objects.nonNull(domainInfo) && Objects.nonNull(refreshToken)){
			 accessToken =  generateGoogleTokensUsingRefreshToken(domainInfo.getChannelClientId(),
					domainInfo.getChannelClientSecret(), refreshToken.getRefreshToken());
		}else{
			LOGGER.error("Domain info can not be null");
		}
		return accessToken;
	}

	@Override
	public GoogleAuthToken getYoutubeAuthTokens(Integer refreshTokenId) {
		GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(refreshTokenId);
		return getYoutubeAuthToken(refreshToken);
	}

	@Override
	@Cacheable(value = "ytRefreshTokenCache", key = "#refreshTokenId.toString()", unless = "#result == null")
	public GoogleAuthToken getYoutubeAuthTokensCached(Integer refreshTokenId) {
		GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(refreshTokenId);
		return getYoutubeAuthToken(refreshToken);
	}

	@Override
	public GoogleAuthToken getYoutubeAuthToken(GoogleRefreshToken refreshToken) {
		SocialAppCredsInfo domainInfo = socialAppService.getYoutubeAppSettings();
		GoogleAuthToken accessToken  =  new GoogleAuthToken();
		if(Objects.nonNull(domainInfo) && Objects.nonNull(refreshToken)){
			accessToken =  generateGoogleTokensUsingRefreshToken(domainInfo.getChannelClientId(),
					domainInfo.getChannelClientSecret(), refreshToken.getRefreshToken());
		}else{
			LOGGER.error("Domain info can not be null");
		}
		return accessToken;
	}


	@Override
	public String getGoogleAccessToken(Integer refreshTokenId) {
		return Objects.isNull(getGoogleAuthTokens(refreshTokenId)) ? null : getGoogleAuthTokens(refreshTokenId).getAccess_token();
	}

	@Override
	public void revokeGoogleToken(String accessToken) throws BirdeyeSocialException {
		String url = GOOGLE_REVOKE_TOKEN_URL + "?token=" + accessToken;
		LOGGER.info("Calling revokeGoogleToken for URL: {}",url);
		try {
			ResponseEntity<?> response = googleRestTemplate.postForEntity(url,null, Map.class);
			LOGGER.info("Response status: {}",response.getStatusCode());
		}catch (HttpStatusCodeException e) {
			LOGGER.error("HttpStatusCodeException while calling revokeGoogleToken for URL {} :: {}", url, e.getResponseBodyAsString());
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_REVOKE_ACCESS_TOKEN, e.getMessage());
		} catch (Exception e) {
			LOGGER.error("Exception while calling revokeGoogleToken for URL {} ", url, e);
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_REVOKE_ACCESS_TOKEN, e.getMessage());
		}
	}


}
