package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.BusinessKeywordMappingRepository;
import com.birdeye.social.dao.MentionRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.Mention;
import com.birdeye.social.external.service.InstagramMentionExternalService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.instagram.response.*;
import com.birdeye.social.model.InstagramEntry;
import com.birdeye.social.model.InstagramEventRequest;
import com.birdeye.social.model.InstagramTagAccount;
import com.birdeye.social.model.InstagramValue;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class InstagramMentionServiceImpl implements InstagramMentionService {
    private static final Logger LOGGER	= LoggerFactory.getLogger(InstagramMentionServiceImpl.class);
    private static final String MENTIONS = "mentions";
    private static final String IG_TAG_ACCOUNTS = "ig-tag-account-topic";
    private static final String MENTION_ES_TOPIC = "mention-es-topic";
    private static final String pattern = "yyyy-MM-dd HH:mm:ss";
    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    @Autowired
    private InstagramMentionExternalService instagramMentionExternalService;
    @Autowired
    private BusinessKeywordMappingRepository keywordRepo;
    @Autowired
    private IBusinessCoreService businessCoreService;
    @Autowired
    private KafkaProducerService kafkaProducerService;
    @Autowired
    private MentionRepository mentionRepository;
    @Autowired
    private SocialMentionService socialMentionService;



    private String getMediaUrl(InstagramTimelineResponse timelineResponse) {
        String mediaURL;
        if(timelineResponse.getMedia_type().equalsIgnoreCase("CAROUSEL_ALBUM")) {
            List<InstagramMediaResponse> mediaResponseList = timelineResponse.getChildren().getData();
            List<String> mediaList = new ArrayList<>();
            mediaResponseList.forEach(media->{
                mediaList.add(media.getMedia_url());
            });
            mediaURL = String.join(",",mediaList);
        } else {
            mediaURL = timelineResponse.getMedia_url();
        }
        return mediaURL;
    }

    private InstagramListenInfo getEntityFromDto(String accountId, Long enterpriseId, String accountName,String type, InstagramTimelineResponse timelineResponse, MentionedComment mentionedComment) {
        InstagramListenInfo instagramListenInfo = new InstagramListenInfo();

        instagramListenInfo.setInstagramAccountId(accountId);
        instagramListenInfo.setEnterpriseId(enterpriseId);
        instagramListenInfo.setInstagramAccountName(accountName);
        instagramListenInfo.setMediaId(timelineResponse.getId());
        instagramListenInfo.setPostUrl(timelineResponse.getPermalink());
        instagramListenInfo.setCaption(timelineResponse.getCaption());
        instagramListenInfo.setPostUsername(timelineResponse.getUsername());
        instagramListenInfo.setPostDate(timelineResponse.getTimestamp());
        instagramListenInfo.setPostLikes(timelineResponse.getLike_count());
        instagramListenInfo.setPostComments(timelineResponse.getComments_count());
        instagramListenInfo.setType(type);
        instagramListenInfo.setMediaUrl(getMediaUrl(timelineResponse));

        if(Objects.nonNull(mentionedComment)) {
            instagramListenInfo.setCommentId(mentionedComment.getId());
            instagramListenInfo.setCommentText(mentionedComment.getText());
            instagramListenInfo.setCommentUsername(mentionedComment.getUsername());
            instagramListenInfo.setCommentLikes(mentionedComment.getLike_count());
            instagramListenInfo.setCommentDate(mentionedComment.getTimestamp());
        }
        return instagramListenInfo;
    }

    private void pushDataToES(MentionEsRequest mentionEsRequest) {
        kafkaProducerService.sendObjectV1(MENTION_ES_TOPIC,mentionEsRequest);
    }

    private MentionEsRequest convertListenInfoToEsRequest(InstagramListenInfo instagramListenInfo) {
        DateFormat df = new SimpleDateFormat(pattern);
        MentionEsRequest mentionEsRequest = new MentionEsRequest();
        mentionEsRequest.setPageId(instagramListenInfo.getInstagramAccountId());
        mentionEsRequest.setEnterpriseId(instagramListenInfo.getEnterpriseId());
        mentionEsRequest.setMediaSequence(instagramListenInfo.getMediaUrl());
        mentionEsRequest.setType(instagramListenInfo.getType());
        mentionEsRequest.setSourceId(SocialChannel.INSTAGRAM.getId());
        mentionEsRequest.setUrl(instagramListenInfo.getPostUrl());
        EsMentionReviewerDataPoint mentionReviewerDataPoint = new EsMentionReviewerDataPoint();
        EsMentionMetaData mentionMetaData = new EsMentionMetaData();
        if(instagramListenInfo.getType().equalsIgnoreCase("comment")) {
            mentionEsRequest.setText(instagramListenInfo.getCommentText());
            mentionEsRequest.setPostId(instagramListenInfo.getCommentId());
            mentionEsRequest.setDate(df.format(instagramListenInfo.getCommentDate()));
            mentionReviewerDataPoint.setProfile(instagramListenInfo.getCommentUsername());
            mentionMetaData.setId(instagramListenInfo.getMediaId());
            mentionMetaData.setDate(String.valueOf(instagramListenInfo.getPostDate()));
            mentionMetaData.setText(instagramListenInfo.getCaption());
            mentionMetaData.setUser_name(instagramListenInfo.getPostUsername());
        } else {
            mentionMetaData = null;
            mentionEsRequest.setText(instagramListenInfo.getCaption());
            mentionEsRequest.setPostId(instagramListenInfo.getMediaId());
            mentionEsRequest.setDate(df.format(instagramListenInfo.getPostDate()));
            mentionReviewerDataPoint.setProfile(instagramListenInfo.getPostUsername());
        }
        mentionEsRequest.setMentionMetaData(mentionMetaData);
        mentionEsRequest.setReviewerData(mentionReviewerDataPoint);
        return mentionEsRequest;
    }

    private MentionEsRequest convertTaggedInfoToEsRequest(InstagramTimelineResponse timelineResponse, BusinessInstagramAccount instagramAccount) {
        DateFormat df = new SimpleDateFormat(pattern);
        MentionEsRequest mentionEsRequest = new MentionEsRequest();
        mentionEsRequest.setPageId(instagramAccount.getInstagramAccountId());
        mentionEsRequest.setEnterpriseId(instagramAccount.getEnterpriseId());
        mentionEsRequest.setMediaSequence(getMediaUrl(timelineResponse));
        mentionEsRequest.setType("tagged");
        mentionEsRequest.setSourceId(SocialChannel.INSTAGRAM.getId());
        mentionEsRequest.setUrl(timelineResponse.getPermalink());
        mentionEsRequest.setText(timelineResponse.getCaption());
        mentionEsRequest.setPostId(timelineResponse.getId());
        mentionEsRequest.setDate(df.format(timelineResponse.getTimestamp()));
        EsMentionReviewerDataPoint mentionReviewerDataPoint = new EsMentionReviewerDataPoint();
        mentionReviewerDataPoint.setProfile(timelineResponse.getUsername());
        mentionEsRequest.setReviewerData(mentionReviewerDataPoint);
        return mentionEsRequest;
    }

    @Override
    public void consumeIGMentionEvent(InstagramEventRequest instagramEventRequest) {
        LOGGER.info("Request received to get mentions for IG request: {}",instagramEventRequest);
        try {
            if (CollectionUtils.isNotEmpty(instagramEventRequest.getEntry()) && Objects.nonNull(instagramEventRequest.getEntry().get(0).getId())) {
                InstagramEntry igEntry = instagramEventRequest.getEntry().get(0);
                String igAccountId = String.valueOf(igEntry.getId());
                BusinessInstagramAccount instagramAccount = instagramAccountRepository.findByInstagramAccountId(igAccountId);
                if (CollectionUtils.isNotEmpty(igEntry.getChanges()) && Objects.nonNull(igEntry.getChanges().get(0).getValue())
                        && igEntry.getChanges().get(0).getField().equalsIgnoreCase(MENTIONS) && Objects.nonNull(instagramAccount)) {
                    Integer businessId = businessCoreService.getBusinessId(instagramAccount.getEnterpriseId());
                    InstagramValue igValue = igEntry.getChanges().get(0).getValue();
                    InstagramListenInfo instagramListenInfo;
                    if (Objects.nonNull(igValue.getComment_id()) && !igValue.getComment_id().equalsIgnoreCase("null")) {
                        LOGGER.info("Getting mention data for comment: {}", igValue);
                        InstagramMentionedCommentResponse mentionedCommentResponse = instagramMentionExternalService.getMentionedCommentInfo(instagramAccount, igValue.getComment_id());
                        instagramListenInfo = getEntityFromDto(igAccountId, instagramAccount.getEnterpriseId(), instagramAccount.getInstagramAccountName(), "comment",
                                mentionedCommentResponse.getMentioned_comment().getMedia(), mentionedCommentResponse.getMentioned_comment());
                    } else if (Objects.nonNull(igValue.getMedia_id()) && !igValue.getMedia_id().equalsIgnoreCase("null")) {
                        LOGGER.info("Getting mention data for post: {}", igValue);
                        InstagramMentionedMediaResponse mentionedMediaResponse = instagramMentionExternalService.getMentionedMediaInfo(instagramAccount, igValue.getMedia_id());
                        instagramListenInfo = getEntityFromDto(igAccountId, instagramAccount.getEnterpriseId(), instagramAccount.getInstagramAccountName(), "post",
                                mentionedMediaResponse.getMentioned_media(), null);
                    } else {
                        LOGGER.info("Invalid event request: {}", instagramEventRequest);
                        return;
                    }
                    Date postDate = instagramListenInfo.getType().equalsIgnoreCase("comment") ? instagramListenInfo.getCommentDate() : instagramListenInfo.getPostDate();
                    MentionEsRequest mentionEsRequest = convertListenInfoToEsRequest(instagramListenInfo);
                    Mention mention = DtoToEntityConverter.forInstagram(mentionEsRequest, postDate, businessId);
                    mention = mentionRepository.save(mention);
                    socialMentionService.publishMentionToES(mention);
//                    mentionEsRequest.setId(mention.getId());
//                    mentionEsRequest.setEnterpriseId(Long.valueOf(businessId));
//                    pushDataToES(mentionEsRequest);

                }
            }
        } catch (Exception e) {
            LOGGER.info("Error fetching IG event response for payload: {}",instagramEventRequest,e);
        }
    }

    @Override
    public void fetchTaggedIGPosts(String accountId) {
        LOGGER.info("Request received to get Tagged posts for IG accountId: {}",accountId);
        try {
            BusinessInstagramAccount instagramAccount = instagramAccountRepository.findByInstagramAccountId(accountId);
            if (Objects.nonNull(instagramAccount)) {
                InstagramTaggedPosts taggedPosts = instagramMentionExternalService.getTaggedPosts(instagramAccount);
                if (CollectionUtils.isNotEmpty(taggedPosts.getData())) {
                    Integer businessId = businessCoreService.getBusinessId(instagramAccount.getEnterpriseId());
                    List<String> previousTagged = mentionRepository.findPostIdByPageIdAndType(accountId,"tagged",50);
                    taggedPosts.getData().forEach(timelineResponse -> {
                        if(previousTagged.contains(timelineResponse.getId())) {
                            return;
                        }
                        Date postDate = timelineResponse.getTimestamp();
                        MentionEsRequest mentionEsRequest = convertTaggedInfoToEsRequest(timelineResponse, instagramAccount);
                        Mention mention = DtoToEntityConverter.forInstagram(mentionEsRequest,postDate, businessId);
                        mention = mentionRepository.save(mention);
                        socialMentionService.publishMentionToES(mention);
//                        mentionEsRequest.setId(mention.getId());
//                        mentionEsRequest.setEnterpriseId(Long.valueOf(businessId));
//                        pushDataToES(mentionEsRequest);
                    });
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error fetching tagged iG posts for accountId: {}",accountId,e);
        }
    }

    @Override
    public void fetchIGAccounts() {
        LOGGER.info("Request received to get new IG tags");
        Integer count=100;
        Integer startIndex=0;
        List<Integer> businessIds = null;
        try {
            do {
                businessIds = keywordRepo.findByStatus("active",new PageRequest(startIndex, count));
                Set<Long> businessNumbers = new HashSet<>();

                if (CollectionUtils.isNotEmpty(businessIds)) {
//                    businessIds.forEach(bId -> {
//                        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(bId, false);
//                        if (Objects.nonNull(businessLiteDTO) && Objects.nonNull(businessLiteDTO.getBusinessNumber())) {
//                            businessNumbers.add(businessLiteDTO.getBusinessNumber());
//                        }
//                    });
//                    List<Long> businessNumberList = new ArrayList<>(businessNumbers);
                    List<BusinessInstagramAccount> instagramAccounts = instagramAccountRepository.findByAccountIdInAndIsValidAndBusinessIdIsNotNull(businessIds, 1);

                    LOGGER.info("IG accounts retrieved for fetching tags: {}", instagramAccounts.size());
                    for (BusinessInstagramAccount instagramAccount : instagramAccounts) {
                        if (Objects.nonNull(instagramAccount.getInstagramAccountId())) {
                            InstagramTagAccount instagramTagAccount = new InstagramTagAccount(instagramAccount.getInstagramAccountId());
                            kafkaProducerService.sendObjectV1(IG_TAG_ACCOUNTS, instagramTagAccount);
                        }
                    }
                }
                startIndex++;
            } while(CollectionUtils.isNotEmpty(businessIds));


        } catch (Exception e) {
            LOGGER.info("Error while retrieving IG accounts for fetching tags:",e);
        }
    }
}
