package com.birdeye.social.service.drafts;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.entities.SocialDraftPagesInfo;
import com.birdeye.social.entities.SocialMasterPost;
import com.birdeye.social.entities.SocialPostDraftInfo;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.socialassets.SocialPostAssetsService;
import com.birdeye.social.service.SocialMasterPostService;
import com.birdeye.social.service.SocialPostService;
import com.birdeye.social.service.SocialTagService;
import com.birdeye.social.service.approvalworkflow.ApprovalWorkflowConvertorService;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.utils.ConversionUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

import static com.birdeye.social.service.DtoToEntityConverter.*;
import static com.birdeye.social.utils.ConversionUtils.prepareSocialDraftChannelDTO;

@Service
public class SocialPostDraftServiceImpl implements SocialPostDraftService{

    @Autowired
    private ApprovalWorkflowConvertorService approvalWorkflowConvertorService;

    @Autowired
    private SocialDraftPagesInfoService socialDraftPagesInfoService;

    @Autowired
    private SocialMasterPostService socialMasterPostService;

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    private SocialPostDraftInfoService socialPostDraftInfoService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private SocialPostAssetsService socialPostAssetsService;

    @Autowired
    private SocialTagService socialTagService;

    private static final Logger logger = LoggerFactory.getLogger(SocialPostDraftServiceImpl.class);

    @Override
    public SocialDraftPostListResponse getDraftsForRequest(BusinessIdRequest socialDraftRequest,
                                                           Integer businessId, Long businessNumber,String requestSource) throws InterruptedException {
        logger.info("Request received to fetch draft for request : {}",socialDraftRequest);
        try {
            SocialDraftPostListResponse socialDraftPostListResponse = new SocialDraftPostListResponse();
            if (Objects.isNull(socialDraftRequest) || CollectionUtils.isEmpty(socialDraftRequest.getBusinessIds()))
                return socialDraftPostListResponse;
            Page<Integer> masterPosts = findMasterPostIdsForPageIdsAndSourceIds(socialDraftRequest,businessId,requestSource);
            List<Integer> masterPostIds = Objects.nonNull(masterPosts) ? masterPosts.getContent() : null;
            if (CollectionUtils.isEmpty(masterPostIds)) {
                logger.info("No post id found for request : {}", socialDraftRequest);
                return socialDraftPostListResponse;
            }
            List<SocialMasterPost> socialMasterPosts = socialMasterPostService.findByIdIn(masterPostIds);
            if (CollectionUtils.isEmpty(socialMasterPosts)) {
                logger.info("No master posts found with request :{}", socialDraftRequest);
                return socialDraftPostListResponse;
            }
            List<SocialPostDraftInfo> socialPostDraftInfoList =socialPostDraftInfoService.findOneByMasterPostIdIn(masterPostIds);
            Map<Integer, SocialPostDraftInfo> masterPostIdToDraftInfoMap = socialPostDraftInfoList.stream()
                    .collect(Collectors.toMap(SocialPostDraftInfo::getMasterPostId, draft -> draft));
            return prepareAndReturnDraftData(masterPostIds,masterPostIdToDraftInfoMap,socialDraftRequest.getSourceIds(),
                    socialMasterPosts,masterPosts.getTotalElements(),socialDraftRequest.getPageIds(), businessNumber,businessId);
        }catch (ExecutionException e){
            logger.info("ExecutionException occurred while fetching drafts ",e);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR,"Exception occurred while fetching drafts");
        }catch (TimeoutException e){
            logger.info("TimeoutException occurred while fetching drafts ",e);
            throw new SocialBirdeyeException(ErrorCodes.TIME_OUT_ERROR,"Time out exception occurred while fetching drafts");
        }
    }

    @Override
    public void removePageIdFromDraft(String channel, String pageId) {
        SocialChannel socialChannel = SocialChannel.getSocialChannelByName(channel);
        if(Objects.isNull(socialChannel)){
            logger.info("No channel with name : {}",channel);
            return;
        }
        List<Integer> masterPostIds = socialDraftPagesInfoService.findDistinctMasterPostIdBySourceIdAndPageId(socialChannel.getId(),pageId);
        if(CollectionUtils.isEmpty(masterPostIds)){
            logger.info("No post found for request channel : {} and pageId : {}",channel,pageId);
            return;
        }
        socialDraftPagesInfoService.deleteByMasterPostIdInAndSourceIdAndPageId(masterPostIds,socialChannel.getId(),pageId);
        List<SocialPostDraftInfo> socialPostDraftInfoList =
                socialPostDraftInfoService.findByMasterPostIdInAndSourceIdIn(masterPostIds, Collections.singletonList(socialChannel.getId()));
        if(CollectionUtils.isEmpty(socialPostDraftInfoList)){
            logger.info("No social draft found for master post ids : {}",masterPostIds);
            return;
        }
        socialPostDraftInfoList.parallelStream().forEach(draft ->{
            if(draft.getPageIds().size() == 1){
                logger.info("Delete social draft for remove page id : {} and draft id : {}",pageId,draft.getId());
                socialPostDraftInfoService.deleteById(draft.getId());
                boolean exists = socialDraftPagesInfoService.existsByMasterPostId(draft.getMasterPostId());
                if(!exists)
                    socialDraftPagesInfoService.savePageInfo(draft.getMasterPostId(),draft.getEnterpriseId());
            }else {
                List<String> pageIds = draft.getPageIds();
                pageIds.remove(pageId);
                socialPostDraftInfoService.save(draft);
            }
        });
    }

    private SocialDraftPostListResponse prepareAndReturnDraftData(List<Integer> masterPostIds, Map<Integer, SocialPostDraftInfo> masterPostIdToDraftInfoMap, List<Integer> sourceIds, List<SocialMasterPost> socialMasterPosts,
                                                                  long totalElements, List<String> pageIds , Long businessNumber, Integer businessId)
            throws ExecutionException, InterruptedException, TimeoutException {
        logger.info("Parallel execution started for master post ids size : {}",masterPostIds.size());
        SocialDraftDTO socialDraftDTO = ConversionUtils.prepareSocialDraftDTO(socialMasterPosts);
        CompletableFuture<Map<Integer,SocialDraftChannelsDTO>> mapOfMasterPostIdAndSocialChannelCF =
                CompletableFuture.supplyAsync(() -> prepareMapOfMasterPostIdAndSocialChannels(masterPostIds,sourceIds,pageIds,businessId));
        CompletableFuture<Map<Integer, BusinessCoreUser>> getUsersFromCoreCF =
                CompletableFuture.supplyAsync(() -> businessCoreService.getBusinessUserForUserId(socialDraftDTO.getUserIds()));
        CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetailCF =
                CompletableFuture.supplyAsync(() -> socialPostAssetsService.mapOfIdAndSocialPostAsset(socialDraftDTO.getPostAssetIds()));
        CompletableFuture<Map<Long, List<SocialTagBasicDetail>>> getTagDetails = CompletableFuture.supplyAsync(() -> {
            List<Long> masterPostIdsLong = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(socialMasterPosts)) {
                masterPostIdsLong = socialMasterPosts.stream().map(s->Long.valueOf(s.getId())).distinct().collect(Collectors.toList());
            }
            return socialTagService.getEntityIdToBasicTagDetailListMap(masterPostIdsLong, SocialTagEntityType.DRAFT);
        });
        CompletableFuture<Void> integrationCompletionFuture = CompletableFuture.allOf(mapOfMasterPostIdAndSocialChannelCF, getUsersFromCoreCF ,getPostsAssetsDetailCF, getTagDetails);
        integrationCompletionFuture.get(100, TimeUnit.SECONDS);
        logger.info("Parallel execution completed for master post ids size : {}",masterPostIds.size());
        return prepareResponseForDraft(socialMasterPosts,masterPostIdToDraftInfoMap,totalElements,mapOfMasterPostIdAndSocialChannelCF.get(),
                getUsersFromCoreCF.get(),getPostsAssetsDetailCF.get(), getTagDetails.get(), businessNumber);
    }

    private SocialDraftPostListResponse prepareResponseForDraft(List<SocialMasterPost> socialMasterPosts, Map<Integer, SocialPostDraftInfo> masterPostIdToDraftInfoMap, long totalElements,
                                                                Map<Integer, SocialDraftChannelsDTO> mapOfMasterPostIdAndSocialChannel,
                                                                Map<Integer, BusinessCoreUser> businessCoreUserMap,
                                                                Map<Integer, SocialPostsAssets> socialPostsAssetsMap,
                                                                Map<Long, List<SocialTagBasicDetail>> masterPostIdVsTags,
                                                                Long businessNumber) {
        SocialDraftPostListResponse socialDraftPostListResponse = new SocialDraftPostListResponse();
        List<SocialDraftPostResponse> socialDraftPostResponses = new ArrayList<>();
        socialMasterPosts.forEach(socialMasterPost -> {
            SocialDraftPostResponse socialDraftPostResponse = new SocialDraftPostResponse();
            convertToSocialDraftResponse(socialPostsAssetsMap, businessNumber, socialMasterPost,
                    socialDraftPostResponse, mapOfMasterPostIdAndSocialChannel);
            socialDraftPostResponse.setCreatedDate(convertDate(socialMasterPost.getCreatedDate()));
            socialDraftPostResponse.setEditedDate(convertDate(socialMasterPost.getLastEditedAt()));
            if(MapUtils.isNotEmpty(masterPostIdToDraftInfoMap) && masterPostIdToDraftInfoMap.containsKey(socialMasterPost.getId())){
                SocialPostDraftInfo socialPostDraftInfo = masterPostIdToDraftInfoMap.get(socialMasterPost.getId());
                if(Objects.nonNull(socialPostDraftInfo) && socialPostDraftInfo.getPlannedFor() != null){
                    socialDraftPostResponse.setPlannedDate(convertDate(socialPostDraftInfo.getPlannedFor()));
                }
            }
            if(MapUtils.isNotEmpty(masterPostIdVsTags) && masterPostIdVsTags.containsKey(Long.valueOf(socialMasterPost.getId())) &&
                    CollectionUtils.isNotEmpty(masterPostIdVsTags.get(Long.valueOf(socialMasterPost.getId())))) {
                socialDraftPostResponse.setTags(masterPostIdVsTags.get(Long.valueOf(socialMasterPost.getId())));
            }
            if (Objects.nonNull(socialMasterPost.getCreatedBy()) && Objects.nonNull(businessCoreUserMap.get(socialMasterPost.getCreatedBy()))) {
                socialDraftPostResponse.setCreatedBy(businessCoreService.getFullUsername(businessCoreUserMap.get(socialMasterPost.getCreatedBy())));
            }
            if (Objects.nonNull(socialMasterPost.getEditedBy()) && Objects.nonNull(businessCoreUserMap.get(socialMasterPost.getEditedBy()))) {
                socialDraftPostResponse.setEditedBy(businessCoreService.getFullUsername(businessCoreUserMap.get(socialMasterPost.getEditedBy())));
            }
            socialDraftPostResponses.add(socialDraftPostResponse);
        });
        socialDraftPostListResponse.setData(socialDraftPostResponses);
        socialDraftPostListResponse.setTotalCount((int) totalElements);
        return socialDraftPostListResponse;
    }

    private String convertDate(Date date) {
        if(Objects.isNull(date)){
            return null;
        }
        return new SimpleDateFormat(TimeStampFormatEnum.MM_dd_yyyy_HH_mm_ss.getFormat()).format(date);
    }

    private Map<Integer, SocialDraftChannelsDTO> prepareMapOfMasterPostIdAndSocialChannels(List<Integer> masterPostIds,List<Integer> sourceIds,
                                                                                           List<String> pageIdsFromBusinessIds,Integer businessId) {
        Map<Integer,SocialDraftChannelsDTO> mapOfMasterPostIdAndSocialChannels = new HashMap<>();
        boolean isRemoved = false;
        if(CollectionUtils.isNotEmpty(pageIdsFromBusinessIds))
            isRemoved = pageIdsFromBusinessIds.remove(String.valueOf(businessId));
        if(CollectionUtils.isEmpty(pageIdsFromBusinessIds) || !isRemoved ) return mapOfMasterPostIdAndSocialChannels;
        List<SocialPostDraftInfo> socialPostDraftInfoList = socialPostDraftInfoService.findByMasterPostIdInAndSourceIdInAndEnterpriseId(masterPostIds,sourceIds,businessId);
        logger.info("Social post draft info list size : {}",socialPostDraftInfoList.size());
        if(CollectionUtils.isEmpty(socialPostDraftInfoList)){
            return mapOfMasterPostIdAndSocialChannels;
        }
        socialPostDraftInfoList.forEach(draft -> {
            Integer masterPostId =  draft.getMasterPostId();
            SocialDraftChannelsDTO socialDraftChannelsDTO = mapOfMasterPostIdAndSocialChannels.get(masterPostId);
            socialDraftChannelsDTO = prepareSocialDraftChannelDTO(pageIdsFromBusinessIds,draft,socialDraftChannelsDTO);
            mapOfMasterPostIdAndSocialChannels.put(masterPostId,socialDraftChannelsDTO);
        });
        return mapOfMasterPostIdAndSocialChannels;
    }

    private Page<Integer> findMasterPostIdsForPageIdsAndSourceIds(BusinessIdRequest socialDraftRequest,Integer businessId,String requestSource) {
        List<Integer> sourceIds = approvalWorkflowConvertorService.getIdsFromListOfChannels(socialDraftRequest.getSocialChannels());
        List<String> pagesIds = approvalWorkflowConvertorService.getPagesForBusinessIds(socialDraftRequest.getBusinessIds(),sourceIds);
        if(sourceIds.contains(Constants.UNDEFINED)){
            pagesIds.add(String.valueOf(businessId));
        }
        logger.info("Page ids size for the request : {}",pagesIds.size());
        if(CollectionUtils.isEmpty(pagesIds)){
            logger.info("No pages found for given business ids : {} and sources :{}",socialDraftRequest.getBusinessIds(),sourceIds);
            return null;
        }
        socialDraftRequest.setPageIds(pagesIds);
        socialDraftRequest.setSourceIds(sourceIds);
        PageRequest pageRequest = new PageRequest(socialDraftRequest.getPageNo(),socialDraftRequest.getSize(), Sort.Direction.DESC , "updatedAt");
        if("mobile".equalsIgnoreCase(requestSource)){
            return socialDraftPagesInfoService.findDistinctMasterPostIdByPagesIdsAndSourceIdsAndIsRestrictedForMobile(sourceIds,pagesIds,0,pageRequest);
        }
        return socialDraftPagesInfoService.findDistinctMasterPostIdByPagesIdsAndSourceIds(sourceIds,pagesIds,pageRequest);
    }
}
