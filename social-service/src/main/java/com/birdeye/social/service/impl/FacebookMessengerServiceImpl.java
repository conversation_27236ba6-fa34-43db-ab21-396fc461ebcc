/**
 *
 *
 */
package com.birdeye.social.service.impl;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.*;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FbMessengerUserDetails;
import com.birdeye.social.facebook.response.FbMessengerUserDetailsResponse;
import com.birdeye.social.model.FbMessageSendRequest;
import com.birdeye.social.model.FbMessengerUserDetailsRequest;
import com.birdeye.social.model.engageV2.message.EventUpdateRequest;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.service.FacebookMessengerService;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 *
 */
@Service
public class FacebookMessengerServiceImpl implements FacebookMessengerService {
	
	@Autowired
	private FbMessengerExternalService		fbMessageService;
	
	@Autowired
	private BusinessFacebookPageRepository	businessFbPageRepo;

	@Autowired
	private SocialFBPageRepository socialFBPageRepository;

	@Autowired
	private KafkaProducerService kafkaProducerService;
	
	private static Logger					logger	= LoggerFactory.getLogger(FacebookMessengerServiceImpl.class);
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookMessengerService#fbPageSubscribeApps(java.lang.String)
	 */
	@Override
	public FacebookBaseResponse fbPageSubscribeApps(Integer businessId) throws IOException {
		logger.info("Request received fbPageSubscribeApps for businessId: {}", businessId);
		BusinessFBPage mappedPage = getMappedPageForBusinessId(businessId);
		String accessToken = mappedPage.getPageAccessToken();
		FacebookBaseResponse response = fbMessageService.fbPageSubscribeApps(mappedPage.getFacebookPageId(), accessToken, Constants.FB_MESSENGER_RATINGS_SUBSCRIPTION_FIELDS);
		if (response.isSuccess()) {
			logger.info("Messenger subscribed for businessId: {} and pageId: {}", businessId, mappedPage.getFacebookPageId());
			//log into BFacebookPage table
			mappedPage.setMessengerOpted(1);
			mappedPage.setRatingsOpted(1);
			socialFBPageRepository.saveAndFlush(mappedPage);
			
		}
		return response;
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookMessengerService#fbPageUnsubscribeApps(java.lang.String)
	 */
	@Override
	public FacebookBaseResponse fbPageUnsubscribeApps(Integer businessId) {
		logger.info("Request received fbPageUnsubscribeApps for businessId: {}", businessId);
		BusinessFBPage mappedPage = getMappedPageForBusinessId(businessId);
		String accessToken = mappedPage.getPageAccessToken();
		FacebookBaseResponse response = fbMessageService.fbPageUnsubscribeApps(mappedPage.getFacebookPageId(), accessToken);
		if (response.isSuccess()) {
			logger.info("Messenger unsubscribed for businessId: {} and pageId: {}", businessId, mappedPage.getFacebookPageId());
			//log into BFacebookPage table
			mappedPage.setMessengerOpted(0);
			mappedPage.setRatingsOpted(0);
			socialFBPageRepository.saveAndFlush(mappedPage);
		}
		return response;
	}
	
	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookMessengerService#getFbMessengerUserDetails(com.birdeye.social.model.FbMessengerUserDetailsRequest)
	 */
	@Override
	public FbMessengerUserDetailsResponse getFbMessengerUserDetails(FbMessengerUserDetailsRequest request) throws IOException {
		logger.info("Request received getFbMessengerUserDetails for pageId: {} and userId: {}", request.getPageId(), request.getUserId());
		String accessToken = getAccessToken(request.getPageId());
		FbMessengerUserDetails fbMessengerUserDetails =  fbMessageService.getFbMessengerUserDetails(accessToken, request.getUserId());
		return convertToMessengerResponse(fbMessengerUserDetails);
	}

	private FbMessengerUserDetailsResponse convertToMessengerResponse(FbMessengerUserDetails fbMessengerUserDetails) {
		FbMessengerUserDetailsResponse fbMessengerUserDetailsResponse = new FbMessengerUserDetailsResponse();
		if(Objects.isNull(fbMessengerUserDetails)){
			return fbMessengerUserDetailsResponse;
		}
		fbMessengerUserDetailsResponse.setId(fbMessengerUserDetails.getId());
		fbMessengerUserDetailsResponse.setFirst_name(fbMessengerUserDetails.getFirst_name());
		fbMessengerUserDetailsResponse.setLast_name(fbMessengerUserDetails.getLast_name());
		if(Objects.nonNull(fbMessengerUserDetails.getPicture()) && Objects.nonNull(fbMessengerUserDetails.getPicture().getData())
				&&StringUtils.isNotEmpty(fbMessengerUserDetails.getPicture().getData().getUrl())){
			fbMessengerUserDetailsResponse.setProfile_pic(fbMessengerUserDetails.getPicture().getData().getUrl());
		}
		return fbMessengerUserDetailsResponse;
	}

	/*
	 * (non-Javadoc)
	 * @see com.birdeye.social.service.FacebookMessengerService#sendFbMessage(com.birdeye.social.model.FbMessageSendRequest)
	 */
	@Override
	public Map<String, Object> sendFbMessage(FbMessageSendRequest request) throws IOException {
		logger.info("Payload {}",request);
		logger.info("Request received sendFbMessage for pageId: {} and receiverId: {}", request.getPageId(), request.getReceiverId());
		String accessToken = getAccessToken(request.getPageId());
		FacebookMessengerSendRequest sendRequest;
		FbMessage message;
		FbRecipient recipient = new FbRecipient();
		EventUpdateRequest eventUpdateRequest = new EventUpdateRequest();
		if(StringUtils.isEmpty(request.getType())){
			request.setType(EngageV2FeedTypeEnum.USER.name());
		}
		recipient.setId(request.getReceiverId());
		eventUpdateRequest.setEventId(request.getSocialFeedId());
		eventUpdateRequest.setSourceId(SocialChannel.FACEBOOK.getId());
		eventUpdateRequest.setEventType(EngageActionsEnum.MESSAGE);
		if (request.getMessage() != null) {
			logger.info("Sending Text message for pageId: {} and receiverId: {}", request.getPageId(), request.getReceiverId());
			message = new FbMessage(request.getMessage());
		} else {
			if( !"audio, file, image, template,video".contains(request.getAttachmentType())) {
				throw new BirdeyeSocialException(ErrorCodes.INVALID_SEND_FB_MESSAGE_REQUEST, "Invalid attachment found. Attachment type can only be - audio, file, image, template,video");
			}
			logger.info("Sending dynamic message for pageId: {}, receiverId: {} and type {}", request.getPageId(), request.getReceiverId(), request.getAttachmentType());
			FbAttachmentPayload payload = new FbAttachmentPayload(request.getUrl(), true);
			FbAttachment attachment = new FbAttachment(request.getAttachmentType(), payload);
			message = new FbMessage(attachment);
		}
		if (Objects.nonNull(request.getMessageTag())) {
			sendRequest = new FacebookMessengerSendRequest("MESSAGE_TAG",recipient, message, request.getMessageTag());
		} else {
			sendRequest = new FacebookMessengerSendRequest("RESPONSE",recipient, message);
		}
		try {
			Map<String, Object> messageSent =  fbMessageService.sendFbMessage(request.getPageId(), accessToken, sendRequest);
			eventUpdateRequest.setStatus(Status.COMPLETE);
			return messageSent;
		}catch (BirdeyeSocialException e){
			if(e.getMessage().equalsIgnoreCase("10900") || e.getMessage().equalsIgnoreCase("10901")) {
				eventUpdateRequest.setStatus(Status.FAILED);
			}
			throw new BirdeyeSocialException(ErrorCodes.valueOf(e.getCode()),e.getMessage());
		}finally {
			logger.info("Facebook send message entered in final block :{}",eventUpdateRequest);
			if (StringUtils.isNotEmpty(eventUpdateRequest.getEventId()) && Objects.nonNull(eventUpdateRequest.getStatus())) {
				kafkaProducerService.sendObjectV1(KafkaTopicEnum.SEND_MESSAGE_EVENT.getName(), eventUpdateRequest);
			}
		}
	}

	/**
	 * function to unsubscribe messenger when rating is 1 and delete when rating is 0
	 * @param businessIds
	 */
	@Override
	public void fbPageSubscribeRatings(List<Integer> businessIds){

		logger.info("Request received fbPageUnsubscribeApps for businessIds: {}", businessIds);
		List<BusinessFBPage> mappedPages = socialFBPageRepository.findByBusinessIdIn(businessIds);
		mappedPages.stream().forEach(page -> {
			FacebookBaseResponse response;
			try {
				if(page.getRatingsOpted().equals(1)){
					response = (fbMessageService.fbPageUnsubscribeMessenger(page.getFacebookPageId(), page.getPageAccessToken(), Constants.FB_RATINGS_SUBSCRIPTION_FIELDS));
				}else{
					response = (fbMessageService.fbPageUnsubscribeApps(page.getFacebookPageId(), page.getPageAccessToken()));
				}
				if(response.isSuccess()) {
					page.setMessengerOpted(0);
					socialFBPageRepository.saveAndFlush(page);
				}
			}catch (Exception e){
				logger.info("Unable to unsubscribe facebook messenger for pageId : {} and business_id : {} and error {}", page.getFacebookPageId(),page.getBusinessId(),e.getMessage());
			}
		});
		logger.info("Messenger unsubscribed for businessIds: {}", businessIds);
	}

	/**
	 * Get mappedPage for the given business id
	 * 
	 * @param businessId
	 * @return
	 */
	private BusinessFBPage getMappedPageForBusinessId(Integer businessId) {
		List<BusinessFBPage> mappedPage = socialFBPageRepository.findByBusinessIdAndIsValid(businessId, 1);
		if (CollectionUtils.isEmpty(mappedPage)) {
			throw new BirdeyeSocialException(ErrorCodes.NO_SUCH_FACEBOOK_PAGE_EXIST, "Invalid/Expired page id for facebook.");
		} else if (mappedPage.size() > 1) {
			throw new BirdeyeSocialException(ErrorCodes.MULTIPLE_FACEBOOK_PAGES_EXIST, "Multiple facebook pages exist for the given page id.");
		}
		return mappedPage.get(0);
	}
	
	/**
	 * Get access token for the given page id
	 * 
	 * @param pageId
	 * @return
	 */
	private String getAccessToken(String pageId) {
		List<BusinessFBPage> findByFacebookPageId = socialFBPageRepository.findByFacebookPageIdAndIsValid(pageId, 1);
		if (CollectionUtils.isEmpty(findByFacebookPageId)) {
			throw new BirdeyeSocialException(ErrorCodes.NO_SUCH_FACEBOOK_PAGE_EXIST, "Invalid/Expired page id for facebook.");
		} else if (findByFacebookPageId.size() > 1) {
			throw new BirdeyeSocialException(ErrorCodes.MULTIPLE_FACEBOOK_PAGES_EXIST, "Multiple facebook pages exist for the given page id.");
		}
		return findByFacebookPageId.get(0).getPageAccessToken();
	}
	
}
