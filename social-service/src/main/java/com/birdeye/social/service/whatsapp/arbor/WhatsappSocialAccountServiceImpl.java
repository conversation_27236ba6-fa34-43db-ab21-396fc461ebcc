package com.birdeye.social.service.whatsapp.arbor;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.BusinessWhatsappAccountsRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessWhatsappAccounts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.FacebookCreds;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.service.*;
import com.birdeye.social.service.tiktok.external.TiktokExternalService;
import com.birdeye.social.service.whatsapp.dto.WhatsappMappingEvent;
import com.birdeye.social.specification.WhatsappSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.SocialProxyHandler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static java.util.Comparator.nullsFirst;

@Service
public class WhatsappSocialAccountServiceImpl extends SocialAccountSetupCommonService implements WhatsappSocialAccountService, ArborService {

    @Autowired
    private TiktokExternalService tiktokExternalService;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private ISocialModulePermissionService socialModulePermissionService;

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private WhatsappCommonPageService whatsappCommonPageService;

    @Autowired
    private BusinessWhatsappAccountsRepository businessWhatsappAccountsRepository;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private WhatsappSpecification whatsappSpecification;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SocialProxyHandler socialProxyHandler;

    @Autowired
    private BusinessUtilsService businessUtilService;

    private static final Logger LOG	= LoggerFactory.getLogger(WhatsappSocialAccountServiceImpl.class);

    private static final String CONNECT = "connect";

    private static final String RECONNECT = "reconnect";
    private static final String WHATSAPP_MAPPING_TOPIC = "social-whatsapp-mapping-event";
    private static final String EXTENDED_TOKEN = "Extended token {}:";
    private static final String SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN = "[Social Setup] Getting users for business: {}, extended token: {}";

    @Autowired
    private FacebookService fbService;




    @Override
    public SocialChannel channelName() {
        return SocialChannel.WHATSAPP;
    }
    @Override
    public TiktokAuthUrlResponse getAuthLoginUrl(String origin, String redirectUri) {
        String url = tiktokExternalService.getAuthLoginUrl(origin, redirectUri);
        return TiktokAuthUrlResponse.builder()
                .redirectUrl(url)
                .build();
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String type) {
        Long parentId = authRequest.getBusinessId();
        Integer birdeyeUserId = authRequest.getBirdeyeUserId();
        FacebookCreds creds;
        String fbGraphApiBaseUrl = getFacebookGraphApiBaseUrl();
        String key = SocialChannel.WHATSAPP.getName().toLowerCase().concat(String.valueOf(parentId));
        boolean lock = redisService.tryToAcquireLock(key);
        LOG.info("[Redis Lock Whatsapp Arbor] Lock status: {}",lock);
        BusinessGetPageRequest request = (type.equalsIgnoreCase(ENTERPRISE)) ?
                businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId,
                        SocialChannel.WHATSAPP.getName().toLowerCase(),CONNECT)
                : businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId,
                SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT);
        if (lock) {
            try {
                List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
                if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
                    pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT, Status.INITIAL.getName(), parentId);
                    BusinessLiteDTO businessLiteDTO =   businessCoreService.getBusinessLiteByNumber(parentId);
                    SocialAppCredsInfo socialAppFbCreds = getFacebookCreds();

                    creds = new FacebookCreds(socialAppFbCreds.getChannelClientId(), socialAppFbCreds.getChannelClientSecret(),
                            authRequest.getAuthCode(),null, fbGraphApiBaseUrl);

                    String extendedToken = fbService.getExtendedFacebookToken(creds);
                    // extendedToken="EAACLXaYYC40BO0PNv2BzM3TGRZAdGefmX7Guv0iwCpffN8RRqZBKONQGHFpCZCZBgZBkyPSkubD428wBBC6c4ohkgxj6OhNdlXejMRXJk4rzwxXB3YcJRVUxTJIJiuNjtyChdXfkHVNJmmy60ElIgeF2U6W7LXb0VtgOkeZCoKcVjZCI4OLPZBNIolthbq1JjXQupXUH5kZClIfpJJkZB2TLyWeUxYAZALO88q4xcpHBCTn4MC37IXsgQqZCQ3Xhl0NN";
                    if(org.apache.commons.lang3.StringUtils.isEmpty(extendedToken))  {
                        LOG.info("Extended token could not be retrieved, cancelling the fetch request");
                        throw new Exception("Invalid extended token");
                    }
                    LOG.info(EXTENDED_TOKEN, extendedToken);

                    LOG.info(SOCIAL_SETUP_GETTING_USERS_FOR_BUSINESS_EXTENDED_TOKEN, businessLiteDTO.getBusinessId(), extendedToken);
                    FbUserProfileInfo user = fbService.getUserDetails(extendedToken);
                    request = saveWhatsappGetPageRequest(birdeyeUserId,user,parentId,extendedToken, CONNECT,type);

                    Long enterpriseId = type.equals(ENTERPRISE) ? request.getEnterpriseId() : request.getResellerId();
                    whatsappCommonPageService.fetchWABADetails(authRequest,request, extendedToken,user,type, enterpriseId,businessLiteDTO);

                } else {
                    LOG.info("[Whatsapp Arbor] BusinessGetPageRequest found with status init or fetched for parent id {}",parentId);
                    pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(),request.getRequestType(),request.getStatus(),parentId);
                    redisService.release(key);
                }
            } catch (Exception e) {
                // Cleanup redis cache for error cases.
                redisService.release(key);
                LOG.error("[Redis Lock Whatsapp Arbor] Lock released for business {} exception {}", parentId, e);
                request = (type == ENTERPRISE) ? businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT)
                        : businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT);
                if (request != null) {
                    pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(),request.getRequestType(),Status.COMPLETE.getName(),parentId,true);
                    request.setErrorLog(e.getMessage()!=null?e.getMessage().substring(0, Math.min(e.getMessage().length(), 4000)) : null);
                    request.setStatus(Status.CANCEL.getName());
                    businessGetPageReqRepo.saveAndFlush(request);
                } else {
                    pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT, Status.COMPLETE.getName(), parentId,true);
                }
            }
        } else {
            LOG.info("[Redis Lock Whatsapp Arbor] Lock is already acquired for business {}", parentId);
            handleFailureLock(request,SocialChannel.WHATSAPP.getName().toLowerCase(), key, parentId, CONNECT);
        }
    }

    private BusinessGetPageRequest saveWhatsappGetPageRequest(Integer birdeyeUserId, FbUserProfileInfo user,
                                                              Long parentId,String extendedToken, String requestType, String type) {
        String userTokenPermissions = createUserTokenPermissions(user);
        BusinessGetPageRequest request = new BusinessGetPageRequest();
        request.setBirdeyeUserId(birdeyeUserId);
        request.setSocialUserId(user !=null ? user.getId() : "");
        request.setChannel(SocialChannel.WHATSAPP.getName());
        if (ENTERPRISE.equals(type)) {
            request.setEnterpriseId(parentId);
        } else {
            request.setResellerId(parentId);
        }
        request.setPageCount(0);
        request.setStatus(Status.INITIAL.getName());
        request.setStatus(Status.INITIAL.getName());
        request.setRequestType(requestType);
        request.setEmail(user !=null ? user.getEmail() : "");
        request.setUserAccessToken(extendedToken);
        request.setUserFbPermissions(userTokenPermissions);
        BusinessGetPageRequest finalRequest = businessGetPageReqRepo.saveAndFlush(request);
        return finalRequest;
    }

    @Override
    public Map<String, List<ChannelAccountInfo>> getIntegratedChannels(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
        List<BusinessWhatsappAccounts> fetchedPages = businessWhatsappAccountsRepository.findByRequestId(businessGetPageRequest.getId().toString());
        List<ChannelAccountInfo> tiktokPages = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(fetchedPages)) {
            getWhatsappAccountInfo(fetchedPages, tiktokPages);
            pageType.put(SocialChannel.WHATSAPP.getName().toLowerCase(), tiktokPages);
        }
        return pageType;
    }

    @Override
    public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize, String search) {
        Map<String, Object> pageData = new HashMap<>();
        Map<String, List<ChannelAccountInfoLite>> pageType = new HashMap<>();
        List<ChannelAccountInfoLite> managed = new ArrayList<>();

        // fetchedPages =  defined pageNumber and pageSize to get pageableResult
        WhatsappLiteDTO fetchedPages = Objects.nonNull(search) && !search.isEmpty() ?
                whatsappCommonPageService.findByRequestIdAndPageName(search, new PageRequest(pageNumber, pageSize, Sort.Direction.ASC, IS_SELECTED), businessGetPageRequest.getId().toString()):
                whatsappCommonPageService.findByRequestId(businessGetPageRequest.getId().toString(), new PageRequest(pageNumber, pageSize,Sort.Direction.ASC, IS_SELECTED));
        if (CollectionUtils.isNotEmpty(fetchedPages.getPageLites())) {
            fetchedPages.getPageLites().forEach(page -> managed.add(getResellerAccInfo(page)));
            if (CollectionUtils.isNotEmpty(managed))
                pageType.put(SocialChannel.WHATSAPP.getName(), managed);
        }
        pageData.put("pageType", pageType);
        pageData.put("totalCount", fetchedPages.getTotalElements());
        pageData.put("pageCount", fetchedPages.getTotalPages());
        return pageData;
    }

    @Override
    public ChannelPageInfo connectPage(TwitterConnectAccountRequest whatsappConnectAccountRequest) {
        try {
            List<String> accountIds = whatsappConnectAccountRequest.getId();
            Long enterpriseId = whatsappConnectAccountRequest.getBusinessId();
            Integer accountId = whatsappConnectAccountRequest.getAccountId();
            String type = whatsappConnectAccountRequest.getType();
            LOG.info("[Whatsapp Arbor] connectWhatsappAccount : account ids : {}", accountIds);
            ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
            List<BusinessGetPageRequest> request = getRequestForBusiness(enterpriseId, Status.FETCHED.getName(), CONNECT,type);
            if (CollectionUtils.isEmpty(request)) {
                LOG.error("[Whatsapp Arbor] seems status has already changed");
                throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems status has already changed");
            } else if (request.size() > 1) {
                LOG.error("[Whatsapp Arbor] multiple fetched rows are not possible for any business");
                throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status");
            } else {
                BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
                checkForUnMappedSmbPages(business, type);
                BusinessGetPageRequest req = request.get(0);
                Long parentId = RESELLER.equals(type) ? req.getResellerId() : req.getEnterpriseId();
                req.setStatus(Status.COMPLETE.getName());
                req.setUpdated(new Date());
                businessGetPageReqRepo.saveAndFlush(req);
                pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), req.getRequestType(), Status.COMPLETE.getName(), parentId);

                List<String> whatsappAccountNumbers = Collections.emptyList();
                if(RESELLER.equals(type) && BooleanUtils.isTrue(whatsappConnectAccountRequest.getSelectAll())
                        && org.apache.commons.lang3.StringUtils.isNotEmpty(whatsappConnectAccountRequest.getSearchStr())) {
                    //  whatsappAccountNumbers = businessWhatsappAccountsRepository.findAllByTiktokPageName(whatsappConnectAccountRequest.getSearchStr(), req.getId().toString());

                } else if(RESELLER.equals(type) && BooleanUtils.isTrue(whatsappConnectAccountRequest.getSelectAll())){
                    // whatsappAccountNumbers = businessWhatsappAccountsRepository.findAllByRequestId(req.getId().toString());

                } else {
                    whatsappAccountNumbers = accountIds;
                }


                LOG.info("[Whatsapp Arbor] pageIds extracted to process  :{}", whatsappAccountNumbers);
                List<BusinessWhatsappAccounts> whatsappAccounts = businessWhatsappAccountsRepository.findByPhoneNumberIdIn(whatsappAccountNumbers);

                whatsappAccounts.stream().forEach(page -> {
                    page.setIsSelected(1);
                    if(RESELLER.equals(type)) {
                        page.setResellerId(enterpriseId);
                    } else {
                        page.setEnterpriseId(enterpriseId);
                        page.setAccountId(accountId);
                    }
                    businessWhatsappAccountsRepository.saveAndFlush(page);
                });
                if (checkBusinessSMB(business) && isBusinessNotMappedToWhatsappAccount(business)) {
                    whatsappCommonPageService.saveWhatsappLocationMapping(business.getBusinessId(), whatsappAccountNumbers.get(0), req.getBirdeyeUserId(),type);
                }else{
                    SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(accountIds,SocialChannel.WHATSAPP.getName().toLowerCase());
                    kafkaProducerService.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
                }
                releaseLock(req.getChannel(), parentId);
                Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();
                List<ChannelAccountInfo> channelAccountInfos = getTiktokAccountsInfo(getWhatsappAccountsList(whatsappAccountNumbers, enterpriseId,type));
                if(RESELLER.equals(type)) {
                    channelAccountInfos.forEach(info->info.setUserId(req.getEmail()));
                }
                accountMap.put(SocialChannel.WHATSAPP.getName().toLowerCase(), channelAccountInfos);
                channelAccountInfo.setPageTypes(accountMap);
                commonService.setDisabledAsNullForAllChannel(channelAccountInfo);
            }
            return channelAccountInfo;
        } catch (Exception ex) {
            LOG.info("Something went wrong while adding page for request {} with error", whatsappConnectAccountRequest, ex);
            throw ex;
        }
    }
    private boolean isBusinessNotMappedToWhatsappAccount(BusinessLiteDTO business) {
        return Objects.isNull(businessWhatsappAccountsRepository.findByBusinessId(business.getBusinessId()));
    }

    @Override
    public List<Integer> getMappedAccountLeafLocationIds(List<Integer> accountLeafLocationIds) {
        if(CollectionUtils.isNotEmpty(accountLeafLocationIds)) {
            return businessWhatsappAccountsRepository.findAllIdByBusinessIdIn(accountLeafLocationIds);
        }
        return Collections.emptyList();
    }

    @Override
    public void reconnectTiktokAccount(Long parentId, ChannelAllPageReconnectRequest twitterAuthRequest, Integer userId, String type) {
        //TODO nothing
    }

    @Override
    public PaginatedConnectedPages getPages(Long parentId, PageConnectionStatus pageConnectionStatus, Integer page,
                                            Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection,
                                            ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                            Boolean locationFilterSelected, String type) {

        PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

        Long invalidPagesCount = type == ENTERPRISE ?
                businessWhatsappAccountsRepository.findCountByEnterpriseIdAndValidityType(parentId,
                        Arrays.asList(ValidTypeEnum.PARTIAL_VALID.getId(), ValidTypeEnum.INVALID.getId())) :
                businessWhatsappAccountsRepository.findCountByResellerIdAndValidityType(parentId,
                        Arrays.asList(ValidTypeEnum.PARTIAL_VALID.getId(), ValidTypeEnum.INVALID.getId()));


        boolean hasMappedPage = type.equals(ENTERPRISE)
                ? businessWhatsappAccountsRepository.existsMappedPageByEnterpriseId(parentId, locationIds)
                : businessWhatsappAccountsRepository.existsMappedPageByResellerId(parentId, locationIds);

        Page<BusinessWhatsappAccounts> connectedPages = searchSortAndPaginate(search, parentId, locationIds, pageConnectionStatus,
                userIds, 1, mappingStatus, page, size, sortDirection, sortParam, locationFilterSelected, type);

        LOG.info("getpages : Found {} pages", CollectionUtils.size(connectedPages));

        if (connectedPages != null && CollectionUtils.isNotEmpty(connectedPages.getContent())) {
            connectedPages.getContent().stream().forEach(x -> {
                if (x.getPhoneNumberId() == null) {
                    LOG.error("Whatsapp phoneNumber Id is null for parentId: {}, Data cleanup is required.", parentId);
                }
            });
        }

        try {
            ChannelPageDetails accountInfo = (connectedPages != null && CollectionUtils.isNotEmpty(connectedPages.getContent()))
                    ? getResellerPageInfo(connectedPages.getContent())
                    : new ChannelPageDetails();
            accountInfo.setDisconnected(Math.toIntExact(invalidPagesCount));
            pageTypes.put(SocialChannel.WHATSAPP.getName().toLowerCase(), accountInfo);
            connectedPage.setPageTypes(pageTypes);
            connectedPage.setPageCount(connectedPages.getTotalPages());
            connectedPage.setTotalCount(connectedPages.getTotalElements());
            connectedPage.setHasMappedPage(hasMappedPage);
            return connectedPage;
        } catch (Exception e) {
            LOG.info("exception occred while setting page details error: {} :: {}",e.getMessage(), e.getStackTrace());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }

    }

    @Override
    public void saveLocationMapping(Integer locationId, String phoneNumberId, Integer userId, String type, Long parentId) {
        try {
            LOG.info("[Arbor Whatsapp] phoneNumber Id {} mapping with location Id {}", phoneNumberId, locationId);

            BusinessWhatsappAccounts whatsappAccount = businessWhatsappAccountsRepository.findByPhoneNumberId(phoneNumberId);
            if (Objects.isNull(whatsappAccount)) {
                LOG.error("[Arbor Whatsapp] For phoneNumber id {} no data found ", phoneNumberId);
                throw new BirdeyeSocialException(ErrorCodes.NO_DATA_FOUND, "Whatsapp phoneNumber data not found");
            }

            if(Objects.nonNull(parentId) && Objects.nonNull(whatsappAccount.getEnterpriseId()) &&
                    !Objects.equals(parentId, whatsappAccount.getEnterpriseId())) {
                throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, ErrorCodes.NOT_AUTHENTICATED.name());
            }

            if(businessWhatsappAccountsRepository.existsByBusinessId(locationId)){
                throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS, MAPPING_ALREADY_EXIST_ERROR);
            }

            if(RESELLER.equalsIgnoreCase(type)){
                commonService.checkRequestFromAuthorizedSourceUsingLongResellerID(whatsappAccount.getResellerId(), parentId);
            }

            if (!Objects.isNull(whatsappAccount.getBusinessId())) {
                LOG.error("[Arbor Whatsapp] phoneNumber id {} is already mapped ", phoneNumberId);
                throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, "Whatsapp phoneNumber is already mapped");
            }
            whatsappAccount.setBusinessId(locationId);
            whatsappAccount.setUpdatedBy(userId);
            updateWhatsAppAccountForReseller(whatsappAccount, type, locationId);
            businessWhatsappAccountsRepository.save(whatsappAccount);
            commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(whatsappAccount),
                    userId.toString(), locationId, whatsappAccount.getEnterpriseId());

            LOG.info("[Arbor Whatsapp] Page with phoneNumber: {} successfully mapped to locationId: {}", phoneNumberId, locationId);
            sendWhatsappMappingEvent(whatsappAccount,locationId,"MAPPED");
        } catch (Exception ex) {
            LOG.error("[Arbor Whatsapp] Mapping failed for phoneNumber {} with error", phoneNumberId, ex);
            throw ex;
        }
    }



    private void sendWhatsappMappingEvent(BusinessWhatsappAccounts data, Integer locationId,String status) {
        WhatsappMappingEvent whatsappMappingEvent = new WhatsappMappingEvent();
        whatsappMappingEvent.setWabaId(data.getWabaId());
        whatsappMappingEvent.setPhoneNumberId(data.getPhoneNumberId());
        whatsappMappingEvent.setBusinessId(locationId);
        whatsappMappingEvent.setMappingStatus(status);
        kafkaProducerService.sendObjectV1(WHATSAPP_MAPPING_TOPIC,whatsappMappingEvent);
    }

    @Override
    public void removePageMapping(List<LocationPageMappingRequest> locationPageMappingRequests, String type, boolean unlink) {
        LOG.info("[Arbor Whatsapp] Remove page mapping request {}", locationPageMappingRequests);
        List<String> pageIds = locationPageMappingRequests.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());

        List<BusinessWhatsappAccounts> businessWhatsappAccounts = businessWhatsappAccountsRepository.findByPhoneNumberIdIn(pageIds);

        if(Objects.nonNull(locationPageMappingRequests.get(0)) && Objects.nonNull(locationPageMappingRequests.get(0).getResellerId())){
            List<Long> storedResellerIds = businessWhatsappAccounts.stream().map(BusinessWhatsappAccounts::getResellerId).collect(Collectors.toList());
            commonService.checkRequestFromAuthorizedSourceUsingResellerIdsList(storedResellerIds, locationPageMappingRequests.get(0).getResellerId());
        }

        businessWhatsappAccounts.forEach(whatsappAccounts -> {
            Integer locationId=whatsappAccounts.getBusinessId();
            commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(), Arrays.asList(whatsappAccounts), null,
                    whatsappAccounts.getBusinessId(), Constants.ENTERPRISE.equals(type) ? whatsappAccounts.getEnterpriseId() : whatsappAccounts.getResellerId());
            whatsappAccounts.setBusinessId(null);
            if(Objects.nonNull(whatsappAccounts.getResellerId())){
                whatsappAccounts.setEnterpriseId(null);
                whatsappAccounts.setAccountId(null);
            }
            if(unlink) {
                whatsappAccounts.setIsSelected(0);
                whatsappAccounts.setEnterpriseId(null);
                whatsappAccounts.setAccountId(null);
                whatsappAccounts.setResellerId(null);
            }
            sendWhatsappMappingEvent(whatsappAccounts,locationId,"UNMAPPED");
        });
        businessWhatsappAccountsRepository.save(businessWhatsappAccounts);

    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
        List<BusinessWhatsappAccounts> whatsappAccountsList = businessWhatsappAccountsRepository.findByEnterpriseId(enterpriseId);
        if ( whatsappAccountsList.isEmpty() ) {
            return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
        } else {

            for ( BusinessWhatsappAccounts whatsappAccounts : whatsappAccountsList ) {
                if (whatsappAccounts.getIsValid() != 1 ) {
                    return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
                }
            }
        }
        return ChannelSetupStatus.PageSetupStatus.OK;
    }

    @Override
    public void triggerAccountTokenValidation() {

        //TODO
    }

    @Override
    public void validateToken(SocialTokenValidationDTO payload) {
        //TODO
    }


    @Override
    public void removeAccounts(List<String> phoneNumberId, Long enterpriseId) {
        LOG.info("[Whatsapp Arbor] Remove phoneNumber Id {} ", phoneNumberId);
        List<BusinessWhatsappAccounts> whatsappAccountsList = businessWhatsappAccountsRepository.findByPhoneNumberIdIn(phoneNumberId);
        removeWhatsAppAccounts(whatsappAccountsList, enterpriseId);
    }

    private void removeWhatsAppAccounts(List<BusinessWhatsappAccounts> whatsappAccountsList, Long enterpriseId) {
        whatsappAccountsList.forEach(rawPage -> {
            LOG.info("[Whatsapp Arbor] Total phoneNumbers removed : {}", rawPage.getPhoneNumberId());
            if(Objects.nonNull(enterpriseId) && Objects.nonNull(rawPage.getEnterpriseId()) && !Objects.equals(enterpriseId, rawPage.getEnterpriseId())) {
                throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, ErrorCodes.NOT_AUTHENTICATED.name());
            }
            businessWhatsappAccountsRepository.delete(rawPage.getId());
            commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(),
                    Collections.singletonList(rawPage), null, rawPage.getBusinessId(), rawPage.getEnterpriseId());
        });
    }

    @Override
    public void getPagesSocialList(Map<String, LocationPageListInfo> connectPage, Long enterpriseId) {
        //ToDO
    }

    @Override
    public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List<Integer> businessIds, Set<String> status,Integer page,Integer size,String search, List<String> includeModules) throws Exception {
        LOG.info("[Arbor Whatsapp] getLocationMappingPages: enterpriseId {} userId {} status {}", enterpriseId, userId, status);
        if ( enterpriseId == null || userId == null || CollectionUtils.isEmpty(businessIds) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid value for enterpriseId/userId/businessIds");
        }
        LocationPageMapping response = new LocationPageMapping();
        response.setTotalLocations(businessIds.size());
        if(status == null || status.size() == 0) {
            response.setLocationList(new ArrayList<>());
            response.setDisconnectedCount(0);
            response.setUnmapped(0);
            response.setAllPagesMapped(false);
            response.setPermissionIssuePageCount(0);
            return response;
        }
        boolean b = Objects.nonNull(search) && !search.isEmpty();
        if(!CollectionUtils.isEmpty(businessIds)){
            List<BusinessWhatsappAccounts> pages =  businessWhatsappAccountsRepository.findAllByBusinessIdIn(businessIds);
            response.setUnmapped(businessIds.size() - pages.size());
            if(status.size()>1){
                prepareLocationWhatsappPhoneNoMapping(businessIds, response, status,page,size,search,b,pages, includeModules);
            }else {
                prepareMappedAndUnMappedLocations(enterpriseId,businessIds,response,status,page,size,search,b,pages, includeModules);
            }

        }
        return response;
    }

    private void prepareLocationWhatsappPhoneNoMapping(List<Integer> businessIds, LocationPageMapping response,
                                                       Set<String> status, Integer page, Integer size, String search, Boolean toSearch,
                                                       List<BusinessWhatsappAccounts> pages, List<String> includeModules) {
        LOG.info("prepareLocationWhatsappPhoneNoMapping: businessIds {} response {} status {}", businessIds, response, status);
        Map<Integer, BusinessLocationLiteEntity> businessLocationsMap;
        if(toSearch) {
            businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
        } else {
            businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
        }
        List<BusinessWhatsappAccounts> accounts = pages.stream().filter(p -> businessLocationsMap.containsKey(p.getBusinessId())).collect(Collectors.toList());
        if (!businessLocationsMap.isEmpty()) {
            Map<Integer, BusinessWhatsappAccounts> businessWhatsappAccountsMap ;
            businessWhatsappAccountsMap = accounts.stream().collect(Collectors.toMap(BusinessWhatsappAccounts::getBusinessId, Function.identity()));
            LOG.info("prepareLocationWhatsappPhoneNoMapping: businessWhatsappAccountsMap received of size {}", businessLocationsMap.size());
            List<ChannelLocationInfo> locationList = prepareChannelInfoData(businessLocationsMap,businessWhatsappAccountsMap, includeModules,status,response);

            List<ChannelLocationInfo> locationListWithoutMapping = new LinkedList<>();
            List<ChannelLocationInfo> locationListWithMapping = new LinkedList<>();
            for (ChannelLocationInfo ll : locationList) {
                if (ll.getPageData() != null) {
                    locationListWithMapping.add(ll);
                } else {
                    locationListWithoutMapping.add(ll);
                }
            }
            locationListWithoutMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
            locationListWithMapping.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));

            response.setUnmapped(locationListWithoutMapping.size());


            List<ChannelLocationInfo> finalList = new LinkedList<>();
            if(status.contains(LocationStatusEnum.UNMAPPED.getName())) {
                finalList.addAll(locationListWithoutMapping);
            }
            if(status.contains(LocationStatusEnum.MAPPED.getName())) {
                finalList.addAll(locationListWithMapping);
            }
            response.setLocationList(finalList);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No business found for given businessIds");
        }
    }

    @Override
    public boolean getModulePermissions(Long enterpriseId, List<String> modules) {
        LOG.info("Check whatsapp post permissions for account id : {}", enterpriseId);
        List<BusinessWhatsappAccounts> whatsappAccounts = businessWhatsappAccountsRepository.findByEnterpriseIdAndBusinessIdNotNull(enterpriseId);
        return whatsappCommonPageService.checkPermission(whatsappAccounts, modules);
    }

    @Override
    public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
        BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.WHATSAPP.getName().toLowerCase(), CONNECT);
        if(Objects.isNull(req)) {
            LOG.error("[Whatsapp Arbor] No record found in business get page request for businessId: {}", businessId);
            return;
        }
        if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
        }
        req.setStatus(Status.CANCEL.getName());
        req.setUpdated(new Date());
        businessGetPageReqRepo.saveAndFlush(req);
        pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), req.getRequestType(),
                Status.COMPLETE.getName(), req.getEnterpriseId());
        releaseLock(req.getChannel(), req.getEnterpriseId());
    }

    @Override
    public void markPageInvalid(SocialTokenValidationDTO request) {
        try {
            BusinessWhatsappAccounts whatsappAccount = businessWhatsappAccountsRepository.findById(request.getId());
            if(Objects.nonNull(whatsappAccount)) {
                if(whatsappAccount.getIsValid()==0) {
                    LOG.info("Page with id: {} is already marked as invalid", request.getId());
                } else {
                    LOG.info("Marking page with id: {} as invalid", request.getId());
                    whatsappAccount.setIsValid(0);
                    whatsappAccount.setValidityType(ValidTypeEnum.INVALID.getId());

                    businessWhatsappAccountsRepository.saveAndFlush(whatsappAccount);
                    commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(whatsappAccount),
                            null, whatsappAccount.getBusinessId(), whatsappAccount.getEnterpriseId());
                }
            } else {
                LOG.info("Cannot found page with id: {}", request.getId());
            }
        } catch (Exception e) {
            LOG.error("Exception occurred while marking whatsapp page with id: {} as invalid, the error: {}", request.getId(), e.getMessage());
        }
    }

    @Override
    public void updateProfileImageCDN(Integer id, String cdnUrl) {
        BusinessWhatsappAccounts whatsappAccount = businessWhatsappAccountsRepository.findById(id);
        if (Objects.nonNull(whatsappAccount)) {
            whatsappAccount.setProfileImageUrl(cdnUrl);
            businessWhatsappAccountsRepository.saveAndFlush(whatsappAccount);
        }
    }

    /**
     * @param requestIds
     */
    @Override
    public List<String> getMappedRequestIds(Set<String> requestIds) {
        return null;
    }

    @Override
    public boolean existsByResellerIdAndIsSelected(Long resellerId, Integer isSelected) {
        //TODO
        return true;
    }

    @Override
    public boolean checkIfAccountExistsByAccountId(Long accountId) {
        return businessWhatsappAccountsRepository.existsByEnterpriseIdAndIsSelected(accountId,1);
    }


    private void prepareMappedAndUnMappedLocations(Long enterpriseId, List<Integer> businessIds, LocationPageMapping response,
                                                   Set<String> status,Integer page,Integer size,String search,boolean b,List<BusinessWhatsappAccounts> pages,
                                                   List<String> includeModules) {
        Map<Integer, BusinessWhatsappAccounts> businessWhatsappAccountsMap  = pages.stream().collect(Collectors.toMap(BusinessWhatsappAccounts::getBusinessId, Function.identity()));
        LOG.info("Get all mapped pages for Whatsapp : {} for enterpriseId :{}",businessWhatsappAccountsMap.size(),enterpriseId);
        Map<Integer, BusinessLocationLiteEntity> businessLocationsMap;
        List<Integer> filterIds = new LinkedList<>();
        filterIds.addAll(businessWhatsappAccountsMap.keySet());
        LOG.info("Remove all mapped business ids to get unmapped business ids for enterpriseId: {}",enterpriseId);
        businessIds.removeAll(filterIds);
        LOG.info("Size of unmapped business ids :{} and enterpriseId :{}",businessIds,enterpriseId);
        if(status.contains(LocationStatusEnum.UNMAPPED.getName())){
            if(b){
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
            }else{
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
            }
        }else{
            if(b){
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(filterIds,page,size,search,response);
            }else{
                businessLocationsMap = businessUtilService.getBusinessLocationsLiteMapPaginated(filterIds,page,size);
            }
        }
        LOG.info("Prepare data for response for enterprise id :{}",enterpriseId);
        List<ChannelLocationInfo> locationList = prepareChannelInfoData(businessLocationsMap,businessWhatsappAccountsMap, includeModules, status,response);
        locationList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
        response.setLocationList(locationList);
        response.setAllPagesMapped(businessIds.size() <= 0);
    }



    private List<ChannelLocationInfo> prepareChannelInfoData(Map<Integer, BusinessLocationLiteEntity> businessLocationsMap,
                                                             Map<Integer, BusinessWhatsappAccounts> businessWhatsappAccountsMap, List<String> includeModules,
                                                             Set<String> status, LocationPageMapping response) {
        Integer disconnectedCount = 0;
        List<ChannelLocationInfo> locationList = new ArrayList<>();
        if(Objects.isNull(businessLocationsMap)){
            LOG.info("No Business found");
            return new ArrayList<>();
        }
        for (Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocationsMap.entrySet()) {
            ChannelLocationInfo locInfo = new ChannelLocationInfo();
            locInfo.setLocationId(entry.getKey());
            locInfo.setLocationName(entry.getValue().getAlias1() != null ? entry.getValue().getAlias1() : entry.getValue().getName());
            locInfo.setAddress(prepareBusinessAddress(entry.getValue()));
            if (Objects.nonNull(businessWhatsappAccountsMap) && businessWhatsappAccountsMap.get(entry.getKey()) != null) {
                Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
                LocationPageListInfo pageInfo = preparePageData(businessWhatsappAccountsMap.get(entry.getKey()), includeModules);

                if(pageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName()) ||
                        pageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())) {
                    disconnectedCount++;
                }
                pageInfoMap.put(SocialChannel.WHATSAPP.getName().toLowerCase(),pageInfo );
                locInfo.setPageData(pageInfoMap);
            }
            locationList.add(locInfo);
        }
        LOG.info("Disconnected count: {}",disconnectedCount);
        if(!(status.size() <= 1 && status.contains(LocationStatusEnum.UNMAPPED.getName())) ) {
            response.setDisconnectedCount(disconnectedCount);
        }
        return locationList;
    }

    private LocationPageListInfo preparePageData(BusinessWhatsappAccounts businessWhatsappAccounts, List<String> includeModules) {
        LocationPageListInfo pageInfo = new LocationPageListInfo();
        pageInfo.setId(String.valueOf(businessWhatsappAccounts.getPhoneNumberId()));
        pageInfo.setImage(businessWhatsappAccounts.getProfileImageUrl());
        pageInfo.setPageName(businessWhatsappAccounts.getVerifiedName());
        pageInfo.setWabaName(businessWhatsappAccounts.getWabaName());
        pageInfo.setCountry(businessWhatsappAccounts.getCountry());
        pageInfo.setMessagingLimit(businessWhatsappAccounts.getMessagingLimit());
        pageInfo.setQualityRating(businessWhatsappAccounts.getQualityRating());
        pageInfo.setStatus(businessWhatsappAccounts.getPhoneNumberStatus());
        Validity validity = whatsappCommonPageService.fetchValidityAndErrorMessage(businessWhatsappAccounts);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setConnectedInReseller(Objects.isNull(businessWhatsappAccounts.getResellerId()) ? false : true);

        if (CollectionUtils.isNotEmpty(includeModules)) {
            Map<String, PermissionDTO> permissionsMap = new HashMap<>();
            for (String module : includeModules) {

                if(ValidTypeEnum.VALID.getName().equalsIgnoreCase(pageInfo.getValidType())) {
                    permissionsMap.put(module, new PermissionDTO(true));
                } else {
                    permissionsMap.put(module, new PermissionDTO(false));
                }
            }
            pageInfo.setModulePermission(permissionsMap);
        }
        return pageInfo;
    }

    private String prepareBusinessAddress(BusinessLocationLiteEntity location) {
        StringBuilder address = new StringBuilder();
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getAddress1())) {
            address.append(location.getAddress1()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getAddress2())) {
            address.append(location.getAddress2()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getCity())) {
            address.append(location.getCity()).append(", ");

        }
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getState())) {
            address.append(location.getState()).append(" ");

        }
        // Zipcode will be always there
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(location.getZip())) {
            address.append(location.getZip());

        }
        return address.toString();
    }


    private BusinessGetPageRequest checkInProgressRequests(Long businessId, String type) {
        LOG.info("Exception occurred in reconnect tiktok, Checking for in progress request for business id {}", businessId);
        List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(businessId, Status.INITIAL.getName(), RECONNECT, type);
        if (CollectionUtils.isNotEmpty(underProcessRequests)) {
            return underProcessRequests.get(0);
        }
        return null;
    }


    public Page<BusinessWhatsappAccounts> searchSortAndPaginate(String search, Long parentId, List<Integer> businessIds,
                                                                PageConnectionStatus pageConnectionStatus,
                                                                List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus,
                                                                Integer page, Integer size,
                                                                PageSortDirection sortDirection, ResellerSortType sortType,
                                                                Boolean locationFilterSelected, String type) {
        Specification<BusinessWhatsappAccounts> spec = Specifications.where(type == ENTERPRISE ? whatsappSpecification.hasEnterpriseId(parentId)
                :  whatsappSpecification.hasResellerId(parentId));
        if(Objects.nonNull(search)) {
            spec = Specifications.where(spec).and(whatsappSpecification.hasPageName(search));
        }
        if(CollectionUtils.isNotEmpty(businessIds)) {
            if(locationFilterSelected) {
                if(MappingStatus.UNMAPPED.equals(mappingStatus)) return new PageImpl<>(new ArrayList<>());
                else spec = Specifications.where(spec).and(whatsappSpecification.inBusinessIds(businessIds));
            } else {
                if(MappingStatus.MAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(whatsappSpecification.inBusinessIds(businessIds));
                } else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(whatsappSpecification.hasBusinessIdNullOrNotNull(true));
                } else {
                    Specification<BusinessWhatsappAccounts> orSpec = Specifications.where(whatsappSpecification.inBusinessIds(businessIds));
                    orSpec = Specifications.where(orSpec).or(whatsappSpecification.hasBusinessIdNullOrNotNull(true));
                    spec = Specifications.where(spec).and(orSpec);
                }
            }
        } else {
            if(MappingStatus.MAPPED.equals(mappingStatus)) {
                return new PageImpl<>(new ArrayList<>());
            } else {
                spec = Specifications.where(spec).and(whatsappSpecification.hasBusinessIdNullOrNotNull(true));
            }
        }
        if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(whatsappSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.VALID.getId())));
            if(Objects.nonNull(isSelected)) {
                spec = Specifications.where(spec).and(whatsappSpecification.isSelected(isSelected));
            }
        } else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(whatsappSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.INVALID.getId(),
                    ValidTypeEnum.PARTIAL_VALID.getId())));
        } else {
            spec = Specifications.where(spec).and(whatsappSpecification.isSelected(isSelected));
        }
        if(CollectionUtils.isNotEmpty(createdByIds)) {
            spec = Specifications.where(spec).and(whatsappSpecification.inCreatedByIds(createdByIds));
        }
        PageRequest pageRequest = null;
        if(ResellerSortType.DISPLAY_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {

            pageRequest = new PageRequest(page, size,
                    new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "verifiedName"));
        } else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            spec = Specifications.where(spec).and(whatsappSpecification.sortValidTypesInGroup(sortDirection));
            pageRequest = new PageRequest(page, size);
        } else {
            spec = Specifications.where(spec).and(whatsappSpecification.sortBusinessIdNullsFirst());
            pageRequest = new PageRequest(page, size);
        }

        return businessWhatsappAccountsRepository.findAll(spec, pageRequest);
    }

    private ChannelPageDetails getResellerPageInfo(List<BusinessWhatsappAccounts> BusinessWhatsappAccounts) throws Exception {
        List<ChannelPages> pageInfo = new ArrayList<>();
        // Process for non empty connected Tiktok accounts.
        if (!BusinessWhatsappAccounts.isEmpty()) {
            List<Integer> businessIds = new ArrayList<>();
            List<Integer> userIds = new ArrayList<>();
            BusinessWhatsappAccounts.forEach(x->{
                if(Objects.nonNull(x.getBusinessId())) businessIds.add(x.getBusinessId());
                if(Objects.nonNull(x.getCreatedBy())) userIds.add(x.getCreatedBy());
            });
            Map<String, Object> businessLocations = null;
            CompletableFuture<Map<String, Object>> businessLocationsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,true);
                } catch (Exception e) {
                    LOG.info("exception while executing business location future, error: {}", e.getMessage());
                    return new HashMap<>();
                }
            });
            CompletableFuture<Map<Integer, BusinessCoreUser>> userDetailsFuture = CompletableFuture.supplyAsync(() -> {
                try {
                    return businessCoreService.getBusinessUserForUserId(userIds);
                } catch (Exception e) {
                    LOG.info("exception while executing user details future, error: {}", e.getMessage());
                    return new HashMap<>();
                }
            });
            CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(businessLocationsFuture, userDetailsFuture);
            allCompletableFuture.get(10, TimeUnit.SECONDS);
            businessLocations = businessLocationsFuture.get();
            Map<Integer, BusinessCoreUser> userIdVsInfoMap= userDetailsFuture.get();
            Map<String, Object> finalBusinessLocations = businessLocations;
            BusinessWhatsappAccounts.stream().forEach(account -> {
                BusinessLocationLiteEntity locationLite = null;
                if(Objects.nonNull(finalBusinessLocations) && Objects.nonNull(account.getBusinessId())){
                    LOG.info("Prepare data for mapped account :{}",account);
                    Map<String ,Object> locationData = (Map<String, Object>) finalBusinessLocations.get(account.getBusinessId().toString());
                    locationLite = commonService.getMappedLocationInfo(locationData, account.getBusinessId(), account.getVerifiedName());
                }

                BusinessCoreUser userDetail = null;
                if(Objects.nonNull(account.getCreatedBy()) && MapUtils.isNotEmpty(userIdVsInfoMap) &&
                        userIdVsInfoMap.containsKey(account.getCreatedBy())) {
                    userDetail = userIdVsInfoMap.get(account.getCreatedBy());
                }

                ChannelPages completePageInfo = getResellerPageInfo(account, locationLite, userDetail);
                pageInfo.add(completePageInfo);
            });
        }
        ChannelPageDetails channelPageDetails = new ChannelPageDetails();
        channelPageDetails.setPages(pageInfo);
        return channelPageDetails;
    }

    private ChannelPages getResellerPageInfo(BusinessWhatsappAccounts page,BusinessLocationLiteEntity locationDetails, BusinessCoreUser userDetail) {
        ChannelPages pageInfo = new ChannelPages();
        pageInfo.setId(String.valueOf(page.getPhoneNumberId()));
        pageInfo.setPageName(page.getVerifiedName());
        pageInfo.setCountry(page.getCountry());
        pageInfo.setWabaName(page.getWabaName());
        pageInfo.setWhatsppNumber(page.getPhoneNumber());
        pageInfo.setIsMetaBusinessVerified(page.getIsBusinessVerified());

        pageInfo.setQualityRating(page.getQualityRating());
        pageInfo.setMessagingLimit(page.getMessagingLimit());
        pageInfo.setStatus(page.getPhoneNumberStatus());
        pageInfo.setUserId(page.getUserEmailId());

        Validity validity = whatsappCommonPageService.fetchValidityAndErrorMessage(page);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setAddedBy(Objects.nonNull(userDetail)?businessCoreService.getFullUsername(userDetail):null);
        if( Objects.nonNull(locationDetails)) {
            pageInfo.setLocationId(locationDetails.getId());
            pageInfo.setLocationName(locationDetails.getAlias1() != null ? locationDetails.getAlias1() : locationDetails.getName());
            pageInfo.setLocationAddress(commonService.prepareBusinessAddress(locationDetails));
            pageInfo.setParentName(locationDetails.getAccountName());
        }
        return pageInfo;

    }



    private List<ChannelAccountInfo> getTiktokAccountsInfo(List<BusinessWhatsappAccounts> whatsappAccountsList) {
        List<ChannelAccountInfo> accountInfo = new ArrayList<>();
        whatsappAccountsList.stream().forEach(page -> accountInfo.add(parseWhatsappAccountInfo(page)));
        return accountInfo;
    }

    private List<BusinessWhatsappAccounts> getWhatsappAccountsList(List<String> twitterAccountIds, long enterpriseId, String type) {
        return RESELLER.equals(type) ? businessWhatsappAccountsRepository.findByResellerIdAndPhoneNumberIdIn(enterpriseId, twitterAccountIds)
                : businessWhatsappAccountsRepository.findByEnterpriseIdAndPhoneNumberIdIn(enterpriseId, twitterAccountIds);

    }

    private void releaseLock(String channel, Long enterpriseId) {
        redisService.release(channel.concat(String.valueOf(enterpriseId)));
    }

    private void checkForUnMappedSmbPages(BusinessLiteDTO business, String type) {
        if (checkBusinessSMB(business)) {
            List<BusinessWhatsappAccounts> existingPages = businessWhatsappAccountsRepository.findByAccountId(business.getBusinessId());
            List<String> accountIds = existingPages.stream().filter(page -> page.getBusinessId() != null ).map(page -> page.getPhoneNumberId()).collect(Collectors.toList());

            if(RESELLER.equals(type)){
                businessWhatsappAccountsRepository.deleteByResellerIdAndPhoneNumberIdNotIn(business.getBusinessNumber(),accountIds);
            }
            else {
                if (CollectionUtils.isNotEmpty(accountIds)) {
                    businessWhatsappAccountsRepository.deleteByEnterpriseIdAndPhoneNumberIdNotIn(business.getBusinessNumber(), accountIds);
                } else {
                    businessWhatsappAccountsRepository.deleteByAccountId(business.getBusinessId());
                }
            }
        }
    }

    private List<BusinessGetPageRequest> getRequestForBusiness(Long businessId, String status, String requestType, String type) {

        return ENTERPRISE.equals(type) ? businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.WHATSAPP.getName().toLowerCase(), requestType)
                : businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.WHATSAPP.getName().toLowerCase(), requestType);
    }


    private void getWhatsappAccountInfo(List<BusinessWhatsappAccounts> fetchedPages, List<ChannelAccountInfo> whatsappAccounts) {
        fetchedPages.stream().forEach(page -> whatsappAccounts.add(parseWhatsappAccountInfo(page)));
    }

    private ChannelAccountInfo parseWhatsappAccountInfo(BusinessWhatsappAccounts fetchedPage) {
        ChannelAccountInfo accountInfo = new ChannelAccountInfo();
        accountInfo.setId(String.valueOf(fetchedPage.getPhoneNumberId()));
        accountInfo.setPageName(fetchedPage.getVerifiedName());
        accountInfo.setImage(fetchedPage.getProfileImageUrl());
        Validity validity = whatsappCommonPageService.fetchValidityAndErrorMessage(fetchedPage);
        accountInfo.setValidType(validity.getValidType());
        accountInfo.setErrorCode(validity.getErrorCode());
        accountInfo.setErrorMessage(validity.getErrorMessage());
        accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);

        return accountInfo;
    }

    private ChannelAccountInfoLite getResellerAccInfo(BusinessWhatsappAccounts fetchedPage) {
        ChannelAccountInfoLite accountInfo = new ChannelAccountInfoLite();
        accountInfo.setId(fetchedPage.getPhoneNumberId());
        accountInfo.setPageName(fetchedPage.getVerifiedName());
        accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
        accountInfo.setParentId(Objects.nonNull(fetchedPage.getResellerId()) ? fetchedPage.getResellerId() : fetchedPage.getEnterpriseId());
        return accountInfo;
    }

    private void updateWhatsAppAccountForReseller(BusinessWhatsappAccounts whatsappAccounts, String type, Integer locationId) {
        if (RESELLER.equals(type)) {
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(locationId, false);
            whatsappAccounts.setAccountId(businessLiteDTO.getAccountId());

            if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
                whatsappAccounts.setEnterpriseId(businessLiteDTO.getBusinessNumber());
            } else {
                whatsappAccounts.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
            }
        }
    }

    @Override
    public List<ChannelAccountInfo> getAllPages(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        return Collections.emptyList();
    }

    @Override
    public PaginatedConnectedPages getPagesForEnterprise(Long enterpriseId, PageConnectionStatus pageConnectionStatus,
                                                         Integer page, Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection,
                                                         ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type) {
        return getPages(enterpriseId, pageConnectionStatus, page, size, search, searchType, sortDirection, sortParam, locationIds, mappingStatus, userIds, locationFilterSelected, type);
    }
}