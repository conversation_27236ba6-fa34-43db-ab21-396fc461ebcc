package com.birdeye.social.service.Youtube;

import com.birdeye.social.aspect.Profiled;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.google.response.GoogleBaseResponse;
import com.birdeye.social.google.response.GoogleErrorResponse;
import com.birdeye.social.model.Youtube.YoutubeAnalytics.YoutubeAnalyticResponse;
import com.birdeye.social.utils.JSONUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Collections;
import java.util.Objects;

@Service
@Profiled
public class YoutubeAnalyticServiceImpl implements YoutubeAnalyticService {

    private static final Logger log	= LoggerFactory.getLogger(YoutubeAnalyticServiceImpl.class);

    public static final String BASEURL = "https://youtubeanalytics.googleapis.com/v2/reports";

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate restTemplate;

    @Override
    public YoutubeAnalyticResponse getReportByMetrics(String accessToken, String metrics, String startDate, String endDate, String filter) {
        MultiValueMap<String, String> parameters = getReportParameters(metrics, startDate, endDate, filter);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set(HttpHeaders.AUTHORIZATION,"Bearer " + accessToken);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        HttpEntity<Void> entity = new HttpEntity<>(httpHeaders);
        URI uri = UriComponentsBuilder.fromHttpUrl(BASEURL).queryParams(parameters).build().encode().toUri();
        log.info("Received request for API getReportsByMetrics URL {}", uri);
        try {
            ResponseEntity<YoutubeAnalyticResponse> responseEntity = restTemplate.exchange(uri, HttpMethod.GET,entity,YoutubeAnalyticResponse.class);
            return responseEntity.getBody();
        }catch (HttpStatusCodeException e) {
            GoogleBaseResponse response = JSONUtils.fromJSON(e.getResponseBodyAsString(), GoogleBaseResponse.class);
            if (e.getStatusCode().is5xxServerError()){
                if (Objects.nonNull(response)) {
                    GoogleErrorResponse errorResponse = response.getError();
                    throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR,errorResponse.getMessage());
                }
            }
            if(e.getStatusCode().is4xxClientError()){
                if (Objects.nonNull(response)) {
                    GoogleErrorResponse errorResponse = response.getError();
                    throw new BirdeyeSocialException(errorResponse.getCode(),errorResponse.getMessage());
                }
            }
        }catch (Exception e){
            throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return null;
    }

    private MultiValueMap<String, String> getReportParameters(String metrics, String startDate, String endDate, String filter) {
        MultiValueMap<String, String> parameters = new LinkedMultiValueMap<>();
        parameters.add("ids", "channel==MINE");
        parameters.add("metrics", metrics);
        parameters.add("startDate", startDate);
        parameters.add("endDate", endDate);
        parameters.add("sort", "day");
        parameters.add("dimensions", "day");
        if(Objects.nonNull(filter)) {
            parameters.add("filter", filter);
        }
        return parameters;
    }


}
