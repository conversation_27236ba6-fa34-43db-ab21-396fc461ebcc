package com.birdeye.social.service;

import com.birdeye.social.entities.AutoMapping;
import com.birdeye.social.model.AutoMappingStatusResponse;

public interface AutoMappingService {

    void createAutoMappingEntry (Long enterpriseId, String channel);
    void updateAutoMappingEntryStatus(Long enterpriseId, String status,String channel);
    void updateAutoMappingEntryStatusWithValue(AutoMapping value);
    void updateAutoMappingRawIds(Long enterpriseId, Integer rawId,String channel);
    AutoMapping fetchAutoMappingRequest(Long enterpriseId,String channel);

    AutoMappingStatusResponse fetchAutoMappingStatus(Long id, String channel, String type);
    AutoMapping fetchResellerAutoMappingRequest(Long resellerId,String channel);

}
