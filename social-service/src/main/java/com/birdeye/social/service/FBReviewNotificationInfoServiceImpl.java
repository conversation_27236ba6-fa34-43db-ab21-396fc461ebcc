package com.birdeye.social.service;

import com.birdeye.social.dao.FBReviewNotificationRepo;
import com.birdeye.social.entities.FBReviewNotificationInfo;
import com.birdeye.social.model.FacebookEventRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

@Service
public class FBReviewNotificationInfoServiceImpl implements IFBReviewNotificationInfoService {

    @Autowired
    private FBReviewNotificationRepo fbReviewNotificationRepo;

    private static final Logger LOG = LoggerFactory.getLogger(FBReviewNotificationInfoServiceImpl.class);

    @Override
    public void saveNotificationRequest(List<Integer> businessFacebookPageNews,
                                        FacebookEventRequest facebookEventRequest,
                                        String verb,
                                        String pageId,
                                        String status,
                                        String comment,
                                        String fbReviewId,
                                        String fbReviewerId) {
        try {
            FBReviewNotificationInfo fbReviewNotificationInfo = new FBReviewNotificationInfo();
            fbReviewNotificationInfo.setBusinessId(!CollectionUtils.isEmpty(businessFacebookPageNews)?businessFacebookPageNews.get(0):null);
            fbReviewNotificationInfo.setComment(comment);
            ObjectMapper objectMapper = new ObjectMapper();
            fbReviewNotificationInfo.setEventData(objectMapper.writeValueAsString(facebookEventRequest));
            fbReviewNotificationInfo.setNotificationType(verb);
            fbReviewNotificationInfo.setPageId(pageId);
            fbReviewNotificationInfo.setCreatedAt(new Date());
            fbReviewNotificationInfo.setUpdatedAt(new Date());
            fbReviewNotificationInfo.setStatus(status);
            fbReviewNotificationInfo.setFbReviewId(fbReviewId);
            fbReviewNotificationInfo.setFbReviewerId(fbReviewerId);
            fbReviewNotificationRepo.save(fbReviewNotificationInfo);
        } catch (JsonProcessingException e) {
            LOG.error("Some error occured while saving info to FBReviewNotificationInfo for profile id {} and exception {}",pageId,e);
        }

    }

    @Override
    public FBReviewNotificationInfo findFirstByFacebookReviewIdAndNotificationTypeAndStatus(String facebookReviewId,
                                                                                             String notificationType,
                                                                                             String status) {
        return fbReviewNotificationRepo.findFirstByFbReviewIdAndNotificationTypeAndStatus(facebookReviewId, notificationType, status);
    }
}
