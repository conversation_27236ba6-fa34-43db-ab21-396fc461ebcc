/**
 *
 */
package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.UnexpectedFacebookErrorResponse;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dao.SocialAuditRepository;
import com.birdeye.social.dao.SocialSuccessAuditRepo;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialAudit;
import com.birdeye.social.entities.SocialSuccessAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.facebook.response.FacebookErrorResponse;
import com.birdeye.social.facebook.response.FacebookPostResponse;
import com.birdeye.social.model.SocialSuccessAuditEvent;
import com.birdeye.social.platform.dao.BusinessFacebookPageRepository;
import com.birdeye.social.platform.entities.BusinessFacebookPageNew;
import com.birdeye.social.service.impl.GMBLocationDetailServiceImpl;
import com.birdeye.social.sro.FbDebugToken;
import com.birdeye.social.utils.FacebookUtils;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;
import java.io.IOException;
import java.util.*;

/**
 ** File:         AuditServiceImpl.java
 ** Created:      25 Apr 2018
 ** Author:       sahilarora
 **
 ** This code is copyright (c) BirdEye Software India Pvt. Ltd.
 **/

@Service("socialAuditService")
public class SocialAuditServiceImpl implements SocialAuditService {

	@Autowired
	private SocialAuditRepository socialAuditRepo;

	@Autowired
	private SocialSuccessAuditRepo socialSuccessAuditRepo;

	@Autowired
	private KafkaProducerService kafkaProducer;

	@Autowired
	private BusinessFacebookPageRepository fbPageRepo;

	@Autowired
	private BusinessInstagramAccountRepository instagramAccountRepository;

	@Autowired
	private FacebookService fbService;

	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private FacebookSocialAccountService facebookSocialAccountService;

	private final Logger logger = LoggerFactory.getLogger(GMBLocationDetailServiceImpl.class);

	@Override
	@Async
	public void auditExternalChannelExceptionLog(String apiType, String apiName, String requestBody,
			Integer httpResponseCode, String errorMessage,
			String errorResponse) {
		auditExternalChannelExceptionLog(null,apiType,apiName,requestBody,httpResponseCode,errorMessage,errorResponse);
	}

	@Override
	public void auditExternalChannelSuccessLog(SocialSuccessAuditEvent socialSuccessAuditEvent) {
		if(Objects.isNull(socialSuccessAuditEvent)) {
			logger.info("invalid payload");
			return;
		}
		SocialSuccessAudit successAudit = createSuccessAudit(socialSuccessAuditEvent.getApiType(),
				socialSuccessAuditEvent.getApiName(), socialSuccessAuditEvent.getRequestBody(),socialSuccessAuditEvent.getCreatedAt());

		logger.info("saving success audit: {}", successAudit);
		socialSuccessAuditRepo.saveAndFlush(successAudit);
	}

	@Override
	@Async
	public void auditExternalChannelExceptionLog(String channelName,
			String apiType, String apiName, String requestBody,
			Integer httpResponseCode, String errorMessage,
			String errorResponse) {
		SocialAudit audit = create(channelName, apiType, apiName, requestBody,
				httpResponseCode, errorMessage, errorResponse);

		if(Objects.nonNull(audit) && Objects.nonNull(audit.getExternalId()) && audit.getExternalId().isEmpty()) {
			if ("facebook".equalsIgnoreCase(audit.getChannelName())) {
				String accessToken = apiName.substring(apiName.lastIndexOf('=') + 1);
				BusinessFacebookPageNew fbPage = fbPageRepo.findByAccessToken(accessToken);
				if(Objects.nonNull(fbPage)){
					audit.setExternalId(fbPage.getFacebookPageId());
				}else{
					FacebookPostResponse response = null;
					try {
						response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(errorResponse, FacebookPostResponse.class);
					} catch (IOException ex) {
						throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Exception while mapping json data to class");
					}
					FacebookErrorResponse errorRes = response != null ? response.getError() : new FacebookErrorResponse();
					if (FacebookUtils.isUnAuthorisedErrorCodeForInstagram(errorRes.getCode(), errorRes.getError_subcode())) {
						audit.setHttpResponseCode(errorRes.getError_subcode()!=null?errorRes.getError_subcode():errorRes.getCode());
						audit.setChannelName(SocialChannel.INSTAGRAM.getName());
						//socialAuditRepo.saveAndFlush(audit);
						kafkaProducer.sendWithKey("instagram-invalid-details",audit.getExternalId(), audit);
					}
				}
			}
		}
		socialAuditRepo.saveAndFlush(audit);

		//Push into kafka to maintain counter for relaxed invalid handling
		List<String> apiReferenceList = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbInvalidApiReference();

		if ("facebook".equalsIgnoreCase(audit.getChannelName()) && (apiReferenceList.contains(audit.getApiRef()))){
			FacebookPostResponse response = null;
			try {
				if(Objects.isNull(errorResponse)) {
					logger.info("null error response found for fb API");
					return;
				}
				response = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).readValue(errorResponse, FacebookPostResponse.class);
			}
			catch(JsonParseException ex)
			{
				if (errorResponse.equalsIgnoreCase(UnexpectedFacebookErrorResponse.SORRY_ERROR_RESPONSE.getName())){
					checkValidateAccessToken(apiName, audit);
					return;
				}
			}
			catch (Exception ex) {
				logger.info("Exception:{}",ex);
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Exception while mapping json data to class");
			}
			FacebookErrorResponse errorRes = response != null ? response.getError() : new FacebookErrorResponse();
			if(FacebookUtils.isUnAuthorisedErrorCode(errorRes.getCode(), errorRes.getError_subcode()))
				kafkaProducer.sendWithKey("fb-invalid-details", audit.getExternalId(), audit);

		}
		if(SocialChannel.GMB.getName().equalsIgnoreCase(audit.getChannelName())
				&& (audit.getErrorMessage().contains("Not Found") || audit.getErrorResponse().contains("PERMISSION_DENIED"))){
			kafkaProducer.sendObjectV1(Constants.CHECK_GMB_VALID,audit);
		}
	}

	private void checkValidateAccessToken (String apiName, SocialAudit audit){
		List<String> details=extractQueryParam(apiName);

		FbDebugToken tokenDetails = new FbDebugToken();

		if(!CollectionUtils.isEmpty(details) && details.size() == 2) {
			tokenDetails.setAccessToken(details.get(1));
			tokenDetails.setExternalId(details.get(0));
		}

		tokenDetails.setExternalId(getFbExternalId(apiName));
		tokenDetails.setErrorResponse(audit.getErrorResponse());
		if(tokenDetails.getExternalId() != null) {
			facebookSocialAccountService.validateAccessToken(tokenDetails);
		}
	}

	private String getFbExternalId (String apiName) {
		try {
			return apiName.substring(apiName.indexOf('/',apiName.indexOf('v'))+1,apiName.indexOf('/',apiName.indexOf('/',apiName.indexOf('v'))+1));
		} catch (Exception e) {
			logger.info("Error occurred while trimming externalId out of api for fb {} , api {}", e, apiName);
			return null;
		}
	}

	private List<String> extractQueryParam(String apiName){
		logger.info("API name : {}",apiName);
		String externalId=apiName.substring(apiName.indexOf('/',apiName.indexOf('v'))+1,apiName.indexOf('/',apiName.indexOf('/',apiName.indexOf('v'))+1));
		MultiValueMap<String, String> uri=UriComponentsBuilder.fromUriString(apiName).build().getQueryParams();
		if(Objects.nonNull(uri) && Objects.nonNull(uri.get("access_token"))) {
			String at = String.valueOf(uri.get("access_token").get(0));
			return Arrays.asList(externalId,at);
		}
		return Collections.singletonList(externalId);
	}

	private SocialAudit create(String channelName, String apiType,
			String apiName, String requestBody, Integer httpResponseCode,
			String errorMessage, String errorResponse) {
		SocialAudit audit = new SocialAudit();
		Map<String,String> apiRef = ApiInfoHelper.getApiDetails(apiName, apiType);
		// Additional data for GMB/FB/Google
		if (apiRef != null && !apiRef.isEmpty()) {
			audit.setApiRef(apiRef.get(ApiInfoHelper.API));
			audit.setExternalId(apiRef.get(ApiInfoHelper.EXT_ID));
			// use supplied channelname
			if(channelName == null){
				audit.setChannelName(apiRef.get(ApiInfoHelper.CHANNEL));
			}
		}
		audit.setApiType(apiType);
		audit.setApiName(apiName);
		audit.setRequestBody(requestBody);
		audit.setHttpResponseCode(httpResponseCode);
		audit.setErrorMessage(errorMessage);
		audit.setErrorResponse(errorResponse);
		return audit;
	}

	private SocialSuccessAudit createSuccessAudit(String apiType,
												  String apiName, String requestBody, Date createdAt) {
		SocialSuccessAudit audit = new SocialSuccessAudit();
		Map<String,String> apiRef = ApiInfoHelper.getApiDetails(apiName, apiType);
		// Additional data for GMB/FB/Google
		if (!apiRef.isEmpty()) {
			audit.setApiRef(apiRef.get(ApiInfoHelper.API));
			audit.setExternalId(apiRef.get(ApiInfoHelper.EXT_ID));
			audit.setChannelName(apiRef.get(ApiInfoHelper.CHANNEL));
		}
		audit.setApiType(apiType);
		audit.setApiName(apiName);
		audit.setRequestBody(requestBody);
		audit.setCreatedAt(createdAt);
		return audit;
	}

}
