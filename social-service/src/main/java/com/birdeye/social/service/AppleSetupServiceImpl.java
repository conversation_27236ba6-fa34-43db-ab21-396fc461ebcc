package com.birdeye.social.service;

import com.birdeye.social.apple.AppleErrorResponse;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.dao.BusinessInactiveAppleLocationRepo;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessInactiveAppleLocation;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.specification.AppleSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.applePost.*;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.ENTERPRISE;

@Service
public class AppleSetupServiceImpl implements AppleSetupService {

    @Autowired
    private AppleConnectService appleConnectService;
    @Autowired
    private BusinessAppleLocationRepo appleLocationRepo;

    @Autowired
    private BusinessInactiveAppleLocationRepo appleInactiveLocationRepo;
    @Autowired
    private AppleAccountService appleAccountService;
    private static final Logger logger = LoggerFactory.getLogger(AppleSetupServiceImpl.class);
    private static final Integer PAGE_SIZE = 50;
    @Autowired
    private KafkaProducerService kafkaProducer;
    private static final String APPLE_CTA_FETCH = "social-apple-cta-fetch";
    @Autowired
    private AppleCtaCategoryService appleCtaCategoryService;
    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private IBusinessGetPageService businessGetPageService;
    @Autowired
    CommonService commonService;

    @Autowired
    private IRedisLockService redisService;
    private static final String APPLE_MAPPING_TOPIC = "social-apple-mapping-event";

    @Autowired
    private AppleSpecification appleSpecification;

    private void sendAppleMappingEvent(BusinessAppleLocation data, Integer businessId) {
        AppleMappingEvent appleMappingEvent = AppleMappingEvent.builder()
                .appleCompanyId(data.getAppleCompanyId()).appleBusinessId(data.getAppleBusinessId()).appleBrandName(data.getAppleBusinessName())
                .appleLocationId(data.getAppleLocationId()).birdeyeBusinessId(businessId).status("MAPPED").build();
        kafkaProducer.sendObjectV1(APPLE_MAPPING_TOPIC,appleMappingEvent);
    }
    BusinessAppleLocation createAppleLocationEntity(AppleLocationDTO appleLocation, String businessName, Long enterpriseId, Integer accountId, Integer requestId) {
        BusinessAppleLocation businessAppleLocation = BusinessAppleLocation.builder()
                .appleBusinessId(appleLocation.getLocationDetails().getBrandId())
                .appleLocationId(appleLocation.getId())
                .appleCompanyId(appleLocation.getCompanyId())
                .appleBusinessName(businessName)
                .enterpriseId(enterpriseId)
                .accountId(accountId)
                .businessId(null)
                .link(appleLocation.getPlaceCardUrl())
                .singleLineAddress(Objects.nonNull(appleLocation.getLocationDetails().getMainAddress())
                        ? getSingleLineAddress(appleLocation.getLocationDetails().getMainAddress())
                        : null)
                .autoMapped(0)
                .createdAt(new Date())
                .requestId(String.valueOf(requestId))
                .build();
        if (CollectionUtils.isNotEmpty(appleLocation.getLocationDetails().getDisplayNames())) {
            businessAppleLocation
                    .setLocationName(appleLocation.getLocationDetails().getDisplayNames().get(0).getName());
        }
        if (CollectionUtils.isNotEmpty(appleLocation.getLocationDetails().getPhoneNumbers())) {
            businessAppleLocation
                    .setPrimaryPhone(appleLocation.getLocationDetails().getPhoneNumbers().get(0).getPhoneNumber());
        }
        return businessAppleLocation;
    }

    private String getSingleLineAddress(AppleLocationDetails.AppleLocationAddress appleLocationAddress) {
        if (Objects.nonNull(appleLocationAddress.getFullAddress()))
            return appleLocationAddress.getFullAddress().trim().replaceAll(",$", "");
        if (Objects.nonNull(appleLocationAddress.getStructuredAddress())){
            AppleLocationDetails.AppleLocationAddress.AppleStructuredLocation structuredAddress = appleLocationAddress.getStructuredAddress();
            StringBuilder singleLineAddress = new StringBuilder();
            if (StringUtils.isNotEmpty(structuredAddress.getThoroughfare()) || StringUtils.isNotEmpty(structuredAddress.getFullThoroughfare())) {
                if (StringUtils.isNotEmpty(structuredAddress.getSubThoroughfare())) {
                    singleLineAddress.append(structuredAddress.getSubThoroughfare()).append(" ");
                }
                if (StringUtils.isNotEmpty(structuredAddress.getThoroughfare())) {
                    singleLineAddress.append(structuredAddress.getThoroughfare());
                } else if (StringUtils.isNotEmpty(structuredAddress.getFullThoroughfare())) {
                    singleLineAddress.append(structuredAddress.getFullThoroughfare());
                }
            }
            addAddressComponent(singleLineAddress, structuredAddress.getLocality());
            addAddressComponent(singleLineAddress, structuredAddress.getSubLocality());
            addAddressComponent(singleLineAddress, structuredAddress.getAdministrativeArea());
            addAddressComponent(singleLineAddress, structuredAddress.getSubAdministrativeArea());
            addAddressComponent(singleLineAddress, structuredAddress.getPostCode());
            addAddressComponent(singleLineAddress, structuredAddress.getCountryCode());

            return singleLineAddress.toString().trim().replaceAll(",$", "");
        }
        return null;
    }

    private void addAddressComponent(StringBuilder singleLineAddress, String component) {
        if (StringUtils.isNotEmpty(component)) {
            if (singleLineAddress.length() > 0) {
                singleLineAddress.append(", ");
            }
            singleLineAddress.append(component);
        }
    }

    @Override
    public void fetchExistingAccounts(AppleExistingLocationRequest input) {
        String lockKey = "apple".concat(Long.toString(input.getBeEnterpriseId()));
        boolean lockAvailable = redisService.tryToAcquireLock(lockKey);
        if(!lockAvailable) {
            logger.info("[Apple] lock for enterprise id: {} is already acquired.", input.getBeEnterpriseId());
            throw new BirdeyeSocialException("Lock already acquired");
        }
        BusinessGetPageRequest request = businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(input.getBeEnterpriseId(), SocialChannel.APPLE_CONNECT.getName(), Constants.CONNECT);
        List<String> statusList = Collections.singletonList(Status.INITIAL.getName());
        try {
            if (Objects.nonNull(request) && statusList.contains(request.getStatus())) {
                logger.info("[Apple] BusinessGetPageRequest found with status init for enterpriseId {}", input.getBeEnterpriseId());
                throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "business get page request already found in init state for apple");
            }

            request = createBusinessGetPageRequest(input.getBeEnterpriseId());
            businessGetPageService.saveAndFlush(request);
            Integer requestId = request.getId();
            if (CollectionUtils.isNotEmpty(input.getBusinessList())) {
                List<BusinessAppleLocation> businessAppleLocationList = appleLocationRepo
                        .findByEnterpriseId(input.getBeEnterpriseId());
                Set<String> existingLocationIds = businessAppleLocationList.stream()
                        .map(BusinessAppleLocation::getAppleLocationId).collect(Collectors.toSet());
                logger.info("Existing apple locations for enterpriseId: {}: {}", input.getBeEnterpriseId(),
                        existingLocationIds);

                AtomicInteger totalLocationsForAllBusiness = new AtomicInteger(0);
                AtomicInteger existingLocationsForAllBusiness = new AtomicInteger(0);
                BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(input.getBeEnterpriseId());
                input.getBusinessList().forEach(business -> {
                    try {
                        List<AppleLocationDTO> locationDTOList = getAppleLocationDTOS(business);
                        int totalLocationsFromApple = 0;
                        int existingLocationsInDb = 0;
                        if (CollectionUtils.isNotEmpty(locationDTOList)) {
                            totalLocationsFromApple = locationDTOList.size();
                            for (AppleLocationDTO data:locationDTOList) {
                                if (Objects.nonNull(data) && Objects.nonNull(data.getLocationDetails())
                                        && !existingLocationIds.contains(data.getId())) {
                                    logger.info("Saving data for apple location: {}", data);
                                    BusinessAppleLocation businessAppleLocation = createAppleLocationEntity(data,
                                            business.getBusinessName(), input.getBeEnterpriseId(), businessLiteDTO.getBusinessId(), requestId);
                                    appleLocationRepo.saveAndFlush(businessAppleLocation);
                                    commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(),
                                            Collections.singletonList(businessAppleLocation), null,
                                            businessAppleLocation.getBusinessId());
                                    if(locationDTOList.size() == 1 && isSMBBusiness(businessLiteDTO)) {
                                        businessAppleLocation.setBusinessId(businessLiteDTO.getBusinessId());
                                        appleLocationRepo.saveAndFlush(businessAppleLocation);
                                        sendAppleMappingEvent(businessAppleLocation,businessLiteDTO.getBusinessId());
                                        commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(),
                                                Collections.singletonList(businessAppleLocation), null,
                                                businessAppleLocation.getBusinessId());
                                    }
                                } else if(Objects.nonNull(data) && Objects.nonNull(data.getLocationDetails())
                                        && existingLocationIds.contains(data.getId())) {
                                    existingLocationsInDb++;
                                }
                            }

                            totalLocationsForAllBusiness.addAndGet(totalLocationsFromApple);
                            existingLocationsForAllBusiness.addAndGet(existingLocationsInDb);
                        }
                    } catch (Exception e) {
                        logger.info("Exception while saving locations for business: {}", business);
                    }
                });
                int totalPages = totalLocationsForAllBusiness.get();
                int existingPages = existingLocationsForAllBusiness.get();
                request.setTotalPages(totalPages);
                request.setPageCount(Math.max(totalPages - existingPages, 0));
                request.setStatus(Status.COMPLETE.getName());
                businessGetPageService.saveAndFlush(request);
            }
        } catch (BirdeyeSocialException e) {
            logger.info("Exception occurred while saving locations for business: {}, error: {}", input.getBeEnterpriseId(), e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.valueOf(e.getCode()), e.getMessage());
        } catch (Exception e) {
            logger.info("Exception occurred while saving locations for business: {}", input.getBeEnterpriseId());
            request.setStatus(Status.COMPLETE.getName());
            request.setErrorLog(e.getMessage());
            businessGetPageService.saveAndFlush(request);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, e.getMessage());
        } finally {
            redisService.release(lockKey);
        }
    }

    private BusinessGetPageRequest createBusinessGetPageRequest(Long beEnterpriseId) {
        BusinessGetPageRequest request = new BusinessGetPageRequest();
        request.setChannel(SocialChannel.APPLE_CONNECT.getName());
        request.setEnterpriseId(beEnterpriseId);
        request.setPageCount(0);
        request.setStatus(Status.INITIAL.getName());
        request.setRequestType(Constants.CONNECT);
        return request;
    }

    private boolean isSMBBusiness(BusinessLiteDTO businessLiteDTO) {
        return StringUtils.equalsIgnoreCase(businessLiteDTO.getType(), "Business") &&
                Objects.nonNull(businessLiteDTO.getBusinessId()) && businessLiteDTO.getBusinessId().equals(businessLiteDTO.getAccountId());
    }

    private void removePreviousMapping(Integer businessId, Long beEnterpriseId) {
        List<BusinessAppleLocation> appleLocations = appleLocationRepo
                .findByBusinessIdIn(Collections.singletonList(businessId));
        if (CollectionUtils.isNotEmpty(appleLocations)) {
            logger.info("Removing mapping for entry: {}", appleLocations.get(0));
            BusinessAppleLocation appleLocation = appleLocations.get(0);
            appleLocation.setBusinessId(null);
            appleLocationRepo.saveAndFlush(appleLocation);
        }
    }

    @Override
    public void saveNewLocation(AppleLocationRequest appleLocationRequest) {
        BusinessGetPageRequest request = businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(appleLocationRequest.getBeEnterpriseId(), SocialChannel.APPLE_CONNECT.getName(), Constants.CONNECT);
        List<String> statusList = Arrays.asList(Status.INITIAL.getName());


        try {
            if (Objects.nonNull(request) && statusList.contains(request.getStatus())) {
                logger.info("[Apple] BusinessGetPageRequest found with status init for enterpriseId {}", appleLocationRequest.getBeEnterpriseId());
                throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "business get page request already found in init state for apple");
            }

            request = createBusinessGetPageRequest(appleLocationRequest.getBeEnterpriseId());
            businessGetPageService.saveAndFlush(request);

            removePreviousMapping(appleLocationRequest.getBeBusinessId(),appleLocationRequest.getBeEnterpriseId());
            Integer accountId = null;
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(appleLocationRequest.getBeEnterpriseId());
            if(Objects.nonNull(businessLiteDTO)) {
                accountId = businessLiteDTO.getBusinessId();
            }
            BusinessAppleLocation businessAppleLocation = BusinessAppleLocation.builder()
                    .appleBusinessId(appleLocationRequest.getBusinessId())
                    .appleLocationId(appleLocationRequest.getLocationId())
                    .appleCompanyId(appleLocationRequest.getCompanyId())
                    .appleBusinessName(appleLocationRequest.getBusinessName())
                    .enterpriseId(appleLocationRequest.getBeEnterpriseId())
                    .accountId(accountId)
                    .businessId(appleLocationRequest.getBeBusinessId())
                    .locationName(appleLocationRequest.getLocationName())
                    .link(appleLocationRequest.getLink())
                    .singleLineAddress(StringUtils.isNotEmpty(appleLocationRequest.getAddress()) ? appleLocationRequest.getAddress().replaceAll(", ,",",").replaceAll(",,",",") : appleLocationRequest.getAddress())
                    .primaryPhone(appleLocationRequest.getPhone())
                    .autoMapped(1)
                    .requestId(String.valueOf(request.getId()))
                    .createdAt(new Date())
                    .build();
            appleLocationRepo.saveAndFlush(businessAppleLocation);
            commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(),
                    Collections.singletonList(businessAppleLocation), null, businessAppleLocation.getBusinessId());
            request.setTotalPages(1);
            request.setPageCount(1);
            request.setStatus(Status.COMPLETE.getName());
            businessGetPageService.saveAndFlush(request);
            logger.info("New apple location saved: {}", businessAppleLocation);
        } catch (BirdeyeSocialException e) {
            logger.info("Exception occurred while saving locations for business: {}, error: {}", appleLocationRequest.getBeEnterpriseId(), e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.valueOf(e.getCode()), e.getMessage());
        } catch (Exception e) {
            logger.info("Exception while saving apple location info: ", e);
            request.setStatus(Status.COMPLETE.getName());
            request.setErrorLog(e.getMessage());
            businessGetPageService.saveAndFlush(request);
        }
    }

    @Override
    public void updateLocation(AppleLocationRequest input) {
        List<BusinessAppleLocation> appleLocations = appleLocationRepo
                .findByBusinessIdIn(Collections.singletonList(input.getBeBusinessId()));
        if (CollectionUtils.isNotEmpty(appleLocations)) {
            BusinessAppleLocation appleLocation = appleLocations.get(0);
            if (StringUtils.isNotEmpty(input.getBusinessName())) {
                appleLocation.setAppleBusinessName(input.getBusinessName());
            }
            if (StringUtils.isNotEmpty(input.getLocationName())) {
                appleLocation.setLocationName(input.getLocationName());
            }
            if (StringUtils.isNotEmpty(input.getLink())) {
                appleLocation.setLink(input.getLink());
            }
            if (StringUtils.isNotEmpty(input.getAddress())) {
                appleLocation.setSingleLineAddress(StringUtils.isNotEmpty(input.getAddress()) ? input.getAddress().replaceAll(", ,",",").replaceAll(",,",",") : input.getAddress());
            }
            if (StringUtils.isNotEmpty(input.getPhone())) {
                appleLocation.setPrimaryPhone(input.getPhone());
            }
            appleLocation.setUpdatedAt(new Date());
            logger.info("Updated apple location details: {}", appleLocation);
            appleLocationRepo.saveAndFlush(appleLocation);
            commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(),
                    Collections.singletonList(appleLocation), null, appleLocation.getBusinessId());
        } else {
            logger.info("No apple location found for businessId: {}", input.getBeBusinessId());
        }
    }

    @Override
    public Map<String, String> getBusinessNameList(Long enterpriseId) {
        Map<String, String> businessNameList;
        List<BusinessAppleLocation> appleLocations = appleLocationRepo.findByEnterpriseId(enterpriseId);
        businessNameList = appleLocations.stream()
                .collect(Collectors.toMap(
                        BusinessAppleLocation::getAppleBusinessName,
                        BusinessAppleLocation::getAppleBusinessId,
                        (existingValue, newValue) -> existingValue));
        Map<String, String> sortedBusinessNameList = new TreeMap<>(businessNameList);
        logger.info("List of business for enterpriseId: {}: {}", enterpriseId, sortedBusinessNameList);
        return sortedBusinessNameList;
    }

    @Override
    public DelegatedAppleAccount getDelegatedBusinessList(Integer pageNumber) {
        DelegatedAppleAccount delegatedAppleAccount = null;
        try {
            Page<BusinessAppleLocation> appleBusinessConnectLocationsPage = appleLocationRepo
                    .findByIsValidAndBusinessIdNotNull(1, new PageRequest(pageNumber, PAGE_SIZE));
            if (Objects.nonNull(appleBusinessConnectLocationsPage)) {
                logger.info("appleBusinessConnectLocationsPage present");
                delegatedAppleAccount = DelegatedAppleAccount.builder()
                        .totalPages(appleBusinessConnectLocationsPage.getTotalPages()).build();
                List<BusinessAppleLocation> appleLocations = appleBusinessConnectLocationsPage.getContent();
                if (CollectionUtils.isNotEmpty(appleLocations)) {
                    logger.info("appleLocations present");
                    List<AppleAccountInfo> delegatedAppleAccountsInfo = appleLocations.stream()
                            .map(AppleAccountInfo::new).collect(Collectors.toList());
                    delegatedAppleAccount.setAppleAccountInfo(delegatedAppleAccountsInfo);
                }
            }
            logger.info("List of delegated apple accounts: {}", delegatedAppleAccount);

        } catch (Exception e) {
            logger.error("Exception while getting list of delegated apple business accounts: ", e);
        }
        return delegatedAppleAccount;
    }

    @Override
    public DelegatedAppleAccount getDelegatedBusiness(Long enterpriseId, String appleCompanyId,
            String appleBusinessId) {
        DelegatedAppleAccount delegatedAppleAccount = new DelegatedAppleAccount();
        try {
            List<BusinessAppleLocation> appleLocations;
            if (StringUtils.isNotEmpty(appleCompanyId) && StringUtils.isNotEmpty(appleBusinessId)) {
                appleLocations = appleLocationRepo.findByEnterpriseIdAndAppleCompanyIdAndAppleBusinessId(enterpriseId,
                        appleCompanyId, appleBusinessId);
            } else {
                appleLocations = appleLocationRepo.findByEnterpriseId(enterpriseId);
            }
            Set<String> usedAppleBusinessId = new HashSet<>();
            List<AppleAccountInfo> appleAccountInfos = new ArrayList<>();
            appleLocations.forEach(appleLocation -> {
                if (!usedAppleBusinessId.contains(appleLocation.getAppleBusinessId())) {
                    usedAppleBusinessId.add(appleLocation.getAppleBusinessId());
                    AppleAccountInfo appleAccountInfo = AppleAccountInfo.builder()
                            .appleLocationId(appleLocation.getAppleBusinessId())
                            .appleCompanyId(appleLocation.getAppleCompanyId())
                            .birdeyeAccountId(appleLocation.getEnterpriseId())
                            .build();
                    appleAccountInfos.add(appleAccountInfo);
                }
            });
            delegatedAppleAccount.setAppleAccountInfo(appleAccountInfos);
            logger.info("Delegated business response: {}", delegatedAppleAccount);
        } catch (Exception e) {
            logger.info("Exception while getting delegated business: ", e);
        }
        return delegatedAppleAccount;
    }

    @Override
    public AppleBrandMappingList getBrandMappingPages(String brandName, LocationMappingRequest request) {
        logger.info("getLocationMappingPages for enterprise {}", request.getBusinessId());
        if (request.getBusinessIds() != null) {
            logger.info("total number business ids received as input: {} , {}", request.getBusinessIds().size(),
                    request.getBusinessIds());
        }
        return appleAccountService.getBrandMappingPages(brandName, request);
    }

    @Override
    public Boolean getAppleEnabledStatus(Long enterpriseId) {
        List<BusinessAppleLocation> appleLocations = appleLocationRepo.findByEnterpriseId(enterpriseId);
        logger.info("Apple locations found for enterpriseId: {}: {}",enterpriseId,appleLocations.size());
        return CollectionUtils.isNotEmpty(appleLocations) && appleLocations.size() > 25 &&
                CacheManager.getInstance().getCache(SystemPropertiesCache.class).isAppleShowcaseEnabled();
    }

    @Override
    public List<String> getAppleLocationsFromBusiness(List<Integer> businessIds) {
        List<BusinessAppleLocation> appleLocations = appleLocationRepo.findByBusinessIdIn(businessIds);
        List<String> appleLocationIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(appleLocations)) {
            appleLocationIds = appleLocations.stream().map(BusinessAppleLocation::getAppleLocationId)
                    .collect(Collectors.toList());
        }
        logger.info("Apple location ids fetched: {}", appleLocationIds);
        return appleLocationIds;
    }

    @Override
    public List<String> getAppleLocationCTA(String appleLocationId) {
        List<String> response = new ArrayList<>();
        try {
            BusinessAppleLocation appleLocation = appleLocationRepo.findByAppleLocationId(appleLocationId);
            if (Objects.nonNull(appleLocation) && StringUtils.isNotEmpty(appleLocation.getCta())) {
                logger.info("appleCTA: {}",appleLocation.getCta());
                Map<String,String> appleCTA = JSONUtils.fromJSON(appleLocation.getCta(), Map.class);
                if (MapUtils.isNotEmpty(appleCTA)) {
                    response = new ArrayList<>(appleCTA.keySet());
                }
            }
        } catch (Exception e) {
            logger.info("Exception while getting list of CTA for locationId: {}: ",appleLocationId,e);
        }
        return  response;
    }

    @Override
    public void fetchAppleLocationCTA(Integer id) {
        BusinessAppleLocation appleLocation = appleLocationRepo.findById(id);
        fetchAppleLocationCTA(appleLocation);
    }

    private void fetchAppleLocationCTA(BusinessAppleLocation appleLocation) {
        if(Objects.nonNull(appleLocation)) {
            AppleCTAResponse appleCTAResponse = appleConnectService.getAppleCTA(appleLocation.getAppleCompanyId(),appleLocation.getAppleBusinessId(),appleLocation.getAppleLocationId());
            if(Objects.nonNull(appleCTAResponse) && CollectionUtils.isNotEmpty(appleCTAResponse.getData())) {
                Map<String,String> appleCtaMap = new HashMap<>();
                appleCTAResponse.getData().forEach(data -> {
                    String type = data.getType();
                    String appId = null;
                    if(CollectionUtils.isNotEmpty(data.getProvider())) {
                        appId = data.getProvider().get(0).getAppId();
                    }
                    appleCtaMap.put(type,appId);
                });
                logger.info("apple CTA map: {}",appleCtaMap);
                Map<String, String> ctaTextMap = appleCtaMap.entrySet().stream()
                        .collect(HashMap::new,
                                (resultMap, entry) -> {
                                    String updatedKey = appleCtaCategoryService.getButtonText(entry.getKey());
                                    resultMap.put(updatedKey, entry.getValue());
                                },
                                HashMap::putAll);
                logger.info("UI text mapped values: {}",ctaTextMap);
                appleLocation.setCta(JSONUtils.toJSON(ctaTextMap));
                appleLocationRepo.saveAndFlush(appleLocation);
            }
        }
    }

    @Override
    public void fetchAppleLocationCTAInit() {
        List<BusinessAppleLocation> appleLocations = appleLocationRepo.findAllByBusinessIdIsNotNull();
        logger.info("Apple locations count fetched for CTA fetch: {}",appleLocations.size());
        if(CollectionUtils.isNotEmpty(appleLocations)) {
            appleLocations.forEach(appleLocation -> {
                AppleCTAJobRequest appleCTAJobRequest = new AppleCTAJobRequest(appleLocation.getId());
                kafkaProducer.sendObjectV1(APPLE_CTA_FETCH,appleCTAJobRequest);
            });
        }
    }

    @Override
    public void updateAppleLocationCTA(AppleUpdateCTARequest appleUpdateCTARequest) {
        if(Objects.nonNull(appleUpdateCTARequest) && StringUtils.isNotEmpty(appleUpdateCTARequest.getAppleLocationId())) {
            BusinessAppleLocation appleLocation = appleLocationRepo.findByAppleLocationId(appleUpdateCTARequest.getAppleLocationId());
            fetchAppleLocationCTA(appleLocation);
        }
    }

    @Override
    public void updateAppleLocationLogoURL(AppleLogoURLRequest appleLogoURLRequest) {
        if(Objects.nonNull(appleLogoURLRequest) && Objects.nonNull(appleLogoURLRequest.getEnterpriseId())) {
//            BusinessLiteDTO business = businessCoreService.getBusinessLite(appleLogoURLRequest.getEnterpriseId(), false);
            List<BusinessAppleLocation> appleLocations = appleLocationRepo.findByAccountId(appleLogoURLRequest.getEnterpriseId());
            appleLocations.forEach(
                    location -> location.setLogoUrl(appleLogoURLRequest.getLogoUrl())
            );
            appleLocationRepo.save(appleLocations);
            appleLocationRepo.flush();
        }
    }
	public Integer getBusinessIdByAppleLocationId(String locationId) {
		logger.info("getBusinessIdByAppleLocationId called with locationId : {}", locationId);
		return appleLocationRepo.getBusinessIdByAppleLocationId(locationId);
	}

    @Override
    public AppleMapUnmapResponse mapUnmapPageForSmb(MappingOperationType mappingOperationType, String pageId, Long enterpriseId, Integer businessId) {
        try {
            AppleMapUnmapResponse response = new AppleMapUnmapResponse();
            switch (mappingOperationType) {
                case MAP:
                    if(StringUtils.isEmpty(pageId)) {
                        throw new BirdeyeSocialException("page id is required for operation type MAP");
                    }
                    logger.info("[Apple] Request received to map page: {} for enterpriseId: {}", pageId,enterpriseId);
                    mapPageForSmb(pageId, enterpriseId, businessId);
                    response.setSuccess(true);
                    break;
                case UNMAP:
                    logger.info("[Apple] Request received to unmap page for enterpriseId: {}",enterpriseId);
                    unmapPageForSmb(enterpriseId);
                    response.setSuccess(true);
                    break;
                default:
                    throw new BirdeyeSocialException("operation type "+mappingOperationType.getType()+" not supported");
            }

            return response;
        } catch (BirdeyeSocialException e) {
            return new AppleMapUnmapResponse(false, e.getMessage());
        } catch (Exception e) {
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR,e.getMessage());
        }
    }

    private List<BusinessAppleLocation> unmapPageForSmb(Long enterpriseId) {
        List<BusinessAppleLocation> appleLocationList = appleLocationRepo.findByEnterpriseId(enterpriseId);
        if(CollectionUtils.isEmpty(appleLocationList)) {
            logger.info("no locations found for enterprise id: {}", enterpriseId);
            return new ArrayList<>();
        }
        appleLocationList.forEach(appleLocation -> {
            if(Objects.nonNull(appleLocation.getBusinessId())) {
                commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(),
                        Collections.singletonList(appleLocation), null,
                        null);
            }
            appleLocation.setBusinessId(null);
        });
        appleLocationRepo.save(appleLocationList);
        appleLocationRepo.flush();
        return appleLocationList;
    }

    private void mapPageForSmb(String pageId, Long enterpriseId, Integer businessId) {
        List<BusinessAppleLocation> appleLocationList =  unmapPageForSmb(enterpriseId);
        if(CollectionUtils.isEmpty(appleLocationList)) {
            throw new BirdeyeSocialException("no location found");
        }
        List<BusinessAppleLocation> matchedAppleLocations = appleLocationList.stream().filter(s->pageId.equals(s.getAppleLocationId())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(matchedAppleLocations)) {
            logger.info("no locations found for pageId: {} and enterprise id: {}", pageId,enterpriseId);
            throw new BirdeyeSocialException("no location matched");
        } else if(matchedAppleLocations.size()>1) {
            logger.info("duplicate locations found for pageId: {} and enterprise id: {}", pageId,enterpriseId);
            throw new BirdeyeSocialException("duplicate location matched");
        } else {
            BusinessAppleLocation businessAppleLocation = matchedAppleLocations.get(0);
            businessAppleLocation.setBusinessId(businessId);
            appleLocationRepo.saveAndFlush(businessAppleLocation);
            commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(),
                    Collections.singletonList(businessAppleLocation), null,
                    businessAppleLocation.getBusinessId());
        }
    }



    @Override
    public void markAppleLocationInvalid(AppleDeleteLocationRequest appleDeleteLocationRequest) throws IOException {
        AppleErrorResponse[] appleErrorResponseList=appleConnectService.getAppleInvalidLocationStatus(appleDeleteLocationRequest);
        if(!ObjectUtils.isEmpty(appleErrorResponseList)
                && (Constants.APPLE_LOCATION_DELETED.equals(appleErrorResponseList[0].getCode())
                || Constants.APPLE_LOCATION_REJECTED.equals(appleErrorResponseList[0].getCode()))) {

            BusinessAppleLocation appleLocation = appleLocationRepo.findByAppleLocationId(appleDeleteLocationRequest.getLocationId());
            if (Objects.isNull(appleLocation)) {
                logger.error("For apple locationId {} no data found ", appleDeleteLocationRequest.getLocationId());
                throw new BirdeyeSocialException(ErrorCodes.APPLE_ACCOUNT_NOT_FOUND, "apple location data not found");
            }
            logger.info("Marking Apple location found for locationId: {} as invalid", appleLocation);
            appleLocation.setIsValid(0);
            appleLocationRepo.saveAndFlush(appleLocation);
//            BusinessInactiveAppleLocation inactiveLocation =  mapToInactiveLocationObject(appleLocation);
//            appleInactiveLocationRepo.saveAndFlush(inactiveLocation);
//            appleAccountService.removeAppleLocation(Arrays.asList(appleLocation.getAppleLocationId()));
        }
    }

    private BusinessInactiveAppleLocation mapToInactiveLocationObject(BusinessAppleLocation appleLocation) {
        BusinessInactiveAppleLocation businessInactiveAppleLocation= new BusinessInactiveAppleLocation();
        businessInactiveAppleLocation.setAppleLocationId(appleLocation.getAppleLocationId());
        businessInactiveAppleLocation.setAppleBusinessId(appleLocation.getAppleBusinessId());
        businessInactiveAppleLocation.setAppleCompanyId(appleLocation.getAppleCompanyId());
        businessInactiveAppleLocation.setAppleBusinessName(appleLocation.getAppleBusinessName());
        businessInactiveAppleLocation.setBusinessId(appleLocation.getBusinessId());
        businessInactiveAppleLocation.setCta(appleLocation.getCta());
        businessInactiveAppleLocation.setLink(appleLocation.getLink());
        businessInactiveAppleLocation.setAccountId(appleLocation.getAccountId());
        businessInactiveAppleLocation.setLocationName(appleLocation.getLocationName());
        businessInactiveAppleLocation.setLogoUrl(appleLocation.getLogoUrl());
        businessInactiveAppleLocation.setEnterpriseId(appleLocation.getEnterpriseId());
        businessInactiveAppleLocation.setPrimaryPhone(appleLocation.getPrimaryPhone());
        businessInactiveAppleLocation.setSingleLineAddress(appleLocation.getSingleLineAddress());
        businessInactiveAppleLocation.setAutoMapped(appleLocation.getAutoMapped());
        businessInactiveAppleLocation.setRequestId(appleLocation.getRequestId());
        businessInactiveAppleLocation.setNextSyncDate(appleLocation.getNextSyncDate());
        return businessInactiveAppleLocation;

    }
    @Override
    public void syncExistingLocations(AppleExistingLocationRequest input) {
        String lockKey = "apple".concat(Long.toString(input.getBeEnterpriseId()));
        boolean lockAvailable = redisService.tryToAcquireLock(lockKey);
        if(!lockAvailable) {
            logger.info("[Apple] lock for enterprise id: {} is already acquired.", input.getBeEnterpriseId());
            throw new BirdeyeSocialException("Lock already acquired");
        }
        try {
            if (CollectionUtils.isNotEmpty(input.getBusinessList())) {
                List<BusinessAppleLocation> businessAppleLocationList = appleLocationRepo.findByEnterpriseId(input.getBeEnterpriseId());
                Set<String> existingLocationIds = businessAppleLocationList.stream().map(BusinessAppleLocation::getAppleLocationId).collect(Collectors.toSet());
                logger.info("[Sync Locations] Existing apple locations for enterpriseId: {}: {}", input.getBeEnterpriseId(),
                        existingLocationIds);
                BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(input.getBeEnterpriseId());
                input.getBusinessList().forEach(business -> {
                    try {
                        List<AppleLocationDTO> locationDTOList = getAppleLocationDTOS(business);
                        if (CollectionUtils.isNotEmpty(locationDTOList)) {
                            for (AppleLocationDTO data:locationDTOList) {
                                if (Objects.nonNull(data) && Objects.nonNull(data.getLocationDetails())&& !existingLocationIds.contains(data.getId())) {
                                    logger.info("[Sync Locations] Saving data for apple location: {}", data);
                                    BusinessAppleLocation businessAppleLocation = createAppleLocationEntity(data,
                                            business.getBusinessName(), input.getBeEnterpriseId(), businessLiteDTO.getBusinessId(), null);
                                    appleLocationRepo.saveAndFlush(businessAppleLocation);
                                    commonService.sendAppleSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(),
                                            Collections.singletonList(businessAppleLocation), null,businessAppleLocation.getBusinessId());
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.info("[Sync Locations] Exception while saving locations for business: {}", business);
                    }
                });
            }
        } catch (BirdeyeSocialException e) {
            logger.info("[Sync Locations] Exception occurred while saving locations for business: {}, error: {}", input.getBeEnterpriseId(), e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.valueOf(e.getCode()), e.getMessage());
        } catch (Exception e) {
            logger.info("[Sync Locations] Exception occurred while saving locations for business: {}", input.getBeEnterpriseId());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, e.getMessage());
        } finally {
            redisService.release(lockKey);
        }
    }

    @NotNull
    private List<AppleLocationDTO> getAppleLocationDTOS(AppleExistingLocationRequest.BusinessList business) {
        String after = null;
        List<AppleLocationDTO> locationDTOList = new ArrayList<>();
        do {
            AppleLocationData appleLocationData = appleConnectService
                    .getLocationForBusiness(business.getCompanyId(), business.getBusinessId(), after);
            logger.info("Location fetched for businessId: {} are : {}", business.getBusinessId(), appleLocationData);
            if (Objects.nonNull(appleLocationData) && CollectionUtils.isNotEmpty(appleLocationData.getData())) {
                locationDTOList.addAll(appleLocationData.getData());
                after = Objects.nonNull(appleLocationData.getPagination()) && Objects.nonNull(appleLocationData.getPagination().getCursors())
                        ? appleLocationData.getPagination().getCursors().getAfter() : null;
            }
        } while (Objects.nonNull(after));
        return locationDTOList;
    }
    @Override
    public void updateAppleLocationState(AppleUpdateLocationStateRequest appleUpdateLocationStateRequest) {
        if(StringUtils.isEmpty(appleUpdateLocationStateRequest.getAppleLocationId()) || StringUtils.isEmpty(appleUpdateLocationStateRequest.getAppleLocationState())) {
            logger.error("Request data not found for apple locationId {} and state {}", appleUpdateLocationStateRequest.getAppleLocationId(), appleUpdateLocationStateRequest.getAppleLocationState());
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid request data");
        }
        BusinessAppleLocation appleLocation = appleLocationRepo.findByAppleLocationId(appleUpdateLocationStateRequest.getAppleLocationId());
        if (Objects.isNull(appleLocation)) {
            logger.error("For apple locationId {} no data found ", appleUpdateLocationStateRequest.getAppleLocationId());
            throw new BirdeyeSocialException(ErrorCodes.APPLE_ACCOUNT_NOT_FOUND, "apple location data not found");
        }
        if(AppleShowcaseStatusEnum.PUBLISHED.name().equalsIgnoreCase(appleUpdateLocationStateRequest.getAppleLocationState())) {
            logger.info("Marking Apple location found for locationId: {} as valid", appleLocation);
            appleLocation.setIsValid(1);
        } else {
            logger.info("Marking Apple location found for locationId: {} as invalid", appleLocation);
            appleLocation.setIsValid(0);
        }
        appleLocationRepo.saveAndFlush(appleLocation);
    }

    public AutoSuggesterPagesResponse fetchApplePages(Long enterpriseId) {
        logger.info("[Apple] Request received to fetch pages and enterpriseId {}", enterpriseId);
        if (enterpriseId == null) {
            return new AutoSuggesterPagesResponse();
        }
        AutoSuggesterPagesResponse autoSuggesterPagesResponse = new AutoSuggesterPagesResponse();
        List<SocialPageListInfo> socialPageListInfoList = appleAccountService.getUnmappedAppleLocationsByEnterpriseId(enterpriseId);
        logger.info("got apple pages for enterpriseId: {}, pages: {}", enterpriseId, CollectionUtils.isNotEmpty(socialPageListInfoList)?socialPageListInfoList:null);
        autoSuggesterPagesResponse.setPages(socialPageListInfoList);
        if(CollectionUtils.isNotEmpty(socialPageListInfoList)) {
            int mappedPagesCount =  socialPageListInfoList.stream().filter(SocialPageListInfo::getMapped).collect(Collectors.toList()).size();
            if(mappedPagesCount > 0) autoSuggesterPagesResponse.setPageMapped(true);
            else autoSuggesterPagesResponse.setPageMapped(false);
        } else {
            autoSuggesterPagesResponse.setPageMapped(false);
        }
        return autoSuggesterPagesResponse;
    }

    @Override
    public void updateAppleLocationLinks(List<String> appleLocationIds) {
        logger.info("[AppleSetupServiceImpl] Updating Apple location links for IDs: {}", appleLocationIds);
        for (String locationId : appleLocationIds) {
            logger.debug("[AppleSetupServiceImpl] Processing locationId: {}", locationId);
            BusinessAppleLocation appleLocation = appleLocationRepo.findByAppleLocationId(locationId);
            if (Objects.nonNull(appleLocation) && Objects.isNull(appleLocation.getLink())) {
                AppleLocationData appleLocationData = appleConnectService
                        .getLocationForBusiness(appleLocation.getAppleCompanyId(), appleLocation.getAppleBusinessId(), null);
                logger.info("[AppleSetupServiceImpl] Location fetched data is : {}", appleLocationData);
                if (Objects.nonNull(appleLocationData) && Objects.nonNull(appleLocationData.getData())) {
                    logger.debug("[AppleSetupServiceImpl] Link is null for locationId: {}, fetching data...", locationId);
                    Optional<AppleLocationDTO> matched = appleLocationData.getData().stream()
                            .filter(dto -> locationId.equals(dto.getId()))
                            .findFirst();
                    if (matched.isPresent() && Objects.nonNull(matched.get().getPlaceCardUrl())) {
                        logger.info("[AppleSetupServiceImpl] Updating link for locationId: {} to {}", locationId, matched.get().getPlaceCardUrl());
                        appleLocation.setLink(matched.get().getPlaceCardUrl());
                        appleLocationRepo.saveAndFlush(appleLocation);
                    }else {
                        logger.warn("[AppleSetupServiceImpl] No matching DTO with placeCardUrl found for locationId: {}", locationId);
                    }
                }  else {
                    logger.warn("[AppleSetupServiceImpl] No AppleLocationData found for locationId: {}", locationId);
                }
            }
        }
    }
}
