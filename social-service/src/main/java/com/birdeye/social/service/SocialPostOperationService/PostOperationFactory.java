package com.birdeye.social.service.SocialPostOperationService;

import com.birdeye.social.service.SocialCompetitorService.SocialCompetitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class PostOperationFactory {

    @Autowired
    List<PostOperation> postOperations;

    public Optional<PostOperation> getPostOperationChannel(String channel) {
        Map<String, PostOperation> operationMap = new HashMap<>();
        for(PostOperation postOperation : postOperations){
            operationMap.put(postOperation.channelName(), postOperation);
        }
        return Optional.ofNullable(operationMap.get(channel.toLowerCase()));
    }
}
