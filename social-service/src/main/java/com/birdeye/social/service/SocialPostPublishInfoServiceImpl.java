package com.birdeye.social.service;

import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.MentionData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SocialPostPublishInfoServiceImpl implements SocialPostPublishInfoService{

    @Autowired
    SocialPostInfoRepository socialPostInfoRepository;

    @Autowired
    SocialSitePostingService socialSitePostingService;

    @Override
    public SocialPostPublishInfo findById(Integer publishInfoId) {
        return socialPostInfoRepository.findOne(publishInfoId);
    }

    @Override
    public void save(SocialPostPublishInfo socialPostPublishInfo) {
        socialPostInfoRepository.save(socialPostPublishInfo);
    }

    @Override
    public void updateEnterpriseIdWhereEnterpriseId(Integer targetEnterpriseId, Integer sourceBusinessId,Integer targetBusinessId) {
        socialPostInfoRepository.updateEnterpriseIdAndBusinessId(sourceBusinessId,targetEnterpriseId,targetBusinessId);
    }

    @Override
    public void updateEnterpriseIdWhereEnterpriseIdAndApprovalIdIsNull(Integer targetEnterpriseId, Integer sourceBusinessId,Integer targetBusinessId) {
        socialPostInfoRepository.updateEnterpriseIdWhereEnterpriseIdAndApprovalIdIsNull(sourceBusinessId,targetEnterpriseId,targetBusinessId);
    }

    @Override
    public String getProfileData(String input, Integer businessId, List<MentionData> mentionData, Integer sourceId) {
     return socialSitePostingService.getProfileData(input, businessId, mentionData, sourceId);
    }
}
