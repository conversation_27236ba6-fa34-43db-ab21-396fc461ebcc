package com.birdeye.social.service;

import com.birdeye.social.external.request.google.*;
import com.birdeye.social.external.response.google.GMBLocationPageUrl;
import com.birdeye.social.external.response.google.GMBLocationPageUrlResponse;
import com.birdeye.social.model.GMBAttribute;
import com.birdeye.social.model.GMBAttributes;
import com.birdeye.social.model.GMBPlaceActionRequest;
import com.birdeye.social.model.gmb.GMBServiceArea;
import com.birdeye.social.model.gmb.GMBServiceAreaResponse;

import java.util.List;
import java.util.Map;

public interface GMBOldObjects {

    GMBLocationRequest.SpecialHoursForCore prepareSpecialHours(GMBLocationRequest.SpecialHours specialHours);
    
    GMBLocationRequest.BusinessHourForCore prepareRegularHours(GMBLocationRequest.BusinessHour regularHours);
    
    GMBCategoriesResponse convertToGMBCategoryResponse(GMBCategories categoriesList);
    
    List<GMBLocationRequest.CategoryResponse> convertCategoryListResponse(List<GMBLocationRequest.Category> category);
    
    GMBLocationRequest.CategoryResponse convertCategoryResponse(GMBLocationRequest.Category category);
    
    List<AttributeResponseForLocation> convertAttributes(List<Attribute> attributes);

    List<AttributeResponseForLocation> convertAttributesForPlaceAction(List<Attribute> attributes);

    GMBLocationPageUrlResponse convertMetaDataResponse(GMBLocationPageUrl metadata);

    List<AttributeMetadataResponse> convertAttribute(List<AttributeMetadata> attributeMetadata);

    GMBAttributesResponse convertToAttributeResponse(GMBAttributes gmbAttributes);

    GMBServiceAreaResponse convertServiceArea(GMBServiceArea serviceArea);

    ServiceList convertServiceListResponse(String locationUrl, List<ServiceItem> serviceItems);

    List<String> convertToAttributeMask(List<String> attributeMask);

    GMBAttribute convertAttributesRequestBody(List<AttributeResponseForLocation> attributes,String locationId);

    GMBPlaceActionRequest convertPlaceActionAttributesRequestBody(List<AttributeResponseForLocation> attributes);

    void convertPlaceActionAttributesRequestBody(Map<String, AttributeResponseForLocation> attributes, List<String> attributeMask, List<GMBPlaceActionRequest> updateRequest, List<GMBPlaceActionRequest> deleteRequest);

    GMBLocationRequest.Categories covertToRequestCategories(GMBLocationRequest.CategoryResponse googlePrimeryCategory, List<GMBLocationRequest.CategoryResponse> googleAdditionalCategory);

    GMBServiceArea convertServiceRequest(GMBServiceAreaResponse serviceArea);

    GMBAttribute convertToAttributeRequest(AttributeResponse gmbAttribute);
}
