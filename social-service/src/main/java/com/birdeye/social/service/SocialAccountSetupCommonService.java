package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.FireBaseConstants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.Status;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.AutoMapping;
import com.birdeye.social.entities.BusinessGetPageOpenUrlRequest;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.request.nexus.CheckStatusRequest;
import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.facebook.UserPermissions;
import com.birdeye.social.facebook.UserPermissionsData;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.sro.CheckStatusResponse;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

public abstract class SocialAccountSetupCommonService {

    private static Logger logger = LoggerFactory.getLogger(SocialAccountSetupCommonService.class);

    @Autowired
    private NexusService nexusService;

    @Autowired
    private FacebookService fbService;

    @Autowired
    private AutoMappingService autoMappingService;

    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private IBusinessCachedService businessService;

    @Autowired
    private ISocialAppService socialAppService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    public void pushCheckStatusInFirebase(String channel,String requestType,String status,Long businessId) {
        CheckStatusRequest checkStatusRequest = new CheckStatusRequest(status,requestType);
        nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialAccountCheckStatusTopic(channel,businessId),checkStatusRequest, businessId.toString());
        logger.info("pushed check status in firebase for business with requestType, status for channel :{} {} {} {}",businessId,requestType,status,channel);
    }

    public void pushCheckStatusInFirebase(String channel,String requestType,String status,Long businessId,boolean error) {
        CheckStatusRequest checkStatusRequest = new CheckStatusRequest(status,requestType,error);
        nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialAccountCheckStatusTopic(channel,businessId),checkStatusRequest, businessId.toString());

        logger.info("pushed check status in firebase for business with requestType, status , error for channel :{} {} {} {} {}",businessId,requestType,status,error,channel);
    }

    /**
     * @param channel
     * @param businessId
     * @param checkStatusRequest
     * checkStatusRequest can be obtained using Builder
     * CheckStatusRequest.toBuilder().with($->{
     *             $.status = "";
     *             $.statusType = "";
     *             $.error = false;
     *             $.accountId = "";
     *         }).build();
     * benefits will be that we can add new field easily without changing any method change and no impact for existing caller asWell
     */
    public void pushCheckStatusInFirebase(String channel,Long businessId,CheckStatusRequest checkStatusRequest) {

        nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialAccountCheckStatusTopic(channel,businessId),checkStatusRequest, businessId.toString());
        logger.info("pushed check status in firebase for business with requestType, status for channel :{} {} {} {}",businessId,checkStatusRequest.getStatusType(),checkStatusRequest.getStatus(),channel);
    }

    public CheckStatusResponse getIntegrationStatus(String channel, Long businessId, Boolean reconnectFlag) {
        String requestType = null;
        if(reconnectFlag) {
            requestType = "reconnect";
        } else {
            requestType = "connect";
        }
        CheckStatusResponse checkStatusResponse = new CheckStatusResponse();
        BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.getSocialChannelByName(channel).getName());
        if(Objects.nonNull(businessGetPageRequest)){
            if(businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_INITIAL.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_REFRESH.getName())){
                checkStatusResponse.setStatus(businessGetPageRequest.getStatus());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            } else if("connect".equalsIgnoreCase(businessGetPageRequest.getRequestType()) && (businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName()) || businessGetPageRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()))) {
                checkStatusResponse.setStatus(businessGetPageRequest.getStatus());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            } else {
                checkStatusResponse.setStatus(Status.COMPLETE.getName());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            }
        }else{
            checkStatusResponse.setStatus(Status.COMPLETE.getName());
            checkStatusResponse.setStatusType(requestType);
        }
        setAdditionalData(channel, businessId, checkStatusResponse);
        return checkStatusResponse;
    }

    public CheckStatusResponse getResellerIntegrationStatus(String channel, Long resellerId, Boolean reconnectFlag) {
        String requestType = null;
        if(reconnectFlag) {
            requestType = "reconnect";
        } else {
            requestType = "connect";
        }
        CheckStatusResponse checkStatusResponse = new CheckStatusResponse();
        BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByResellerIdAndChannel(resellerId, SocialChannel.getSocialChannelByName(channel).getName());
        logger.info("Get Page Request: {}", businessGetPageRequest);
        if(Objects.nonNull(businessGetPageRequest)){
            if(businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_INITIAL.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName()) ||
                    businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_REFRESH.getName())){
                checkStatusResponse.setStatus(businessGetPageRequest.getStatus());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            } else if("connect".equalsIgnoreCase(businessGetPageRequest.getRequestType()) && (businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName()) || businessGetPageRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()))) {
                checkStatusResponse.setStatus(businessGetPageRequest.getStatus());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            } else {
                checkStatusResponse.setStatus(Status.COMPLETE.getName());
                checkStatusResponse.setStatusType(businessGetPageRequest.getRequestType());
            }
        }else{
            checkStatusResponse.setStatus(Status.COMPLETE.getName());
            checkStatusResponse.setStatusType(requestType);
        }
        // set additional data in checkStatusResponse
            logger.info("checking for automapping for channel and business {} {}", channel, resellerId);
            AutoMapping autoMapping = autoMappingService.fetchResellerAutoMappingRequest(resellerId, channel);
            if(Objects.nonNull(autoMapping)){
                checkStatusResponse.setAutoMappingStatus(autoMapping.getStatus());
            }
            if(channel.equalsIgnoreCase(SocialChannel.GMB.getName())){
                boolean showAccount = showAccount(resellerId);
                checkStatusResponse.setShowAccount(showAccount);
            }

        return checkStatusResponse;
    }

    private void setAdditionalData(String channel, Long businessId, CheckStatusResponse checkStatusResponse) {
        if(channel.equalsIgnoreCase(SocialChannel.FACEBOOK.getName()) || channel.equalsIgnoreCase(SocialChannel.GMB.getName())){
            logger.info("checking for automapping for channel and business {} {}", channel, businessId);
            AutoMapping autoMapping = autoMappingService.fetchAutoMappingRequest(businessId, channel);
            if(Objects.nonNull(autoMapping)){
                checkStatusResponse.setAutoMappingStatus(autoMapping.getStatus());
            }
            if(channel.equalsIgnoreCase(SocialChannel.GMB.getName())){
                boolean showAccount = showAccount(businessId);
                checkStatusResponse.setShowAccount(showAccount);
            }
        }
    }

    public boolean showAccount(Long parentId){
        AtomicBoolean showAccount = new AtomicBoolean(false); // when multiple thread are accessing
            String config = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("gmb.showaccount.config");
        List list = JSONUtils.fromJSON(config,List.class);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(parentId);
        list.forEach(o -> {
            String type  = ((Map)o).get("type").toString();
            String value = ((Map)o).get("value").toString();
            if(type !=null && value !=null && !showAccount.get()){
                if("business_type".equalsIgnoreCase(type)){
                   if(value.contains("ALL")){
                       showAccount.set(true);
                   }else{
                       if(value.contains(businessLiteDTO.getAccountType())){
                           showAccount.set(true);
                       }
                   }
                }else if("business".equalsIgnoreCase(type)){
                    List<Integer> allowedBusinessIds = StringUtils.convertToList(value).stream().map(Integer::parseInt).collect(Collectors.toList());
                    List<Integer> businessHierarchyList = businessCoreService.getBusinessHierarchyList(businessLiteDTO.getBusinessId());
                    if (!CollectionUtils.isEmpty(businessHierarchyList) && !CollectionUtils.isEmpty(allowedBusinessIds) && !Collections.disjoint(allowedBusinessIds,businessHierarchyList)) {
                        showAccount.set(true);
                    }
                }
            }
        });
        return showAccount.get();
    }

    /**
     * check for existing last request
     * if no request exists then release lock
     * if request exists then check for status
     * if status is init,fetched - > return existing status
     * if status is complete or cancel then release lock and return complete connect without error
     * @param businessGetPageRequest
     * @param key
     */
    public void handleFailureLock(BusinessGetPageRequest businessGetPageRequest,String channel,String key,Long enterpriseId,String requestType){
        if(Objects.isNull(businessGetPageRequest)){
            redisService.release(key);
            pushCheckStatusInFirebase(channel,requestType,Status.COMPLETE.getName(),enterpriseId);
        }else if(businessGetPageRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) ||
                businessGetPageRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) ||
                businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName()) ||
                businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_INITIAL.getName()) ||
                businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_REFRESH.getName())){
            pushCheckStatusInFirebase(channel,businessGetPageRequest.getRequestType(),businessGetPageRequest.getStatus(),enterpriseId);
        }else{
            redisService.release(key);
            pushCheckStatusInFirebase(channel,businessGetPageRequest.getRequestType(),Status.COMPLETE.getName(), enterpriseId);
        }
    }

    public void handleFailureLockForOpenUrl(BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest,String channel,String key,Long enterpriseId,String requestType, String firebaseKey){
        if(Objects.isNull(businessGetPageOpenUrlRequest)){
            redisService.release(key);
            pushCheckStatusInFirebase(channel,requestType,Status.COMPLETE.getName(),enterpriseId);
        }else if(businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) ||
                businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) ||
                businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName()) ||
                businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_INITIAL.getName()) ||
                businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_REFRESH.getName())){
            //pushCheckStatusInFirebase(channel,businessGetPageOpenUrlRequest.getRequestType(),businessGetPageOpenUrlRequest.getStatus(),businessGetPageOpenUrlRequest.getEnterpriseId());
            nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,businessGetPageOpenUrlRequest.getStatus());
        }else{
            redisService.release(key);
            //pushCheckStatusInFirebase(channel,businessGetPageOpenUrlRequest.getRequestType(),Status.COMPLETE.getName(), businessGetPageOpenUrlRequest.getEnterpriseId());
            nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.COMPLETE.getName());
        }
    }

    public String getFacebookGraphApiBaseUrl() {
        String baseUrl = FacebookApis.GRAPH_API_BASE_WITH_VERSION;
        if (StringUtils.isEmpty(baseUrl)) {
            baseUrl = FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21;
        }
        return baseUrl;
    }

    public SocialAppCredsInfo getFacebookCreds() {
        SocialAppCredsInfo socialAppFbCreds = socialAppService.getFacebookAppSettings();
        return socialAppFbCreds;
    }

    public SocialAppCredsInfo getYoutubeCreds() {
        SocialAppCredsInfo socialAppFbCreds = socialAppService.getYoutubeAppSettings();
        return socialAppFbCreds;
    }

    /**
     * Method to get the permissions of page access token
     * @param business
     * @param acessToken
     * @return
     * @throws Exception
     */
    public String getPageTokenPermissions(Business business, String acessToken, List<String> pageId) throws Exception {
        ConsumerTokenAndSecret tokenAndSecret = commonService.getAppKeyAndToken(business, "facebook");
        String appAccessToken = tokenAndSecret.getAppAccessToken();
        DebugTokenResponse tokenResponse = fbService.getTokenDetails(acessToken, appAccessToken);
        if (tokenResponse == null || tokenResponse.getData() == null) {
            logger.error("Token response is null");
            return null;
        } else {
            List<String> tokenScopes = tokenResponse.getData().getScopes();
            return String.join(",", tokenScopes);
        }
    }

    public DebugTokenResponse getPageToken(Business business, String acessToken) throws Exception {
        ConsumerTokenAndSecret tokenAndSecret = commonService.getAppKeyAndToken(business, "facebook");
        String appAccessToken = tokenAndSecret.getAppAccessToken();
        return fbService.getTokenDetails(acessToken, appAccessToken);
    }
    public DebugTokenResponse getWhatsappPageToken(String acessToken) {
        ConsumerTokenAndSecret tokenAndSecret = commonService.getAppKeyAndTokenV2("facebook");
        String appAccessToken = tokenAndSecret.getAppAccessToken();
        return fbService.getTokenDetails(acessToken, appAccessToken);
    }


    public  String filterScopes(Map<String, List<String>> scopesMap, String pageId) {
        List<String> filteredScopes = new ArrayList<>();
        if(ObjectUtils.isEmpty(scopesMap))
            return null;

        for (Map.Entry<String, List<String>> entry : scopesMap.entrySet()) {
            String scope = entry.getKey();
            List<String> associatedPageIds = entry.getValue();

           // if (CollectionUtils.isEmpty(associatedPageIds) || associatedPageIds.contains(pageId) || associatedPageIds.contains(null)) {
                filteredScopes.add(scope);
           // }
        }
        return String.join(",", filteredScopes);
    }

    public Map<String,List<String>> getUserPageTokenPermissions(Business business, String acessToken) throws Exception {
        Map<String,List<String>> filteredScope = new HashMap<>();
        ConsumerTokenAndSecret tokenAndSecret = commonService.getAppKeyAndToken(business, "facebook");
        String appAccessToken = tokenAndSecret.getAppAccessToken();
        DebugTokenResponse tokenResponse = fbService.getTokenDetails(acessToken, appAccessToken);
        if (tokenResponse == null || tokenResponse.getData() == null) {
            logger.error("Token response is null");
            return null;
        } else {
            List<String> scopesArray=tokenResponse.getData().getScopes();
            List<GranularScope> granularScopesArray=tokenResponse.getData().getGranular_scopes();
            for (int i = 0; i < scopesArray.size(); i++) {
                String scope = scopesArray.get(i);
                List<String> associatedPageIds = getPageIdsForScope(scope, granularScopesArray);
                filteredScope.put(scope, associatedPageIds);
            }

            return filteredScope;
        }
    }
    private static List<String> getPageIdsForScope(String scope, List<GranularScope> granularScopesArray) {
        List<String> associatedPageIds = new ArrayList<>();

        for (int i = 0; i < granularScopesArray.size(); i++) {
            GranularScope granularScopeObject = granularScopesArray.get(i);
            String granularScope = granularScopeObject.getScope();
            List<String> targetIdsArray = granularScopeObject.getTarget_ids();

            if (StringUtils.isNotEmpty(granularScope) && granularScope.equals(scope)) {
                if (targetIdsArray != null) {
                for (int j = 0; j < targetIdsArray.size(); j++) {
                    String pageId = targetIdsArray.get(j);
                    associatedPageIds.add(pageId);
                }
                // If targetIdsArray is empty, it implies the scope applies to all targets
                } else {
                    associatedPageIds.add(null);  // Set null value to indicate it applies to all targets
                }
                break;  // No need to check further granular scopes for this scope
            }
        }

        return associatedPageIds;
    }





    public String createUserTokenPermissions(FbUserProfileInfo user) {
        String userTokenPermissions = "";
        if (user!=null && user.getPermissions() != null) {
            UserPermissions userPermissions = user.getPermissions();
            List<UserPermissionsData> permissionList = userPermissions.getData();
            List<String> permissions = new ArrayList<>();
            for (UserPermissionsData permission : permissionList) {
                permissions.add(permission.getPermission()+":"+permission.getStatus());
            }
            userTokenPermissions = String.join(",", permissions);
        }
        return userTokenPermissions;
    }

    public boolean checkBusinessSMB(BusinessLiteDTO business) {
        return ("Business".equals(business.getType()) || "Product".equals(business.getType()) && business.getEnterpriseId()==null);
    }

    public boolean checkBusinessReseller(BusinessLiteDTO business) {
        return ("Business".equals(business.getType()) || "Product".equals(business.getType()));
    }

    public List<BusinessGetPageOpenUrlRequest> getRequestForBusinessOpenUrl(Long businessId, String status) {
        return businessGetPageOpenUrlReqRepo.findByEnterpriseIdAndStatusAndChannel(businessId, status, SocialChannel.TWITTER.getName());
    }

    public BusinessGetPageOpenUrlRequest checkInProgressRequestsOpenUrl(Long businessId) {
        logger.info("Exception occurred in connect facebook, Checking for in progress request for business id {}", businessId);
        List<BusinessGetPageOpenUrlRequest> underProcessRequests = getRequestForBusinessOpenUrl(businessId, Status.INITIAL.getName());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(underProcessRequests)) {
            return underProcessRequests.get(0);
        }
        return null;
    }

    public List<BusinessGetPageOpenUrlRequest> getRequestForOpenUrlBusiness(Long businessId, String status, String channel, String requestType, String firebaseKey) {
        return businessGetPageOpenUrlReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestTypeAndFirebaseKey(businessId, status, channel, requestType, firebaseKey);
    }

    public void handleCleanupRedisForOpenurl(String key, ChannelAuthOpenUrlRequest authRequest, Long businessId) {
        redisService.release(key);
        nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(), authRequest.getFirebaseKey(),Status.CANCEL.getName());

        logger.error("[Redis Lock] Lock released for business {}", businessId);
        BusinessGetPageOpenUrlRequest reqGMB = businessGetPageOpenUrlReqRepo.findByFirebaseKey(authRequest.getFirebaseKey());
        if (reqGMB != null) {
            reqGMB.setStatus(Status.CANCEL.getName());
            businessGetPageOpenUrlReqRepo.saveAndFlush(reqGMB);
        }
        throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
    }
    public void cancelRequest(BusinessGetPageRequest request, Boolean forceCancel) {
        Long parentId = Objects.nonNull(request.getEnterpriseId()) ? request.getEnterpriseId() : request.getResellerId();
        if(!forceCancel && Status.INITIAL.getName().equals(request.getStatus())) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
        }
        request.setStatus(Status.CANCEL.getName());
        request.setUpdated(new Date());
        businessGetPageReqRepo.saveAndFlush(request);
        pushCheckStatusInFirebase(request.getChannel(), request.getRequestType(), Status.COMPLETE.getName(), parentId);
        releaseLock(request.getChannel(), parentId);
    }

    private void releaseLock(String channel, Long parentId) {
        redisService.release(channel.concat(String.valueOf(parentId)));
    }
}
