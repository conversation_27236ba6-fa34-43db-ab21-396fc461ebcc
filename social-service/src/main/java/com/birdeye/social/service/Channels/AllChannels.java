package com.birdeye.social.service.Channels;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.LocationPagePair;
import com.birdeye.social.service.SocialReportService.AllChannels.AllChannelsInsights;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class AllChannels implements SocialInsights {

    @Autowired
    private AllChannelsInsights allChannelsInsights;

    private static final Logger log = LoggerFactory.getLogger(AllChannels.class);

    @Override
    public String channelName() {
        return "allChannels";
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {

    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {

    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO socialScanEventDTO) {

    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {

    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.isNull(searchTemplate)){
            return null;
        }
        return allChannelsInsights.getAllChannelInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) throws IOException {

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {

    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return null;
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) {
		// TODO Auto-generated method stub
		
	}

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {

    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return null;
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return false;
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        //No action for all channels
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        return null;
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return null;
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    // Not supported for this
    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return null;
    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.isNull(searchTemplate)){
            return null;
        }
        return allChannelsInsights.getAllChannelInsightsForTopPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds, List<LocationPagePair> locationPagePairList) {
        // No action for all channels
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList) {
        // No action for all channels
    }
}
