package com.birdeye.social.service;


import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.birdeye.social.dao.GMBLocationAuditRepo;
import com.birdeye.social.entities.GMBLocationAudit;
import com.birdeye.social.utils.StringUtils;


@Service
public class GMBLocationAuditService implements IGMBLocationAuditService {
		
	@Autowired
	GMBLocationAuditRepo	gmbLocationAuditRepo;
	
	@Override
	public void update(GMBLocationAudit gmbLocationAudit) {
			gmbLocationAuditRepo.saveAndFlush(gmbLocationAudit)	;
			return;
	}

	@Override
	public void create(Integer rawGmbLocationId, String gmbResponse, String locationId, String eventTriggered, String activity) {
		GMBLocationAudit gmbLCR = new GMBLocationAudit();
		gmbLCR.setGmbRawLocationId(rawGmbLocationId);
		gmbLCR.setGmbResponse(gmbResponse);
		gmbLCR.setLocationId(locationId);
		gmbLCR.setEventTriggered(eventTriggered);
		gmbLCR.setActivity(activity);
		gmbLocationAuditRepo.saveAndFlush(gmbLCR);
	}
	
	@Override
	public void create(Integer rawGmbLocationId, String gmbResponse, String locationId, String eventTriggered) {
		create(rawGmbLocationId, gmbResponse, locationId, eventTriggered, null);
	}
	
}
