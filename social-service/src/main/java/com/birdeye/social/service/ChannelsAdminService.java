package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.report.MonthlyScanDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

public interface ChannelsAdminService {

	List<SocialScanEventDTO> fetchEligibleRecords(Integer count, Integer startId, Date date) throws ParseException;

	void updateNextSyncDate(List<Integer> eligibleIds, Date nextDate);

	SocialChannel channelName();

	List<MonthlyScanDTO> fetchEligibleRecordsMonthly() throws ParseException;

	SocialScanEventDTO fetchChannelDetails(String channelId, Date date) throws ParseException;
}
