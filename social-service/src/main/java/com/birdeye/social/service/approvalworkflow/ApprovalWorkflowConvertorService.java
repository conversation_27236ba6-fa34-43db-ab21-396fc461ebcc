package com.birdeye.social.service.approvalworkflow;

import com.birdeye.social.entities.SocialPostScheduleInfo;
import com.birdeye.social.model.*;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.model.approval_workflow.ApprovalEventFromSocial;
import com.birdeye.social.model.approval_workflow.ApprovalWorkFlowData;
import com.birdeye.social.model.approval_workflow.ApprovalWorkflowResponse;
import com.birdeye.social.model.approval_workflow.ResetCountRequest;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;

import java.io.IOException;
import java.util.List;

public interface ApprovalWorkflowConvertorService {
    ApprovalEventFromSocial prepareCreateEventForApprovalWorkflow(SocialPostInputMessageRequest socialPost, Integer externalId);

    List<Integer> getIdsFromListOfChannels(List<String> socialChannels);

    ApprovalWorkflowResponse convertToApprovalResponse(List<SocialPostEsRequest> searchResponse,
                                                       Long businessNumber, Long totalCount, boolean isSearchText,
                                                       String searchText, Integer userId, String timeZone,
                                                       Integer businessId, String businessName, String requestSource);
    ApprovalEventFromSocial prepareEventForDeleteApprovalWorkflow(SocialPost socialPost, Integer enterpriseId);

    ApprovalEventFromSocial prepareEventForEditApprovalWorkflow(SocialPost socialPost, Integer enterpriseId, Integer approvalWorkflowId);

    ApprovalEventFromSocial prepareEventForExpiredApprovalWorkflow(SocialPost socialPost, Integer enterpriseId);
    
    BoolQueryBuilder prepareApprovalEsQuery(Integer userId, Integer businessId, List<String> approvalStatus);

    BoolQueryBuilder prepareResetQuery(ResetCountRequest resetCountRequest);

    ApprovalActivity prepareApprovalActivityRequest(ApprovalWorkflowEvent approvalWorkflowEvent, CurrentApprovalDetails currentApprovalDetails);

    ApprovalActivity prepareApprovalActivityRequest(ApprovalWorkflowEvent approvalWorkflowEvent, Long updatedAt);

    ApprovalWorkFlowData convertToApprovalPostResponse(SocialPost socialPost, List<PageDetail> pageDetails, SocialPostScheduleInfo socialPostScheduleInfo, Long enterpriseId, String timezone, String businessName) throws IOException;

    List<String> getPagesForBusinessIds(List<Integer> businessIds,List<Integer> socialChannels);

    ApprovalMetadata updateApprovalMetaData(String approvalMetadata, ApprovalWorkflowEvent approvalWorkflowEvent);

    ApprovalActivity prepareApprovalRejectedRequest(ApprovalWorkflowEvent approvalWorkflowEvent, Long updatedAt);

    SocialBusinessStatusResponse getAndConvertToBusinessIdResponse(List<Integer> socialChannels, List<Integer> businessIds);
}
