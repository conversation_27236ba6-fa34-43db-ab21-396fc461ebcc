package com.birdeye.social.service;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public interface CacheService {

    public void clearCacheByName(String cacheName);

    public void clearCacheByKey(String cacheName, String key);

    public void set(String key, String value, int ttl);

    /**
     *
     * @param key
     * @return
     */
    public Object get(String key);

    /**
     *
     * @param key
     */
    void del(String key);
    public void putInCache(String cacheName, String key, Serializable value);

    public Object get(String cacheName, String key);

}
