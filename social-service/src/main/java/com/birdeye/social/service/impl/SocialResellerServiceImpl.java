package com.birdeye.social.service.impl;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;

import com.birdeye.social.external.request.business.BusinessAccountLocationRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.factory.arbor.ArborServiceFactory;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.service.*;
import com.birdeye.social.service.resellerservice.ResellerFactory;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.service.doup.DoupConsumeRecords;
import com.birdeye.social.service.doup.DoupConsumeRecordsFactory;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.socialReseller.*;
import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.businessCore.BusinessCoreServiceImpl.*;
import static java.util.Comparator.nullsFirst;
import static org.apache.commons.collections4.CollectionUtils.isNotEmpty;

@Service("socialResellerService")
public class SocialResellerServiceImpl extends SocialAccountSetupCommonService implements SocialResellerService {

    private static final String CONNECT = "connect";

    private static Logger logger = LoggerFactory.getLogger(SocialResellerServiceImpl.class);

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Autowired
    private GMBLocationDetailService gmbLocationDetailService;

    @Autowired
    private IBusinessGetPageService businessGetPageService;

    @Autowired
    private FacebookSocialAccountService fbSocialAccountService;

    @Autowired
    private TwitterSocialAccountService twitterSocialAccountService;

    @Autowired
    private IInstragramSetupService socialInstagramService;

    @Autowired
    private SocialAccountService socialAccountService;

    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    @Autowired
    private IBusinessCoreService iBusinessCoreService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private SocialErrorMessagePageService socialErrorMessageService;

    @Autowired
    private DoupConsumeRecordsFactory doupConsumeRecordsFactory;

    @Autowired
    private ResellerFactory resellerFactory;

    @Autowired
    private BusinessUtilsService businessUtilService;

    @Autowired
    private SocialLinkedinService socialLinkedinService;

    @Autowired
    private YouTubeAccountService youTubeAccountService;

    @Autowired
    private CacheService cacheService;

    private static final String INVALID_REQUEST = "Invalid Request";

    @Autowired
    private IBusinessCoreService businessCoreService;


    @Override
    public void cancelRequest(String channel, Long resellerId, Boolean forceCancel) {
        logger.info("cancel request for channel {}  reseller id {}", channel, resellerId);
        BusinessGetPageRequest req;
        String channelName = SocialChannel.getSocialChannelByName(channel).getName();
        if(StringUtils.isNotEmpty(channelName)) {
            req = businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(resellerId, channelName, CONNECT);
            if(Objects.isNull(req)){
                logger.error("Business GetPageRequest Empty for resellerId {}", resellerId);
                return;
            }
        } else {
            logger.error("Channel not found {}", channel);
            return;
        }

        if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
        }

        if (SocialChannel.GOOGLE_PLUS_GMB.getName().equalsIgnoreCase(channelName) || SocialChannel.GMB.getName().equalsIgnoreCase(channelName)) {
            if(req.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())) {
                redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(resellerId)));
            } else {
                redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(resellerId)));
            }
        } else {
           redisService.release(channelName.concat(String.valueOf(resellerId)));
        }
        req.setStatus(Status.CANCEL.getName());
        req.setUpdated(new Date());
        businessGetPageReqRepo.saveAndFlush(req);
        pushCheckStatusInFirebase(channelName, req.getRequestType(), Status.COMPLETE.getName(), req.getResellerId());
    }


    @Override
    public void initiatePageRequest(String channel, ChannelAuthRequest authRequest,String type) throws Exception {
        logger.info("initiate request for channel {}  request {} ", channel, authRequest.toString());

        if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
            ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
            arborService.submitFetchPageRequest(authRequest, Constants.RESELLER);
        } else {
            SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            execute.submitFetchPageRequest(authRequest, type);
        }
    }

    @Override
    public void fetchAccountsForReseller(ChannelAuthRequest request,String type) {
        logger.info("initiate request for gmb request {} ", request.toString());
        googleSocialAccountService.submitFetchAccountRequest(request,type);
    }

    @Override
    public void reconnetPages(String channel, Long resellerId, ChannelAllPageReconnectRequest request, Integer userId,String type) throws Exception {
        logger.info("Initiate reconnect request for channel {}  request {} ", channel, request.toString());

        if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
            ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
            arborService.reconnectTiktokAccount(resellerId, request, userId, Constants.RESELLER);
        } else {
            //NOTE- There is no hit of google_plus_gmb channel for this request so removing it
            Integer limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).reconnectLimitForReseller();
            SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            execute.reconnectResellerPages(resellerId, request, userId, type, limit);
        }
    }


    @Override
    public GmbAccountInfo getGmbAccount(Long resellerId,String type) {
        logger.info("request received to get gmb accounts for reseller {}",resellerId);
        return googleSocialAccountService.getGmbAccount(resellerId,type);
    }

    @Override
    public void gmbPageFetchByAccount(GMBAccountDTO gmbAccountDTO, Long resellerId,String type) {
        logger.info("request received to initiate fetch page request for account {} and reseller {}",gmbAccountDTO.getAccountId(),resellerId);
        gmbLocationDetailService.gmbPageFetchByAccount(gmbAccountDTO,resellerId,type);
    }

    @Override
    public void gmbBackStatus(Long resellerId,String type) {
        logger.info("request received to change gmb request status to account fetched reseller {}",resellerId);
        googleSocialAccountService.gmbBackStatus(resellerId, type);
    }

    @Override
    public void validityCheck(ValidityRequestDTO request) {
        String channel = request.getChannel().toLowerCase();
        switch(channel) {
            case Constants.FACEBOOK:
                fbSocialAccountService.validityCheckForFB(request.getFacebookPageId());
                break;

            case Constants.GMB:
                gmbLocationDetailService.validityCheckForGMB(request.getLocationId(),false);
                break;

            case Constants.INSTAGRAM:
                socialInstagramService.validityCheckForIG(request.getIgAccountIds());
                break;

            case Constants.TWITTER:
                twitterSocialAccountService.validityCheckForX(request.getXProfileIds());
                break;

            case Constants.LINKEDIN:
                socialLinkedinService.validityCheckForLinkedin(request.getLinkedinProfileIds());
                break;

            case Constants.YOUTUBE:
                youTubeAccountService.validityCheckForYoutube(request.getYoutubeChannelIds());
                break;

            case Constants.TIKTOK:
                ArborService arborService = ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
                SocialTokenValidationDTO validationDTO = SocialTokenValidationDTO.builder()
                                                            .id(request.getId()).channel(Constants.TIKTOK).build();
                arborService.validateToken(validationDTO);
                break;

            default:
                logger.error("Channel not found {}", request.getChannel());
        }
    }

    @Override
    public void testnifi(Map<String, ?> request) {
        logger.info("request is {}", request);
        kafkaProducerService.sendWithKey("social-fb-bulk-import-data-process", "testkey", request);
    }

    @Transactional
    @Override
    public FetchPaginatedPageResponseForReseller getIntegrationPage(String channel, Integer page,Integer size,Long resellerId, String search) {
        logger.info("Request received to fetch integrated pages for business and channel {} {}",resellerId,channel);
        FetchPaginatedPageResponseForReseller fetchPaginatedPageResponse = new FetchPaginatedPageResponseForReseller();
        Map<String, Object> data;

        BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByResellerIdAndChannel(resellerId, SocialChannel.getSocialChannelByName(channel).getName());
        logger.info("BusinessGetPageRequest for reseller and channel {} {}", businessGetPageRequest, channel);
        if(Objects.isNull(businessGetPageRequest)){
            logger.info("No request exists for reseller and channel {} {}",resellerId, channel);
            throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND, "No request exists for reseller");
        }

        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        data = execute.getPaginatedPages(businessGetPageRequest, page, size, search);

        if (Objects.nonNull(data)) {
            fetchPaginatedPageResponse.setPageTypes((Map<String, List<ChannelAccountInfo>>) data.get("pageType"));
            fetchPaginatedPageResponse.setTotalCount((Long) data.get("totalCount"));
            fetchPaginatedPageResponse.setPageCount((Integer) data.get("pageCount"));
            fetchPaginatedPageResponse.setGmbAccountSize((Integer) data.get("gmbAccountSize"));
            fetchPaginatedPageResponse.setGmbGroup((String) data.get("gmb-group"));
        }

        return fetchPaginatedPageResponse;
    }

    @Override
    public void refreshGmbResellerAccount(String userEmail, Long parentId, String type) {
        logger.info("request received to refresh gmb accounts for user , business {} {}",userEmail,parentId);
        googleSocialAccountService.refreshGmbUserAccount(userEmail,parentId,type);
    }

    @Override
    public ChannelPageInfo connectPagesForReseller(String channel, Long resellerId, Map<String, List<String>> pageIds, Boolean selectAll, String searchStr) throws Exception  {

        if(SocialChannel.TIKTOK.getName().equalsIgnoreCase(channel)) {
            TwitterConnectAccountRequest tiktokConnectAccountRequest = createAccountRequest(pageIds.get(SocialChannel.TIKTOK.getName().toLowerCase()), resellerId);
            ArborService execute =  ArborServiceFactory.getService(SocialChannel.getSocialChannelByName(channel));
            return execute.connectPage(tiktokConnectAccountRequest);
        } else {
            //Note There is no request for google_plus_gmb so removing it
            SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            return execute.connectResellerPages(pageIds.get(execute.channelName()), resellerId, selectAll, searchStr);
        }
    }

    private TwitterConnectAccountRequest createAccountRequest(List<String> pageIds, Long enterpriseId) {

        TwitterConnectAccountRequest twitterConnectAccountRequest= new TwitterConnectAccountRequest();
        twitterConnectAccountRequest.setId(pageIds);
        twitterConnectAccountRequest.setBusinessId(enterpriseId);
        twitterConnectAccountRequest.setType(Constants.RESELLER);
        return twitterConnectAccountRequest;
    }




    @Override
    public PaginatedConnectedPages getPaginatedPages(String channel, Long resellerId, Integer userId, String type,
                                                     Integer page, Integer size, String search, ResellerSearchType searchType,
                                                     PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected) {
        if ( !PageConnectionStatus.contains(type) ) {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for type");
        }
        logger.info("Request received to get all selected pages for Reseller id {}, channel {}, user {}, pageConnectionStatus {}", resellerId, channel, userId, type);
        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        return  execute.getPages(resellerId, PageConnectionStatus.valueOf(type.toUpperCase()), page,
                size,search, searchType, sortDirection, sortParam, locationIds, mappingStatus, userIds, locationFilterSelected, Constants.RESELLER);
 }

    @Override
    public void removePage(String channel, List<LocationPageMappingRequest> input, Long resellerId, Integer userId) {
        logger.info("Request received to remove page from Social for Reseller, Input {}", input);
        Integer limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).deleteLimitForReseller();
        List<String> pageIds = new ArrayList<>();
        input.stream().forEach(i -> pageIds.add(i.getPageId()));
        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        execute.removeResellerPages(pageIds, limit);
    }

    @Override
    public void fetchGMBLocationsForReconnect(GMBAccountSyncRequest gmbAccountSyncRequest) {
        logger.info("Request received to reconnect page from Social for Reseller, Input {}", gmbAccountSyncRequest);
        gmbLocationDetailService.fetchGMBLocationsForReconnect(gmbAccountSyncRequest);
    }

    @Override

    public 	CheckStatusResponse getIntegrationRequestStatus(String channel, Long resellerId, Boolean reconnectFlag) {
        logger.info("Request received to check status for channel {} and reseller {}", channel, resellerId);
        return getResellerIntegrationStatus(channel, resellerId, reconnectFlag);
    }

    @Override
    public ChannelConnectedPageInfo checkForConnectedPages(Long accountId, String channel) {
        logger.info("Request received to check for connected pages for account {}, channel {}", accountId, channel);

        if (StringUtils.isEmpty(channel)) {
            ChannelConnectedPageInfo combinedResult = new ChannelConnectedPageInfo();
            for (SocialReseller execute : resellerFactory.getAllSocialReseller().values()) {
                ChannelConnectedPageInfo pageInfo = execute.checkIfAccountExistsByResellerId(accountId);
                prepareCombinedResult(combinedResult, pageInfo, execute.channelName());
            }
            return combinedResult;
        } else {
            SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            return execute.checkIfAccountExistsByResellerId(accountId);
        }
    }

    @Override
    public Object search(SearchDTO request, String channel) {
        logger.info("Request for search :{}",request);
        //  PageRequest pageRequest = new PageRequest(request.getPageNumber(),request.getSize());
        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            //return fbSocialAccountService.search(request,pageRequest);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            // return googleSocialAccountService.search(request,pageRequest);
        }
        return null;
    }

    @Override
    public ConnectPagesResponse getConnectPagesForReseller(String channel, Long resellerId, Integer size, Integer page) {
        logger.info("Request received to get pages for connected pages for reseller {}, channel {}", resellerId, channel);
        if (SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)) {
            return fbSocialAccountService.getPagesAfterConnect(resellerId,size,page);
        } else if (SocialChannel.GMB.getName().equalsIgnoreCase(channel)) {
            return googleSocialAccountService.getPagesAfterConnect(resellerId,size,page);
        }
        return null;
    }
     @Override
    public void processResellerPage(SocialResellerBulkImportDTO data, String channel) {
        BusinessLiteDTO businessDetails;
        Long enterpriseId;

        SocialResellerBulkStatusDTO operationStatus = new SocialResellerBulkStatusDTO();

        logger.info("Request received to map pages for channel {} and data is {}", channel, data);

        try {
            Long resellerId = Long.parseLong(data.getMetaData().getExtraParams().getBusinessNumber());
            Integer accountId = data.getMetaData().getAccountId();
            // fetching short business id of business provided by user
            businessDetails = iBusinessCoreService.getBusinessLiteByNumber(Long.parseLong(data.getData().getBusinessId()));

            List<Integer> businessHeirarchy  = businessCoreService.getBusinessHierarchyList(businessDetails.getBusinessId());
            if(CollectionUtils.isEmpty(businessHeirarchy) || !businessHeirarchy.contains(accountId)){
                logger.info("Inside business hierarchy error");
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BUSINESS_ERR.name(),SocialChannel.getSocialChannelByName(channel).getLabel());
                createDoupErrorResponse(data.getEventId(),errMessage,ResellerMappingStatusEnum.REJECTED.name());
                return;
            }

            // check if business is type is business/product
            if(!checkBusinessReseller(businessDetails)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.LOCATION_ERR.name(),SocialChannel.getSocialChannelByName(channel).getLabel());
                createDoupErrorResponse(data.getEventId(),errMessage,ResellerMappingStatusEnum.REJECTED.name());
                return;
            }

            // find parent business number of business provided by user
            List<Integer> accountIdSmallList = new ArrayList<>();
            enterpriseId = getEnterpriseIdParentEnterpriseBusinessNumber(businessDetails.getBusinessId(),accountIdSmallList);
            Integer accountIdSmall = null;
            if(isNotEmpty(accountIdSmallList)) {
                accountIdSmall = accountIdSmallList.get(0);
            }
            DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(channel)
                    .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
            operationStatus =  execute.mapPageWithLocation(data, resellerId, businessDetails, enterpriseId, accountIdSmall);
            kafkaProducerService.sendWithKey(KafkaTopicEnum.SOCIAL_CALLBACK_EVENT.getName(), Long.toString(data.getEventId()), operationStatus);
        } catch (Exception e) {
            logger.error("Something went wrong while processing the data {}", e.toString());
            String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.getSocialChannelByName(channel).getLabel());
            createDoupErrorResponse(data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
        }
    }

    private void createDoupErrorResponse(long eventId, String errMessage, String status) {
        SocialResellerBulkStatusDTO operationStatus = new SocialResellerBulkStatusDTO();
        operationStatus.setEventId(eventId);
        operationStatus.setStatus(status);
        operationStatus.setOperation(Constants.REJECTED);
        operationStatus.setErrorMessage(errMessage);
        kafkaProducerService.sendWithKey(KafkaTopicEnum.SOCIAL_CALLBACK_EVENT.getName(),  Long.toString(eventId), operationStatus );
    }


    private Long getEnterpriseIdParentEnterpriseBusinessNumber(Integer businessId, List<Integer> accountId) {
        Long enterpriseId = null;

        BusinessLiteDTO resellerParentBusinessDetails = iBusinessCoreService.getBusinessLite(businessId, false);

        if(Objects.isNull(resellerParentBusinessDetails.getEnterpriseId()) ) {
            enterpriseId = resellerParentBusinessDetails.getBusinessNumber();
            accountId.add(resellerParentBusinessDetails.getBusinessId());
        } else {
            //check for one level
            Integer parentBusinessId = resellerParentBusinessDetails.getEnterpriseId();
            resellerParentBusinessDetails = iBusinessCoreService.getBusinessLite(parentBusinessId, false);

            enterpriseId = resellerParentBusinessDetails.getBusinessNumber();
            accountId.add(resellerParentBusinessDetails.getBusinessId());

        }
        return enterpriseId;
    }

    public SocialResellerReportUploadDTO processMappingIntegrationReport(SocialResellerDoupRequestDTO data, Integer size, Integer page) {
        logger.info("Received request to process mapping integration for channel {} and request {}", data.getChannel(), data);
        DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(data.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        return execute.processMappingIntegrationReport(data.getResellerId(), size, page);
    }

    @Override
    public void findEnterpriseWithNoReseller() {
        try {
            fbSocialAccountService.updateEnterpriseWithNoReseller();
            googleSocialAccountService.updateEnterpriseWithNoReseller();
        } catch (Exception e) {
            logger.info("Someting went wrong while updating the data", e);
        }
    }

    @Override
    public void checkValidityForAllPages() {
        logger.info("Request received for check validity for all pages");
        try {
            googleSocialAccountService.validityCheckForAllGMBPages();
            fbSocialAccountService.validityCheckForAllFBPages();
        }catch (Exception e){
            logger.error("Unable to process request for check validity {}",e.getMessage());
        }
        logger.info("Process completed");
    }

    @Override
    public SocialResellerReportUploadDTO processLocationMappingIntegrationReport(SocialResellerDoupRequestDTO data, Integer size, Integer page, Integer userId) {
        DoupConsumeRecords execute = doupConsumeRecordsFactory.getDoupConsumeRecords(data.getChannel())
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        try {
            Integer businessId = iBusinessCoreService.getBusinessId(data.getResellerId());
           
            List<Integer> filteredLocationData = null;
            if(SocialChannel.LINKEDIN.getName().equals(data.getChannel()) || SocialChannel.YOUTUBE.getName().equals(data.getChannel())
                    || SocialChannel.FACEBOOK.getName().equals(data.getChannel())
                    || SocialChannel.GMB.getName().equals(data.getChannel())
                    || SocialChannel.TWITTER.getName().equals(data.getChannel())
                    || SocialChannel.INSTAGRAM.getName().equals(data.getChannel())
                    || SocialChannel.TIKTOK.getName().equals(data.getChannel())) {
            	 GetBusinessHierarchyList getBusinessHierarchyList = getBusinessHierarchyListRequest();
                 List<Long> bizHierarchyList = iBusinessCoreService.getBusinessSubHierarchyList(data.getResellerId(), getBusinessHierarchyList);
                 if(CollectionUtils.isEmpty(bizHierarchyList)) {
                 	 logger.error("Exception while fetching reseller sub hierarchy list from core for resellerId:{}",data.getResellerId());
                      throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No accounts found for given reseller businessId");
                 }
            	filteredLocationData = execute.getMappedLocations(bizHierarchyList);
            } else {
            	filteredLocationData = execute.processLocationUnMappingIntegration(data.getResellerId());
            }
            return mappingLocationData(businessId, userId, filteredLocationData, size, data.getProcessIdentifier(),data.getChannel());
        }catch (Exception e){
            logger.error("Exception while processing unmapped location for resellerId:{} and exception :{}",data.getResellerId(), e);
            throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No location found for given businessId");
        }
    }


	private GetBusinessHierarchyList getBusinessHierarchyListRequest() {
		GetBusinessHierarchyList businessHierarchyList = GetBusinessHierarchyList.builder().closedIncluded(false).enterpriseIncluded(true).resellerIncluded(true).returnBizNumber(true).skipEnterprise(false).skipEnterpriseLocation(true).skipReseller(true).smbincluded(true).subResellerIncluded(true).build();
		return businessHierarchyList;
	}

    private SocialResellerReportUploadDTO mappingLocationData( Integer accountId, Integer userId, List<Integer> filteredLocationData, Integer doupPageSize, String requestId,
                                                               String channelName) {
        String newRequestId;
        if(StringUtils.isEmpty(requestId)){
            newRequestId = Constants.RESELLER_BULK_MAPPING_CONSTANT.concat(channelName).concat("_");
        }
        else {
            newRequestId = requestId.concat("_");
        }
        Integer lastKeyIndex = (Integer) this.cacheService.get(Constants.RESELLER_BULK_MAPPING_FOLDER, newRequestId.concat(String.valueOf(accountId)));
        // Set default value if not present in Redis
        lastKeyIndex = (lastKeyIndex == null) ? 0 : lastKeyIndex;
        List<BusinessAccountLocationData> responseList = new ArrayList<>();
        int startIndex = lastKeyIndex;
        int lastIndexAdded = startIndex;
        Long totalLocations = 0L;
        Integer batchLimit=CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBusinessDetailsBatchSize();
        // Integer batchLimit = 10;
        try {
            while (responseList.size() < doupPageSize) {
                BusinessAccountLocationRequest businessAccountLocationRequest = createResellerLocationRequest(batchLimit, startIndex);
                BusinessAccountLocationResponse response = iBusinessCoreService.getAllLocationsDetailsUnderAccount(accountId, userId, businessAccountLocationRequest);
                if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
                    totalLocations = response.getTotalCount();
                    for (BusinessAccountLocationData data : response.getData()) {
                        lastIndexAdded++;
                        if (!filteredLocationData.contains(data.getId())) {
                            if (responseList.size() < doupPageSize) {
                                responseList.add(data);
                            }
                            else{
                                lastIndexAdded--;
                                break;
                            }
                        }
                    }

                    if (responseList.size() < doupPageSize) {
                        startIndex += batchLimit;
                    } else {
                        if (responseList.size() >= doupPageSize || responseList.size() < batchLimit) {
                            break;
                        }
                    }
                } else {
                    break;
                }
            }
        } catch (Exception ex) {
            logger.info("Exception occurred while processing location for accountId:{} and exception:{}",accountId,ex);
            evictBulkMappingCache(newRequestId.concat(String.valueOf(accountId)));
            throw new BirdeyeSocialException("Exception occurred while getting the unmapped locations",ex);
        }
        this.cacheService.putInCache(Constants.RESELLER_BULK_MAPPING_FOLDER, newRequestId.concat(String.valueOf(accountId)), lastIndexAdded);
        if (lastIndexAdded >= totalLocations)
            evictBulkMappingCache(newRequestId.concat(String.valueOf(accountId)));
        return mapLocationData(responseList);
    }

    private SocialResellerReportUploadDTO mapLocationData(List<BusinessAccountLocationData> responseList) {
        SocialResellerReportUploadDTO socialResellerReportUploadDTO= new SocialResellerReportUploadDTO();
        List<SocialResellerLocationDTO> socialResellerLocationDTOList =
                responseList.stream().map(data -> {
                    SocialResellerLocationDTO conf = new SocialResellerLocationDTO();
                    conf.setLocationName(data.getName());
                    conf.setLocationId(String.valueOf(data.getNumber()));
                    conf.setLocationAddress(data.getAddress());
                    return conf;
                } ).collect(Collectors.toList());
        socialResellerLocationDTOList.sort(Comparator.comparing(SocialResellerLocationDTO::getLocationName, nullsFirst(Comparator.naturalOrder())));
        socialResellerReportUploadDTO.setData(socialResellerLocationDTOList);
        return socialResellerReportUploadDTO;

    }


    private void evictBulkMappingCache(String key) {
        this.cacheService.clearCacheByKey(Constants.RESELLER_BULK_MAPPING_FOLDER,key);
    }

    private BusinessAccountLocationRequest createResellerLocationRequest(Integer batchLimit, int startIndex) {
        BusinessAccountLocationRequest businessAccountLocationRequest= new BusinessAccountLocationRequest();
        businessAccountLocationRequest.setRequestedSource(SOCIAL);
        businessAccountLocationRequest.setLimit(batchLimit);
        businessAccountLocationRequest.setStartIndex(startIndex);
        businessAccountLocationRequest.setStatus(Arrays.asList("active","demo"));
        return businessAccountLocationRequest;
    }

    @Override
    public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId,String pageType
                                                       ,Integer userId, Boolean force, Long resellerId) throws Exception {
        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        return  execute.saveLocationPageMapping(channel, locationId, pageId, pageType, userId, force, resellerId);
    }

    @Override
    public void removePageMappings(String channel, List<LocationPageMappingRequest> input) throws Exception {
        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
        execute.removePageMappings(input);
	}


    @Override
    public ResellerLeafLocationResponse getAllLocations(ResellerLeafLocationRequest requestMap, String channel, Integer accountId, Integer userId, Integer page, Integer size) throws Exception {
        logger.info("[Reseller] Request received for to getAllLocations with data {}: {}", accountId, requestMap);
        SocialReseller execute = resellerFactory.getSocialResellerChannel(channel)
                .orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));

        Integer businessId;
        try {
            businessId = requestMap.getBusinessId();

            BusinessAccountLocationRequest request
                    = createBusinessAccountLocationRequest(Arrays.asList(businessId), page, size, requestMap.getSearchStr(),
                    "social-locations", Arrays.asList("active","demo"));

            BusinessAccountLocationResponse response = getResellerLeafLocations(accountId, userId, request);

            List<BusinessAccountLocationData> locationDataList = (response.getData() == null) ? new ArrayList<>() : response.getData();
            List<Integer> resellerLeafLocationIds = locationDataList.stream().map(BusinessAccountLocationData::getId).collect(Collectors.toList());
            List<Integer> mappedLocationIds = execute.getMappedResellerLeafLocationIds(resellerLeafLocationIds);

            List<ResellerLeafLocation> resellerLeafLocations = new ArrayList<>();
            locationDataList.stream().forEach(location -> {
                boolean isCurrentLocationMapped = mappedLocationIds.contains(location.getId());
                resellerLeafLocations.add(new ResellerLeafLocation(location.getId(), location.getName(), location.getAddress(), isCurrentLocationMapped));
            });
            String accountName = (locationDataList.isEmpty()) ? null : locationDataList.get(0).getAccountName();
            return new ResellerLeafLocationResponse(response.getTotalCount(), page, businessId, accountName, resellerLeafLocations);
        } catch(BirdeyeSocialException e1) {
            logger.error("Unable to fetch all leaf location for reseller: {} with error: {}", accountId, e1.getMessage());
            throw(e1);
        } catch (Exception e2) {
            logger.error("Unable to fetch all leaf location for reseller: {} with error: {}", accountId, e2.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, e2.getMessage());
        }
    }



    private BusinessAccountLocationRequest createBusinessAccountLocationRequest(List<Integer> businessIds, Integer page, Integer size, String searchString,
                                                                                String requestedSource, List<String> status) {
        BusinessAccountLocationRequest request = new BusinessAccountLocationRequest();
        request.setBusinessIds(businessIds);
        request.setSearchStr(searchString);
        request.setRequestedSource(requestedSource);
        request.setStartIndex(page*size);
        request.setLimit(size);
        request.setStatus(status);
        request.setLocationsDeselected(0);
        return request;
    }


    private BusinessAccountLocationResponse getResellerLeafLocations(Integer resellerId, Integer userId, BusinessAccountLocationRequest request) {
        try {
            BusinessAccountLocationResponse response = iBusinessCoreService.getAllLocationsDetailsUnderAccount(resellerId, userId, request);
            if (Objects.nonNull(response)) {
                return response;
            } else {
                logger.error("Unable to fetch reseller leaf locations for: {}", resellerId);
                throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, "Error while fetching reseller leaf locations");
            }
        } catch (Exception e) {
            logger.error("Unable to fetch reseller leaf locations with error: {}", e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.ERROR_FETCHING_LEAF_LOCATIONS, e.getMessage());
        }
    }

    private void prepareCombinedResult(ChannelConnectedPageInfo combinedResult, ChannelConnectedPageInfo pageInfo, String channel) {
        if(Objects.isNull(pageInfo)) return;
        switch(channel) {
            case Constants.FACEBOOK:
                combinedResult.setFbPageExists(pageInfo.isFbPageExists());
                break;
            case Constants.INSTAGRAM:
                combinedResult.setIgAccountExists(pageInfo.isIgAccountExists());
                break;
            case Constants.GMB:
                combinedResult.setGmbLocationExists(pageInfo.isGmbLocationExists());
                break;
            case Constants.LINKEDIN:
                combinedResult.setLinkedinAccountExists(pageInfo.isLinkedinAccountExists());
                break;
            case Constants.TWITTER:
                combinedResult.setTwitterAccountExists(pageInfo.isTwitterAccountExists());
                break;
            case Constants.YOUTUBE:
                combinedResult.setYoutubeChannelsExists(pageInfo.isYoutubeChannelsExists());
                break;
            case Constants.TIKTOK:
                combinedResult.setTiktokChannelsExists(pageInfo.isTiktokChannelsExists());
                break;
        }
    }

}
