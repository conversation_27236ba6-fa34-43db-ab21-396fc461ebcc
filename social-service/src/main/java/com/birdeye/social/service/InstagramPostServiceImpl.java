package com.birdeye.social.service;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.SocialStreams;
import com.birdeye.social.model.*;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("InstagramPostServiceImpl")
public class InstagramPostServiceImpl implements ChannelPostService {

    @Autowired
    private BusinessInstagramAccountRepository socialIgRepo;

    @Autowired
    private IInstagramService instagramService;

    private static final Logger log = LoggerFactory.getLogger(InstagramPostServiceImpl.class);

    @Override
    public SocialChannel channelName() {
        return SocialChannel.INSTAGRAM;
    }

    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        try {
            BusinessInstagramAccount page =
                    socialIgRepo.findById(data.getChannelPrimaryId());
            if (page == null) {
                log.info("No social enabled instagram page found for pageId: {}", data.getChannelPrimaryId());
                return null;
            }

            log.info("[Social Report] request received to scan for instagram page {}", page.getInstagramAccountId());

            SocialTimeline socialTimeline;
            socialTimeline = instagramService.getInstagramTimeline(page, SocialStreams.StreamType.MY_POSTS.getType(), null, lastPostDate, false, 100, false);

            //Getting IG story
            socialTimeline = instagramService.getInstagramStories(page,socialTimeline);

            return socialTimeline;
        } catch (Exception ex) {
            log.info("Social feed data exception caught for data {} with error {}", data, ex);
            return null;
        }
    }

    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> instagramAccountIds) {
        return socialIgRepo.findInstagramAccountIdAndEnterpriseIdbyInstagramAccountIds(instagramAccountIds);
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessInstagramAccount> instagramAccounts = socialIgRepo.findByInstagramAccountIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(instagramAccounts)) {
            pageDetails.addAll(instagramAccounts.stream().map(s -> new PageDetail(s.getInstagramAccountId(),
                    s.getInstagramAccountName(), s.getInstagramAccountPictureUrl(), s.getBusinessId())).collect(Collectors.toList()));
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        Feed feed = new Feed();
        feed.setFeedId(request.getPostId());
        SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(socialPost.getPostMetadata(), SocialPostSchedulerMetadata.class);

        if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
            IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);

            if (Objects.nonNull(igPostMetadata)) {
                String postType = igPostMetadata.getType();

                if ("reel".equals(postType)) {
                    feed.setReel(true);
                } else if ("story".equals(postType)) {
                    feed.setStory(true);
                }
            }
        }

        return feed;
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        BusinessInstagramAccount businessInstagramAccount = socialIgRepo.findByInstagramAccountId(pageId);
        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setChannelPrimaryId(businessInstagramAccount.getId());
        scanEventDTO.setBusinessId(businessInstagramAccount.getBusinessId());
        scanEventDTO.setEnterpriseId(businessInstagramAccount.getEnterpriseId());
        scanEventDTO.setExternalId(businessInstagramAccount.getInstagramAccountId());
        scanEventDTO.setPageName(businessInstagramAccount.getInstagramAccountName());
        scanEventDTO.setSourceName(SocialChannel.INSTAGRAM.getName());
        scanEventDTO.setSourceId(SocialChannel.INSTAGRAM.getId());
        return scanEventDTO;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        return Collections.emptyList();
    }
}
