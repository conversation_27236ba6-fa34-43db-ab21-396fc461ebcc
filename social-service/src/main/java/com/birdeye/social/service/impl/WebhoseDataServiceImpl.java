package com.birdeye.social.service.impl;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.GoogleResponseStatus;
import com.birdeye.social.dao.BusinessKeywordMappingRepository;
import com.birdeye.social.dao.WebhoseRepository;
import com.birdeye.social.entities.BusinessKeywordMapping;
import com.birdeye.social.entities.WebhoseRaw;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.google.GoogleApiKeys;
import com.birdeye.social.response.GoogleReviewResponse;
import com.birdeye.social.service.WebhoseDataService;

import com.birdeye.social.utils.BusinessUtilsService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.webhose.request.WebhoseFetchRequest;
import com.birdeye.social.webhose.utils.WebHoseUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class WebhoseDataServiceImpl implements WebhoseDataService
{
    private final Logger LOGGER = LoggerFactory.getLogger(WebhoseDataServiceImpl.class);

    @Autowired
    WebhoseRepository webhoseRepository;

	@Autowired
	private KafkaProducerService producer;

	@Autowired
	private BusinessUtilsService businessUtilService;

	@Autowired
	private BusinessKeywordMappingRepository keywordRepo;

	@Value("${webhose.kafka.request.topic}")
	private String webhoseRequestTopic;
    
	@Override
	public List<WebhoseRaw> findByRequestInfoId(Integer webhoseRequestId) {
		LOGGER.info("fetching webhose saved posts for request ID : {}",webhoseRequestId);
		return webhoseRepository.findByRequestInfoId(webhoseRequestId); 
	}

	@Override
	public List<WebhoseRaw> saveWebhoseListData(List<WebhoseRaw> webhoseRawDataList) {
		return webhoseRepository.save(webhoseRawDataList);
		
	}
	
	@Override
	public WebhoseRaw saveWebhoseData(WebhoseRaw webhoseRawData) {
		return webhoseRepository.save(webhoseRawData);
		
	}

	@Override
	public void fetchWebhoseKeywords() {
		LOGGER.info("Request received to get Webhose mentions");
		Set<Integer> enterpriseList = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getWebhoseEnabledEnterprises();
		
		List<BusinessKeywordMapping> businessKeywordMappings;
		if (enterpriseList.isEmpty()) {
			businessKeywordMappings = keywordRepo.findByStatusAndOrderByIsExcludedDesc("active");
		}else {
			businessKeywordMappings = keywordRepo.findByStatusAndBusinessIdInOrderByIsExcludedDesc("active",enterpriseList);
		}
		LOGGER.info("Count of Active Business gnip rules received for Webhose is {}", businessKeywordMappings.size());
		Map<Integer, List<String>> exclusionKeywordsDetails = businessUtilService.getExclusionKeywordsDetails(businessKeywordMappings);
		LOGGER.info("Count of excluded keywords received for Webhose is {}", exclusionKeywordsDetails.size());
		prepareRequestAndSubmitToKafka(businessKeywordMappings,exclusionKeywordsDetails);
	}

	private void prepareRequestAndSubmitToKafka(List<BusinessKeywordMapping> businessKeywordMappings, Map<Integer, List<String>> exclusionKeywordsDetails){
		List<WebhoseFetchRequest> webHoseKafkaRequest = WebHoseUtils.prepareKafkaRequest(businessKeywordMappings,exclusionKeywordsDetails);
		LOGGER.info("pushing request to kafka total at date : {} {}",webHoseKafkaRequest.size(),new Date().toString());
		webHoseKafkaRequest.forEach(request -> {
			boolean isProduced = producer.sendWithKey(webhoseRequestTopic,null,request);
			if(isProduced){
				LOGGER.info("activities data pushed to kafka for business {}",request.getBusinessId());
			}else{
				LOGGER.error("activities data failed to push to kafka for business {}",request.getBusinessId());
			}
		});
	}
}
