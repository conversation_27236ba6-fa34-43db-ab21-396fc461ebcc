package com.birdeye.social.service.approvalworkflow;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.PicturesqueResponse;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostApproval;
import com.birdeye.social.entities.SocialPostScheduleInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.InvalidSocialArgsException;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.request.media.Picturesque;
import com.birdeye.social.external.request.media.PicturesqueRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.factory.ChannelPostServiceFactory;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.lock.RedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.approval_workflow.*;
import com.birdeye.social.nexus.EmailDTO;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.service.*;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.SumAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;


import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;

@Service
@RequiredArgsConstructor
public class ApprovalWorkflowServiceImpl implements ApprovalWorkflowService {

    private final ApprovalWorkflowConvertorService approvalWorkflowConvertorService;
    private final EsService esService;
    private final IBusinessCoreService businessCoreService;
    private final KafkaProducerService kafkaProducerService;
    private final PicturesqueGen picturesqueGen;
    private final NexusService nexusService;
    private final SocialTagService socialTagService;
    private final SocialPostApprovalRepo socialPostApprovalRepo;
    private final CommonService commonService;
    private final RestTemplate restTemplate;
    private final SocialPostRepository socialPostRepository;
    private final SocialPostScheduleInfoRepo socialPostScheduleInfoRepo;
    @Autowired
    private RedisLockService redisLockService;
    @Autowired
    private SocialMasterPostRepository socialMasterPostRepository;
    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepo;

    @Value("${aes.encryption}")
    private String postingKey;

    private static final String ES_PATTERN_DATE = "EEE, MMM dd, yyyy h:mm a";
    private static final String ES_APPLE_END_DATE = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    private static final String EMAIL_SUBJECT_PATTERN_DATE = "MMM dd";

    private static final Logger logger = LoggerFactory.getLogger(ApprovalWorkflowServiceImpl.class);

    @Override
    public ApprovalWorkflowResponse getAllApprovals(ApprovalWorkFlowRequest approvalRequest,Integer businessId,Long businessNumber,Integer userId,
                                                    Integer startIndex,Integer pageSize,String sortParam,String sortOrder, String requestSource) {
        ApprovalWorkflowResponse approvalWorkflowResponse = new ApprovalWorkflowResponse();
        approvalRequest.setBusinessId(businessId);
        List<String> pageIds;
        if(CollectionUtils.isEmpty(approvalRequest.getAccessibleLocationIds())) {
            logger.info("No business ids present in request :{}",approvalRequest);
            return approvalWorkflowResponse;
        }
        List<Integer> socialChannels = approvalWorkflowConvertorService.getIdsFromListOfChannels(approvalRequest.getSocialChannels());
        pageIds = approvalWorkflowConvertorService.getPagesForBusinessIds(approvalRequest.getAccessibleLocationIds(),socialChannels);
        if(CollectionUtils.isEmpty(pageIds)){
            logger.info("No pages present for business ids :{}",approvalRequest.getBusinessIds());
            return approvalWorkflowResponse;
        }
        ResetCountRequest request = new ResetCountRequest();
        BoolQueryBuilder boolQueryBuilder = createEsQuery(approvalRequest,userId,request,pageIds,socialChannels);
        Long totalCount = getTotalCount(businessId, boolQueryBuilder,
                sortParam, sortOrder, approvalWorkflowResponse);

        List<SocialPostEsRequest> searchResponse = searchForEsIndex(businessId, boolQueryBuilder,
                startIndex, pageSize, sortParam, sortOrder, approvalWorkflowResponse);
        searchResponse = createApprovalDataFromDB(searchResponse, sortParam, sortOrder);
        approvalWorkflowResponse = approvalWorkflowConvertorService.convertToApprovalResponse(searchResponse,
                businessNumber, totalCount, approvalRequest.isTextSearch(),
                approvalRequest.getSearchStr(),userId, null, businessId, null, requestSource);
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_RESET_APPROVAL_COUNT.getName(), request);
        logger.info("response for business number: {} and request: {}, response: {}", businessNumber, approvalRequest, approvalWorkflowResponse);
        return approvalWorkflowResponse;
    }

    private Long getTotalCount(Integer businessId,BoolQueryBuilder boolQueryBuilder,
                               String sortParam, String sortOrder,
                               ApprovalWorkflowResponse approvalWorkflowResponse) {
        List<Integer> socialPostIds = searchForEsIndexWithoutPagination(businessId, boolQueryBuilder,
                sortParam, sortOrder, approvalWorkflowResponse);

        long totalCount = socialPostApprovalRepo.countBySocialPostIdIn(socialPostIds);
        long esCount = Long.parseLong(String.valueOf(socialPostIds.size()));
        if(totalCount != esCount) {
            logger.info("Approval mismatch found in DB and ES for businessId: {}", businessId);
        }
        return totalCount;
    }

    public ApprovalWorkFlowData getApprovalDataFromPostId(Integer userId, Integer businessId, Long enterpriseId, Integer postId, boolean isPublicPost, String timeZone) {
        logger.info("Approval request fetch for post id : {} , user id : {} business id :{}",postId,userId,businessId);
        ApprovalWorkFlowData approvalWorkFlowData = new ApprovalWorkFlowData();
        BoolQueryBuilder boolQueryBuilder = createEsQueryForPostId(businessId,postId);
        List<SocialPostEsRequest> socialPostEsRequest = searchFromEsIndex(boolQueryBuilder, true);
        if(socialPostEsRequest.isEmpty()) {
            logger.info("No pending approval post found for postId: {}", postId);
            return null;
        }
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId,false);
        enterpriseId = businessLiteDTO.getBusinessNumber();
        String businessName = businessLiteDTO.getBusinessName();
        ApprovalWorkflowResponse approvalWorkflowResponse =
                approvalWorkflowConvertorService.convertToApprovalResponse(socialPostEsRequest,enterpriseId,0L,false,null,userId, timeZone, businessId, businessName,"");
        if(Objects.isNull(approvalWorkflowResponse) || CollectionUtils.isEmpty(approvalWorkflowResponse.getPosts())){
            logger.info("Data is not present for request post id : {} , user id : {} business id :{}",postId,userId,businessId);
            return approvalWorkFlowData;
        }
        approvalWorkFlowData = approvalWorkflowResponse.getPosts().get(0);
        approvalWorkFlowData.setCoBranded(BusinessAccountTypeEnum.COBRANDED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
        approvalWorkFlowData.setWhitelabel(BusinessAccountTypeEnum.WHITELABELED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
        // UI requirement for public form
        if(isPublicPost && (CollectionUtils.isNotEmpty(approvalWorkFlowData.getImageUrls())
                || CollectionUtils.isNotEmpty(approvalWorkFlowData.getVideoUrls()))) {
            List<String> mediaList = new ArrayList<>();
            List<Attachments> attachments = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(approvalWorkFlowData.getImageUrls())) {
                mediaList.addAll(approvalWorkFlowData.getImageUrls());
            } else {
                mediaList.addAll(approvalWorkFlowData.getVideoUrls());
            }
            if (CollectionUtils.isNotEmpty(approvalWorkFlowData.getVideoUrlsMetaData())) {
                approvalWorkFlowData.getVideoUrlsMetaData().forEach(video -> {
                    Attachments attachment = new Attachments();
                    attachment.setType(Constants.VIDEO.toLowerCase());
                    attachment.setMediaMetaData(video.getMediaMetaData());
                    attachment.setCompleteURL(video.getMediaUrl());
                    attachments.add(attachment);
                });
            } else {
                approvalWorkFlowData.getImageUrlsMetaData().forEach(image -> {
                    Attachments attachment = new Attachments();
                    attachment.setType(Constants.IMAGE.toLowerCase());
                    attachment.setMediaMetaData(image.getMediaMetaData());
                    attachment.setCompleteURL(image.getMediaUrl());
                    attachments.add(attachment);
                });
            }
            approvalWorkFlowData.setMediaFiles(mediaList);
            approvalWorkFlowData.setAttachments(attachments);
        }
        boolean isReseller = checkBusinessIsReseller(approvalWorkFlowData.getBusinessNumber());
        approvalWorkFlowData.setReseller(isReseller);
        return approvalWorkFlowData;
    }

    @Override
    public void updateCountForApproval(SocialPostEsRequest socialPostEsRequest) {
        if(Objects.isNull(socialPostEsRequest) ||
                (CollectionUtils.isEmpty(socialPostEsRequest.getApproval_user_ids()) && ApprovalStatus.PENDING.getName().equals(socialPostEsRequest.getApproval_status()))){
            logger.info("Data can not be null");
            return;
        }
        logger.info("Update count for post id : {} and user id : {}",socialPostEsRequest.getId(),socialPostEsRequest.getApproval_user_ids());
        Integer enterpriseId = socialPostEsRequest.getEnterprise_id();
        Integer postId = socialPostEsRequest.getId();
        ApprovalStatus approvalStatus = ApprovalStatus.getApprovalStatusByName(socialPostEsRequest.getApproval_status());
        if(ApprovalStatus.TERMINATED.equals(approvalStatus)) {
            //entry for approved and rejected are stored as REJECTED only
            approvalStatus = ApprovalStatus.REJECTED;
        }
        if(Objects.isNull(enterpriseId)){
            logger.info("No status found in es request:{}",socialPostEsRequest);
            return;
        }

        // If post id contained in other than these approvers
        BoolQueryBuilder getPost = getPostByEnterpriseId(socialPostEsRequest.getEnterprise_id(),socialPostEsRequest.getId());
        List<ApprovalCounterDetails> searchApproversFromPostId = searchApprovalCounter(getPost);
        if(CollectionUtils.isNotEmpty(searchApproversFromPostId)){
            decreaseCount(socialPostEsRequest, searchApproversFromPostId);
        }
        if(ApprovalCountUpdateType.DELETE.getName().equalsIgnoreCase(socialPostEsRequest.getRequestType())) return;
        if(Objects.equals(approvalStatus,ApprovalStatus.REJECTED) || Objects.equals(approvalStatus,ApprovalStatus.TERMINATED)){
            BoolQueryBuilder rejectedBoolQueryBuilder = createEsCounterQuery(null,enterpriseId,approvalStatus.name());
            List<ApprovalCounterDetails> approvalCounterDetails = searchApprovalCounter(rejectedBoolQueryBuilder);
            updateEsCount(approvalCounterDetails,null,enterpriseId,approvalStatus,postId);
            return;
        }
        List<Integer> approvalUserIds = listOfIntegerFromApprovalIds(socialPostEsRequest.getApproval_user_ids());
        if(CollectionUtils.isEmpty(approvalUserIds)){
            logger.info("No user id found for request : {}",socialPostEsRequest);
            return;
        }
        ApprovalStatus finalApprovalStatus = approvalStatus;
        approvalUserIds.forEach(id -> {
            BoolQueryBuilder boolQueryBuilder = createEsCounterQuery(id,enterpriseId, finalApprovalStatus.name());
            List<ApprovalCounterDetails> approvalCounterDetails = searchApprovalCounter(boolQueryBuilder);
            updateEsCount(approvalCounterDetails,id,enterpriseId, finalApprovalStatus,postId);
        });
    }

    private void decreaseCount(SocialPostEsRequest socialPostEsRequest, List<ApprovalCounterDetails> searchApproversFromPostId) {
        logger.info("Posts found for post id : {} and enterprise id :{}", socialPostEsRequest.getId(), socialPostEsRequest.getEnterprise_id());
        searchApproversFromPostId.forEach(approver -> {
            logger.info("For approver user id : {} removed the post id : {}",approver.getUserId(), socialPostEsRequest.getId());
            if(CollectionUtils.isNotEmpty(approver.getPostIds())
                    && approver.getCount() > 0
                    && approver.getPostIds().contains(socialPostEsRequest.getId())) {
                approver.getPostIds().remove(socialPostEsRequest.getId());
                approver.setCount(approver.getPostIds().size());
                upsertApprovalStatDocument(approver);
            }
        });
    }

    private void updateEsCount(List<ApprovalCounterDetails> approvalCounterDetails,Integer id , Integer enterpriseId,ApprovalStatus approvalStatus,Integer postId){
        ApprovalCounterDetails approval;
        if(CollectionUtils.isEmpty(approvalCounterDetails)){
            logger.info("Document not found for user id : {} and enterprise id :{}",id,enterpriseId);;
            approval = new ApprovalCounterDetails();
            approval.setApprovalStatus(approvalStatus.getName());
            approval.setUserId(id);
            approval.setPostIds(Collections.singleton(postId));
            approval.setCount(approval.getPostIds().size());
            approval.setEnterpriseId(enterpriseId);
            upsertApprovalStatDocument(approval);
        }else {
            for(ApprovalCounterDetails data : approvalCounterDetails){
                if(!approvalStatus.name().equals(data.getApprovalStatus())
                        && CollectionUtils.isNotEmpty(data.getPostIds())
                        && data.getPostIds().contains(postId)){
                    continue;
                }
                Set<Integer> postIds = data.getPostIds();
                if (CollectionUtils.isNotEmpty(data.getPostIds())) {
                    postIds.add(postId);
                    data.setPostIds(postIds);
                } else {
                    data.setPostIds(Collections.singleton(postId));
                }
                data.setCount(data.getPostIds().size());
                upsertApprovalStatDocument(data);
            }
        }
    }


    private void upsertApprovalStatDocument(ApprovalCounterDetails approver) {
        try {
            String doc = JSONUtils.toJSON(approver, JsonInclude.Include.NON_NULL);
            UpdateRequest request = new UpdateRequest(ElasticConstants.SOCIAL_APPROVAL_STATS.getName(), approver.getEnterpriseId()+"_"+approver.getUserId()+"_"+approver.getApprovalStatus());
            esService.upsertDocumentWithImmediateRefresh(doc, request);
            updateFirebase(approver);
        } catch (IOException e) {
            logger.info("IO Exception occurred while saving document :{}",approver, e);
        }
    }

    private void updateFirebase(ApprovalCounterDetails approver) {
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(approver.getEnterpriseId(), false);
        if(ApprovalStatus.PENDING.getName().equals(approver.getApprovalStatus())) {
            insertMapDataInFirebaseWithKey(FireBaseConstants.getApprovalApprovedCountPath(approver.getUserId()), "count",
                    approver.getCount(), Integer.toString(approver.getUserId()));
        } else if(ApprovalStatus.REJECTED.getName().equals(approver.getApprovalStatus())) {
            insertMapDataInFirebaseWithKey(FireBaseConstants.getApprovalRejectedAndTerminatedCountPath(businessLiteDTO.getBusinessNumber(),
                    ApprovalStatus.REJECTED), "count", approver.getCount(), Long.toString(businessLiteDTO.getBusinessNumber()));
        } else if(ApprovalStatus.TERMINATED.getName().equals(approver.getApprovalStatus())){
            insertMapDataInFirebaseWithKey(FireBaseConstants.getApprovalRejectedAndTerminatedCountPath(businessLiteDTO.getBusinessNumber(),
                    ApprovalStatus.TERMINATED), "count", approver.getCount(), Long.toString(businessLiteDTO.getBusinessNumber()));
        }
    }

    private void insertMapDataInFirebaseWithKey(String bucket, String key, Object value, String firebaseKey) {
        Map<String, Object> fireBaseMap = new HashMap<>();
        fireBaseMap.put(key, value);
        nexusService.insertDataInFirebaseWithKey(bucket, fireBaseMap, firebaseKey);
    }

    private List<Integer> listOfIntegerFromApprovalIds(List<String> approvalUserIds) {
        List<Integer> ids = new ArrayList<>();
        for(String id : approvalUserIds){
            try {
                Integer userId = Integer.parseInt(id);
                ids.add(userId);
            }catch (Exception e){
                logger.info("Unable to parse string : {}",approvalUserIds);
            }
        }
        return ids;
    }

    @Override
    public ApprovalCountResponse getApprovalCount(Integer userId, Integer businessId) {
        logger.info("Approval Count for User id :{} and businessId :{}",userId,businessId);
        ApprovalCountResponse approvalCountResponse = new ApprovalCountResponse();
        approvalCountResponse.setUserId(userId);
        //for pending posts
        BoolQueryBuilder pendingPostCount =
                approvalWorkflowConvertorService.prepareApprovalEsQuery(userId,businessId,Collections.singletonList(ApprovalStatus.PENDING.getName().toLowerCase()));
        ApprovalCounterDetails approvalCounterDetails = getESCounterForPending(pendingPostCount);
        if(Objects.nonNull(approvalCounterDetails)) {
            approvalCountResponse.setPendingCount(approvalCounterDetails.getCount());
        }
        // for rejected + terminated posts
        BoolQueryBuilder rejectedPostCount =
                approvalWorkflowConvertorService.prepareApprovalEsQuery(userId,businessId, Arrays.asList(ApprovalStatus.REJECTED.getName().toLowerCase(),ApprovalStatus.TERMINATED.name().toLowerCase()));
        SumAggregationBuilder aggregation = AggregationBuilders.sum(ApprovalEnum.count.name()).field(ApprovalEnum.count.name());
        Integer rejectedPosts = rejectedPostCountSearch(rejectedPostCount,aggregation);
        approvalCountResponse.setRejectedCount(rejectedPosts);
        return approvalCountResponse;
    }

    @Override
    public void resetApprovalCount(ResetCountRequest resetCountRequest) {
        logger.info("Request Received to reset count : {}",resetCountRequest);
        BoolQueryBuilder boolQueryBuilder = approvalWorkflowConvertorService.prepareResetQuery(resetCountRequest);
        int page = 0;
        int size = 1000;
        int count = 0;
        while (true) {
            count++;
            List<ApprovalCounterDetails> approvalCounterDetails = getApprovalCountDetails(boolQueryBuilder,page,size);
            if (CollectionUtils.isEmpty(approvalCounterDetails)) {
                logger.info("No value found for request : {}", resetCountRequest);
                break;
            }
            approvalCounterDetails.forEach(approval -> {
                approval.setCount(0);
                approval.setPostIds(new HashSet<>());
                upsertApprovalStatDocument(approval);
            });
            page = (count*size);
        }
    }

    private Integer rejectedPostCountSearch(BoolQueryBuilder rejectedPostCount, SumAggregationBuilder aggregation) {
        SearchSourceBuilder builder = new SearchSourceBuilder().query(rejectedPostCount).size(0).aggregation(aggregation);
        SearchRequest searchRequest = new SearchRequest().indices(ElasticConstants.SOCIAL_APPROVAL_STATS.getName()).source(builder);
        try {
            SearchResponse response = esService.search(searchRequest);
            ParsedSum parsedSum = response.getAggregations().get(ApprovalEnum.count.name());
            return (int) parsedSum.getValue();
        }catch (IOException e){
            logger.info("Exception occurred " , e);
        }
        return null;
    }

    private ApprovalCounterDetails getESCounterForPending(BoolQueryBuilder boolQueryBuilder) {
        ApprovalCounterDetails approvalCounterDetail = new ApprovalCounterDetails();
        List<ApprovalCounterDetails> approvalCounterDetails = getApprovalCountDetails(boolQueryBuilder);
        if(CollectionUtils.isEmpty(approvalCounterDetails)){
            logger.info("Approval Counter Details are null for request : {}",boolQueryBuilder);
            return approvalCounterDetail;
        }
        approvalCounterDetail = approvalCounterDetails.get(0);
        return approvalCounterDetail;
    }

    private List<ApprovalCounterDetails> getApprovalCountDetails(BoolQueryBuilder boolQueryBuilder) {
        List<ApprovalCounterDetails> approvalCounterDetails = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.size(1000);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.SOCIAL_APPROVAL_STATS.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                ApprovalCounterDetails approvalCounter = JSONUtils.fromJSON(hit.getSourceAsString(), ApprovalCounterDetails.class);
                approvalCounterDetails.add(approvalCounter);
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search ",exception);
        }
        return approvalCounterDetails;
    }

    private List<ApprovalCounterDetails> getApprovalCountDetails(BoolQueryBuilder boolQueryBuilder,Integer from,Integer to) {
        List<ApprovalCounterDetails> approvalCounterDetails = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(to);
        searchSourceBuilder.sort(ApprovalEnum.userId.name());
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.SOCIAL_APPROVAL_STATS.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                ApprovalCounterDetails approvalCounter = JSONUtils.fromJSON(hit.getSourceAsString(), ApprovalCounterDetails.class);
                approvalCounterDetails.add(approvalCounter);
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search ",exception);
        }
        return approvalCounterDetails;
    }

    private BoolQueryBuilder getPostByEnterpriseId(Integer enterpriseId, Integer postId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.postIds.name(),postId));
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterpriseId.name(),enterpriseId));
        return boolQueryBuilder;
    }

    private List<ApprovalCounterDetails> searchApprovalCounter(BoolQueryBuilder boolQueryBuilder) {
        List<ApprovalCounterDetails> approvalCounterDetails = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(10000);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.SOCIAL_APPROVAL_STATS.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                ApprovalCounterDetails approvalCounterDetail = JSONUtils.fromJSON(hit.getSourceAsString(), ApprovalCounterDetails.class);
                approvalCounterDetails.add(approvalCounterDetail);
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search ",exception);
        }
        return approvalCounterDetails;
    }

    private BoolQueryBuilder createEsCounterQuery(Integer userId, Integer enterpriseId,String approvalStatus) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if(Objects.nonNull(userId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.userId.name(), userId));
        }
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterpriseId.name(), enterpriseId));
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.approvalStatus.name(), approvalStatus.toLowerCase()));
        return boolQueryBuilder;
    }

    @Override
    public List<ApprovalWorkFlowData> getApprovalDataFromPostId(Integer userId, Integer businessId, Long enterpriseId, String postId, boolean isPublicPost, String timeZone) {
        List<String> encryptedPostIds;
        if(postId.contains(",")) {
            encryptedPostIds = Arrays.asList(postId.split(","));
        } else {
            encryptedPostIds = Collections.singletonList(postId);
        }
        List<Integer> decryptedPostIds = encryptedPostIds.stream().map(encryptedPostId -> {
                try {
                    return Integer.parseInt(decryptData(encryptedPostId));
                } catch (Exception e){
                    throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,"Unable to decrypt data from social");
                }
        }).collect(Collectors.toList());

        return decryptedPostIds.stream().map(decryptedPostId -> {
            return getApprovalDataFromPostId(userId, businessId, enterpriseId, decryptedPostId, isPublicPost, timeZone);
        }).filter(approvalWorkFlowData -> Objects.nonNull(approvalWorkFlowData)).collect(Collectors.toList());
    }

    @Override
    public ApprovalWorkFlowData getApprovalDataFromPostId(Integer postId, Long enterpriseId) throws IOException {
        logger.info("Fetching data for post id: {}", postId);

        SocialPost socialPost = socialPostRepository.findById(postId);
        if (Objects.isNull(socialPost)) {
            logger.info("No data found for post id: {}", postId);
            return new ApprovalWorkFlowData();
        }

        if (Objects.isNull(socialPost.getApprovalMetadata())) {
            logger.info("No approval metadata data found for post id: {}", postId);
            return new ApprovalWorkFlowData();
        }

        List<SocialPostScheduleInfo> socialPostScheduleInfoList = socialPostScheduleInfoRepo.findBySocialPostId(postId);
        if (CollectionUtils.isEmpty(socialPostScheduleInfoList)) {
            logger.info("No scheduling information found for post id: {}", postId);
            return new ApprovalWorkFlowData();
        }

        SocialPostScheduleInfo socialPostScheduleInfo = socialPostScheduleInfoList.get(0);
        Integer sourceId = socialPostScheduleInfo.getSourceId();
        ChannelPostService execute = ChannelPostServiceFactory.getService(SocialChannel.getSocialChannelById(sourceId));
        List<PageDetail> pageDetails = execute.getPageDetails(socialPostScheduleInfo.getPageIds());
        if (CollectionUtils.isEmpty(pageDetails)) {
            logger.info("Page details not found for post id: {}", postId);
            return new ApprovalWorkFlowData();
        }

        ApprovalWorkFlowData approvalWorkFlowData;
        if(Objects.isNull(enterpriseId)) {
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(socialPostScheduleInfo.getEnterpriseId(), false);
            approvalWorkFlowData = approvalWorkflowConvertorService.convertToApprovalPostResponse(socialPost, pageDetails, socialPostScheduleInfo,
                    businessLiteDTO.getBusinessNumber(), businessLiteDTO.getTimeZone(), businessLiteDTO.getBusinessName());
            // Encrypt aid and socialPostId
            try {
                approvalWorkFlowData.setApprovalWorkFlowId(encryptData(approvalWorkFlowData.getApprovalWorkFlowId()));
                approvalWorkFlowData.setPostId(encryptData(String.valueOf(approvalWorkFlowData.getId())));
            } catch (Exception e) {
                logger.error("Error while encrypting approvalWorkflowId: ", e);
            }

            approvalWorkFlowData.setCoBranded(BusinessAccountTypeEnum.COBRANDED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
            approvalWorkFlowData.setWhitelabel(BusinessAccountTypeEnum.WHITELABELED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
            approvalWorkFlowData.setReseller(checkBusinessIsReseller(businessLiteDTO));
            return approvalWorkFlowData;
        } else {
            approvalWorkFlowData = approvalWorkflowConvertorService.convertToApprovalPostResponse(socialPost, pageDetails,
                    socialPostScheduleInfo, enterpriseId, null, null);
        }
        return approvalWorkFlowData;
    }

    private List<ApprovalWorkFlowData> getBulkApprovalData(List<SocialPost> socialPostList,
                                                           Map<Integer, SocialPostScheduleInfo> socialPostIdVsScheduleInfoMap) throws IOException {
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = new ArrayList<>();
        if(CollectionUtils.isEmpty(socialPostList) || socialPostIdVsScheduleInfoMap.isEmpty()) {
            logger.info("No data found for social posts");
            return approvalWorkFlowDataList;
        }
        // since all the scheduleInfo will have same enterpriseId
        Integer firstSocialPostId = socialPostList.get(0).getId();
        SocialPostScheduleInfo firstSocialPostScheduleInfo = socialPostIdVsScheduleInfoMap.get(firstSocialPostId);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(firstSocialPostScheduleInfo.getEnterpriseId(), false);

        for(SocialPost socialPost : socialPostList) {
            SocialPostScheduleInfo socialPostScheduleInfo = socialPostIdVsScheduleInfoMap.get(socialPost.getId());
            List<PageDetail> pageDetails = getPageDetails(socialPostScheduleInfo);
            if (CollectionUtils.isEmpty(pageDetails)) {
                logger.info("Page details not found for post id: {}", socialPost.getId());
                continue;
            }
            ApprovalWorkFlowData approvalWorkFlowData = approvalWorkflowConvertorService.convertToApprovalPostResponse(socialPost, pageDetails, socialPostScheduleInfo,
                    businessLiteDTO.getBusinessNumber(), businessLiteDTO.getTimeZone(), businessLiteDTO.getBusinessName());
            approvalWorkFlowData.setCoBranded(BusinessAccountTypeEnum.COBRANDED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
            approvalWorkFlowData.setWhitelabel(BusinessAccountTypeEnum.WHITELABELED.getName().equalsIgnoreCase(businessLiteDTO.getAccountType()));
            approvalWorkFlowData.setReseller(checkBusinessIsReseller(businessLiteDTO));
            approvalWorkFlowDataList.add(approvalWorkFlowData);
        }

        return approvalWorkFlowDataList;
    }

    private List<PageDetail> getPageDetails(SocialPostScheduleInfo socialPostScheduleInfo) {
        if(socialPostScheduleInfo == null) {
            logger.info("No scheduling information found for post id: {}", socialPostScheduleInfo.getSocialPostId());
        }
        Integer sourceId = socialPostScheduleInfo.getSourceId();
        ChannelPostService execute = ChannelPostServiceFactory.getService(SocialChannel.getSocialChannelById(sourceId));
        return execute.getPageDetails(socialPostScheduleInfo.getPageIds());
    }
    private List<SocialPostEsRequest> searchFromEsIndex(BoolQueryBuilder boolQueryBuilder, Boolean withPendingStateOnly) {
        List<SocialPostEsRequest> socialPostEsRequests = new ArrayList<>();
        logger.info("Social post query : {}",boolQueryBuilder);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(ElasticConstants.SOCIAL_POST_APPROVAL.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostEsRequest socialPostEsRequest = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostEsRequest.class);
                if(withPendingStateOnly) {
                    if(ApprovalEnum.PENDING.getName().equalsIgnoreCase(socialPostEsRequest.getApproval_status())) {//add approvals which are pending
                        socialPostEsRequests.add(socialPostEsRequest);
                    }
                } else {
                    socialPostEsRequests.add(socialPostEsRequest);
                }
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search ",exception);
        }
        return socialPostEsRequests;
    }

    private BoolQueryBuilder createEsQueryForPostId(Integer enterpriseId, Integer postId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.id.name(), postId));
        if(Objects.nonNull(enterpriseId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(ApprovalEnum.enterprise_id.name(), enterpriseId));
        }

        return boolQueryBuilder;

    }

    private List<SocialPostEsRequest> searchForEsIndex(Integer businessId,BoolQueryBuilder boolQueryBuilder,
                                                       Integer startIndex,Integer pageSize, String sortParam, String sortOrder,
                                                       ApprovalWorkflowResponse approvalWorkflowResponse) {
        List<SocialPostEsRequest> socialPostEsRequests = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.from(startIndex);
        searchSourceBuilder.size(pageSize);
        searchSourceBuilder.sort(sortParam, SortOrder.valueOf(sortOrder.toUpperCase()));
        searchRequest.source(searchSourceBuilder);
        searchRequest.routing(String.valueOf(businessId));
        searchRequest.indices(ElasticConstants.SOCIAL_POST_APPROVAL.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            approvalWorkflowResponse.setTotalCount(searchResponse.getHits().getTotalHits().value);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
                SocialPostEsRequest socialElasticDto = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostEsRequest.class);
                socialPostEsRequests.add(socialElasticDto);
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search ",exception);
        }
        return socialPostEsRequests;
    }

    private List<Integer> searchForEsIndexWithoutPagination(Integer businessId,BoolQueryBuilder boolQueryBuilder,
                                                                        String sortParam, String sortOrder,
                                                                        ApprovalWorkflowResponse approvalWorkflowResponse) {
       List<Integer> socialPostIds = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(boolQueryBuilder);
        searchSourceBuilder.sort(sortParam, SortOrder.valueOf(sortOrder.toUpperCase()));
        searchSourceBuilder.fetchSource(new String[] {"id"}, null); //get only id
        searchRequest.source(searchSourceBuilder);
        searchRequest.routing(String.valueOf(businessId));
        searchRequest.indices(ElasticConstants.SOCIAL_POST_APPROVAL.getName());
        try {
            SearchResponse searchResponse = esService.search(searchRequest);
            approvalWorkflowResponse.setTotalCount(searchResponse.getHits().getTotalHits().value);
            SearchHit[] searchHits = searchResponse.getHits().getHits();
            for (SearchHit hit : searchHits) {
//                SocialPostEsRequest socialElasticDto = JSONUtils.fromJSON(hit.getSourceAsString(), SocialPostEsRequest.class);
//                socialPostEsRequests.add(socialElasticDto);
                socialPostIds.add(Integer.valueOf(String.valueOf(hit.getSourceAsMap().get("id"))));
            }
        }catch (IOException exception){
            logger.info("Exception for Elastic search {}", exception.getMessage());
        }
        return socialPostIds;
    }

    private BoolQueryBuilder createEsQuery(ApprovalWorkFlowRequest approvalRequest, Integer userId,ResetCountRequest request,List<String> pageIds,List<Integer> socialChannels) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        TermQueryBuilder enterpriseId = new TermQueryBuilder(ApprovalEnum.enterprise_id.name(),approvalRequest.getBusinessId());
        TermsQueryBuilder channelsQuery = new TermsQueryBuilder(ApprovalEnum.channel.name(),socialChannels);
        TermsQueryBuilder approvalStatusQuery = new TermsQueryBuilder(ApprovalEnum.approval_status.name(),approvalRequest.getPostStatus());
        RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder(ApprovalEnum.created_date.name()).gte(approvalRequest.getStartDate()).lt(approvalRequest.getEndDate());
        if(Objects.nonNull(approvalRequest.getSearchStr())){
            if(approvalRequest.isTextSearch()) {
                boolQueryBuilder.filter(QueryBuilders.matchPhrasePrefixQuery(ApprovalEnum.post_text.name(),approvalRequest.getSearchStr()));
            }else{
                boolQueryBuilder.filter(QueryBuilders.matchPhrasePrefixQuery(ApprovalEnum.page_name.name(),approvalRequest.getSearchStr()));
            }
        }
        if(CollectionUtils.isNotEmpty(pageIds)){
            TermsQueryBuilder approvalPageIds = new TermsQueryBuilder(ApprovalEnum.page_ids.name(),pageIds);
            boolQueryBuilder.filter(approvalPageIds);
        }
        if(socialTagService.isUntaggedRequest(approvalRequest.getTagIds())) {
            Set<Long> tagIdsWithoutUntagged = approvalRequest.getTagIds().stream().filter(s->!s.equals(-1l)).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(tagIdsWithoutUntagged)) {
                ExistsQueryBuilder existsQueryBuilder = new ExistsQueryBuilder(ApprovalEnum.tagIds.name());
                boolQueryBuilder.mustNot(existsQueryBuilder);
            } else {
                BoolQueryBuilder existBoolQuery = new BoolQueryBuilder();
                ExistsQueryBuilder existsQueryBuilder = new ExistsQueryBuilder(ApprovalEnum.tagIds.name());
                existBoolQuery.mustNot(existsQueryBuilder);
                TermsQueryBuilder tagIdsQuery = new TermsQueryBuilder(ApprovalEnum.tagIds.name(), tagIdsWithoutUntagged);
                BoolQueryBuilder tagIdBoolQuery = new BoolQueryBuilder();
                tagIdBoolQuery.should(existBoolQuery);
                tagIdBoolQuery.should(tagIdsQuery);
                boolQueryBuilder.filter(tagIdBoolQuery);
            }
        } else if(CollectionUtils.isNotEmpty(approvalRequest.getTagIds())) {
            TermsQueryBuilder tagIdsQuery = new TermsQueryBuilder(ApprovalEnum.tagIds.name(), approvalRequest.getTagIds());
            boolQueryBuilder.filter(tagIdsQuery);
        }
        boolQueryBuilder.filter(enterpriseId).filter(channelsQuery).filter(approvalStatusQuery).filter(rangeQueryBuilder);
        request.setEnterpriseId(approvalRequest.getBusinessId());
        if(CollectionUtils.isNotEmpty(approvalRequest.getPostStatus()) && approvalRequest.getPostStatus().contains(ApprovalEnum.pending.name())) {
            TermQueryBuilder approverQuery = new TermQueryBuilder(ApprovalEnum.approval_user_ids.name(), userId);
            boolQueryBuilder.filter(approverQuery);
            request.setUserId(userId);
        }
        request.setStatus(approvalRequest.getPostStatus());
        if(CollectionUtils.isNotEmpty(approvalRequest.getCreators())){
            TermsQueryBuilder creatorQuery = new TermsQueryBuilder(ApprovalEnum.created_by.name(),approvalRequest.getCreators());
            boolQueryBuilder.filter(creatorQuery);
        }
        if(CollectionUtils.isNotEmpty(approvalRequest.getApprovals())){
            TermsQueryBuilder approvalWorkflowQuery = new TermsQueryBuilder(ApprovalEnum.approval_workflow_id.name(),approvalRequest.getApprovals());
            boolQueryBuilder.filter(approvalWorkflowQuery);
        }
        logger.info("Query builder request : {}",boolQueryBuilder);
        return boolQueryBuilder;
    }

    private String encryptData (String socialPostId) throws
            NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException
    {
        SecretKey aesKey = new SecretKeySpec(postingKey.getBytes(), Constants.AES_ENCRYPT_DECRYPT);
        Cipher cipher = Cipher.getInstance(Constants.AES_ENCRYPT_DECRYPT);
        cipher.init(Cipher.ENCRYPT_MODE, aesKey);
        byte[] encrypted = cipher.doFinal(socialPostId.getBytes());
        return Hex.encodeHexString(encrypted);
    }

    private String decryptData (String trackingId) {
        String decrypted = "";
        try {
            SecretKey aesKey = new SecretKeySpec(postingKey.getBytes(), Constants.AES_ENCRYPT_DECRYPT);
            Cipher cipher = Cipher.getInstance(Constants.AES_ENCRYPT_DECRYPT);
            cipher.init(Cipher.DECRYPT_MODE, aesKey);
            decrypted = new String(cipher.doFinal(Hex.decodeHex(trackingId.toCharArray())));
        } catch (Exception e) {
            logger.error("Error occurred while decrypting trackingId:{} with exception", trackingId, e);
            throw new InvalidSocialArgsException(ErrorCodes.INVALID_TRACKING_SOCIAL, ExternalAPIErrorCode.SOCIAL_TRACKING_MISMATCH.getDescription());
        }
        return decrypted;
    }

    private List<ApprovalEmailDTO> getApprovarsEmailIds(List<String> approvalUserIds) {
        List<ApprovalEmailDTO> approvalEmailIds = new ArrayList<>();
        approvalUserIds.forEach(approvalUserIdString -> {
            ApprovalEmailDTO emailDTO = new ApprovalEmailDTO();
            int approvalUserId;
            try {
                try {
                    approvalUserId = Integer.parseInt(approvalUserIdString);
                    BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(approvalUserId);
                    emailDTO.setEmailId(businessCoreUser.getEmailId());
                    emailDTO.setUserId(approvalUserId);
                    emailDTO.setApprover(true);
                    emailDTO.setPublic(false);
                } catch (Exception e) {
                    emailDTO.setEmailId(approvalUserIdString);
                    emailDTO.setUserId(null);
                    emailDTO.setApprover(true);
                    emailDTO.setPublic(true);
                }
                approvalEmailIds.add(emailDTO);
            } catch (Exception e) {
                logger.info("Exception while getting email id for userId: {}",approvalUserIdString,e);
            }
        });
        return approvalEmailIds;
    }

    private List<ApprovalEmailDTO> getCreatorEmailIdAsList(Integer userId) {
        List<ApprovalEmailDTO>  approvalEmailDTOS = new ArrayList<>();
        try {
            ApprovalEmailDTO approvalEmailDTO = new ApprovalEmailDTO();
            BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
            approvalEmailDTO.setEmailId(businessCoreUser.getEmailId());
            approvalEmailDTO.setPublic(false);
            approvalEmailDTOS.add(approvalEmailDTO);
        } catch (Exception e) {
            logger.info("Exception while getting email id for userId: {}",userId,e);
        }
        return approvalEmailDTOS;
    }


    private void sendReminderEmail(ApprovalWorkFlowData approvalWorkFlowData, List<String> emailIds) {
        EmailDTO emailDTO = new EmailDTO();
        emailDTO.setTo(emailIds);
        emailDTO.setSubject("To be confirmed by Product"); //@TODO
        emailDTO.setRequestType(ApprovalEnum.social_approval_request_template.name());
        emailDTO.setExternalUuid(emailDTO.getRequestType().concat(String.valueOf(approvalWorkFlowData.getId())));
        try {
            approvalWorkFlowData.setApprovalWorkFlowId(encryptData(approvalWorkFlowData.getApprovalWorkFlowId()));
            approvalWorkFlowData.setPostId(encryptData(String.valueOf(approvalWorkFlowData.getId())));
        } catch (Exception e) {
            logger.info("Error while encrypting approvalWorkflowId: ",e);
        }
        nexusService.sendEmailV2(emailDTO, approvalWorkFlowData, false);
    }

    private void sendEmail(List<ApprovalWorkFlowData> approvalWorkFlowDataList, List<ApprovalEmailDTO> emailDTOS, String subject, String subSubject) {
        if (CollectionUtils.isEmpty(emailDTOS) || CollectionUtils.isEmpty(approvalWorkFlowDataList)) {
            logger.info("No email present in data");
            return;
        }

        // Extract common data
        ApprovalWorkFlowData firstApprovalWorkFlowData = approvalWorkFlowDataList.get(0);
        boolean isSameContent;
        if(approvalWorkFlowDataList.size() == 1) { //this is a socialPostId event, which means there will be single card in email
            isSameContent = true;
        } else { //this is masterPostId event, which means it depends on isSameContent sent from UI
            isSameContent = firstApprovalWorkFlowData.getIsSameContent();
//            if(isSameContent) {
//                subSubject =
//            }
        }
        String websiteDomain = businessCoreService.getWebsiteDomain(firstApprovalWorkFlowData.getBusinessId());

        // Prepare payload
        Map<String, Object> payload = new HashMap<>();
        payload.put("isSameContent", isSameContent);
        payload.put("subSubject", subSubject);

        // Process each email
        for (ApprovalEmailDTO email : emailDTOS) {
            List<Map<String, Object>> approvalWorkFlowRequestList = new ArrayList<>();

            if(Boolean.FALSE.equals(isSameContent)) { // multiple cards for email (bulk approval view1)
                // Process each approval workflow data
                payload.put("isInternalUser", !email.isPublic()); // needed for removing individual approval/reject token for each card for external user
                for (ApprovalWorkFlowData approvalWorkFlowData : approvalWorkFlowDataList) {
                    String locationString = getLocationString(approvalWorkFlowData.getLocationCount());
                    ApprovalEnum status = ApprovalEnum.valueOf(approvalWorkFlowData.getApprovalStatus());
                    Map<String, Object> approvalWorkFlowDataPayload = buildPayload(approvalWorkFlowData, email, status, websiteDomain, locationString);
                    approvalWorkFlowDataPayload.put("channel", Collections.singletonList(approvalWorkFlowData.getChannel()));
                    approvalWorkFlowRequestList.add(approvalWorkFlowDataPayload);
                }
                // Add approve/reject all tokens if content is not the same
                payload.put("approvalAllToken", buildAllTokenUrl(approvalWorkFlowDataList, email, websiteDomain, "1"));
                payload.put("rejectAllToken", buildAllTokenUrl(approvalWorkFlowDataList, email, websiteDomain, "2"));
            } else { // Single Card for Email
                ApprovalWorkFlowData approvalWorkFlowData = approvalWorkFlowDataList.get(0);
                String locationString = getLocationString(approvalWorkFlowData.getLocationCount());
                ApprovalEnum status = ApprovalEnum.valueOf(approvalWorkFlowData.getApprovalStatus());
                String currentWebsiteDomain = businessCoreService.getWebsiteDomain(firstApprovalWorkFlowData.getBusinessId());
                Map<String, Object> approvalWorkFlowDataPayload = buildPayload(approvalWorkFlowData, email, status, currentWebsiteDomain, locationString);
                Set<String> channels = getAllChannelsForSameContent(approvalWorkFlowDataList);
                approvalWorkFlowDataPayload.put("channel", channels);
                if(approvalWorkFlowDataList.size() > 1) { // if multiple channels (bulk approval view2)
                    approvalWorkFlowDataPayload.put("approvalToken", buildAllTokenUrl(approvalWorkFlowDataList, email, websiteDomain, "1"));
                    approvalWorkFlowDataPayload.put("rejectedToken", buildAllTokenUrl(approvalWorkFlowDataList, email, websiteDomain, "2"));
                    approvalWorkFlowDataPayload.put("postToken", buildBulkPostTokenUrl(approvalWorkFlowDataList, email, websiteDomain));
                }
                approvalWorkFlowRequestList.add(approvalWorkFlowDataPayload);
            }

            // Add list of approval workflow data to payload
            payload.put("approvalWorkFlowData", approvalWorkFlowRequestList);

            // Build and send email
            EmailDTO emailDTO = buildEmailDTO(firstApprovalWorkFlowData, email, subject);
            nexusService.sendEmailV2(emailDTO, payload, firstApprovalWorkFlowData.isReseller());
        }

    }


    private String prepareApprovalToken(ApprovalWorkFlowData data, ApprovalEnum approvalEnum, ApprovalEmailDTO email, ApprovalEnum currentStatus, String websiteDomain) {
        boolean isPublic = email.isPublic();
        String url = null;
        String commonUrl = getCommonUrl(data, isPublic, websiteDomain);
        String approvalUrl = getCommonUrl(data, true, websiteDomain); //for approvalToken, we always show public page
        Integer approverUserId = null;
        String externalApproverEmail = null;
        if(email.isApprover()) {
            if(Objects.nonNull(email.getUserId())) {
                approverUserId = email.getUserId();
            } else {
                externalApproverEmail = email.getEmailId();
            }
        }
        switch (approvalEnum){
            case APPROVAL_TOKEN:
                url = buildApprovalTokenUrl(data, currentStatus, approvalUrl, approverUserId, externalApproverEmail);
                break;
            case REJECTED_TOKEN:
                url = buildRejectedTokenUrl(data, currentStatus, commonUrl, isPublic, approverUserId, externalApproverEmail);
                break;
            case POST_TOKEN:
                url = buildPostTokenUrl(data, currentStatus, commonUrl, isPublic, approverUserId, externalApproverEmail);
                break;
            case EDIT_POST_TOKEN:
                url = buildEditPostTokenUrl(data, currentStatus, commonUrl);
                break;
            case VIEW_DETAIL_TOKEN:
                url = buildViewDetailTokenUrl(data, currentStatus, commonUrl);
                break;
            case RESCHEDULE_SUBMIT_TOKEN:
                url = buildRescheduleSubmitTokenUrl(data, currentStatus, commonUrl);
                break;
        }
        logger.info("Url for {} for socialPostId {}: {}", approvalEnum.name(), data.getId(), url);
        return url;
    }

    private boolean checkBusinessIsReseller(Long businessNumber) {
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(businessNumber);
        return businessLiteDTO.getResellerId() != null && !BusinessAccountTypeEnum.DIRECT.getName().equalsIgnoreCase(businessLiteDTO.getAccountType());
    }

    @Override
    public void processReminderEvent(SocialPostPostIdRequest socialPostPostIdRequest) throws Exception {
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = new ArrayList<>();
        Integer masterPostId = socialPostPostIdRequest.getMasterPostId();
        if(masterPostId == null) {
            masterPostId = socialPostRepository.findMasterPostIdById(socialPostPostIdRequest.getPostId());
        }
        Integer masterApprovalWorkflowId = socialMasterPostRepository.getApprovalWorkFlowIdFromId(masterPostId);
        String encryptedApprovalWorkflowId = encryptData(String.valueOf(masterApprovalWorkflowId));
        List<Integer> socialPostIds = socialPostRepository.findIdByMasterPostIdAndApprovalWorkflowIdIsNotNull(masterPostId);
        if(CollectionUtils.isEmpty(socialPostIds)) {
            logger.info("No social post found for masterPostId where ApprovalWorkflowId is not null: {}", masterPostId);
            return;
        }
        if(socialPostPostIdRequest.getPostId() != null) { //single reminder event
            boolean isEligibleForSingleEvent = socialPostPostIdRequest.getIsEligibleForSingleEvent();

            if(!isEligibleForSingleEvent && isEligibleForBulkReminder(masterPostId, socialPostIds)) { // check if it is eligible for bulk this is for backward compatibility
                String key = "ApprovalReminder_" + masterPostId;
                boolean lockStatus = redisLockService.tryToAcquireLock(key, 60L);
                logger.info("Lock Status: {} for key: {}", lockStatus, key);
                if(lockStatus) {
                    logger.info("First event detected for masterPostId: {}", masterPostId);
                    for (Integer socialPostId : socialPostIds) {
                        ApprovalWorkFlowData approvalWorkFlowData = createApprovalWorkflowData(socialPostId);
                        if (approvalWorkFlowData != null && encryptedApprovalWorkflowId.equals(approvalWorkFlowData.getApprovalWorkFlowId())) { // check post for edited approvalWorkFlow
                            approvalWorkFlowDataList.add(approvalWorkFlowData);
                        } else {
                            logger.info("Social Post: {} has edited approval workflow", socialPostId);
                        }
                    }
                } else {
                    logger.info("Unable to acquire lock with key: {} bulk process already in process for masterPostId: {}", key, masterPostId);
                }
            } else {
                logger.info("Single reminder event, not eligible for bulk reminder, sending individual reminder email");
                approvalWorkFlowDataList.add(createApprovalWorkflowData(socialPostPostIdRequest.getPostId()));
            }
            //send email
            createCommonDataForReminderAndSendReminderMail(approvalWorkFlowDataList);
        } else { // bulk reminder event
            for (Integer socialPostId : socialPostIds) {
                ApprovalWorkFlowData approvalWorkFlowData = createApprovalWorkflowData(socialPostId);
                if (approvalWorkFlowData != null
                        && !approvalWorkFlowData.getIsEditedPost()) { // check edited post (handles approval id changes and schedule date changes)
                    approvalWorkFlowDataList.add(approvalWorkFlowData);
                } else {
                    logger.info("Social Post: {} has edited approval workflow", socialPostId);
                }
            }
            if(isEligibleForBulkReminder(masterPostId, socialPostIds)) {
                createCommonDataForReminderAndSendReminderMail(approvalWorkFlowDataList);
            } else { //looks buggy (why will you send reminder for posts which do not have same schedule date???)
                for(ApprovalWorkFlowData approvalWorkFlowData : approvalWorkFlowDataList) {
                    createCommonDataForReminderAndSendReminderMail(Collections.singletonList(approvalWorkFlowData));
                }
            }
        }

    }

    @NotNull
    private String getScheduledDateString(ApprovalWorkFlowData approvalWorkFlowData, String channel) throws ParseException {
        if(StringUtils.equalsIgnoreCase(channel,SocialChannel.APPLE_CONNECT.getName())) {
            SocialPostSchedulerMetadata postMetadata = JSONUtils.fromJSON(approvalWorkFlowData.getPostMetaData(),SocialPostSchedulerMetadata.class);
            if(Objects.nonNull(postMetadata)) {
                ApplePostMetadata metadata = JSONUtils.fromJSON(postMetadata.getAppleMetaData(), ApplePostMetadata.class);
                if (Objects.nonNull(metadata)) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(ES_APPLE_END_DATE);
                    Date startDate = DateTimeUtils.addTimeInMinutes(simpleDateFormat.parse(metadata.getStartDate()),metadata.getTimezoneOffSetValue());
                    return new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(startDate);
                }
            }
        }
        SimpleDateFormat sdf = new SimpleDateFormat(ES_PATTERN_DATE);
        Date scheduledDate  = sdf.parse(approvalWorkFlowData.getScheduleDate());
        return new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(scheduledDate);
    }

    @Override
    public ApprovalEmailResponse sendApprovalEmail(Long enterpriseId, ApprovalEmailReminder approvalEmailReminder, Integer businessId, Integer userId) throws Exception {
        ApprovalWorkFlowData approvalWorkFlowData = getApprovalDataFromPostId(approvalEmailReminder.getPostId(), null);
        BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
        String name = businessCoreUser.getName();
        approvalWorkFlowData.setAuthorName(name);
        String approvalStatus = ApprovalStatus.PENDING.getName();
        ApprovalEmailResponse approvalEmailResponse = new ApprovalEmailResponse(0);
        logger.info("Approval status for postId: {}: {}", approvalEmailReminder.getPostId(), approvalStatus);
        if(ApprovalStatus.PENDING.getName().equalsIgnoreCase(approvalStatus)) {
            String scheduledDateString = getScheduledDateString(approvalWorkFlowData,approvalWorkFlowData.getChannel());
            List<String> vowelChannels = Arrays.asList("instagram","apple_connect");
            boolean vowel = vowelChannels.contains(approvalWorkFlowData.getChannel());
            String subject = String.format(Constants.APPROVAL_MANUAL_REMINDER_EMAIL_SUB_SUBJECT, vowel?"an":"a",
                    getPostType(approvalWorkFlowData.getTopicType(),SocialChannel.getEmailDisplayByName(approvalWorkFlowData.getChannel())));
            approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData,enterpriseId));
            List<ApprovalEmailDTO> approvalEmailIds = getApprovarsEmailIds(approvalWorkFlowData.getApprovalUserIds());
            String subSubject = String.format(Constants.APPROVAL_REMINDER_EMAIL_SUB_SUBJECT,StringUtils.capitalize(name),scheduledDateString);
            approvalWorkFlowData.setText(addMentionsInTheText(approvalWorkFlowData.getText(), approvalWorkFlowData.getMentions()));
            if(StringUtils.equalsIgnoreCase(approvalWorkFlowData.getChannel(),SocialChannel.APPLE_CONNECT.getName())) {
                approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
            }
            sendEmail(Collections.singletonList(approvalWorkFlowData), approvalEmailIds, subject, subSubject);
            approvalEmailResponse.setCount(CollectionUtils.isNotEmpty(approvalWorkFlowData.getApprovalUserIds())?approvalWorkFlowData.getApprovalUserIds().size():0);
        }

        return approvalEmailResponse;
    }

    private String getScheduledDate(String postMetaData) throws ParseException {
        SocialPostSchedulerMetadata postMetadata = JSONUtils.fromJSON(postMetaData,SocialPostSchedulerMetadata.class);
        if(Objects.nonNull(postMetadata)) {
            ApplePostMetadata metadata = JSONUtils.fromJSON(postMetadata.getAppleMetaData(), ApplePostMetadata.class);
            if (Objects.nonNull(metadata)) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat(ES_APPLE_END_DATE);
                Date startDate = DateTimeUtils.addTimeInMinutes(simpleDateFormat.parse(metadata.getStartDate()),metadata.getTimezoneOffSetValue());
                Date endDate = DateTimeUtils.addTimeInMinutes(simpleDateFormat.parse(metadata.getEndDate()),metadata.getTimezoneOffSetValue());
                String scheduledDateString = new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(startDate);
                String scheduledEndDateString = new SimpleDateFormat(EMAIL_SUBJECT_PATTERN_DATE).format(endDate);
                return String.format("Scheduled from %s to %s", scheduledDateString, scheduledEndDateString);
            }
        }
        return "";
    }

    private String getPostType(String type,String channel){
        return GOOGLE_OFFER.equalsIgnoreCase(type) ? GOOGLE_OFFERS : channel;
    }

    private String addMentionsInTheText(String postText, List<MentionData> mentionDataList) {
        if(CollectionUtils.isEmpty(mentionDataList) || StringUtils.isEmpty(postText)) return postText;
        Map<String, String> replaceMentionMap = new HashMap<>();
        for(MentionData mentionData: mentionDataList) {
            String id = mentionData.getValue();
            replaceMentionMap.put(id, "["+mentionData.getName()+"]");
        }
        if(MapUtils.isEmpty(replaceMentionMap)) return postText;
        return ConversionUtils.replaceStringKeyWords(postText, replaceMentionMap);
    }



    private String generateCollage(ApprovalWorkFlowData approvalWorkFlowData,Long businessNumber) {
        PicturesqueRequest picturesqueRequest=new PicturesqueRequest();
        List<Picturesque> attachments = getPicturesqueList(approvalWorkFlowData);
        if(CollectionUtils.isEmpty(attachments)) {
            logger.info("no attachment for approvalWorkflowId {}",approvalWorkFlowData.getApprovalWorkFlowId());
            return null;
        }
        picturesqueRequest.setAttachments(attachments);
        picturesqueRequest.setBusinessNumber(businessNumber);
        PicturesqueResponse response=picturesqueGen.getSingleMediaData(picturesqueRequest);
        if(response.isSuccess()) {
            return response.getS3Url();
        } else {
            logger.info("could not create collage for images and videos of approvalWorkflowId {}",approvalWorkFlowData.getApprovalWorkFlowId());
            return null;
        }
    }

    private static List<Picturesque> getPicturesqueList(ApprovalWorkFlowData approvalWorkFlowData) {
        List<Picturesque> attachments=new ArrayList<>();
        if(Objects.nonNull(approvalWorkFlowData.getImageUrls())) {
            for(String image: approvalWorkFlowData.getImageUrls()) {
                Picturesque newElement = new Picturesque();
                newElement.setType("image");
                newElement.setCompleteURL(image);
                attachments.add(newElement);
            }
        }
        if(Objects.nonNull(approvalWorkFlowData.getVideoUrls())) {
            for(String video: approvalWorkFlowData.getVideoUrls()) {
                String type = Objects.equals(approvalWorkFlowData.getChannel(),SocialChannel.FACEBOOK.getName()) ? checkVideoType(video) : "video";
                Picturesque newElement = new Picturesque();
                newElement.setType(type);
                newElement.setCompleteURL(video);
                attachments.add(newElement);
            }
        }
        return attachments;
    }

    private static String checkVideoType(String video) {
        try {
            URLConnection urlConnection = new URL(video).openConnection();
            String mimeType = urlConnection.getContentType();
            logger.info("Mime Type : {} for url : {}",mimeType,video);
            if(mimeType.contains("gif")) return "image";
            else return "video";
        }catch (Exception e){
            logger.info("Url type not found : {}",video);
        }
        return "video";
    }

    @Override
    @Async
    public void deleteApproval(ApprovalEventFromSocial approvalEventFromSocial) throws Exception {
        try {
            getApprovalDataFromApproval(approvalEventFromSocial);
        } catch (Exception e) {
            logger.error("exception while call approval for delete for post id: {} exception: {}", approvalEventFromSocial.getEntityId(), e.getMessage());
        }
    }

    @Override
    public ApprovalWorkflowEvent createOrUpdateApprovalEvent(SocialPostInputMessageRequest socialPostInput, SocialPost oldPost,String activity) {
        try {
            ApprovalEventFromSocial approvalEventFromSocial = new ApprovalEventFromSocial();
            if (!activity.equals(PostActivityType.RESCHEDULED.getName())) {
                if(Objects.isNull(socialPostInput.getApprovalWorkflowId()) && Objects.nonNull(oldPost.getApprovalWorkflowId())){ // removed approval work flow
                    approvalEventFromSocial =
                            approvalWorkflowConvertorService.prepareEventForDeleteApprovalWorkflow(oldPost,socialPostInput.getBusinessId());
                }else if(Objects.nonNull(socialPostInput.getApprovalWorkflowId())){ // edit/add approval workflow event
                    approvalEventFromSocial = Objects.nonNull(oldPost.getApprovalWorkflowId())
                            ? approvalWorkflowConvertorService.prepareEventForEditApprovalWorkflow(oldPost, socialPostInput.getBusinessId(), socialPostInput.getApprovalWorkflowId())
                            : approvalWorkflowConvertorService.prepareCreateEventForApprovalWorkflow(socialPostInput, oldPost.getId());
                }
            } else if (Objects.nonNull(oldPost.getApprovalWorkflowId())) { // in case of approval is rejected/terminated
                if(ApprovalStatus.TERMINATED.getName().equals(oldPost.getApprovalStatus())
                        || ApprovalStatus.REJECTED.getName().equals(oldPost.getApprovalStatus())) {
                    approvalEventFromSocial =
                            approvalWorkflowConvertorService.prepareEventForEditApprovalWorkflow(oldPost, socialPostInput.getBusinessId(),oldPost.getApprovalWorkflowId());
                }
            }
            if(Objects.isNull(approvalEventFromSocial) || Objects.isNull(approvalEventFromSocial.getEntityId())){
                logger.info("No condition satisfied for post id : {}",oldPost.getId());
                return null;
            }
            ApprovalWorkflowEvent approvalWorkflowEvent = getApprovalDataFromApproval(approvalEventFromSocial);
            if(Objects.isNull(approvalWorkflowEvent)) {
                oldPost.setApprovalStatus(null);
                oldPost.setApprovalWorkflowId(null);
                oldPost.setApprovalUserIds(null);
                oldPost.setApprovalMetadata(null);
                socialPostRepository.saveAndFlush(oldPost);
                esService.deleteDocument(ElasticConstants.SOCIAL_POST_APPROVAL.getName(), String.valueOf(oldPost.getId()));
                socialPostApprovalRepo.deleteBySocialPostId(oldPost.getId());
            }
            return approvalWorkflowEvent;
        }catch (SocialBirdeyeException e){
            logger.info("SocialBirdeyeException occurred from core's end :{}",e.getMessage());
            throw e;
        }catch (Exception e){
            logger.info("Unknown exception occurred from core's end :",e);
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,"Unknown Error Occurred");
        }
    }

    @Override
    public SocialPostEsRequest findByPostId(Integer postId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.must(new TermQueryBuilder(ApprovalEnum.id.name(),postId));
        List<SocialPostEsRequest> socialPostEsRequests = searchFromEsIndex(boolQueryBuilder, false);
        if(CollectionUtils.isEmpty(socialPostEsRequests)) return null;
        return socialPostEsRequests.get(0);
    }

    @Override
    public ApprovalWorkflowEvent getApprovalDataFromApproval(ApprovalEventFromSocial request) throws Exception {
        String businessCoreServiceUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social.business.core.services.url");
//        String businessCoreServiceUrl = "http://10.51.41.184:8080/";
        String approvalUrl = businessCoreServiceUrl + "api/v1/approvals/requests/event/handler";
        HttpEntity<ApprovalEventFromSocial> requestEntity = new HttpEntity<>(request);
        try{
            logger.info("approval request :{}", JSONUtils.toJSON(request));
            ResponseEntity<ApprovalWorkflowEvent> approvalWorkflowEventResponseEntity =
                    restTemplate.exchange(approvalUrl, HttpMethod.POST,requestEntity,ApprovalWorkflowEvent.class);
            if (approvalWorkflowEventResponseEntity.getStatusCode().equals(HttpStatus.OK)){
                String responseFromApproval = null;
                if(Objects.nonNull(approvalWorkflowEventResponseEntity.getBody())) {
                    responseFromApproval = approvalWorkflowEventResponseEntity.getBody().toString();
                }
                logger.info("response from approval for post id: {}, is: {}", request.getEntityId(), responseFromApproval);
                return approvalWorkflowEventResponseEntity.getBody();
            } else{
                logger.error("Error while calling approval service for request with : response code:{}",approvalWorkflowEventResponseEntity.getStatusCode());
            }
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                logger.error("InternalServerException while approval  service for URL {} and exception {}", approvalUrl, ex.getResponseBodyAsString());
                CoreException socialBirdeyeException = JSONUtils.fromJSON(ex.getResponseBodyAsString(),CoreException.class);
                if(Objects.nonNull(socialBirdeyeException) && socialBirdeyeException.getCode() == 500){
                    throw new SocialBirdeyeException(ErrorCodes.APPROVAL_WORKFLOW_DELETED,Constants.DELETED_WORKFLOW_MESSAGE);
                }
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                logger.error("ClientException while calling approval service for URL {} and exception {}", approvalUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        }
        return null;
    }

    @Override
    public void updateTagInEs(Integer postId, Integer enterpriseId) {
        List<SocialTagBasicDetail> tagMapping = socialTagService.getBasicTagDetailForSingleEntityId(Long.valueOf(postId), SocialTagEntityType.POST);

        Map<String, Object> parameterMap = new HashMap<>();
        Set<Long> tagIds = null;
        if(CollectionUtils.isNotEmpty(tagMapping)) {
            tagIds = tagMapping.stream().map(SocialTagBasicDetail::getId).collect(Collectors.toSet());
            parameterMap.put(ApprovalEnum.tagIds.name(), tagIds);
        } else {
            parameterMap.put(ApprovalEnum.tagIds.name(), null);
        }
        try {
            esService.updateDocumentWithRouting(JSONUtils.toJSON(parameterMap), ElasticConstants.SOCIAL_POST_APPROVAL.getName(), String.valueOf(postId), String.valueOf(enterpriseId));
            socialPostApprovalRepo.updateTagsInDb(CollectionUtils.isEmpty(tagIds)?null:JSONUtils.toJSON(tagIds), postId);
        } catch (Exception e) {
            logger.info("exception occurred while updating document  for post id: {}, error: {}",
                    postId, e.getMessage());
        }
    }

    @Override
    public void processApprovalWorkflowEvent(SocialPostPostIdRequest postIdRequest,
                                             String operation) throws Exception {
        ApprovalWorkflowEventType event;
        if (Objects.isNull(postIdRequest) || (Objects.isNull(postIdRequest.getPostId()) && Objects.isNull(postIdRequest.getMasterPostId()))) {
            logger.info("No data found to send email");
            return;
        }

        List<Integer> socialPostIds = getSocialPostIdsForApproval(postIdRequest);
        if (CollectionUtils.isEmpty(socialPostIds)) {
            logger.info("No data found to send email : {}", postIdRequest);
            return;
        }

        try {
            event = ApprovalWorkflowEventType.valueOf(operation.toUpperCase());
        } catch (Exception e) {
            logger.error("Invalid event type: {}", operation);
            throw e;
        }

        switch(event) {
            case SCHEDULE:
                processScheduleEvent(postIdRequest, socialPostIds);
                break;

            case APPROVED:
                processApprovedEvent(postIdRequest, socialPostIds);
                break;

            case REJECTED:
                processRejectedEvent(postIdRequest, socialPostIds);
                break;

            case EXPIRED:
                processExpiredEvent(postIdRequest, socialPostIds);
                break;

            default:
                logger.info("Invalid event type: {}", operation);
        }
    }

    @Override
    public void processScheduleEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception {
        logger.info("Request received to send email for scheduled post for: {}", socialPostIds);

        boolean isBulkSchedule = socialPostIds.size() > 1;

        // Create ApprovalWorkflowData for each socialPostId
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = processSinglePostForScheduleEvent(socialPostIds, socialPostPostIdRequest);

        // Send email if there are valid approval workflows
        if (CollectionUtils.isNotEmpty(approvalWorkFlowDataList)) {
            ApprovalWorkFlowData firstData = approvalWorkFlowDataList.get(0);
            List<ApprovalEmailDTO> emailIds = getApprovarsEmailIds(firstData.getApprovalUserIds());
            String scheduledDateString = getScheduledDateString(firstData, firstData.getChannel());

            String subject = determineEmailSubject(firstData, scheduledDateString, isBulkSchedule);
            String subSubject = determineEmailSubSubject(firstData, scheduledDateString, isBulkSchedule);

            sendEmail(approvalWorkFlowDataList, emailIds, subject, subSubject);
        }
    }

    @Override
    public void processApprovedEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception {
        logger.info("Request received to send email for approved mail for: {}", socialPostPostIdRequest);
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = new ArrayList<>();
        List<ApprovalEmailDTO> emailIds;
        String subject;
        String subSubject;

        for (Integer socialPostId : socialPostIds) {
            ApprovalWorkFlowData approvalWorkFlowData = getApprovalDataFromPostId(socialPostId, null);
            setApproverName(socialPostPostIdRequest, approvalWorkFlowData);

            approvalWorkFlowData.setApprovalStatus(ApprovalStatus.APPROVED.getName());
            String approvalStatus = approvalWorkFlowData.getType();
            logger.info("Approval status for postId: {}: {}", socialPostId, approvalStatus);

            if (ApprovalStatus.APPROVED.getName().equalsIgnoreCase(approvalStatus)) {
                approvalWorkFlowData.setText(addMentionsInTheText(approvalWorkFlowData.getText(), approvalWorkFlowData.getMentions()));
                approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData, approvalWorkFlowData.getBusinessNumber()));
                String channel = approvalWorkFlowData.getChannel();
                if (StringUtils.equalsIgnoreCase(channel, SocialChannel.APPLE_CONNECT.getName())) {
                    approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
                }
                approvalWorkFlowDataList.add(approvalWorkFlowData);
            }
        }

        if(CollectionUtils.isNotEmpty(approvalWorkFlowDataList)) {
            ApprovalWorkFlowData approvalWorkFlowData = approvalWorkFlowDataList.get(0);
            String scheduledDateString = getScheduledDateString(approvalWorkFlowData, approvalWorkFlowData.getChannel());
            if (Boolean.TRUE.equals(approvalWorkFlowData.getIsEditedPost())) {
                subject = getSubject2Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_APPROVED_EMAIL_SUBJECT_EDIT);
                subSubject = getSubject3Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_APPROVED_EMAIL_SUB_SUBJECT_EDIT, approvalWorkFlowData.getAuthorName());
            } else {
                subject = String.format(Constants.APPROVAL_APPROVED_EMAIL_SUBJECT, approvalWorkFlowData.getAuthorName(), getVowelPrefix(approvalWorkFlowData.getChannel()), getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(approvalWorkFlowData.getChannel())), scheduledDateString);
                subSubject = subject;
            }
            emailIds = getCreatorEmailIdAsList(approvalWorkFlowData.getCreatedBy());
            sendEmail(approvalWorkFlowDataList, emailIds, subject, subSubject);
        }
    }

    @Override
    public void processRejectedEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception {
        logger.info("Request received to send email for rejected mail for: {}", socialPostPostIdRequest);
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = new ArrayList<>();
        List<ApprovalEmailDTO> emailIds;
        String subject;
        String subSubject;

        for (Integer socialPostId : socialPostIds) {
            ApprovalWorkFlowData approvalWorkFlowData = getApprovalDataFromPostId(socialPostId, null);
            approvalWorkFlowData.setApprovalStatus(ApprovalStatus.REJECTED.getName());
            String approvalStatus = approvalWorkFlowData.getType();
            logger.info("Approval status for postId: {}: {}", socialPostId, approvalStatus);
            if (ApprovalStatus.REJECTED.getName().equalsIgnoreCase(approvalStatus)) {
                approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData, approvalWorkFlowData.getBusinessNumber()));
                if (StringUtils.equalsIgnoreCase(approvalWorkFlowData.getChannel(), SocialChannel.APPLE_CONNECT.getName())) {
                    approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
                }
                approvalWorkFlowDataList.add(approvalWorkFlowData);
            }
        }

        if(CollectionUtils.isNotEmpty(approvalWorkFlowDataList)) {
            ApprovalWorkFlowData approvalWorkFlowData = approvalWorkFlowDataList.get(0);
            String scheduledDateString = getScheduledDateString(approvalWorkFlowData, approvalWorkFlowData.getChannel());
            String rejecterName;
            String rejectedUserId = socialPostPostIdRequest.getApproverUserId();
            try {
                Integer userId = Integer.parseInt(rejectedUserId);
                BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
                rejecterName = businessCoreUser.getName();
            } catch (Exception e) {
                rejecterName = rejectedUserId;
            }
            if (Boolean.TRUE.equals(approvalWorkFlowData.getIsEditedPost())) {
                subject = getSubject3Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_REJECTED_EMAIL_SUBJECT_EDIT, approvalWorkFlowData.getAuthorName());
                subSubject = getSubject3Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_REJECTED_EMAIL_SUBJECT_EDIT, approvalWorkFlowData.getAuthorName());
            } else {
                subject = String.format(Constants.APPROVAL_REJECTED_EMAIL_SUBJECT, rejecterName,
                        getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(approvalWorkFlowData.getChannel())), scheduledDateString);
                subSubject = subject;
            }
            emailIds = getCreatorEmailIdAsList(approvalWorkFlowData.getCreatedBy());
            sendEmail(approvalWorkFlowDataList, emailIds, subject, subSubject);
        }
    }

    @Override
    public void processExpiredEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception {
        logger.info("Request received to send email for expired mail for: {}", socialPostPostIdRequest);
        List<ApprovalWorkFlowData> approvalWorkFlowDataList = new ArrayList<>();
        List<ApprovalEmailDTO> emailIds;
        String subject;
        String subSubject;

        for (Integer socialPostId : socialPostIds) {
            ApprovalWorkFlowData approvalWorkFlowData = getApprovalDataFromPostId(socialPostId, null);
            if (Objects.nonNull(approvalWorkFlowData)) {
                String approvalStatus = ApprovalStatus.TERMINATED.getName();
                logger.info("Approval status for postId: {}: {}", socialPostId, approvalStatus);
                if (ApprovalStatus.TERMINATED.getName().equalsIgnoreCase(approvalStatus)) {
                    approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData, approvalWorkFlowData.getBusinessNumber()));
                    if (StringUtils.equalsIgnoreCase(approvalWorkFlowData.getChannel(), SocialChannel.APPLE_CONNECT.getName())) {
                        approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
                    }
                    approvalWorkFlowDataList.add(approvalWorkFlowData);
                }
            }
        }

        if(CollectionUtils.isNotEmpty(approvalWorkFlowDataList)) {
            ApprovalWorkFlowData approvalWorkFlowData = approvalWorkFlowDataList.get(0);
            String scheduledDateString = getScheduledDateString(approvalWorkFlowData, approvalWorkFlowData.getChannel());
            if (Boolean.TRUE.equals(approvalWorkFlowData.getIsEditedPost())) {
                subject = getSubject2Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_EXPIRED_EMAIL_SUBJECT_EDIT);
                subSubject = getSubject3Param(approvalWorkFlowData, scheduledDateString, approvalWorkFlowData.getChannel(), Constants.APPROVAL_EXPIRED_EMAIL_SUB_SUBJECT_EDIT, approvalWorkFlowData.getAuthorName());
            } else {
                subject = String.format(Constants.APPROVAL_EXPIRED_EMAIL_SUBJECT,
                        getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(approvalWorkFlowData.getChannel())));
                subSubject = Constants.APPROVAL_EXPIRED_EMAIL_SUB_SUBJECT;
            }
            emailIds = getCreatorEmailIdAsList(approvalWorkFlowData.getCreatedBy());
            sendEmail(approvalWorkFlowDataList, emailIds, subject, subSubject);
        }
    }

    @Override
    public void processApprovalAndSendRequestToCore(ApprovalBulkRequest approvalBulkRequest, String action, Integer userId) {
        List<ApprovalRequest> approvalRequests = approvalBulkRequest.getApprovalRequests();
        if(CollectionUtils.isEmpty(approvalRequests)) {
            logger.info("Approval Bulk Request should not be empty");
            throw new BirdeyeSocialException("Approval Bulk Request should not be empty");
        }
        //find all approvals from DB
        List<Integer> ids = approvalRequests.stream().map(ApprovalRequest::getApprovalRequestId).collect(Collectors.toList());
        List<SocialPostApproval> socialPostApprovals = socialPostApprovalRepo.findAllByApprovalRequestIdIn(ids);

        if(CollectionUtils.isEmpty(socialPostApprovals)) {
            logger.info("Approvals not found for approvalRequestIds: {}", ids);
            throw new BirdeyeSocialException("Approvals not found for given approvalRequestIds");
        }
        Integer enterpriseId = socialPostApprovals.get(0).getEnterpriseId();
        try {
            Map<Integer, ApprovalRequest> idVsApprovalRequestMap = approvalRequests.stream()
                    .collect(Collectors.toMap(ApprovalRequest::getApprovalRequestId, Function.identity()));

            List<ApprovalRequest> requestToCore = new ArrayList<>();
            List<SocialPostApproval> processedApprovals = new ArrayList<>();
            for(SocialPostApproval socialPostApproval : socialPostApprovals) {
                if("PENDING".equalsIgnoreCase(socialPostApproval.getApprovalStatus())) {
                    Integer approvalRequestId = socialPostApproval.getApprovalRequestId();
                    ApprovalRequest approvalRequest = idVsApprovalRequestMap.get(approvalRequestId);
                    approvalRequest.setRemarks(approvalBulkRequest.getRemarks());
                    requestToCore.add(approvalRequest);
                    processedApprovals.add(socialPostApproval);
                }
            }
            if(requestToCore.isEmpty()) {
                logger.info("No pending approvals found to send event to core with approvalRequestIds: {}", ids);
                return;
            }
            sendEventToCore(requestToCore, action, userId);
            List<Integer> processedSocialPostIds = processedApprovals.stream().map(SocialPostApproval::getSocialPostId).collect(Collectors.toList());
            socialPostApprovalRepo.updateStatusForSocialPostIds(ApprovalEnum.PROCESSING.name(), processedSocialPostIds);
            socialPostRepository.updateApprovalStatusForIds(ApprovalEnum.PROCESSING.name(), processedSocialPostIds);
            for(SocialPostApproval processedApproval : processedApprovals) {
                updateApprovalStatusInES(processedApproval.getSocialPostId(), enterpriseId, ApprovalEnum.PROCESSING.name());
            }
        } catch (Exception e) {
            throw new BirdeyeSocialException("Unable to process the request");
        }
    }

    @Override
    public void handleFailedEvent(Integer socialPostId) {
        SocialPostApproval socialPostApproval = socialPostApprovalRepo.findBySocialPostId(socialPostId);
        if (Objects.isNull(socialPostApproval)) {
            logger.info("SocialPostApproval not found for socialPostId: {}", socialPostId);
            throw new BirdeyeSocialException(ErrorCodes.APPROVAL_NOT_FOUND, "Approval not found for given socialPostId");
        }
        if (Objects.isNull(socialPostApproval.getEnterpriseId())) {
            logger.info("EnterpriseId not found for socialPostId: {}", socialPostId);
            throw new BirdeyeSocialException(ErrorCodes.ENTERPRISE_ID_NOT_FOUND, "EnterpriseId not found");
        }
        List<Integer> socialPostIdList = Collections.singletonList(socialPostId);
        socialPostRepository.updateApprovalStatusForIds(ApprovalEnum.PENDING.name(), socialPostIdList);
        socialPostApprovalRepo.updateStatusForSocialPostIds(ApprovalEnum.PENDING.name(), socialPostIdList);
        try {
            updateApprovalStatusInES(socialPostId, socialPostApproval.getEnterpriseId(), ApprovalEnum.PENDING.name());
        } catch (IOException e) {
            logger.info("IO EXCEPTION while updating approval status in ES");
            throw new RuntimeException(e);
        }
    }

    private void updateApprovalStatusInES (Integer socialPostId, Integer enterpriseId, String status) throws IOException {
        Map<String, Object> parameterMap = new HashMap<>();
        parameterMap.put("approval_status", status);
        UpdateRequest request = new UpdateRequest(ElasticConstants.SOCIAL_POST_APPROVAL.getName(), String.valueOf(socialPostId));
        request.routing(String.valueOf(enterpriseId));
        esService.upsertDocumentWithImmediateRefresh(JSONUtils.toJSON(parameterMap), request);
    }


    private void sendEventToCore(List<ApprovalRequest> requestToCore, String action, Integer userId) {
        String businessCoreServiceUrl =  CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("social.business.core.services.url");
        ApprovalBulkRequest approvalBulkRequest = new ApprovalBulkRequest(requestToCore, action);
        String approvalUrl = UriComponentsBuilder.fromHttpUrl(businessCoreServiceUrl + APPROVAL_BULK_REQUEST_CORE)
                .queryParam("action",action).toUriString();
        HttpHeaders headers = new HttpHeaders();
        headers.add("user-id", String.valueOf(userId));
        HttpEntity<ApprovalBulkRequest> requestEntity = new HttpEntity<>(approvalBulkRequest, headers);
        try {
            logger.info("Approval bulk request : {}", approvalBulkRequest);
            restTemplate.exchange(approvalUrl, HttpMethod.POST, requestEntity, Void.class);
        } catch (Exception ex) {
            logger.info("Error while calling bulk approval request to core: {}", ex.getMessage());
            throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getMessage());
        }
    }

    private void setApprovalUserIds(SocialPostPostIdRequest socialPostPostIdRequest, ApprovalWorkFlowData approvalWorkFlowData) {
        if (CollectionUtils.isNotEmpty(socialPostPostIdRequest.getPendingApproverUserIds())) {
            approvalWorkFlowData.setApprovalUserIds(socialPostPostIdRequest.getPendingApproverUserIds());
        }
    }

    private void setAuthorName(ApprovalWorkFlowData approvalWorkFlowData) {
        String rejectedUserId = String.valueOf(approvalWorkFlowData.getCreatedBy());
        String name;
        try {
            Integer userId = Integer.parseInt(rejectedUserId);
            BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
            name = businessCoreUser.getName();
        } catch (Exception e) {
            name = rejectedUserId;
        }
        approvalWorkFlowData.setAuthorName(name);
    }

    private void setApproverName(SocialPostPostIdRequest socialPostPostIdRequest, ApprovalWorkFlowData approvalWorkFlowData) {
        String approverUserId = socialPostPostIdRequest.getApproverUserId();
        String approverName;
        try {
            Integer userId = Integer.parseInt(approverUserId);
            BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
            approverName = businessCoreUser.getName();
        } catch (Exception e) {
            approverName = approverUserId;
        }
        approvalWorkFlowData.setAuthorName(approverName);
    }

    private String getSubject(ApprovalWorkFlowData approvalWorkFlowData, String channel, String subjectTemplate, String authorName) {
        return String.format(subjectTemplate, StringUtils.capitalize(authorName), getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(channel)));
    }

    private String getSubSubject(ApprovalWorkFlowData approvalWorkFlowData, String scheduledDateString, String channel, String subjectTemplate, String authorName) {
        boolean vowel = isVowelChannel(channel);
        return String.format(subjectTemplate, StringUtils.capitalize(authorName), vowel ? "an" : "a", getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(channel)), scheduledDateString);
    }

    private String getSubject3Param(ApprovalWorkFlowData approvalWorkFlowData, String scheduledDateString, String channel, String subjectTemplate, String authorName) {
        boolean vowel = isVowelChannel(channel);
        return String.format(subjectTemplate, StringUtils.capitalize(authorName), vowel ? "an" : "a", getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(channel)), scheduledDateString);
    }

    private String getSubject2Param(ApprovalWorkFlowData approvalWorkFlowData, String scheduledDateString, String channel, String subjectTemplate) {
        boolean vowel = isVowelChannel(channel);
        return String.format(subjectTemplate, vowel ? "an" : "a", getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(channel)), scheduledDateString);
    }

    private boolean isVowelChannel(String channel) {
        List<String> vowelChannels = Arrays.asList("instagram", "apple_connect");
        return vowelChannels.contains(channel);
    }

    private String getVowelPrefix(String channel) {
        return isVowelChannel(channel) ? "an" : "a";
    }

    private boolean checkBusinessIsReseller(BusinessLiteDTO businessLiteDTO) {
        return businessLiteDTO.getResellerId() != null && !BusinessAccountTypeEnum.DIRECT.getName().equalsIgnoreCase(businessLiteDTO.getAccountType());
    }

    private List<SocialPostEsRequest> createApprovalDataFromDB(List<SocialPostEsRequest> searchResponse, String sortParam, String sortOrder) {
        List<SocialPostEsRequest> socialPostEsRequests = new ArrayList<>();
        List<Integer> socialPostIds = searchResponse.stream().map(SocialPostEsRequest::getId).collect(Collectors.toList());
        logger.info("Response data for socialPostIds: {} from ES", socialPostIds);
        Sort.Direction direction = sortOrder.equalsIgnoreCase("asc") ? Sort.Direction.ASC : Sort.Direction.DESC;
        sortParam = sortParam.equals("schedule_time") ? "scheduleTime" : "id";
        List<SocialPostApproval> approvals = socialPostApprovalRepo.findAllBySocialPostIdIn(socialPostIds, new Sort(direction, Collections.singletonList(sortParam)));
        Map<Integer, SocialPostEsRequest> searchResponseMap = searchResponse.stream().collect(Collectors.toMap(SocialPostEsRequest::getId, Function.identity()));
        for(SocialPostApproval approval : approvals) {
            Integer socialPostId = approval.getSocialPostId();
            logger.info("Creating data for socialPostId: {} from db", socialPostId);
            SocialPostEsRequest socialPostEsRequest = searchResponseMap.get(socialPostId);
            SocialPostEsRequest esRequest = buildEsRequest(approval, socialPostEsRequest);
            socialPostEsRequests.add(esRequest);
        }
        return socialPostEsRequests;
    }

    private SocialPostEsRequest buildEsRequest(SocialPostApproval approval, SocialPostEsRequest searchResponse) {
        return SocialPostEsRequest.builder()
                .approval_request_id(approval.getApprovalRequestId())
                .approval_workflow_id(approval.getApprovalWorkflowId())
                .approval_status(approval.getApprovalStatus())
                .approval_uuid(approval.getApprovalUUId())
                .created_by(approval.getCreatedBy())
                .approval_user_ids(approval.getApprovalUserIds())
                .isEditedPost(Objects.nonNull(searchResponse.getIsEditedPost()) && searchResponse.getIsEditedPost())
                .aiPost(searchResponse.getAiPost())
                .created_by_name(searchResponse.getCreated_by_name())
                .created_date(approval.getCreatedDate())
                .edited_by(approval.getEditedBy())
                .id(approval.getSocialPostId())
                .auto_share(approval.getAutoShare())
                .image_ids(approval.getImageIds())
                .compressed_image_ids(approval.getCompressedImageIds())
                .conversation_id(approval.getConversationId())
                .mentions(approval.getMentions())
                .enterprise_id(approval.getEnterpriseId())
                .last_edited_at(approval.getLastEditedAt())
                .link_preview_url(approval.getLinkPreviewUrl())
                .master_post_id(approval.getMasterPostId())
                .page_ids(approval.getPageIds())
                .page_name(approval.getPageName())
                .post_meta_data(approval.getPostMetadata())
                .post_text(approval.getPostText())
                .referenceStepId(approval.getReferenceStepId())
                .profileImages(searchResponse.getProfileImages())
                .quoted_post_id(approval.getQuotedPostId())
                .requestType(approval.getApprovalStatus())
                .review_id(approval.getReviewId())
                .quoted_post_url(approval.getQuotedPostUrl())
                .tagIds(approval.getTags())
                .save_type(approval.getSaveType())
                .rejectedBy(approval.getRejectedBy())
                .rejectedById(approval.getRejectedById())
                .rejectedReason(approval.getRejectedReason())
                .video_ids(approval.getVideoIds())
                .schedule_time(approval.getScheduleTime())
                .scheduler_acknowledgement_id(approval.getSchedulerAcknowledgementId())
                .channel(approval.getChannel())
                .build();
    }
  
    private String buildAllTokenUrl(List<ApprovalWorkFlowData> dataList, ApprovalEmailDTO email, String websiteDomain, String status) {
        String combinedEncryptedPostId = dataList.stream().map(ApprovalWorkFlowData::getPostId).collect(Collectors.joining(","));
        String combinedEncryptedUUId = dataList.stream().map(ApprovalWorkFlowData::getApprovalUUId).collect(Collectors.joining(","));
        String url;
        ApprovalWorkFlowData data = dataList.get(0);
        String commonUrl;
        if("1".equals(status)) { //for approvalToken, we always show public page
            commonUrl = getCommonUrl(dataList.get(0), true, websiteDomain);
        } else { // rejectToken depends on user
            commonUrl = getCommonUrl(dataList.get(0), email.isPublic(), websiteDomain);
        }
        Integer approverUserId = null;
        String externalApproverEmail = null;
        if(email.isApprover()) {
            if(Objects.nonNull(email.getUserId())) {
                approverUserId = email.getUserId();
            } else {
                externalApproverEmail = email.getEmailId();
            }
        }
        if(!email.isPublic() && "2".equals(status)) { //for internal user, rejectAll will trigger to social bulk request on dashboard
            url = UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/approvals")
                    .queryParam("businessId", data.getBusinessId())
                    .queryParam("approvalPostId", combinedEncryptedPostId)
                    .queryParam("action", "reject")
                    .toUriString();
        } else {
            url = UriComponentsBuilder.fromUriString(commonUrl + data.getBusinessNumber() + "/approvalflow")
                    .queryParam("aid", combinedEncryptedUUId)
                    .queryParam("postId", combinedEncryptedPostId)
                    .queryParam("referenceStepId", data.getReferenceStepId())
                    .queryParam("status", status)
                    .toUriString();
        }
        return appendApproverDetails(url, approverUserId, externalApproverEmail);
    }

    private EmailDTO buildEmailDTO(ApprovalWorkFlowData data, ApprovalEmailDTO email, String subject) {
        EmailDTO emailDTO = new EmailDTO();
        emailDTO.setBusinessId(data.getBusinessId());
        emailDTO.setTo(Collections.singletonList(email.getEmailId()));
        emailDTO.setSubject(subject);
        emailDTO.setRequestType(ApprovalEnum.social_bulk_approval_request_template.name());
        emailDTO.setExternalUuid(emailDTO.getRequestType().concat(String.valueOf(data.getId())));
        return emailDTO;
    }

    private Map<String, Object> buildPayload(ApprovalWorkFlowData data, ApprovalEmailDTO email, ApprovalEnum status, String websiteDomain, String locationString) {
        logger.info("Building email payload for socialPostId: {}", data.getId(), status);
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> payload = objectMapper.convertValue(data, new TypeReference<Map<String, Object>>() {});

        payload.put("locations", locationString);
        payload.put("isPublishedEdit", data.getIsEditedPost());

        // Add tokens to payload
        Arrays.stream(ApprovalEnum.values())
                .filter(enumValue -> enumValue.getName().endsWith("Token")) //building token for ENUMS having 'Token' keyword prefixed
                .forEach(enumValue -> payload.put(enumValue.getName(),
                        prepareApprovalToken(data, enumValue, email, status, websiteDomain)));

        return payload;
    }

    private List<Integer> getSocialPostIdsForApproval(SocialPostPostIdRequest postIdRequest) {
        Integer masterPostId = postIdRequest.getMasterPostId();
        Integer socialPostId = postIdRequest.getPostId();
        List<Integer> socialPostIds = new ArrayList<>();
        if(masterPostId != null) {
            socialPostIds = socialPostRepository.findIdByMasterPostIdAndApprovalWorkflowIdIsNotNull(masterPostId);
        } else if(socialPostId != null) {
            socialPostIds = Collections.singletonList(socialPostId);
        }
        return socialPostIds;
    }

    private List<SocialPost> getSocialPostsForApproval(SocialPostPostIdRequest postIdRequest) {
        Integer masterPostId = postIdRequest.getMasterPostId();
        Integer socialPostId = postIdRequest.getPostId();
        List<SocialPost> socialPostIds = new ArrayList<>();
        if(masterPostId != null) {
            socialPostIds = socialPostRepository.findByMasterPostIdAndApprovalWorkflowIdIsNotNull(masterPostId);
        } else if(socialPostId != null) {
            socialPostIds = Collections.singletonList(socialPostRepository.findById(socialPostId));
        }
        return socialPostIds;
    }


    private String buildRescheduleSubmitTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl) {
        if (currentStatus.equals(ApprovalEnum.TERMINATED)) {
            UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/publish/createpost")
                    .queryParam("businessId", data.getBusinessId())
                    .queryParam("getBusinessIdpostId", data.getId())
                    .queryParam("action", "edit")
                    .queryParam("isQuotedPost", data.isQuoted())
                    .toUriString();
        }
        return null;
    }

    private String buildViewDetailTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl) {
        if(currentStatus.equals(ApprovalEnum.APPROVED)) {
            return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/publish")
                    .queryParam("businessId", data.getBusinessId())
                    .toUriString();
        }
        return null;
    }
    private String buildEditPostTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl) {
        if(currentStatus.equals(ApprovalEnum.REJECTED)) {
            return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/publish/createpost")
                    .queryParam("businessId", data.getBusinessId())
                    .queryParam("postId", data.getId())
                    .queryParam("action", "edit")
                    .queryParam("isQuotedPost", data.isQuoted())
                    .toUriString();
        }
        return null;
    }

    private String buildPostTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl,
                                     boolean isPublic, Integer approverUserId, String externalApproverEmail) {
        if(!isPublic) {
            if(currentStatus.equals(ApprovalEnum.PENDING))
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/approvals")
                        .queryParam("businessId", data.getBusinessId())
                        .queryParam("approvalPostId", data.getPostId())
                        .toUriString();
            else if(currentStatus.equals(ApprovalEnum.REJECTED) || currentStatus.equals(ApprovalEnum.TERMINATED))
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/rejectedpost")
                        .queryParam("businessId", data.getBusinessId())
                        .queryParam("approvalPostId", data.getPostId())
                        .toUriString();
            else
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/publish")
                        .queryParam("businessId", data.getBusinessId())
                        .toUriString();
        } else {
            String url = UriComponentsBuilder.fromUriString(commonUrl + data.getBusinessNumber() + "/approvalflow")
                    .queryParam("aid", data.getApprovalUUId())
                    .queryParam("postId", data.getPostId())
                    .queryParam("referenceStepId", data.getReferenceStepId())
                    .toUriString();
            return appendApproverDetails(url, approverUserId, externalApproverEmail);
        }
    }

    private String buildBulkPostTokenUrl(List<ApprovalWorkFlowData> dataList, ApprovalEmailDTO email, String websiteDomain) {
        boolean isPublic = email.isPublic();
        ApprovalWorkFlowData firstApprovalWorkFlowData = dataList.get(0);
        String currentStatus = firstApprovalWorkFlowData.getApprovalStatus();
        Integer businessId = firstApprovalWorkFlowData.getBusinessId();
        String commonUrl = getCommonUrl(dataList.get(0), isPublic, websiteDomain);
        Long businessNumber = firstApprovalWorkFlowData.getBusinessNumber();
        Integer referenceStepId = firstApprovalWorkFlowData.getReferenceStepId();
        Integer approverUserId = null;
        String externalApproverEmail = null;
        if(email.isApprover()) {
            if(Objects.nonNull(email.getUserId())) {
                approverUserId = email.getUserId();
            } else {
                externalApproverEmail = email.getEmailId();
            }
        }

        String combinedEncryptedPostId = dataList.stream().map(ApprovalWorkFlowData::getPostId).collect(Collectors.joining(","));
        String combinedEncryptedUUId = dataList.stream().map(ApprovalWorkFlowData::getApprovalUUId).collect(Collectors.joining(","));

        if(!isPublic) {
            if(currentStatus.equals(ApprovalEnum.PENDING.getName()))
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/approvals")
                        .queryParam("businessId", businessId)
                        .queryParam("approvalPostId", combinedEncryptedPostId)
                        .toUriString();
//                return commonUrl + "dashboard/social/approvals?businessId=" + businessId + "&approvalPostId=" + combinedEncryptedPostId;
            else if(currentStatus.equals(ApprovalEnum.REJECTED) || currentStatus.equals(ApprovalEnum.TERMINATED))
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/rejectedpost")
                        .queryParam("businessId", businessId)
                        .queryParam("approvalPostId", combinedEncryptedPostId)
                        .toUriString();
//                return commonUrl + "dashboard/social/rejectedpost?businessId=" + businessId + "&approvalPostId=" + combinedEncryptedPostId;
            else
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/publish")
                        .queryParam("businessId", businessId)
                        .toUriString();
//                return commonUrl + "dashboard/social/publish" + "?businessId=" + businessId;
        } else {
            String url = UriComponentsBuilder.fromUriString(commonUrl + businessNumber + "/approvalflow")
                    .queryParam("aid", combinedEncryptedUUId)
                    .queryParam("postId", combinedEncryptedPostId)
                    .queryParam("referenceStepId", referenceStepId)
                    .toUriString();
//            String url = commonUrl + businessNumber + "/approvalflow?aid="
//                    + combinedEncryptedUUId + "&postId=" + combinedEncryptedPostId + "&referenceStepId=" + referenceStepId;
            return appendApproverDetails(url, approverUserId, externalApproverEmail);
        }
    }


    private String buildRejectedTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl,
                                         boolean isPublic, Integer approverUserId, String externalApproverEmail) {
        if (currentStatus.equals(ApprovalEnum.PENDING)) {
            if (!isPublic) { //internal User
                return UriComponentsBuilder.fromUriString(commonUrl + "dashboard/social/approvals")
                        .queryParam("businessId", data.getBusinessId())
                        .queryParam("approvalPostId", data.getPostId())
                        .queryParam("action", "reject")
                        .toUriString();
//                return commonUrl + "dashboard/social/approvals?businessId=" + data.getBusinessId() + "&approvalPostId=" + data.getPostId() + "&action=reject";
            } else {
                String url = UriComponentsBuilder.fromUriString(commonUrl + data.getBusinessNumber() + "/approvalflow")
                        .queryParam("aid", data.getApprovalUUId())
                        .queryParam("postId", data.getPostId())
                        .queryParam("referenceStepId", data.getReferenceStepId())
                        .queryParam("status", "2")
                        .toUriString();
//                String url = commonUrl + data.getBusinessNumber() + "/approvalflow?aid=" + data.getApprovalUUId() + "&postId=" + data.getPostId() + "&referenceStepId=" + data.getReferenceStepId() + "&status=2";
                return appendApproverDetails(url, approverUserId, externalApproverEmail);
            }
        }
        return null;
    }

    private String buildApprovalTokenUrl(ApprovalWorkFlowData data, ApprovalEnum currentStatus, String commonUrl, Integer approverUserId, String externalApproverEmail) {
        if (currentStatus.equals(ApprovalEnum.PENDING)) {
            String url = UriComponentsBuilder.fromUriString(commonUrl + data.getBusinessNumber() + "/approvalflow")
                    .queryParam("aid", data.getApprovalUUId())
                    .queryParam("postId", data.getPostId())
                    .queryParam("referenceStepId", data.getReferenceStepId())
                    .queryParam("status", "1")
                    .toUriString();
//            String url = commonUrl + data.getBusinessNumber() + "/approvalflow?aid=" + data.getApprovalUUId() + "&postId=" + data.getPostId() + "&referenceStepId=" + data.getReferenceStepId() + "&status=1";
            return appendApproverDetails(url, approverUserId, externalApproverEmail);
        }
        return null;
    }

    private String appendApproverDetails(String url, Integer approverUserId, String externalApproverEmail) {
        if (approverUserId != null) {
            url += "&approverUserId=" + approverUserId;
        } else if (externalApproverEmail != null) {
            url += "&externalApproverEmail=" + externalApproverEmail;
        }
        return url;
    }

    private String getCommonUrl(ApprovalWorkFlowData data, boolean isPublic, String websiteDomain) {
        String publicUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getPublicUrl();
        String whitelabelUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getWhiteLabelUrl();
        String dashboardUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBirdeyeUrl();

        return isPublic
                ? (data.isWhitelabel() ? whitelabelUrl : publicUrl)
                : (StringUtils.isEmpty(websiteDomain) ? dashboardUrl : websiteDomain + "/");
    }

    private boolean isEligibleForBulkReminder(Integer masterPostId, List<Integer> socialPostIds) {
        Integer enterpriseId = socialPostPublishInfoRepo.getEnterpriseIdBySocialPostId(socialPostIds.get(0));
        boolean isEligibleForBulk = false;
        if(socialPostIds.size() > 1 && commonService.checkSocialPostScheduleDateIsSame(masterPostId)
        && commonService.checkEligibilityForBulkApprovalEvents(enterpriseId)) { // has multiple cards and schedule date is same
            isEligibleForBulk = true;
        }
        return  isEligibleForBulk;
    }

    private List<ApprovalWorkFlowData> processSinglePostForScheduleEvent(List<Integer> socialPostIds, SocialPostPostIdRequest request) throws Exception {

        List<SocialPost> socialPostList = socialPostRepository.findByIdIn(socialPostIds);

        List<SocialPostScheduleInfo> socialPostScheduleInfoList = socialPostScheduleInfoRepo.findBySocialPostIdIn(socialPostIds);
        Map<Integer, SocialPostScheduleInfo> socialPostIdVsScheduleInfoMap = socialPostScheduleInfoList.stream()
                .collect(Collectors.toMap(SocialPostScheduleInfo::getSocialPostId, Function.identity()));

        List<ApprovalWorkFlowData> approvalWorkFlowDataList = getBulkApprovalData(socialPostList, socialPostIdVsScheduleInfoMap);
        for(ApprovalWorkFlowData approvalWorkFlowData : approvalWorkFlowDataList) {
            setApprovalUserIds(request, approvalWorkFlowData);
            setAuthorName(approvalWorkFlowData);
            approvalWorkFlowData.setApprovalStatus(ApprovalStatus.PENDING.getName());

            String approvalStatus = approvalWorkFlowData.getType();
            logger.info("Approval status for postId: {}: {}", approvalWorkFlowData.getId(), approvalStatus);

            if (!ApprovalStatus.PENDING.getName().equalsIgnoreCase(approvalStatus)) {
                return null; // Skip if not pending
            }

            // Add schedule date for apple
            String channel = approvalWorkFlowData.getChannel();
            if (StringUtils.equalsIgnoreCase(channel, SocialChannel.APPLE_CONNECT.getName())) {
                approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
            }

            // Generate collage and update text
            approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData, approvalWorkFlowData.getBusinessNumber()));
            approvalWorkFlowData.setText(addMentionsInTheText(approvalWorkFlowData.getText(), approvalWorkFlowData.getMentions()));

            // Encrypt aid and socialPostId
            try {
                approvalWorkFlowData.setApprovalWorkFlowId(encryptData(approvalWorkFlowData.getApprovalWorkFlowId()));
                approvalWorkFlowData.setPostId(encryptData(String.valueOf(approvalWorkFlowData.getId())));
            } catch (Exception e) {
                logger.error("Error while encrypting approvalWorkflowId: ", e);
            }

            // Set page initials if profile image is invalid
            if (StringUtils.isEmpty(approvalWorkFlowData.getPageProfileImage())
                    || !commonService.isValidUrl(approvalWorkFlowData.getPageProfileImage())) {
                approvalWorkFlowData.setPageInitials(commonService.getInitials(approvalWorkFlowData.getPageName()));
            }
        }

        return approvalWorkFlowDataList;
    }

    private String determineEmailSubject(ApprovalWorkFlowData data, String scheduledDateString, boolean isBulkSchedule) {
        if (Boolean.TRUE.equals(data.getIsEditedPost())) {
            return getSubject2Param(data, scheduledDateString, data.getChannel(), Constants.APPROVAL_EMAIL_SUBJECT_EDIT);
        } else if (isBulkSchedule) {
            return Constants.APPROVAL_BULK_SCHEDULE_EMAIL_SUBJECT;
        } else {
            return getSubject(data, data.getChannel(), Constants.APPROVAL_EMAIL_SUBJECT, data.getAuthorName());
        }
    }

    private String determineEmailSubSubject(ApprovalWorkFlowData data, String scheduledDateString, boolean isBulkSchedule) throws ParseException {
        if (Boolean.TRUE.equals(data.getIsEditedPost())) {
            return getSubject3Param(data, scheduledDateString, data.getChannel(), Constants.APPROVAL_EMAIL_SUB_SUBJECT_EDIT, data.getAuthorName());
        } else if (isBulkSchedule) {
            if(Boolean.TRUE.equals(data.getIsSameContent())) {
                return String.format(Constants.APPROVAL_BULK_SCHEDULE_SINGLE_CARD_EMAIL_SUB_SUBJECT, StringUtils.capitalize(data.getAuthorName()), getScheduledDateString(data, data.getChannel()));
            } else {
                return String.format(Constants.APPROVAL_BULK_SCHEDULE_MULTI_CARD_EMAIL_SUB_SUBJECT, StringUtils.capitalize(data.getAuthorName()));
            }
        } else {
            return getSubSubject(data, scheduledDateString, data.getChannel(), Constants.APPROVAL_SCHEDULE_EMAIL_SUBJECT, data.getAuthorName());
        }
    }

    private String getLocationString(int locationCount) {
        return locationCount > 1 ? locationCount + " locations" : locationCount + " location";
    }

    private Set<String> getAllChannelsForSameContent(List<ApprovalWorkFlowData> approvalWorkFlowDataList) {
        return approvalWorkFlowDataList.stream().map(ApprovalWorkFlowData::getChannel).collect(Collectors.toSet());
    }

    private ApprovalWorkFlowData createApprovalWorkflowData(Integer socialPostId) throws ParseException, IOException {
        ApprovalWorkFlowData approvalWorkFlowData = getApprovalDataFromPostId(socialPostId, null);
        ApprovalStatus approvalStatus = ApprovalStatus.getApprovalStatusByName(approvalWorkFlowData.getApprovalStatus()); // null if approvalMetadata not present
        logger.info("Approval status for postId: {}: {}", socialPostId, approvalStatus);
        if (Objects.equals(ApprovalStatus.PENDING, approvalStatus)) { // false if approvalMetadata not found
            approvalWorkFlowData.setText(addMentionsInTheText(approvalWorkFlowData.getText(), approvalWorkFlowData.getMentions()));
            approvalWorkFlowData.setCollageUrl(generateCollage(approvalWorkFlowData, approvalWorkFlowData.getBusinessNumber()));
            if (StringUtils.equalsIgnoreCase(approvalWorkFlowData.getChannel(), SocialChannel.APPLE_CONNECT.getName())) {
                approvalWorkFlowData.setScheduleDate(getScheduledDate(approvalWorkFlowData.getPostMetaData()));
            }
            return approvalWorkFlowData;
        } else {
            return null;
        }
    }

    private String getCreatorName(String createdBy) {
        String createdName;
        try {
            Integer userId = Integer.parseInt(createdBy);
            BusinessCoreUser businessCoreUser = businessCoreService.getUserInfo(userId);
            createdName = businessCoreUser.getName();
        } catch (Exception e) {
            createdName = createdBy;
        }
        return createdName;
    }

    private void createCommonDataForReminderAndSendReminderMail(List<ApprovalWorkFlowData> approvalWorkFlowDataList) throws ParseException {
        if (CollectionUtils.isNotEmpty(approvalWorkFlowDataList)) {
            ApprovalWorkFlowData approvalWorkFlowData = approvalWorkFlowDataList.get(0);
            if(approvalWorkFlowData != null) {
                String createdName = getCreatorName(String.valueOf(approvalWorkFlowData.getCreatedBy()));
                approvalWorkFlowData.setAuthorName(createdName);
                String scheduledDateString = getScheduledDateString(approvalWorkFlowData, approvalWorkFlowData.getChannel());
                String subject = String.format(Constants.APPROVAL_REMINDER_EMAIL_SUBJECT, getPostType(approvalWorkFlowData.getTopicType(), SocialChannel.getEmailDisplayByName(approvalWorkFlowData.getChannel())));
                String subSubject = String.format(Constants.APPROVAL_REMINDER_EMAIL_SUB_SUBJECT, StringUtils.capitalize(createdName), scheduledDateString);
                List<ApprovalEmailDTO> approvalEmailIds = getApprovarsEmailIds(approvalWorkFlowData.getApprovalUserIds());
                sendEmail(approvalWorkFlowDataList, approvalEmailIds, subject, subSubject);
            }
        }
    }
}

