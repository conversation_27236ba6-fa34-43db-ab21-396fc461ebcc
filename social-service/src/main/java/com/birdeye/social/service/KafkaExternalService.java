package com.birdeye.social.service;

import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.AppleShowcaseEmail;
import com.birdeye.social.model.IgContainerCheckDTO;
import com.birdeye.social.model.SocialPostInputMessageRequest;
import com.birdeye.social.model.TemplateDeleteEventDTO;
import com.birdeye.social.model.linkinbio.LinkInfoEventRequest;

import java.io.Serializable;
import java.util.Date;

public interface KafkaExternalService {

    void publishSocialPostEvent(SocialPostPublishInfo socialPostPublishInfo);

    void publishSocialPostEvent(Integer id, Integer userId, Integer status, String failureReason, String saveType,
                                Date publishDate, Boolean autoShare, String postUrl, Integer sourceId, Integer businessId);

    void pushSocialPostingToES(Integer followerCount,Integer source, Date postedOn,Integer id, Integer enterpriseId, Integer reviewId,Integer postedBy);

    void publishDeleteTemplateEvent(TemplateDeleteEventDTO deleteEventDTO);

    void pushLinkInBio(LinkInfoEventRequest linkInfoEventRequest);

    void publishToNexusKafkaAsync(String topic, Serializable key, Serializable value);

    void sendAppleStatusEmailEvent(AppleShowcaseEmail showcaseEmail);
    void pushIgContainerCheck(IgContainerCheckDTO igContainerCheckDTO);

    void markPageInvalid(String channel, String pageId);
}
