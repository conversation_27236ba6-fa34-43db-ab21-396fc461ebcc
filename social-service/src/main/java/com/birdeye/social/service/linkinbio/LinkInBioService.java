package com.birdeye.social.service.linkinbio;

import com.birdeye.social.model.linkinbio.*;

import java.util.List;

public interface LinkInBioService {

    LinkInBioResponse createLinkInBio(LinkInBioRequest linkInBioRequest);

    LinkInBioWrapperResponse getLinkInBioDetails(Long enterpriseId, LinkInBioGetRequest request);

    LinkValidationResponse checkIfLinkIsPresent(java.lang.String url);

    List<LinkValidationResponse> validateCreatePostLinks(Long enterpriseId,Integer businessId,List<LinkValidationRequest> request);

    void updateLinksForLinkInBio(Integer linkInBioId, LinkInBioRequest linkInBioRequest);

    LinkInBioResponse getLinksOfLinkInBio(Integer linkInBioId);

    void deleteLinkInBio(Integer linkInBioId);

    void deleteLinkForLinkInBio(Integer linkInBioId, Integer linkId);

    LinkInBioPublicResponse getLinkInBioResponse(String linkInBioUrl);

    void updateClickCount(Integer buttonId);

    void updateViewCount(Integer linkId, Long enterpriseId);

    LinkInBioWrapperResponse getReportsData(Long enterpriseId, LinkInBioGetRequest request);

    void createLinkInBioForCoreEvent(BusinessSignUpEvent businessSignUpEvent);

    LinkInBioResponse updateLinkInBioInfo(LinkInBioRequest linkInBioRequest, Integer linkId);

    void createLinksIfNotPresent(LinkInfoEventRequest request);

    void postLinkInBioForBusiness(BusinessSignUpEvent event);

    LinkInBioWrapperResponse getLinkInBioResponseForEnterprise(Long businessNumber);

    LinkInBioResponse getLinksForSmb(Long enterpriseId,Integer businessId);

    void updateSocialBusinessProperty(BusinessPropertyEventRequest request);

    void createLinkInBioViaSocialEnabled(BusinessPropertyEventRequest propertyEventRequest);

    void removeDuplicate(List<Long> businessNumberList);

    void deleteScheduledPostsOnSocialDisbale(BusinessPropertyEventRequest request);
}
