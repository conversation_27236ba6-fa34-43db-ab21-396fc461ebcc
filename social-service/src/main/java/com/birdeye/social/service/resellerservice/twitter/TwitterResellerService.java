package com.birdeye.social.service.resellerservice.twitter;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.*;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.TwitterSocialAccountService;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.service.SocialPostTwitterService;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import org.springframework.stereotype.Service;
import twitter4j.TwitterException;

@Service
@Slf4j
public class TwitterResellerService implements SocialReseller {

    @Autowired
    private SocialPostTwitterService socialPostTwitterService;

    @Autowired
    private SocialTwitterAccountRepository socialTwitterRepo;

	 @Autowired
	  private TwitterSocialAccountService twitterSocialAccountService;

    @Autowired
    private CommonService commonService;

    



    @Override
    public String channelName() {
        return SocialChannel.TWITTER.getName();
    }

	@Override
	public PaginatedConnectedPages getPages(Long resellerId, PageConnectionStatus pageConnectionStatus,
                                            Integer page, Integer size, String search, ResellerSearchType searchType,
                                            PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds,
                                            MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type) {
        return twitterSocialAccountService.getResellerPages(resellerId, pageConnectionStatus, page, size, search,
                searchType, sortDirection, sortParam, locationIds, mappingStatus, userIds, locationFilterSelected, Constants.RESELLER);

    }

	@Override
	public void removeResellerPages(List<String> pageIds, Integer limit) {
		twitterSocialAccountService.removeXAccountsForReseller(pageIds, limit);
	}

    @Override
    public ChannelPageInfo connectResellerPages(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr){

        TwitterConnectAccountRequest twitterConnectAccountRequest= createAccountRequest(pageIds,resellerId,selectAll,searchStr);
        ChannelPageInfo accountInfo= socialPostTwitterService.connectTwitterPagesV1(twitterConnectAccountRequest);
        commonService.setDisabledAsNullForAllChannel(accountInfo);
        return accountInfo;
    }

    @Override
    public ChannelConnectedPageInfo checkIfAccountExistsByResellerId(Long accountId) {
        ChannelConnectedPageInfo channelConnectedPageInfo= new ChannelConnectedPageInfo();
        boolean igAccountExists = socialTwitterRepo.existsByResellerIdAndIsSelected(accountId,1);
        channelConnectedPageInfo.setTwitterAccountExists(igAccountExists);
        return channelConnectedPageInfo;
    }

    @Override
    public void reconnectResellerPages(Long resellerId, ChannelAllPageReconnectRequest request,
                                         Integer userId, String type, Integer limit) {

        TwitterAuthRequest twitterAuthRequest= createAuthRequest(resellerId,request,userId,type);
        socialPostTwitterService.reconnectTwitterEnhancedFlow(twitterAuthRequest);
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String type) throws TwitterException {
        socialPostTwitterService.submitFetchPageRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(), authRequest.getRequestToken(), authRequest.getRequestSecret(), authRequest.getOauthVerifier(),type);
    }


    @Override
    public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer size, String search) {
        return socialPostTwitterService.getPaginatedPages(businessGetPageRequest,pageNumber,size,search);

    }




    private TwitterAuthRequest createAuthRequest(Long resellerId,ChannelAllPageReconnectRequest request,Integer userId,  String type) {
        TwitterAuthRequest authRequest = new TwitterAuthRequest();
        authRequest.setBirdeyeUserId(userId);
        authRequest.setBusinessId(resellerId);
        authRequest.setTempAccessToken(request.getRequestToken());
        authRequest.setSecret(request.getRequestSecret());
        authRequest.setOauthVerifier(request.getOauthVerifier());
        authRequest.setPageId(request.getPageId());
        authRequest.setType(type);
        return authRequest;
    }

    private TwitterConnectAccountRequest createAccountRequest(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
        TwitterConnectAccountRequest twitterConnectAccountRequest= new TwitterConnectAccountRequest();
        twitterConnectAccountRequest.setId(pageIds);
        twitterConnectAccountRequest.setBusinessId(resellerId);
        twitterConnectAccountRequest.setType(Constants.RESELLER);
        twitterConnectAccountRequest.setSearchStr(searchStr);
        twitterConnectAccountRequest.setSelectAll(selectAll);
        return twitterConnectAccountRequest;
    }

	@Override
	public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId,String pageType,
			Integer userId,Boolean force, Long resellerId) {
		Map<String, Object> response = new HashMap<>();
		log.info("[Reseller page mapping] For channel {}  page Id {} mapping with location Id {}", channel, pageId, locationId);
		if (StringUtils.isEmpty(pageId) || locationId == null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_PAGE_OR_LOCATION_ID, "page/location id is null");
		}
		twitterSocialAccountService.saveTwitterLocationMapping(locationId, Long.valueOf(pageId), userId,Constants.RESELLER, resellerId);
		return response;
	}

	@Override
	public void removePageMappings(List<LocationPageMappingRequest> input) {
		twitterSocialAccountService.removeTwitterLocationAccountMappings(input,Constants.RESELLER, false);
	}

    /**
     * @param resellerLeafLocationIds
     * @return
     */
    @Override
    public List<Integer> getMappedResellerLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        return socialPostTwitterService.getMappedResellerLeafLocations(resellerLeafLocationIds);
    }

}
