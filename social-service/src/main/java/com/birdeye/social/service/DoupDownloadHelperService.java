package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.SocialSchedulePostStatusEnum;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.insights.ES.CalendarExportViewPagePostInsightsData;
import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DoupDownloadHelperService {

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";


    private static final Logger log = LoggerFactory.getLogger(DoupDownloadHelperService.class);

    public GlobalFilterCriteriaSchedulePostMessage convertToGlobalFilterCriteria(CalendarPostsExportRequest exportFilter,
                                                        Integer startIndex,String sortOrder,Integer pageSize, Integer accountId) {
        GlobalFilterCriteriaSchedulePostMessage filter = new GlobalFilterCriteriaSchedulePostMessage();
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(SCHEDULE_DATE_FORMAT);
            filter.setStartDate(dateFormat.format(exportFilter.getStartDate()));
            filter.setEndDate(dateFormat.format(exportFilter.getEndDate()));

            filter.setPostStatus(exportFilter.getPostStatus());
            filter.setSocialChannels(exportFilter.getSocialChannels());
            filter.setBusinessId(exportFilter.getBusinessId());
            filter.setBusinessIds(exportFilter.getBusinessIds());
            filter.setAccessibleLocationIds(exportFilter.getAccessibleLocationIds());
            filter.setTagIds(exportFilter.getTagIds()!=null?exportFilter.getTagIds(): new HashSet<>());
            filter.setApprovals(exportFilter.getApprovals()!=null?exportFilter.getApprovals(): new ArrayList<>());
            filter.setCreators(exportFilter.getCreators()!=null?exportFilter.getCreators(): new ArrayList<>());
            filter.setPostType(exportFilter.getPostType()!=null?exportFilter.getPostType(): new ArrayList<>());
            filter.setPostContent(exportFilter.getPostContent()!=null?exportFilter.getPostContent(): new ArrayList<>());
            filter.setBusinessIdsSelected(Objects.equals(exportFilter.getBusinessIdsSelected(), Boolean.TRUE));
            filter.setShowAiSuggestions(Objects.equals(exportFilter.getShowAiSuggestions(), Boolean.TRUE));
            filter.setHasFullAccess(Objects.equals(exportFilter.getHasFullAccess(), Boolean.TRUE));
            filter.setPostStatuses(exportFilter.getPostStatuses()!= null ? exportFilter.getPostStatuses() : new ArrayList<>());

            filter.setBusinessId(accountId);
            filter.setPageNo(startIndex);
            filter.setPageSize(pageSize);
            filter.setOrder("asc".equalsIgnoreCase(sortOrder) ? PageSortDirection.ASC : PageSortDirection.DESC);
        }catch (Exception e) {
            log.error("Error converting export filter to global filter criteria: {}", e.getMessage());
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid export filter criteria provided.");
        }

        return filter;
    }

    // This method prepares the calendar posts data for export.
    public Map<String, List<CalendarSchedulePostExportMessage>> prepareCalendarPostsData(Map<String, List<SocialSchedulePostMessage>> calendarPosts){
        if(calendarPosts == null || calendarPosts.isEmpty()) {
            log.info("No calendar posts found for the given export filter.");
            return Collections.emptyMap();
        }

        return calendarPosts.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> Optional.ofNullable(entry.getValue())
                                .orElse(Collections.emptyList())
                                .stream()
                                .map(this::mapToExportMessage)
                                .collect(Collectors.toList())
                ));
    }

    private CalendarSchedulePostExportMessage mapToExportMessage(SocialSchedulePostMessage post) {
        return CalendarSchedulePostExportMessage.builder()
                .id(post.getId())
                .postText(post.getPostText())
                .postHeader(post.getPostHeader())
                .images(post.getImages())
                .compressedImages(post.getCompressedImages())
                .videos(post.getVideos())
                .videoThumbnails(post.getVideoThumbnails())
                .linkPreviewUrl(post.getLinkPreviewUrl())
                .publishDate(post.getPublishDate())
                .publishedBy(post.getPublishedBy())
                .datePublish(post.getDatePublish())
                .scheduleInfoId(post.getScheduleInfoId())
                .postingSites(post.getPostingSites())
                .isPublished(post.getIsPublished())
                .mediaSequence(post.getMediaSequence())
                .mentions(post.getMentions())
                .permissionStatus(post.getPermissionStatus())
                .postInsights(post.getPostInsights())
                .hasAccess(post.getHasAccess())
                .incompleteChannel(post.getIncompleteChannel())
                .hasPostFailed(post.getHasPostFailed())
                .failedChannelCount(post.getFailedChannelCount())
                .failedPageCount(post.getFailedPageCount())
                .failedSites(post.getFailedSites())
                .type(post.getType())
                .gmbOfferDetails(post.getGmbOfferDetails())
                .isExpired(post.getIsExpired())
                .isQuotedTweet(post.getIsQuotedTweet())
                .tags(post.getTags())
                .isCreator(post.getIsCreator())
                .isApprover(post.getIsApprover())
                .approveWorkflowId(post.getApproveWorkflowId())
                .approvalStatus(post.getApprovalStatus())
                .approvalUUId(post.getApprovalUUId())
                .approvalRequestId(post.getApprovalRequestId())
                .conversationId(post.getConversationId())
                .referenceStepId(post.getReferenceStepId())
                .endDate(post.getEndDate())
                .applePublishStatus(post.getApplePublishStatus())
                .isOperationAllowed(post.getIsOperationAllowed())
                .quotedTweetSource(post.getQuotedTweetSource())
                .createdByName(post.getCreatedByName())
                .aiPost(post.getAiPost())
                .aiSuggestion(post.getAiSuggestion())
                .build();
    }

    public Map<String, Object> fetchBusinessDataFromCore(List<Integer> businessIds) {
        Map<String, Object> businessData;
        try {
            businessData = businessCoreService.getBusinessesInBulkByBusinessIds(businessIds, true);
        }catch (Exception e){
            log.error("[DoupDownloadServiceImpl] Error while fetching business data: {}", e.getMessage());
            businessData = new HashMap<>();
        }
        return businessData;
    }

    public Map <String, String> getLocationIdVsLocationNameFromCore(List<Integer> businessIds){
        if(CollectionUtils.isEmpty(businessIds)) {
            log.info("No business IDs provided for fetching location names.");
            return Collections.emptyMap();
        }
        Map<String, Object> businessData = fetchBusinessDataFromCore(businessIds);

        Map <String, String> responseMap = new HashMap<>();
        for(Map.Entry < String, Object > entry : businessData.entrySet()) {
            String businessId = entry.getKey();
            String locationName = null;
            ObjectMapper om = new ObjectMapper();
            JsonNode jsonNode = om.convertValue(businessData, JsonNode.class);

            if (Objects.nonNull(jsonNode) && Objects.nonNull(jsonNode.get(businessId))) {
                JsonNode node = jsonNode.get(businessId).get("businessName");
                if (Objects.nonNull(node) && StringUtils.isNotEmpty(node.asText())) {
                    locationName = node.asText();
                }
            }
            if (StringUtils.isNotEmpty(locationName)) {
                responseMap.put(businessId, locationName);
            }
        }
        return responseMap;
    }

    /*
     * This method prepares the post page data for each post in the response.
     * This is an alternative for @PostMapping(value = "/pages") used in postingController for exporting posts.
     */
    public void preparePostPageAndInsightsData(CalendarPostsExportResponse response, CalendarExportPostPageStatusData postPageData,
                                               Map<String, String> locIdVsName, Map<Integer, List<CalendarViewPagePostInsightsData>> insightsData) {
        if (postPageData == null || postPageData.getPageData() == null || postPageData.getPageData().isEmpty()) {
            log.info("No post page data found for the given response.");
            return;
        }

        Map<String, LocationPagePair> filteredPageDataMap = postPageData.getPageData();

        for (String dateKey : response.getPosts().keySet()) {
            for (CalendarSchedulePostExportMessage post : response.getPosts().get(dateKey)) {
                try {
                    processPostPageAndInsights(post, postPageData, filteredPageDataMap, locIdVsName, insightsData);
                } catch (Exception ex) {
                    log.error("Error preparing post page data for post ID: {} error: {}", post.getId(), ex.getMessage());
                }
            }
        }
    }

    private void processPostPageAndInsights(CalendarSchedulePostExportMessage post,
                                            CalendarExportPostPageStatusData postPageData,
                                            Map<String, LocationPagePair> filteredPageDataMap,
                                            Map<String, String> locIdVsName,
                                            Map<Integer, List<CalendarViewPagePostInsightsData>> insightsData) {
        List<String> pageIds = postPageData.getPostIdVsPageIdsMap().get(post.getId());
        if (CollectionUtils.isEmpty(pageIds)) {
            log.info("No page IDs found for post ID: {}", post.getId());
            return;
        }
        List<SocialPostInfoRepository.PostPageStatus> postPageStatusList =
                postPageData.getPostPageStatusMap().getOrDefault(post.getId(), new ArrayList<>());
        Map<String, SocialPostInfoRepository.PostPageStatus> postPageStatusMap = postPageStatusList.stream()
                .collect(Collectors.toMap(SocialPostInfoRepository.PostPageStatus::getExternalPageId, Function.identity()));

        preparePostPageMessageMap(post, pageIds, filteredPageDataMap, postPageStatusMap, locIdVsName);

        List<CalendarViewPagePostInsightsData> insightsList = insightsData.get(post.getId());
        if (CollectionUtils.isNotEmpty(insightsList)) {
            post.setInsightsData(getInsightsDataTotal(insightsList));
        }
    }

    /*
    * This method set the post page message map for each post.
    * It prepares the List of PostPageMessage for each post ID.
    * It also groups the PostPageMessage objects by their publishState and counts the number of entries for each state.
    * For states other than 'failed', it trims the list to only keep the first entry for each state.
     */
    public void preparePostPageMessageMap(CalendarSchedulePostExportMessage post, List<String> pageIds,
                                          Map<String, LocationPagePair> filteredPageDataMap,
                                          Map<String, SocialPostInfoRepository.PostPageStatus> postPageStatusMap,
                                          Map <String, String> locIdVsName) {
        List<PostPageMessage> postPageMessage = new ArrayList<>();
        for(String pageId: pageIds) {
            try {
                LocationPagePair pageData = filteredPageDataMap.get(pageId);
                SocialPostInfoRepository.PostPageStatus postPageStatus = postPageStatusMap.get(pageId);
                PostPageMessage postPageMessageItem = preparePostPageMessage(post.getIsPublished(), pageData, postPageStatus, locIdVsName);
                postPageMessage.add(postPageMessageItem);
            }catch (Exception ex){
                log.warn("Error preparing post page message for post ID: {} and page ID: {} error: {}", post.getId(), pageId, ex.getMessage());
            }
        }
        Map<String, List<PostPageMessage>> pageData = postPageMessage.stream().collect(Collectors.groupingBy(PostPageMessage::getPublishState));
        Map<String, Integer> pageDataCount = new HashMap<>();
        int failedCount = 0;

        for (Map.Entry<String, List<PostPageMessage>> entry : pageData.entrySet()) {
            String key = entry.getKey();
            if(key.toLowerCase().contains("failed")){
                // All failed categories are grouped under 'failedCount' and list will not be trimmed as need all failureReasons
                failedCount += entry.getValue().size();
            }else {
                List<PostPageMessage> messages = entry.getValue();
                pageDataCount.put(key + "Count", messages.size());
                PostPageMessage selectedMessage = messages.stream()
                        .filter(msg ->  StringUtils.isNotEmpty(msg.getPageName()))
                        .findFirst()
                        .orElse(messages.get(0)); // If all pageNames are null, take the first one
                // Trim list as we only need the first page data for each key
                if (CollectionUtils.isNotEmpty(messages) && messages.size() > 1) {
                    pageData.put(key, Collections.singletonList(selectedMessage)); // Keep only the first page data
                }
            }
        }
        if(failedCount>0){
            pageDataCount.put("failedCount", failedCount);
        }
        post.setPageData(pageData);
        post.setPageDataCount(pageDataCount);
    }

    /*
     * This method prepares the PostPageMessage object based on the post state and page data.
     * It sets the page ID, location ID, page name, profile picture URL, and location name.
     * It also sets the publishState and failure comment based on the post state and post page status.
     */
    public PostPageMessage preparePostPageMessage(Integer postState, LocationPagePair pageData,
                                                  SocialPostInfoRepository.PostPageStatus postPageStatus,
                                                  Map <String, String> locIdVsName) {
        PostPageMessage postPageMessage = new PostPageMessage();
        if (pageData != null) {
            postPageMessage.setPageId(pageData.getPageId());
            postPageMessage.setLocationId(pageData.getLocationId());
            postPageMessage.setPageName(pageData.getPageName());
            postPageMessage.setProfilePictureUrl(pageData.getProfilePictureUrl());
            postPageMessage.setLocationName(pageData.getLocationId()!=null?locIdVsName.get(pageData.getLocationId().toString()): null);
        }
        if(postState.equals(SocialSchedulePostStatusEnum.SCHEDULED.getId()) ||
                postState.equals(SocialSchedulePostStatusEnum.APPROVAL_REJECTED.getId()) ||
                postState.equals(SocialSchedulePostStatusEnum.APPROVAL_TERMINATED.getId())){
            postPageMessage.setPublishState(socialPostService.convertPublishedStateToStatus(postState, null));
            postPageMessage.setFailureComment(null);
        }else if(postPageStatus!=null && (postState.equals(SocialSchedulePostStatusEnum.POSTED.getId()) ||
                postState.equals(SocialSchedulePostStatusEnum.IS_PICKED.getId()))){
            postPageMessage.setPublishState(socialPostService.convertPublishedStateToStatus(postPageStatus.getIsPublished(), postPageStatus.getBucket()));
            postPageMessage.setFailureComment(postPageStatus.getFailureReason());
        }
        return postPageMessage;
    }

    /*
        * We receive the filtersSelected from the export filter request.
        * We only add the location names and timePeriod to them and return that in response.
        * When user hits download, we get time according to user timezone, so we convert the start and end date to user timezone.
     */
    public Map<String, Object> getFiltersSelectedData(CalendarPostsExportRequest exportFilter, Map<String, String> locIdVsName, String userTimeZone) {
        Map<String, Object> filtersSelected = exportFilter.getFiltersSelected();
        if(StringUtils.isEmpty(userTimeZone)){
            userTimeZone = "UTC"; // Default to UTC if no timezone is provided
        }
        if (filtersSelected == null) {
            log.info("No filters selected for the given export filter. Setting filters selected to empty map.");
            filtersSelected = new HashMap<>();
        }

        if (exportFilter.getStartDate() != null && exportFilter.getEndDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM d, yyyy");
            sdf.setTimeZone(TimeZone.getTimeZone(userTimeZone));

            String startDate = sdf.format(new Date(exportFilter.getStartDate()));
            String endDate = sdf.format(new Date(exportFilter.getEndDate()));

            filtersSelected.put("timePeriod", startDate + " – " + endDate);
        }else{
            log.info("No start date or end date found for the given export filter. Setting timePeriod to empty string.");
            filtersSelected.put("timePeriod", "");
        }

        List<String> businessNames = exportFilter.getBusinessIds() == null
                ? new ArrayList<>()
                : exportFilter.getBusinessIds().stream()
                .map(String::valueOf)
                .map(locIdVsName::get)
                .filter(Objects::nonNull)
                .sorted()
                .collect(Collectors.toList());

        filtersSelected.put("businessIds", businessNames);

        return filtersSelected;
    }

    public Map<Integer, List<CalendarViewPagePostInsightsData>>  getBulkPostPageInsightsDataFromEs(CalendarExportPostPageStatusData postPageData) {
        if (postPageData.getPageData()==null || postPageData.getPageData().isEmpty() ||
                postPageData.getPostPageStatusMap() == null || postPageData.getPostPageStatusMap().isEmpty()) {
            log.info("No post page data or post page status data found for the given export filter.");
            return new HashMap<>();
        }
        List<String> pageIds = new ArrayList<>(postPageData.getPageData().keySet());
        List<Integer> bePostIds = new ArrayList<>(postPageData.getPostPageStatusMap().keySet()); // all published posts, socialPostIds
        try {
            Map<Integer, List<CalendarViewPagePostInsightsData>> insightsData = socialPostService.getInsightDataBulk(bePostIds, pageIds);
            log.info("Fetched post page insights data from ES for postIds : {}", insightsData.keySet());
            return insightsData;
        }catch (Exception e) {
            log.error("Error fetching bulk post page insights data from ES: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    public CalendarExportViewPagePostInsightsData getInsightsDataTotal(List<CalendarViewPagePostInsightsData> postInsightsData){

        CalendarExportViewPagePostInsightsData insightDataTotal = new CalendarExportViewPagePostInsightsData();
        int totalImpression = 0;
        int totalEngagement = 0;
        int totalReach = 0;
        int totalLike = 0;
        int totalComment = 0;
        int totalShare = 0;
        int totalClick = 0;
        for (CalendarViewPagePostInsightsData data : postInsightsData) {
            totalImpression += data.getImpression();
            totalEngagement += data.getEngagement();
            totalReach += data.getReach();
            totalReach += data.getReach();
            totalLike += data.getLikeCount();
            totalComment += data.getCommentCount();
            totalShare += data.getShareCount();
            totalClick += data.getClickCount();
        }

        Double aggregatedEngagementRate = 0d;
        if(totalImpression != 0 ) {
            aggregatedEngagementRate =  ((double)totalEngagement/(double)totalImpression)*100d;
        }
        insightDataTotal.setTotalEngagementRate(Double.parseDouble(new DecimalFormat("#.#").format(aggregatedEngagementRate)));

        insightDataTotal.setTotalImpression(totalImpression);
        insightDataTotal.setTotalEngagement(totalEngagement);
        insightDataTotal.setTotalReach(totalReach);
        insightDataTotal.setTotalLike(totalLike);
        insightDataTotal.setTotalComment(totalComment);
        insightDataTotal.setTotalShare(totalShare);
        insightDataTotal.setTotalClick(totalClick);

        return insightDataTotal;
    }
}
