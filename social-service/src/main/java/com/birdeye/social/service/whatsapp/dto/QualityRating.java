package com.birdeye.social.service.whatsapp.dto;

public enum QualityRating {
    /**
     * high Quality
     */

    GREEN("GREEN","High"),
    /**
     * Medium Quality
     */
    YELLOW("YELLOW","Medium"),
    /**
     * Low Quality
     */
    RED("RED","Low"),
    /**
     * Quality has not been determined
     */
    NA("NA",null),
    UNKNOWN("UNKNOWN",null);

    QualityRating(String code, String value) {
        this.code = code;
        this.value = value;
    }
    private final String code;
    private final String value;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }


    public static String getValueByCode(String code) {
        for (QualityRating tier : values()) {
            if (tier.code.equalsIgnoreCase(code)) {
                return tier.value;
            }
        }
        return null;
    }
}
