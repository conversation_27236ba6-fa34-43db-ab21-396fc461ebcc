package com.birdeye.social.service.impl;

import com.birdeye.social.constant.SocialChannel;

import javax.validation.constraints.NotNull;

/*
* This is a common request model for filtering the page details across multiple SocialChannels.
* */

public class FilterPageRequest {

    private Integer businessId;
    @NotNull
    private SocialChannel channel;
    private String facebookPageId;

    private String locationId;


    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public SocialChannel getChannel() {
        return channel;
    }

    public void setChannel(SocialChannel channel) {
        this.channel = channel;
    }

    public String getFacebookPageId() {
        return facebookPageId;
    }

    public void setFacebookPageId(String facebookPageId) {
        this.facebookPageId = facebookPageId;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    @Override
    public String toString() {
        return "FilterPageRequest{" +
                "businessId=" + businessId +
                ", channel=" + channel +
                ", facebookPageId='" + facebookPageId + '\'' +
                ", locationId='" + locationId + '\'' +
                '}';
    }
}


