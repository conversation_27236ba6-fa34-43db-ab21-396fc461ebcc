package com.birdeye.social.service.SocialReportService.ExternalService;

import com.birdeye.social.facebook.FacebookApis;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Data;
import com.birdeye.social.insights.Facebook.FacebookInsightRequest;

import java.util.List;

public interface FacebookExternalService extends FacebookApis {


    List<Data> getFacebookPageInsights(FacebookInsightRequest facebookPageData);

    List<Data> getTotalPostCount(String pageId,String accessToken,Integer count);

    String getPromotableId(String postId, String accessToken, String externalPageId);
}
