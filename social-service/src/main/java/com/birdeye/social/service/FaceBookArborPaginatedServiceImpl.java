package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.specification.FBSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.birdeye.social.constant.Constants.ENTERPRISE;
import static com.birdeye.social.constant.Constants.FACEBOOK;
import static java.util.Comparator.nullsFirst;

@Slf4j
@Service
public class FaceBookArborPaginatedServiceImpl implements ArborService{

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Autowired
    private FBSpecification fbSpecification;

    @Autowired
    private CommonService commonService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private FacebookSocialAccountService fbSocialAccountService;

    @Override
    public SocialChannel channelName() {
        return SocialChannel.FACEBOOK;
    }

    @Override
    public TiktokAuthUrlResponse getAuthLoginUrl(String origin, String redirectUri) {
        return null;
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String accountType) {

    }

    @Override
    public Map<String, List<ChannelAccountInfo>> getIntegratedChannels(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        return Collections.emptyMap();
    }

    @Override
    public ChannelPageInfo connectPage(TwitterConnectAccountRequest twitterConnectAccountRequest) {
        return null;
    }

    @Override
    public List<Integer> getMappedAccountLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        if (CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
            return socialFBPageRepository.findAllIdByBusinessIdIn(resellerLeafLocationIds);
        }
        return Collections.emptyList();
    }

    @Override
    public void reconnectTiktokAccount(Long parentId, ChannelAllPageReconnectRequest twitterAuthRequest, Integer userId, String type) {

    }

    @Override
    public void saveLocationMapping(Integer locationId, String pageId, Integer userId, String type, Long enterpriseId) {

    }

    @Override
    public void removePageMapping(List<LocationPageMappingRequest> locationPageMappingRequests, String type, boolean unlink) {

    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
        return null;
    }

    @Override
    public void triggerAccountTokenValidation() {

    }

    @Override
    public void validateToken(SocialTokenValidationDTO socialTokenValidationDTO) {

    }

    @Override
    public void removeAccounts(List<String> profileIds, Long enterpriseId) {

    }

    @Override
    public void getPagesSocialList(Map<String, LocationPageListInfo> connectPage, Long enterpriseId) {

    }

    @Override
    public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List<Integer> businessIds, Set<String> status, Integer page, Integer size, String search, List<String> includeModules) throws Exception {
        return null;
    }

    @Override
    public boolean getModulePermissions(Long enterpriseId, List<String> modules) {
        return false;
    }

    @Override
    public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {

    }

    @Override
    public void markPageInvalid(SocialTokenValidationDTO request) {

    }

    @Override
    public void updateProfileImageCDN(Integer id, String cdnUrl) {

    }

    @Override
    public List<String> getMappedRequestIds(Set<String> requestIds) {
        return Collections.emptyList();
    }

    @Override
    public PaginatedConnectedPages getPagesForEnterprise(Long enterpriseId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type) {
        PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();

        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

        Long disconnectedPagesCount = socialFBPageRepository.findCountByEnterpriseIdAndValidityTypeOrIsValid(enterpriseId,
                Arrays.asList(ValidTypeEnum.PARTIAL_VALID.getId(), ValidTypeEnum.INVALID.getId()), 0);

        boolean hasMappedPage = type.equals(ENTERPRISE)
                ? socialFBPageRepository.existsMappedPageByEnterpriseId(enterpriseId, locationIds)
                : socialFBPageRepository.existsMappedPageByResellerId(enterpriseId, locationIds);

        Page<BusinessFBPage> connectedPages = searchSortAndPaginate(search, enterpriseId, locationIds, pageConnectionStatus,
                userIds, 1, mappingStatus, page, size, sortDirection, sortParam, locationFilterSelected, type);

        log.info("FacebookArborPaginatedServiceImpl#getPagesForEnterprise() : Found {} pages for enterprise : {} ", CollectionUtils.size(connectedPages), enterpriseId);
        try {
            ChannelPageDetails accountInfo = getPagesInfo(connectedPages.getContent(),enterpriseId);
            accountInfo = commonService.additionalSorting(accountInfo, sortParam, sortDirection);
            accountInfo.setDisconnected(Math.toIntExact(disconnectedPagesCount));
            pageTypes.put(SocialChannel.FACEBOOK.getName().toLowerCase(), accountInfo);
            connectedPage.setPageTypes(pageTypes);
            connectedPage.setPageCount(connectedPages.getTotalPages());
            connectedPage.setTotalCount(connectedPages.getTotalElements());
            connectedPage.setHasMappedPage(hasMappedPage);
            return connectedPage;
        } catch (Exception e) {
            log.info("FacebookArborPaginatedServiceImpl#getPagesForEnterprise() : exception occurred while setting page details for enterprise : {},  error: {} :: {}",enterpriseId, e.getMessage(), e.getStackTrace());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public List<ChannelAccountInfo> getAllPages(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        List<ChannelAccountInfo> managed = new ArrayList<>();
        List<BusinessFBPage> fetchedPages = socialFBPageRepository.findByRequestId(businessGetPageRequest.getId().toString());
        if (CollectionUtils.isNotEmpty(fetchedPages)) {
            fbSocialAccountService.getAcntsInfo(fetchedPages, managed, enterpriseId);
        }
        return managed;
    }

    public Page<BusinessFBPage> searchSortAndPaginate(String search, Long parentId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                      List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
                                                      PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected, String type) {

        Specification<BusinessFBPage> spec = getSpecification(search, parentId, businessIds, pageConnectionStatus,
                createdByIds, 1, mappingStatus,  locationFilterSelected, type, sortType, sortDirection);
        if(Objects.isNull(spec)) {
            return new PageImpl<>(new ArrayList<>());
        }
        org.springframework.data.domain.PageRequest pageRequest = getPageRequest( page, size, sortType, sortDirection);
        return socialFBPageRepository.findAll(spec, pageRequest);
    }

    private ChannelPageDetails getPagesInfo(List<BusinessFBPage> pages, Long enterpriseId) throws Exception {
        List<ChannelPages> pageInfo = new ArrayList<>();
        if (!pages.isEmpty()) {
            List<Integer> businessIds = extractBusinessIds(pages);
            List<Integer> userIds = extractUserIds(pages);

            Map<String, Object> businessLocations = commonService.fetchBusinessLocations(businessIds, enterpriseId, FACEBOOK);
            Map<Integer, BusinessCoreUser> userDetails = commonService.fetchUserDetails(userIds, enterpriseId, FACEBOOK);
            Boolean isWebChatEnabled = businessCoreService.isWebChatEnabledByNumber(enterpriseId);
            boolean granularFlag = commonService.checkGranular(enterpriseId);

            pages.forEach(account -> {
                String fbPermission = (granularFlag && org.apache.commons.lang3.StringUtils.isNotEmpty(account.getGranularPagePermissions()))? account.getGranularPagePermissions() :
                        account.getPagePermissions();
                BusinessLocationLiteEntity locationLite = getLocationDetails(account, businessLocations);
                BusinessCoreUser userDetail = getUserDetail(account, userDetails);
                ChannelPages completePageInfo = buildChannelPageInfo(account, locationLite, userDetail, isWebChatEnabled, fbPermission);
                pageInfo.add(completePageInfo);
            });
        }
        return createChannelPageDetails(pageInfo);
    }

    private List<Integer> extractBusinessIds(List<BusinessFBPage> pages) {
        List<Integer> businessIds = new ArrayList<>();
        pages.forEach(page -> {
            if (Objects.nonNull(page.getBusinessId())) {
                businessIds.add(page.getBusinessId());
            }
        });
        return businessIds;
    }

    private List<Integer> extractUserIds(List<BusinessFBPage> pages) {
        List<Integer> userIds = new ArrayList<>();
        pages.forEach(page -> {
            if (Objects.nonNull(page.getCreatedBy())) {
                userIds.add(page.getCreatedBy());
            }
        });
        return userIds;
    }


    private BusinessLocationLiteEntity getLocationDetails(BusinessFBPage account, Map<String, Object> businessLocations) {
        if (Objects.nonNull(businessLocations) && Objects.nonNull(account.getBusinessId())) {
            Map<String, Object> locationData = (Map<String, Object>) businessLocations.get(account.getBusinessId().toString());
            return commonService.getMappedLocationInfo(locationData, account.getBusinessId(), account.getFacebookPageName());
        }
        return null;
    }

    private BusinessCoreUser getUserDetail(BusinessFBPage account, Map<Integer, BusinessCoreUser> userDetails) {
        if (Objects.nonNull(account.getCreatedBy()) && MapUtils.isNotEmpty(userDetails)) {
            return userDetails.get(account.getCreatedBy());
        }
        return null;
    }

    private ChannelPages buildChannelPageInfo(BusinessFBPage page, BusinessLocationLiteEntity locationDetails, BusinessCoreUser userDetail, boolean isWebChatEnabled, String fbPermission) {
        ChannelPages pageInfo = new ChannelPages();
        pageInfo.setId(String.valueOf(page.getFacebookPageId()));
        pageInfo.setPageName(page.getFacebookPageName());
        pageInfo.setLink(page.getLink());
        pageInfo.setImage(page.getFacebookPagePictureUrl());
        pageInfo.setUserId(page.getUserEmailId());
        pageInfo.setAddress(StringUtils.isNotBlank(page.getSingleLineAddress())?page.getSingleLineAddress():page.getPrimaryPhone());
        Validity validity = fbSocialAccountService.fetchValidityAndErrorMessage(page,isWebChatEnabled,fbPermission);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setAddedBy(Objects.nonNull(userDetail) ? businessCoreService.getFullUsername(userDetail) : null);

        setPageHandle(page, pageInfo);
        setLocationDetails(locationDetails, pageInfo);

        return pageInfo;
    }

    private void setPageHandle(BusinessFBPage page, ChannelPages pageInfo) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(page.getHandle()) &&
                !org.apache.commons.lang3.StringUtils.startsWith(page.getHandle(), "@")) {
            pageInfo.setHandle(org.apache.commons.lang3.StringUtils.join("@", page.getHandle()));
        } else {
            pageInfo.setHandle(page.getHandle());
        }
    }

    private void setLocationDetails(BusinessLocationLiteEntity locationDetails, ChannelPages pageInfo) {
        if (Objects.nonNull(locationDetails)) {
            pageInfo.setLocationId(locationDetails.getId());
            pageInfo.setLocationName(locationDetails.getAlias1() != null ? locationDetails.getAlias1() : locationDetails.getName());
            pageInfo.setLocationAddress(commonService.prepareBusinessAddress(locationDetails));
            pageInfo.setParentName(locationDetails.getAccountName());
        }
    }

    private ChannelPageDetails createChannelPageDetails(List<ChannelPages> pageInfo) {
        ChannelPageDetails channelPageDetails = new ChannelPageDetails();
        channelPageDetails.setPages(pageInfo);
        return channelPageDetails;
    }

    public Specification<BusinessFBPage> getSpecification(String search, Long parentId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                          List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Boolean locationFilterSelected, String type, ResellerSortType sortType, PageSortDirection sortDirection) {
        Specification<BusinessFBPage> spec = Specifications.where((fbSpecification.hasEnterpriseId(parentId)));
        if(Objects.nonNull(search)) {
            spec = Specifications.where(spec).and(fbSpecification.hasPageName(search));
        }
        if(CollectionUtils.isNotEmpty(businessIds)) {
            if(Boolean.TRUE.equals(locationFilterSelected)) {
                if(MappingStatus.UNMAPPED.equals(mappingStatus))
                    return null;
                else spec = Specifications.where(spec).and(fbSpecification.inBusinessIds(businessIds));
            } else {
                if(MappingStatus.MAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(fbSpecification.inBusinessIds(businessIds));
                } else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(fbSpecification.hasBusinessIdNullOrNotNull(true));
                } else {
                    Specification<BusinessFBPage> orSpec = Specifications.where(fbSpecification.inBusinessIds(businessIds));
                    orSpec = Specifications.where(orSpec).or(fbSpecification.hasBusinessIdNullOrNotNull(true));
                    spec = Specifications.where(spec).and(orSpec);
                }
            }
        } else {
            if(MappingStatus.MAPPED.equals(mappingStatus)) {
                return null;
            } else {
                spec = Specifications.where(spec).and(fbSpecification.hasBusinessIdNullOrNotNull(true));
            }
        }
        if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(fbSpecification.inValidityTypes(Collections.singletonList(ValidTypeEnum.VALID.getId())));
            if(Objects.nonNull(isSelected)) {
                spec = Specifications.where(spec).and(fbSpecification.isSelected(isSelected));
            }
        } else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
            Specification<BusinessFBPage> orSpec = Specifications.where(fbSpecification.inValidityTypes(Arrays.asList(ValidTypeEnum.INVALID.getId(),
                    ValidTypeEnum.PARTIAL_VALID.getId())));
            orSpec = Specifications.where(orSpec).or(fbSpecification.isValid(0));
            spec = Specifications.where(spec).and(orSpec);
        } else {
            spec = Specifications.where(spec).and(fbSpecification.isSelected(isSelected));
        }
        if(CollectionUtils.isNotEmpty(createdByIds)) {
            spec = Specifications.where(spec).and(fbSpecification.inCreatedByIds(createdByIds));
        }

        if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            spec = Specifications.where(spec).and(fbSpecification.sortValidTypesInGroup(sortDirection));
        } else {
            spec = Specifications.where(spec).and(fbSpecification.sortBusinessIdNullsFirst());
        }
        return spec;
    }

    private org.springframework.data.domain.PageRequest getPageRequest(Integer page, Integer size, ResellerSortType sortType, PageSortDirection sortDirection) {
        org.springframework.data.domain.PageRequest pageRequest;

        if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
            pageRequest = new org.springframework.data.domain.PageRequest(page, size,
                    new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "facebookPageName"));
        } else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            pageRequest = new org.springframework.data.domain.PageRequest(page, size);
        } else {
            pageRequest = new PageRequest(page, size);
        }
        return pageRequest;

    }
}
