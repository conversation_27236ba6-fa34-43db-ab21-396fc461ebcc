package com.birdeye.social.service.SocialReportService;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.insights.PageInsightsRequest;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.twitter.HistoricalTweetInsight;

public interface BackfillReportService {


    boolean backfillProfilePages(PageInsightsRequest pageInsights, String channel);

    void backfillIgInsight(BackfillInsightReq igBackFillInsightReq);

    void backfillXInsight(BackfillInsightReq xBackfillInsightReq);

    void backfillFbInsight(BackfillInsightReq fbBackfillInsightReq);

    void backfillYtInsight(BackfillInsightReq ytBackfillInsightReq);

    void backfillLnInsight(BackfillInsightReq lnBackfillInsightReq);

    void backFillXHistorical(HistoricalTweetInsight historicalTweetInsight);

    void engagementBreakdownBackfill(String channel,BackfillInsightReq backfillInsightReq);

    void engagementBreakdown(String channel, BackfillRequest backfillRequest);

    void backfillSocialPostsAssetsThumbnails(Integer count, String businessNumber);
}
