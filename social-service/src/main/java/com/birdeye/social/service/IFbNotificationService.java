package com.birdeye.social.service;

import com.birdeye.social.model.FacebookDataDeletionPayload;
import com.birdeye.social.model.FacebookEventRequest;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.SubscribeToWebhookRequest;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;

public interface IFbNotificationService {

    void processFbEvent(FacebookEventRequest facebookEventRequest);

    void processFbPostEvent(FacebookEventRequest facebookEventRequest);

    void subscribeEngageToWebhook(SubscribeToWebhookRequest request);

    void unSubscribeToWebhook(SubscribeToWebhookRequest request);

    void processFbEngageEvent(FacebookEventRequest facebookEventRequest);

    Boolean subscribeToWebhookV2(SubscribeToWebhookRequest request);

    void unSubscribeEngageToWebhook(SubscribeToWebhookRequest request);

    void removePageEngageToWebhook(EngageWebhookSubscriptionRequest request);

    Integer validateFBContentDetails(EngageNotificationDetails details, String pageId, boolean isValidated);

    void handleDataDeletionCallback(FacebookDataDeletionPayload payload);
}
