package com.birdeye.social.service;

import com.birdeye.social.dao.GoogleAccountRepo;
import com.birdeye.social.entities.GoogleAccount;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoogleAccountServiceImpl implements IGoogleAccountService{

    private static Logger logger = LoggerFactory.getLogger(GoogleAccountServiceImpl.class);

    @Autowired
    private GoogleAccountRepo googleAccountRepo;

    @Override
    public List<GoogleAccount> findByAccountIds(List<String> accountIds){
        return googleAccountRepo.findByAccountIdIn(accountIds);
    }

    @Override
    public List<GoogleAccount> saveOrUpdate(List<GoogleAccount> accounts) {
        return googleAccountRepo.save(accounts);
    }

    @Override
    public List<GoogleAccount> findAllByRequestId(String requestId) {
        return googleAccountRepo.findAllByBusinessGetPageReqId(requestId);
    }

    @Override
    public GoogleAccount findByAccountId(String accountId){
        return googleAccountRepo.findByAccountId(accountId);
    }

    @Override
    public GoogleAccount findByAccountIdAndBusinessGetPageReqId(String accountId, String requestId) {
        return googleAccountRepo.findByAccountIdAndBusinessGetPageReqId(accountId,requestId);
    }

    @Override
    public List<GoogleAccount> findByBusinessGetPageReqId(String requestId) {
        return googleAccountRepo.findByBusinessGetPageReqId(requestId);
    }

    @Override
    public List<GoogleAccount> findByUser(String userEmail) {
        return googleAccountRepo.findByUserEmail(userEmail);
    }

    @Override
    public List<GoogleAccount> findByUserAndActive(String userEmail, Integer active) {
        return googleAccountRepo.findByUserEmailAndActive(userEmail,active);
    }

    @Override
    public List<GoogleAccount> findByUserOrderByBusinessGetPageReqIdDesc(String userEmail) {
        return googleAccountRepo.findByUserEmailOrderByBusinessGetPageReqIdDesc(userEmail);
    }

    @Override
    public List<GoogleAccount> findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(String accountId, String requestId) {
        return googleAccountRepo.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(accountId, requestId);
    }
}
