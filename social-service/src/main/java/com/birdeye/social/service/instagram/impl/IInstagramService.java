package com.birdeye.social.service.instagram.impl;

import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.instagram.*;
import com.birdeye.social.instagram.response.InstagramCompetitorProfile;
import com.birdeye.social.instagram.response.InstagramTimelineResponse;
import com.birdeye.social.instagram.response.InstagramTimelineResponseList;
import com.birdeye.social.model.SocialTimeline;

import java.util.Date;

public interface IInstagramService {

	InstagramGenerateAccessTokenResponse getAccessToken(String authCode, String clientId, String clientSecret,
			String domainUrl);

	String getInstagramAuthorizationCodeURL(String clientId, String domainUrl);

	InstagramTimelineResponseList getInstagramMyPostsFeeed(String accessToken, String instagramUserId, Integer count,
														   String nextToken);
	InstagramTimelineResponseList getInstagramMyPostsFeedEngagement(String accessToken, String instagramUserId, Integer count,
														   String nextToken);

	InstagramGetCommentsResponse getInstagramCommentsOfAPost(String accessToken, String postId);

	InstagramGetCommentsResponse getInstagramCommentsOfAPost(String accessToken, String postId, Integer limit);

	InstagramPostCommentResponse postCommentOnAnInstagramPost(String accessToken, String postId, String comment);

	InstagramPostCommentResponse postReplyOnAnInstagramPost(String accessToken, String postId, String comment);

	InstagramPostCommentResponse postReplyOnMentionPost(String accessToken, String pageId, String postId, String comment);

	//{ig-user-id}/mentions?media_id={media_id}&comment_id={comment_id}&message={message}
	InstagramPostCommentResponse postReplyOnMentionComment(String accessToken, String pageId, String postId, String commentId, String comment);

	boolean deleteCommentOnAnInstagramPost(String accessToken, String commentId);

	boolean likePost(String accessToken, String postId);

	boolean unlikePost(String accessToken, String postId);

	SocialTimeline getInstagramTimeline(BusinessInstagramAccount instagramAccount, String type, String nextToken, Date maxDate, boolean parseInRelativeDate, int limit, boolean isStream);


	InstagramStreamResponse getInstagramLocationsFeed(String accessToken, String locationId, Integer count,
			String nextMaxId);

	InstagramLocationResponse getInstagramLocationDetails(String accessToken, String facebookPlacesId);

	Integer getIgAccPostingLimit(String accessToken, String accountId);

	SocialTimeline getInstagramStories(BusinessInstagramAccount instagramAccount, SocialTimeline socialTimeline);

	boolean hideComment(String accessToken, String commentId, boolean hide);

    InstagramTimelineResponse getInstagramPostDetails(String pageAccessToken, String postId);

	InstagramCommentResponse getCommentDetails(String pageAccessToken, String commentId);

	InstagramCompetitorProfile getCompetitorProfileData(String igAccountId, String accessToken, String userName, boolean isMediaReq, String afterToken);

	Boolean getCommentDeleteDetails(String pageAccessToken, String commentId);
}
