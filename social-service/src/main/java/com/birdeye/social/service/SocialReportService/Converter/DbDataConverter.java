package com.birdeye.social.service.SocialReportService.Converter;

import com.birdeye.social.entities.report.*;
import com.birdeye.social.insights.PageInsights;
import com.birdeye.social.insights.PostData;

import java.util.Date;

public interface DbDataConverter {
    FacebookPostInsight convertPostInsight(PostData postData);

    FacebookPageInsight convertPageInsight(PageInsights pageInsights);

    InstagramPageInsight convertPageInsightForInstagram(PageInsights pageInsights);

    TwitterPageInsight convertPageInsightForTwitter(PageInsights pageInsights);

    LinkedinPostInsight convertPostInsightForLinkedin(PostData postData);

    LinkedInPageInsight convertPageInsightForLinkedin(PageInsights pageInsights,Date end);

    GMBPageInsight convertPageInsightForGmb(PageInsights pageInsights);

    YoutubeChannelInsight convertPostInsightForYoutube(PageInsights pageInsights);
}
