package com.birdeye.social.service.scripts;

import com.birdeye.social.entities.LinkedinRefreshToken;
import com.birdeye.social.model.GMBPostPublishResponse;
import com.birdeye.social.model.GenericScriptRequest;

import java.util.List;
import java.util.Map;

public interface ScriptService {
    void populateAgentIdToGMBTable(GenericScriptRequest gmbScriptRequest);

    void updateVOM(GenericScriptRequest gmbScriptRequest);

    void updateGmbTable(Map<String,List<String>> locationIds);

    Object getPages(String accountId, String accessToken);

    GMBPostPublishResponse getPostsFromGMB(Integer enterpriseId, Integer socialPostId);

    void updateSocialPublishInfo(GMBPostPublishResponse request);

    void addLinkInBio(GenericScriptRequest genericScriptRequest);

    void updateLinkedinAccessTokenAndUserId(GenericScriptRequest scriptRequest);

    void updateProfileData(List<LinkedinRefreshToken> linkedinRefreshTokens);

    void migrateDraftPageInfo(GenericScriptRequest request);
}
