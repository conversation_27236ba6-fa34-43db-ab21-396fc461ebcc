package com.birdeye.social.service.impl;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.GoogleClientCredRepo;
import com.birdeye.social.entities.GoogleClientCred;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.ConsumerTokenAndSecret;
import com.birdeye.social.platform.dao.DomainSocialAppCredRepository;
import com.birdeye.social.platform.entities.DomainSocialAppCreds;
import com.birdeye.social.service.ISocialAppService;
import com.birdeye.social.service.SocialAppCredsInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SocialAppService implements ISocialAppService {

	private static final Logger LOGGER = LoggerFactory
			.getLogger(SocialAppService.class);

	@Autowired
	private GoogleClientCredRepo googleClientCredRepo;

	@Autowired
	private DomainSocialAppCredRepository domainSocialAppCredRepo;

	@Override
	//@Cacheable(value = "domainCache", key = "#root.methodName", unless = "#result == null")
	public SocialAppCredsInfo getGoogleAppSettings() {
		// Google App information
		GoogleClientCred googleCreds = googleClientCredRepo.findGoogleCredDefault();
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();

		if(Objects.nonNull(googleCreds)){
			domainInfo.setChannel("google");
			domainInfo.setChannelClientId(googleCreds.getClientId());
			domainInfo.setChannelClientSecret(googleCreds.getClientSecret());
			domainInfo.setSocialCredsId(String.valueOf(googleCreds.getId()));
		}else {
			throw new BirdeyeSocialException("Google creds can not be null");
		}
		return domainInfo;
	}

	@Override
	//@Cacheable(value = "domainCache", key = "#root.methodName", unless = "#result == null")
	public SocialAppCredsInfo getFacebookAppSettings() {
		DomainSocialAppCreds domainSocialAppCreds = getDefaultFacebookCreds();
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();
		domainInfo.setChannel("facebook");
		domainInfo.setChannelClientId(domainSocialAppCreds.getAppKey());
		domainInfo.setChannelClientSecret(domainSocialAppCreds.getAppSecret());
		domainInfo.setChannelAccessToken(domainSocialAppCreds.getAppAccessToken());
		return domainInfo;
	}

	@Override
	@Cacheable(value = "domainCache", key = "#root.methodName", unless = "#result == null")
	public SocialAppCredsInfo getLinkedinAppSettings() {
		DomainSocialAppCreds domainSocialAppCreds = getDefaultLinkedInCreds();
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();
		domainInfo.setChannel("linkedin");
		//domainInfo.setDomainId(domainId);
		domainInfo.setChannelClientId(domainSocialAppCreds.getAppKey());
		domainInfo.setChannelClientSecret(domainSocialAppCreds.getAppSecret());
		domainInfo.setChannelAccessToken(domainSocialAppCreds.getAppAccessToken());
		//domainInfo.setDomainName(domainName);
		return domainInfo;
	}

	@Override
	@Cacheable(value = "domainCache", key = "'TWITTER:'+#domainId", unless = "#result == null")
	public SocialAppCredsInfo getTwitterAppSettings(Long domainId, String domainName) {
		DomainSocialAppCreds domainSocialAppCreds = getDomainSocialAppCreds(domainId,"twitter");
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();
		domainInfo.setChannel("twitter");
		domainInfo.setDomainId(domainSocialAppCreds.getDomain()!=null?domainSocialAppCreds.getDomain().getId():null);
		domainInfo.setChannelClientId(domainSocialAppCreds.getAppKey());
		domainInfo.setChannelClientSecret(domainSocialAppCreds.getAppSecret());
		domainInfo.setChannelAccessToken(domainSocialAppCreds.getAppAccessToken());
		domainInfo.setDomainName(domainName);
		return domainInfo;
	}

	@Override
	@Cacheable(value = "domainCache",  key = "#root.methodName", unless = "#result == null")
	public SocialAppCredsInfo getTwitterAppSettingsV2() {
		DomainSocialAppCreds domainSocialAppCreds = getDomainSocialAppCreds(0L,"twitter");
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();
		domainInfo.setChannel("twitter");
		domainInfo.setDomainId(domainSocialAppCreds.getDomain()!=null?domainSocialAppCreds.getDomain().getId():null);
		domainInfo.setChannelClientId(domainSocialAppCreds.getAppKey());
		domainInfo.setChannelClientSecret(domainSocialAppCreds.getAppSecret());
		domainInfo.setChannelAccessToken(domainSocialAppCreds.getAppAccessToken());
		domainInfo.setDomainName(null);
		return domainInfo;
	}

	@Override
	@Cacheable(value = "domainCache", key = "#root.methodName", unless = "#result == null")
	public SocialAppCredsInfo getYoutubeAppSettings() {
		DomainSocialAppCreds domainSocialAppCreds = getDefaultYoutubeCreds();
		SocialAppCredsInfo domainInfo = new SocialAppCredsInfo();
		domainInfo.setChannel("youtube");
		domainInfo.setChannelClientId(domainSocialAppCreds.getAppKey());
		domainInfo.setChannelClientSecret(domainSocialAppCreds.getAppSecret());
		domainInfo.setChannelAccessToken(domainSocialAppCreds.getAppAccessToken());
		domainInfo.setSocialCredsId(String.valueOf(domainSocialAppCreds.getId()));
		return domainInfo;
	}

	@Override
	public ConsumerTokenAndSecret getGooglePlusCredentials(Long domainId) {
		DomainSocialAppCreds creds = getCredsForDomainIdAndAppType(domainId, Constants.GOOGLE_PLUS);

		if (creds == null) {
			throw new BirdeyeSocialException("Google creds can not be null");
		}

		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		consumerToken.setToken(creds.getAppKey());
		consumerToken.setSecret(creds.getAppSecret());
		consumerToken.setAlbumId(creds.getCurrentGoogleAlbumId());

		LOGGER.info("google plus credentials : {} for domain id {}", consumerToken, domainId);
		return consumerToken;
	}

	@Override
	public ConsumerTokenAndSecret getFacebookCredentials(Long domainId) {
		DomainSocialAppCreds creds = getCredsForDomainIdAndAppType(domainId, Constants.FACEBOOK);

		if(creds == null ||
				StringUtils.isBlank(creds.getAppKey())){
			throw new BirdeyeSocialException("No default Facebook app id present");
		}

		if(StringUtils.isBlank(creds.getAppSecret())){
			throw new BirdeyeSocialException("No default Facebook app secret present");
		}

		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		consumerToken.setToken(creds.getAppKey());
		consumerToken.setSecret(creds.getAppSecret());

		LOGGER.info("facebook credentials : {} for domain id {}", consumerToken, domainId);
		return consumerToken;
	}

	@Override
	public ConsumerTokenAndSecret getGoogleCreds(Long domainId) {
		GoogleClientCred creds = null;
		if (domainId != null){
			List<GoogleClientCred> credList =  googleClientCredRepo.findGoogleCredByDomain(domainId);
			if(credList != null && ! credList.isEmpty()){
				creds = credList.get(0);
			}
		}

		if(creds == null ||
				StringUtils.isBlank(creds.getClientId())){
			creds =  googleClientCredRepo.findGoogleCredDefault();
		}

		if(creds == null ||
				StringUtils.isBlank(creds.getClientId()) || StringUtils.isEmpty(creds.getClientSecret())){
			throw new BirdeyeSocialException("No default account credentials for Google+.");
		}

		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		consumerToken.setToken(creds.getClientId());
		consumerToken.setSecret(creds.getClientSecret());

		LOGGER.info("google plus credentials from google client cred tableg : {} for domain id {}", consumerToken, domainId);
		return consumerToken;
	}

	private DomainSocialAppCreds getCredsForDomainIdAndAppType(Long domainId, String appType) {
		LOGGER.info("Fetching {} credentials for domain id {}", appType, domainId);
		DomainSocialAppCreds creds = null;
		if (domainId != null){
			List<DomainSocialAppCreds> credList =  domainSocialAppCredRepo.findByDomainAndAppType(domainId, appType);
			if(credList != null && ! credList.isEmpty()){
				creds = credList.get(0);
			}
		}

		if(creds == null ||
				StringUtils.isBlank(creds.getAppKey()) ||
				StringUtils.isBlank(creds.getAppSecret())){
			List<DomainSocialAppCreds> credList =  domainSocialAppCredRepo.findDefaultConfig(appType);
			if(credList != null && ! credList.isEmpty()){
				creds = credList.get(0);
			}
		}

		return creds;
	}

	private DomainSocialAppCreds getDomainSocialAppCreds(Long domainId, String channel) {
		DomainSocialAppCreds creds = null;
		List<DomainSocialAppCreds> credList;
		LOGGER.info("Fetching DomainSocialAppCreds for channel: {}",channel);
		credList = domainSocialAppCredRepo.findDefaultConfig(channel);
		if (CollectionUtils.isNotEmpty(credList)) {
			creds = credList.get(0);
		}
		LOGGER.info("DomainSocialAppCreds for channel: {}",channel);
//		List<DomainSocialAppCreds> credList = domainSocialAppCredRepo
//				.findByDomainAndAppType(domainId, channel);
//		if (credList != null && !credList.isEmpty()) {
//			creds = credList.get(0);
//		}
//
//		if (creds == null || StringUtils.isBlank(creds.getAppKey())
//				|| StringUtils.isBlank(creds.getAppSecret())) {
//			// LOGGER.info("Using default domain creds for facebook...");
//			credList = domainSocialAppCredRepo.findDefaultConfig(channel);
//			if (credList != null && !credList.isEmpty()) {
//				creds = credList.get(0);
//			}
//		}
		return creds;
	}

	private DomainSocialAppCreds getDefaultFacebookCreds() {
		List<DomainSocialAppCreds> credList = domainSocialAppCredRepo.findDefaultConfig("facebook");
		if (credList != null && !credList.isEmpty()) {
			return credList.get(0);
		}
		return  null;
	}
	
	private DomainSocialAppCreds getDefaultLinkedInCreds() {
		List<DomainSocialAppCreds> credList = domainSocialAppCredRepo.findDefaultConfig("linkedin");
		if (credList != null && !credList.isEmpty()) {
			return credList.get(0);
		}
		return  null;
	}

	private DomainSocialAppCreds getDefaultYoutubeCreds() {
		List<DomainSocialAppCreds> credList = domainSocialAppCredRepo.findDefaultConfig("youtube");
		if (credList != null && !credList.isEmpty()) {
			return credList.get(0);
		}
		return  null;
	}
}
