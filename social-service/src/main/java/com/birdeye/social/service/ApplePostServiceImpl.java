package com.birdeye.social.service;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialPostRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.*;
import com.birdeye.social.model.tiktok.TikTokHashtagResponse;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 23/11/23
 */

@Service("ApplePostServiceImpl")
public class ApplePostServiceImpl implements ChannelPostService{

    private static final Logger log = LoggerFactory.getLogger(GooglePostServiceImpl.class);

    @Value("${apple.client.id}") private String clientId;
    @Value("${apple.client.secret}") private String clientSecret;

    @Autowired
    private BusinessAppleLocationRepo businessAppleLocationRepo;

    @Autowired
    private SocialPostInfoRepository socialPostInfoRepository;

    @Autowired
    private SocialPostRepository socialPostRepository;

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;


    @Override
    public SocialTimeline getFeedData(Date lastPostDate, SocialScanEventDTO data) {
        try {
            BusinessAppleLocation page = businessAppleLocationRepo.findById(data.getChannelPrimaryId());

            List<SocialPostPublishInfo> socialPostInfos = socialPostInfoRepository.getAllPublishedPostByExternalId(data.getExternalId());
            if (socialPostInfos == null) {
                log.info("No published posts found for Apple location: {}", data.getExternalId());
                return null;
            }

            List<Integer> postIds = socialPostInfos.stream()
                    .map(SocialPostPublishInfo::getSocialPostId)
                    .collect(Collectors.toList());

            List<SocialPost> socialPosts = socialPostRepository.findByIdIn(postIds);

            log.info("[Social Report] Request received to scan {}", page.getAppleLocationId());

            if (CollectionUtils.isEmpty(socialPosts)) {
                return null;
            }

            SocialTimeline socialTimeline = convertApplePostToTimelineObject(socialPostInfos,socialPosts, socialPostInfos.get(0).getEnterpriseId(), page, lastPostDate);

            return socialTimeline;
        } catch (Exception ex) {
            log.error("Social feed data exception caught for data {} with error", data, ex);
            return null;
        }
    }

    @Override
    public List<Object[]> findPageIdAndEnterpriseIdbyPageIds(List<String> pageIds) {
        return businessAppleLocationRepo.findPageIdAndEnterpriseIdbyPageIds(pageIds);
    }


    @Override
    public SocialChannel channelName() {
        return SocialChannel.APPLE_CONNECT;
    }


    @Autowired
    CacheService cacheService;

    public SocialTimeline convertApplePostToTimelineObject(List<SocialPostPublishInfo> socialPostInfos, List<SocialPost>  applePosts, Integer enterpriseId, BusinessAppleLocation applePage, Date lastPostDate) throws ParseException {
        SocialTimeline socialTimeline = new SocialTimeline();
        List<Feed> feedsList = new ArrayList<>();
        Feed feed;
        if (applePosts != null) {
            for (SocialPost applePost : applePosts) {
                log.info("create time for applepost:{}",applePost.getCreatedDate());

                feed = new Feed();
                feed.setFeedId(getAppleShowcaseId(socialPostInfos,applePost.getId()));
                feed.setFeedText(applePost.getPostText());
                String postMetaDataJson = applePost.getPostMetadata();

                if (com.birdeye.social.utils.StringUtils.isNotEmpty(postMetaDataJson)) {
                    try {
                        ObjectMapper objectMapper = new ObjectMapper();
                        Map<String, Object> postMetaDataMap = objectMapper.readValue(postMetaDataJson, new TypeReference<Map<String, Object>>() {});

                        String appleMetaDataJson = (String) postMetaDataMap.get("appleMetaData");

                        if (com.birdeye.social.utils.StringUtils.isNotEmpty(appleMetaDataJson)) {
                            Map<String, Object> appleMetaDataMap = objectMapper.readValue(appleMetaDataJson, new TypeReference<Map<String, Object>>() {
                            });

                            String startDateString = (String) appleMetaDataMap.get("startDate");
                            String endDateString = (String) appleMetaDataMap.get("endDate");
                            feed.setDatePublished(startDateString);
                            feed.setPostEndDate(endDateString);
                        }else {
                            log.error("appleMetaData is empty or null");
                        }
                    } catch (Exception e) {
                        log.error("Error while parsing postMetaData JSON: {}", e.getMessage());
                    }
                } else {
                    log.error("postMetaData is empty or null");
                }

                feed.setPublisherId(applePage.getAppleLocationId());
                feed.setPublisherName(applePage.getLocationName());
                feed.setFeedUrl(applePage.getLink());
                if (CollectionUtils.isNotEmpty(Collections.singleton(applePost.getImageIds()))) {
                    List<MediaData> mediaData = socialPostService.getMediaData(applePost.getImageIds(), enterpriseId ,Constants.IMAGE);

                    List<String> mediaUrls = mediaData.stream()
                            .map(MediaData::getMediaUrl)
                            .collect(Collectors.toList());

                    feed.setImages(mediaUrls);
                }

                feedsList.add(feed);
            }
            socialTimeline.setFeeds(feedsList);
        }
        socialTimeline.setChannel(SocialChannel.APPLE_CONNECT.getName());
        socialTimeline.setStreamName(applePage.getLocationName());
        socialTimeline.setBusinessId(applePage.getBusinessId());
        socialTimeline.setPageId(applePage.getId());
        return socialTimeline;
    }

    private String getAppleShowcaseId(List<SocialPostPublishInfo> socialPostInfos, Integer id) {
        for (SocialPostPublishInfo socialPostInfo : socialPostInfos) {
            if(Objects.nonNull(socialPostInfo.getSocialPostId()) && id.intValue()==socialPostInfo.getSocialPostId().intValue())
                return  socialPostInfo.getPostId();
        }
        return null;
    }

    @Override
    public List<PageDetail> getPageDetails(List<String> pageIds) {
        List<PageDetail> pageDetails = new ArrayList<>();
        List<BusinessAppleLocation> appleLocations = businessAppleLocationRepo.findByAppleLocationIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(appleLocations)) {
            pageDetails.addAll(appleLocations.stream().map(s -> new PageDetail(s.getAppleLocationId(), s.getLocationName(),
                    s.getLogoUrl(),s.getBusinessId())).collect(Collectors.toList()));
        }
        return pageDetails;
    }

    @Override
    public Feed getPostFeedDetails(SocialPostPublishInfo request, SocialPost socialPost) {
        Feed feed = new Feed();
        feed.setFeedId(request.getPostId());
        Map postMetadata = JSONUtils.fromJSON(socialPost.getPostMetadata(), Map.class);
        String postMetaData = (String) postMetadata.get("appleMetaData");
        ApplePostMetadata metadata = JSONUtils.fromJSON(postMetaData, ApplePostMetadata.class);
        if (Objects.nonNull(metadata)) {
            feed.setPostEndDate(metadata.getEndDate());
        }
        return feed;
    }

    @Override
    public SocialScanEventDTO prepareSocialScanEventDto(String pageId) {
        BusinessAppleLocation appleLocation = businessAppleLocationRepo.findByAppleLocationId(pageId);
        SocialScanEventDTO scanEventDTO = new SocialScanEventDTO();
        scanEventDTO.setChannelPrimaryId(appleLocation.getId());
        scanEventDTO.setBusinessId(appleLocation.getBusinessId());
        scanEventDTO.setEnterpriseId(appleLocation.getEnterpriseId());
        scanEventDTO.setExternalId(appleLocation.getAppleLocationId());
        scanEventDTO.setPageName(appleLocation.getLocationName());
        scanEventDTO.setSourceName(SocialChannel.APPLE_CONNECT.getName());
        scanEventDTO.setSourceId(SocialChannel.APPLE_CONNECT.getId());
        return scanEventDTO;
    }

    @Override
    public List<TikTokHashtagResponse.HashtagResponse> fetchRecommendedHashtags(String keyword, Integer businessId) throws Exception {
        return Collections.emptyList();
    }
}
