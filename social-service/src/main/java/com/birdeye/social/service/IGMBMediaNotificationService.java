package com.birdeye.social.service;

import com.birdeye.social.model.GMBNotificationReviewRequest;
import com.birdeye.social.response.gmb.GMBCustomerMediaByIdResponse;

public interface IGMBMediaNotificationService {
    void createGMBMediaNotificationAsSuccess(GMBNotificationReviewRequest request, GMBCustomerMediaByIdResponse response, String mediaName);

    void createGMBMediaNotificationAsFailed(GMBNotificationReviewRequest request, String mediaName, Integer code, String message);
}
