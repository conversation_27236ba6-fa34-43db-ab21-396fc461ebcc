package com.birdeye.social.service;

import com.birdeye.social.model.TwitterDeleteEvent;
import com.birdeye.social.model.TwitterFollowNotification;
import com.birdeye.social.model.TwitterNotification;

public interface ITwitterNotificationService {
    void processTwitterNotification(TwitterNotification twitterNotification);

    void receiveNewFollowerNotification(TwitterFollowNotification twitterNotification);

    void receiveMentionNotification(TwitterNotification twitterNotification);

    void receiveReTweetNotification(TwitterNotification twitterNotification);

    void receiveQuoteTweetNotification(TwitterNotification twitterNotification);

    void receiveDeleteNotification(TwitterDeleteEvent twitterDeleteEvent);

    void receiveCommentNotification(TwitterNotification twitterNotification);

    void receiveFavoriteReactionNotification(TwitterNotification twitterNotification);

    void processBlockNotification(TwitterNotification twitterNotification);
}
