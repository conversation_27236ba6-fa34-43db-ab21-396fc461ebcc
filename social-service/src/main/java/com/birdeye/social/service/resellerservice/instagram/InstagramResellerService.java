package com.birdeye.social.service.resellerservice.instagram;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.InstagramAuthRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.FacebookPageService;
import com.birdeye.social.service.IInstragramSetupService;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.service.resellerservice.facebook.FacebookResellerService;
import com.birdeye.social.specification.FBSpecification;
import com.birdeye.social.specification.InstagramSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.birdeye.social.constant.Constants.ENTERPRISE;
import static java.util.Comparator.nullsFirst;

@Service
public class InstagramResellerService implements SocialReseller {

    @Autowired
    private IInstragramSetupService socialInstagramService;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    
    @Autowired
	private IBusinessCoreService iBusinessCoreService;

    @Autowired
    private InstagramSpecification igSpecification;

    @Autowired
    private CommonService commonService;

    @Autowired
    private  IInstragramSetupService instragramSetupService;

    private static Logger logger = LoggerFactory.getLogger(InstagramResellerService.class);

    @Override
    public String channelName() {
        return SocialChannel.INSTAGRAM.getName();
    }

	@Override
	public PaginatedConnectedPages getPages(Long resellerId, PageConnectionStatus pageConnectionStatus,
                                            Integer page, Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection,
                                            ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                            Boolean locationFilterSelected, String type) {
        return socialInstagramService.getResellerPages(resellerId, pageConnectionStatus, page, size,search, searchType, sortDirection,
                sortParam, locationIds, mappingStatus, userIds, locationFilterSelected);
	}

	@Override
	public void removeResellerPages(List<String> pageIds, Integer limit) {
		socialInstagramService.removeIGPageForReseller(pageIds, limit);
	}

    @Override
    public ChannelPageInfo connectResellerPages(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
        return connectInstagramAccountForReseller(resellerId, pageIds, selectAll, searchStr);
    }

    @Override
    public ChannelConnectedPageInfo checkIfAccountExistsByResellerId(Long accountId) {
        ChannelConnectedPageInfo channelConnectedPageInfo= new ChannelConnectedPageInfo();
        channelConnectedPageInfo.setInstagramAccountExists(instagramAccountRepository.existsByResellerIdAndIsSelected(accountId,1));
        return channelConnectedPageInfo;
    }

    @Override
    public void reconnectResellerPages(Long resellerId, ChannelAllPageReconnectRequest request,
                                       Integer userId, String type, Integer limit) {
        InstagramAuthRequest authRequest = createInstagramAuthRequest(userId, resellerId, request);
        socialInstagramService.reconnectInstagramAccounts(authRequest);
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String type) {
        socialInstagramService.submitFetchPageRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(),
                authRequest.getAuthCode(), authRequest.getRedirectUri(), authRequest.getTempAccessToken(), type);

    }

    @Override
    public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer page, Integer size, String search) {
        return socialInstagramService.getPaginatedPages(businessGetPageRequest, page, size,search);
    }

    private InstagramAuthRequest createInstagramAuthRequest(Integer userId, Long resellerId, ChannelAllPageReconnectRequest request) {
        InstagramAuthRequest authRequest = new InstagramAuthRequest();
        authRequest.setBirdeyeUserId(userId);
        authRequest.setBusinessId(resellerId);
        authRequest.setAuthCode(request.getAuthCode());
        authRequest.setRedirectUri(request.getRedirectUri());
        authRequest.setType(Constants.RESELLER);
        return authRequest;
    }

    private ChannelPageInfo connectInstagramAccountForReseller(Long resellerId, List<String> pageIds, Boolean selectAll,
                                                               String searchStr) {
        ChannelPageInfo accountInfo;
        InstagramConnectAccountRequest instagramConnectAccountRequest=new InstagramConnectAccountRequest();
        instagramConnectAccountRequest.setId(pageIds);
        instagramConnectAccountRequest.setBusinessId(resellerId);
        instagramConnectAccountRequest.setType(Constants.RESELLER);
        instagramConnectAccountRequest.setSearchStr(searchStr);
        instagramConnectAccountRequest.setSelectAll(selectAll);
        accountInfo=socialInstagramService.connectInstagramAccountV1(instagramConnectAccountRequest,null);
        return accountInfo;
    }

	@Override
	public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId,String pageType
			,Integer userId, Boolean force, Long resellerId) {
		if (userId != null && StringUtils.isNotEmpty(pageId) && locationId != null) {
			socialInstagramService.saveInstagramLocationMapping(locationId, pageId, userId,Constants.RESELLER, resellerId);
			return new HashMap<>();
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,
					"Invalid input present for saveLocationPageMapping");
		}
	}
	
	@Override
	public void removePageMappings(List<LocationPageMappingRequest> input) {
		socialInstagramService.removeInstagramLocationAccountMapping(input,Constants.RESELLER,false);
	}

    /**
     * @param resellerId
     * @param resellerLeafLocationIds
     * @return
     */
    @Override
    public List<Integer> getMappedResellerLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        return socialInstagramService.getMappedResellerLeafLocations(resellerLeafLocationIds);
    }

}
