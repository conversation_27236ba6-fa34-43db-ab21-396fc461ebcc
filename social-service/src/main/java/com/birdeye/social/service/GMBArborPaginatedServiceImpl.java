package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.model.*;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.specification.GMBSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.birdeye.social.constant.Constants.ENTERPRISE;
import static com.birdeye.social.constant.Constants.GMB;
import static java.util.Comparator.nullsFirst;

@Slf4j
@Service
public class GMBArborPaginatedServiceImpl implements ArborService{

    @Autowired
    private BusinessGMBLocationRawRepository socialGMBRepo;

    @Autowired
    private GMBSpecification gmbSpecification;

    @Autowired
    private CommonService commonService;

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Autowired
    private IBusinessCoreService businessCoreService;



    @Override
    public SocialChannel channelName() {
        return SocialChannel.GMB;
    }

    @Override
    public TiktokAuthUrlResponse getAuthLoginUrl(String origin, String redirectUri) {
        return null;
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String accountType) {

    }

    @Override
    public Map<String, List<ChannelAccountInfo>> getIntegratedChannels(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        return Collections.emptyMap();
    }

    @Override
    public ChannelPageInfo connectPage(TwitterConnectAccountRequest twitterConnectAccountRequest) {
        return null;
    }

    @Override
    public List<Integer> getMappedAccountLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        if (CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
            return socialGMBRepo.findAllIdByBusinessIdIn(resellerLeafLocationIds);
        }
        return Collections.emptyList();
    }

    @Override
    public void reconnectTiktokAccount(Long parentId, ChannelAllPageReconnectRequest twitterAuthRequest, Integer userId, String type) {

    }

    @Override
    public void saveLocationMapping(Integer locationId, String pageId, Integer userId, String type, Long enterpriseId) {

    }

    @Override
    public void removePageMapping(List<LocationPageMappingRequest> locationPageMappingRequests, String type, boolean unlink) {

    }

    @Override
    public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
        return null;
    }

    @Override
    public void triggerAccountTokenValidation() {

    }

    @Override
    public void validateToken(SocialTokenValidationDTO socialTokenValidationDTO) {

    }

    @Override
    public void removeAccounts(List<String> profileIds, Long enterpriseId) {

    }

    @Override
    public void getPagesSocialList(Map<String, LocationPageListInfo> connectPage, Long enterpriseId) {

    }

    @Override
    public LocationPageMapping getLocationMappingPages(Long enterpriseId, Integer userId, List<Integer> businessIds, Set<String> status, Integer page, Integer size, String search, List<String> includeModules) throws Exception {
        return null;
    }

    @Override
    public boolean getModulePermissions(Long enterpriseId, List<String> modules) {
        return false;
    }

    @Override
    public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {

    }

    @Override
    public void markPageInvalid(SocialTokenValidationDTO request) {

    }

    @Override
    public void updateProfileImageCDN(Integer id, String cdnUrl) {

    }

    @Override
    public List<String> getMappedRequestIds(Set<String> requestIds) {
        return Collections.emptyList();
    }

    @Override
    public PaginatedConnectedPages getPagesForEnterprise(Long enterpriseId, PageConnectionStatus pageConnectionStatus, Integer page, Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type) {
        PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
        Map<String, ChannelPageDetails> pageTypes = new HashMap<>();

        Long disconnectedPagesCount = socialGMBRepo.findCountByEnterpriseIdAndIsValid(enterpriseId, 0);


        boolean hasMappedPage = type.equals(ENTERPRISE)
                ? socialGMBRepo.existsMappedPageByEnterpriseId(enterpriseId, locationIds)
                : socialGMBRepo.existsMappedPageByResellerId(enterpriseId, locationIds);

        Page<BusinessGoogleMyBusinessLocation> connectedPages = searchSortAndPaginate(search, enterpriseId, locationIds, pageConnectionStatus,
                userIds, 1, mappingStatus, page, size, sortDirection, sortParam, locationFilterSelected, type);

        log.info("GMBArborPaginatedServiceImpl#getPagesForEnterprise() : Found {} pages for enterprise : {} ", CollectionUtils.size(connectedPages), enterpriseId);
        try {
            ChannelPageDetails accountInfo = getResellerPageInfo(connectedPages.getContent(), enterpriseId);
            if(Objects.nonNull(accountInfo.getPages())) {
                accountInfo = commonService.additionalSorting(accountInfo, sortParam, sortDirection);
                accountInfo.setDisconnected(Math.toIntExact(disconnectedPagesCount));
            } else {
                accountInfo.setDisconnected(0);
            }
            pageTypes.put(SocialChannel.GMB.getName().toLowerCase(), accountInfo);
            connectedPage.setPageTypes(pageTypes);
            connectedPage.setPageCount(connectedPages.getTotalPages());
            connectedPage.setTotalCount(connectedPages.getTotalElements());
            connectedPage.setHasMappedPage(hasMappedPage);
            return connectedPage;
        } catch (Exception e) {
            log.info("GMBArborPaginatedServiceImpl#getPagesForEnterprise() : exception occurred while setting page details for enterprise : {},  error: {} :: {}",enterpriseId, e.getMessage(), e.getStackTrace());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public List<ChannelAccountInfo> getAllPages(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
        List<BusinessGoogleMyBusinessLocation> fetchedPages = socialGMBRepo.findByRequestId(businessGetPageRequest.getId().toString());
        if (CollectionUtils.isNotEmpty(fetchedPages)) {
            return googleSocialAccountService.getAccountInfoForGmb(fetchedPages, enterpriseId);
        }
        return Collections.emptyList();
    }

    public Page<BusinessGoogleMyBusinessLocation> searchSortAndPaginate(String search, Long parentId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                                        List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
                                                                        PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected, String type) {

        Specification<BusinessGoogleMyBusinessLocation> spec = getSpecification(search, parentId, businessIds, pageConnectionStatus,
                createdByIds, 1, mappingStatus,  locationFilterSelected, type, sortType, sortDirection);
        if(Objects.isNull(spec)) {
            return new PageImpl<>(new ArrayList<>());
        }
        org.springframework.data.domain.PageRequest pageRequest = getPageRequest( page, size, sortType, sortDirection);
        return socialGMBRepo.findAll(spec, pageRequest);
    }

    private ChannelPageDetails getResellerPageInfo(List<BusinessGoogleMyBusinessLocation> pages, long enterpriseId) throws Exception {
        List<ChannelPages> pageInfo = new ArrayList<>();
        if (!pages.isEmpty()) {
            List<Integer> businessIds = extractBusinessIds(pages);
            List<Integer> userIds = extractUserIds(pages);

            Map<String, Object> businessLocations = commonService.fetchBusinessLocations(businessIds, enterpriseId, GMB);
            Map<Integer, BusinessCoreUser> userDetails = commonService.fetchUserDetails(userIds, enterpriseId, GMB);

            pages.forEach(account -> {
                BusinessLocationLiteEntity locationLite = getLocationDetails(account, businessLocations);
                BusinessCoreUser userDetail = getUserDetail(account, userDetails);
                ChannelPages completePageInfo = buildChannelPageInfo(account, locationLite, userDetail);
                pageInfo.add(completePageInfo);
            });
        }
        return createChannelPageDetails(pageInfo);
    }

    private List<Integer> extractBusinessIds(List<BusinessGoogleMyBusinessLocation> pages) {
        List<Integer> businessIds = new ArrayList<>();
        pages.forEach(page -> {
            if (Objects.nonNull(page.getBusinessId())) {
                businessIds.add(page.getBusinessId());
            }
        });
        return businessIds;
    }

    private List<Integer> extractUserIds(List<BusinessGoogleMyBusinessLocation> pages) {
        List<Integer> userIds = new ArrayList<>();
        pages.forEach(page -> {
            if (Objects.nonNull(page.getCreatedBy())) {
                userIds.add(page.getCreatedBy());
            }
        });
        return userIds;
    }

    private BusinessLocationLiteEntity getLocationDetails(BusinessGoogleMyBusinessLocation account, Map<String, Object> businessLocations) {
        if (Objects.nonNull(businessLocations) && Objects.nonNull(account.getBusinessId())) {
            Map<String, Object> locationData = (Map<String, Object>) businessLocations.get(account.getBusinessId().toString());
            return commonService.getMappedLocationInfo(locationData, account.getBusinessId(), account.getLocationName());
        }
        return null;
    }

    private BusinessCoreUser getUserDetail(BusinessGoogleMyBusinessLocation account, Map<Integer, BusinessCoreUser> userDetails) {
        if (Objects.nonNull(account.getCreatedBy()) && MapUtils.isNotEmpty(userDetails)) {
            return userDetails.get(account.getCreatedBy());
        }
        return null;
    }

    private ChannelPages buildChannelPageInfo(BusinessGoogleMyBusinessLocation page, BusinessLocationLiteEntity locationDetails, BusinessCoreUser userDetail) {
        ChannelPages pageInfo = new ChannelPages();
        pageInfo.setId(String.valueOf(page.getLocationId()));
        pageInfo.setPageName(page.getLocationName());
        pageInfo.setLink(page.getLocationMapUrl());
        pageInfo.setImage(page.getCoverImageUrl());
        pageInfo.setUserId(page.getEmailId());
        pageInfo.setAddress(StringUtils.isNotBlank(page.getSingleLineAddress())? page.getSingleLineAddress() : page.getPrimaryPhone());
        Validity validity = googleSocialAccountService.fetchValidityAndErrorMessage(page, false);
        pageInfo.setValidType(validity.getValidType());
        pageInfo.setErrorCode(validity.getErrorCode());
        pageInfo.setErrorMessage(validity.getErrorMessage());
        pageInfo.setAddedBy(Objects.nonNull(userDetail) ? businessCoreService.getFullUsername(userDetail) : null);
        commonService.setLocationDetails(locationDetails, pageInfo);

        return pageInfo;
    }


    private ChannelPageDetails createChannelPageDetails(List<ChannelPages> pageInfo) {
        ChannelPageDetails channelPageDetails = new ChannelPageDetails();
        channelPageDetails.setPages(pageInfo);
        return channelPageDetails;
    }

    public Specification<BusinessGoogleMyBusinessLocation> getSpecification(String search, Long parentId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
                                                                            List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Boolean locationFilterSelected, String type, ResellerSortType sortType, PageSortDirection sortDirection ) {
        Specification<BusinessGoogleMyBusinessLocation> spec = Specifications.where(Objects.equals(type, ENTERPRISE) ? gmbSpecification.hasEnterpriseId(parentId)
                :  gmbSpecification.hasResellerId(parentId));
        if(Objects.nonNull(search)) {
            spec = Specifications.where(spec).and(gmbSpecification.hasLocationName(search));
        }
        if(CollectionUtils.isNotEmpty(businessIds)) {
            if(locationFilterSelected) {
                if(MappingStatus.UNMAPPED.equals(mappingStatus))
                    return null;
                else
                    spec = Specifications.where(spec).and(gmbSpecification.inBusinessIds(businessIds));
            } else {
                if(MappingStatus.MAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(gmbSpecification.inBusinessIds(businessIds));
                } else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
                    spec = Specifications.where(spec).and(gmbSpecification.hasBusinessIdNullOrNotNull(true));
                } else {
                    Specification<BusinessGoogleMyBusinessLocation> orSpec = Specifications.where(gmbSpecification.inBusinessIds(businessIds));
                    orSpec = Specifications.where(orSpec).or(gmbSpecification.hasBusinessIdNullOrNotNull(true));
                    spec = Specifications.where(spec).and(orSpec);
                }
            }
        } else {
            if(MappingStatus.MAPPED.equals(mappingStatus)) {
                return null;
            } else {
                spec = Specifications.where(spec).and(gmbSpecification.hasBusinessIdNullOrNotNull(true));
            }
        }
        if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(gmbSpecification.isValid(1));
            if(Objects.nonNull(isSelected)) {
                spec = Specifications.where(spec).and(gmbSpecification.isSelected(isSelected));
            }
        } else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
            spec = Specifications.where(spec).and(gmbSpecification.isValid(0));
        } else {
            spec = Specifications.where(spec).and(gmbSpecification.isSelected(isSelected));
        }
        if(CollectionUtils.isNotEmpty(createdByIds)) {
            spec = Specifications.where(spec).and(gmbSpecification.inCreatedByIds(createdByIds));
        }
        spec = Specifications.where(spec).and(gmbSpecification.sortBusinessIdNullsFirst());
        return spec;
    }

    private org.springframework.data.domain.PageRequest getPageRequest(Integer page, Integer size, ResellerSortType sortType , PageSortDirection sortDirection) {
        org.springframework.data.domain.PageRequest pageRequest;
        if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
            pageRequest = new org.springframework.data.domain.PageRequest(page, size,
                    new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "locationName"));
        } else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
            pageRequest = new org.springframework.data.domain.PageRequest(page, size,
                    new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.DESC:Sort.Direction.ASC, "isValid")
                            .and(new Sort(Sort.Direction.ASC, "locationName")));
        } else {
            pageRequest = new org.springframework.data.domain.PageRequest(page, size);
        }
        return pageRequest;
    }
}
