package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.LocationPagePair;
import com.birdeye.social.model.PageInfoDto;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.service.SocialPostOperationService.Apple.ApplePostOperationService;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialReportService.Apple.AppleInsights;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.applechat.AppleChatService;
import com.birdeye.social.service.notification.NotificationAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class Apple implements SocialInsights,NotificationAuditService, PostOperation {

    @Autowired
    private AppleChatService appleChatService;

    @Autowired
    private AppleInsights appleInsights;

    @Autowired
    private ApplePostOperationService postOperationService;
    @Autowired
    private BusinessAppleLocationRepo appleLocationRepo;

    @Autowired
    private BusinessAppleLocationRepo businessAppleLocationRepo;

    private static final Logger log = LoggerFactory.getLogger(GoogleMyBusiness.class);


    @Override
    public String channelName() {
        return SocialChannel.APPLE_CONNECT.getName();
    }

    @Override
    public List<SocialNotificationAudit> putNotificationForChannel(Object notificationObject) {
        return appleChatService.auditNotification(notificationObject);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        appleInsights.getPostInsights(businessPosts.get(0),isFreshRequest);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {

        return appleInsights.getAppleInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        if(Objects.isNull(postData)){
            return;
        }
        appleInsights.postApplePostInsightsToEs(postData);
    }


    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {

        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {

    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO getPageInsights) {

    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) {

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {

    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return null;
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

    @Override
    public void getGMBPageAnalytics(String pageId, Integer businessId) throws Exception {

    }

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {

    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return null;
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return false;
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        return null;
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return appleInsights.createPostData(businessPosts, new HashMap<>());
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) {
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return appleInsights.getAppleInsightsForTopPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList) {

        List<BusinessAppleLocation> applePages = businessAppleLocationRepo.findByBusinessIdIn(businessIds);
        if (CollectionUtils.isNotEmpty(applePages)) {
            applePages.forEach(page ->
                    locationPagePairList.add(
                        LocationPagePair.builder()
                                .pageId(page.getAppleLocationId())
                                .locationId(page.getBusinessId())
                                .pageName(page.getLocationName())
                                .profilePictureUrl(page.getLogoUrl())
                                .pagePermission(true)
                                .build()
                    )
            );
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList) {
        List<BusinessAppleLocation> applePages = businessAppleLocationRepo.findByAppleLocationIdIn(pageIds);
        if (CollectionUtils.isNotEmpty(applePages)) {
            applePages.forEach(page ->
                    locationPagePairList.add(
                            LocationPagePair.builder()
                            .pageId(page.getAppleLocationId())
                            .locationId(page.getBusinessId())
                            .pageName(page.getLocationName())
                            .profilePictureUrl(page.getLogoUrl())
                            .pagePermission(true)
                            .build()
                    )
            );
        }
    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest,
                                                               ReportSortingCriteria postSortingCriteria, SortOrder order,
                                                               Integer startIndex, Integer pageSize) {
        return null;
    }


    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }
    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? appleLocationRepo.findAllByEnterpriseId(enterpriseId):
                appleLocationRepo.findPageIdsWithPagination(new PageRequest(page,size));
    }

}
