package com.birdeye.social.service.whatsapp.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown=true)
public class WhatsappMessageRequest {
    private Integer accountId;
    private Integer businessId;
    private String receiverPhoneNumber;
    private String type;

    private Text text;
    private Image image;
    private Video video;
    private Template template;

    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class Text {
        private String body;
        private boolean previewUrl;

        // Getters and Setters
    }
    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class Image {
        private String imageUrl;
        private String caption;
    }
    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class Video {
        private String videoUrl;
        private String caption;

    }
    @Getter
    @Setter
    @NoArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class Template {
        private String name;
        private Language language;
        private List<Component> components;

        // Getters and Setters

        @Getter
        @Setter
        @NoArgsConstructor
        @ToString
        @JsonIgnoreProperties(ignoreUnknown=true)
        public static class Language {
            private String code;

            // Getters and Setters
        }

        @Getter
        @Setter
        @NoArgsConstructor
        @ToString
        @JsonIgnoreProperties(ignoreUnknown=true)
        public static class Component {
            private String type;
            private List<Parameter> parameters;


            @Getter
            @Setter
            @NoArgsConstructor
            @ToString
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class Parameter {
                private String type;
                private String parameter_name; // Optional field
                private String text;
                private com.birdeye.social.service.whatsapp.dto.messages.Image image;

            }

            @Getter
            @Setter
            @NoArgsConstructor
            @ToString
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class ImageParameter {
                private String link;
            }
        }
    }
}
