package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.external.request.media.Picturesque;
import com.birdeye.social.external.request.media.PicturesqueRequest;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.MediaData;
import com.birdeye.social.model.engageV2.EngageBusinessDetails;
import com.birdeye.social.nexus.EmailDTO;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessUserRepository;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.get.GetResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SocialEmailAlertServiceImpl implements  SocialEmailAlertService{

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private EsService esService;
    @Autowired
    private NexusService nexusService;

    @Autowired
    private PicturesqueGen picturesqueGen;

    @Autowired
    private IBusinessCachedService businessService;

    @Autowired
    private EngageAlertAuditService engageAlertAuditService;

    @Autowired
    private BusinessUserRepository businessUserRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private EngageConverterService engageConverterService;


    private static final Logger LOGGER = LoggerFactory.getLogger(SocialEmailAlertServiceImpl.class);

    private static final String ES_EXCEPTION="Exception occurred while fetching data from ES";
    private static final String DASHBOARD_ENGAGE_SOCIAL_CONNECT_URL = "/dashboard/social/engage";

    private static final String REPLY_COMMENT_SUBJECT="New reply to a comment on your %s post";
    private static final String COMMENT_SUBJECT="New  comment on your %s post";
    private static final String POST_MENTION_SUBJECT="New mention on a %s post";
    private static final String RETWEET_SUBJECT="Your tweet was retweeted";
    private static final String TWEET_QUOTE_SUBJECT="Your tweet was quoted";
    private static final String DIRECT_MESSAGE_SUBJECT="New direct message for you on %s post";
    private static final String NEW_FOLLOWER_SUBJECT="New follower on Twitter";
    private static final String STORY_MENTION_SUBJECT="New mention on an Instagram story";
    private static final String PICTURESQUE_TYPE_IMAGE="image";
    private static final String PICTURESQUE_TYPE_VIDEO="video";
    private static final String DATE_FORMAT="E, MMM d, h:mm a";
    private static final String AUTHOR_PROFILE_IMAGE="authorProfileImage";
    private static final String PAGE_PROFILE_IMAGE="pageProfileImage";
    private static final String AUTHOR_NAME="authorName";
    private static final String FEED_DATE="feedDate";
    private static final String FEED_TEXT="feedText";
    private static final String FEED_IMAGES="feedImages";
    private static final String PAGE_NAME="pageName";

    private static final List<String> CHANNELS_ALLOWED_FOR_COMMENT_NOTIFICATION = Arrays.asList(SocialChannel.INSTAGRAM.getName(), SocialChannel.FACEBOOK.getName(),
            SocialChannel.TWITTER.getName(), SocialChannel.LINKEDIN.getName(), SocialChannel.TIKTOK.getName());
    private static final List<String> CHANNELS_ALLOWED_FOR_POST_NOTIFICATION = Arrays.asList(SocialChannel.INSTAGRAM.getName(), SocialChannel.FACEBOOK.getName(),
            SocialChannel.TWITTER.getName(), SocialChannel.LINKEDIN.getName(), SocialChannel.TIKTOK.getName());
    private static final List<String> CHANNELS_ALLOWED_FOR_STORY_MENTION_NOTIFICATION= Collections.singletonList(SocialChannel.INSTAGRAM.getName());
    private static final List<String> CHANNELS_ALLOWED_FOR_DIRECT_MESSAGE_NOTIFICATION=Arrays.asList(SocialChannel.INSTAGRAM.getName(),SocialChannel.FACEBOOK.getName(),
            SocialChannel.TWITTER.getName());
    private static final List<String> CHANNELS_ALLOWED_FOR_FOLLOW_NOTIFICATION = Collections.singletonList(SocialChannel.TWITTER.getName());

    @Override
    public void sendEmailNotification(String feedId, EngageBusinessDetails businessDetails) {
      try {
          EngageNotificationDetails feedDetails = getFeedDocumentFromEs(Integer.parseInt(feedId));
          if(Objects.isNull(feedDetails))
          {
              LOGGER.info("No record found in ES for feedId {}",feedId);
              engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0), feedId,new ArrayList<>(),null,null,"No record found in ES for feedId","Failed");
              return;
          }
          String channel=feedDetails.getChannel();
          boolean isChannelTwitter = SocialChannel.TWITTER.getName().equalsIgnoreCase(channel);
          if(Objects.isNull(businessDetails.getEnterpriseId()))
          {
              engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0),feedId,new ArrayList<>(),true,null,"Received null enterpriseId in businessDetails","Failed");
              LOGGER.info("Enterprise Id for feedId {} is null",feedId);
              return;
          }
          Integer smallEnterpriseId = businessCoreService.getBusinessId(businessDetails.getEnterpriseId());
          String subject="";
          EmailDTO emailDTO=prepareMetadataForSendEmail(businessDetails);
          if(Objects.isNull(smallEnterpriseId))
          {
              engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0),feedId,new ArrayList<>(),true,null,String.format("BusinessId for EnterpriseId %s is null",businessDetails.getEnterpriseId()).toString(),"Failed");
              LOGGER.info("BusinessId for enterpriseId {} and feedId {} is null",businessDetails.getEnterpriseId(),feedId);
              return;
          }
          BusinessLiteDTO business = businessCoreService.getBusinessLite(smallEnterpriseId,false);
          if(Objects.isNull(business))
          {
              engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0),feedId,new ArrayList<>(),true,null,String.format("BusinessId for EnterpriseId %s is null",businessDetails.getEnterpriseId()).toString(),"Failed");
              LOGGER.info("BusinessId for enterpriseId {} and feedId {} is null",businessDetails.getEnterpriseId(),feedId);
              return;
          }
          List<SocialNotificationUsers> receiverUsers=new ArrayList<>();
          List<SocialNotificationUsers> guestReceiverUsers=new ArrayList<>();
          List<String> nonGuestUsers=new ArrayList<>();
          List<String> guestUsers=new ArrayList<>();
          Map<String,Object> payload=new HashMap<>();
          addHeaderTokensToPayload(business,feedDetails,payload,businessDetails.getBusinessIds().get(0), isChannelTwitter);
          Boolean isReseller=checkBusinessReseller(business);
          //List<SocialNotificationUsers> genericReceiverList=new ArrayList<>();
          Map<String, List<SocialNotificationUsers>> receiverMap= prepareGenericReceiverLists(businessDetails,smallEnterpriseId);
          receiverUsers=receiverMap.get("userList");
          guestReceiverUsers=receiverMap.get("guestUserList");
          if(CollectionUtils.isEmpty(receiverUsers) && CollectionUtils.isEmpty(guestReceiverUsers))
          {
              LOGGER.info("User enabled for this activity notification on this enterprise {} is null",businessDetails.getEnterpriseId());
              engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0),feedId,new ArrayList<>(),true,null,"Users enabled for this activity notification on this enterprise is null","Failed");
              return;
          }
          if((feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.COMMENT.name()) || feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.AD_COMMENT.name())) && CHANNELS_ALLOWED_FOR_COMMENT_NOTIFICATION.contains(channel))
          {
              if((Objects.isNull(feedDetails.getSubType()) || feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.REPLY.name()))
              && Boolean.FALSE.equals(feedDetails.getIsAdminComment())) {
                  receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_post_comments_email()) && user.getSocialNotification().getEnable_post_comments_email()).collect(Collectors.toList());
                  guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_post_comments_email()) && user.getSocialNotification().getEnable_post_comments_email()).collect(Collectors.toList());
                  if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
                  {
                      nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());
                      guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());

                  }
                  subject=preparePayloadForTypeComment(feedDetails,payload,business.getBusinessNumber(), isChannelTwitter);

              }
          }
          else if(feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.POST.name()) && CHANNELS_ALLOWED_FOR_POST_NOTIFICATION.contains(channel)
                 && Objects.nonNull(feedDetails.getSubType()) && feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name())){
              receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_mentions_email()) && user.getSocialNotification().getEnable_mentions_email()).collect(Collectors.toList());
              guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_mentions_email()) && user.getSocialNotification().getEnable_mentions_email()).collect(Collectors.toList());
              LOGGER.info("Non guest users for mention post {}",receiverUsers.toString());
              LOGGER.info("guest users for mention post {}",guestUsers.toString());
              if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
              {
                  nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());
                  guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());

              }

                  preparePayloadForPostMention(feedDetails,payload,business.getBusinessNumber());
                  subject=String.format(POST_MENTION_SUBJECT,isChannelTwitter
                          ? SocialChannel.getXAndTwitterMerged()
                          :org.apache.commons.lang3.StringUtils.capitalize(feedDetails.getChannel()));

          }
          else if(feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.RETWEET.name()))
          {
              receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_retweet_email()) && user.getSocialNotification().getEnable_retweet_email()).collect(Collectors.toList());
              guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_retweet_email()) && user.getSocialNotification().getEnable_retweet_email()).collect(Collectors.toList());
              if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
              {
                  nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());
                  guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());

              }
               preparePayloadForRetweet(feedDetails,payload,business.getBusinessNumber());
               subject=RETWEET_SUBJECT;
          }
          else if(feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.QUOTE_TWEET.name()))
          {
              receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_quote_tweet_email()) && user.getSocialNotification().getEnable_quote_tweet_email()).collect(Collectors.toList());
              guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_quote_tweet_email()) && user.getSocialNotification().getEnable_quote_tweet_email()).collect(Collectors.toList());
              if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
              {
                  nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());
                  guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());

              }
              preparePayloadForTweetQuote(feedDetails,payload,business.getBusinessNumber());
              subject=TWEET_QUOTE_SUBJECT;
          }
          else if(feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.FOLLOW.name()) && CHANNELS_ALLOWED_FOR_FOLLOW_NOTIFICATION.contains(channel))
          {
              receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_new_followers_email()) && user.getSocialNotification().getEnable_new_followers_email()).collect(Collectors.toList());
              guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_new_followers_email()) && user.getSocialNotification().getEnable_new_followers_email()).collect(Collectors.toList());
              if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
              {
                  nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());
                  guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                          .collect(Collectors.toList());

              }
              preparePayloadForNewFollower(feedDetails,payload);
              subject=NEW_FOLLOWER_SUBJECT;

          }
          else if(feedDetails.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.MESSAGE.name()))
          {
              if(Objects.isNull(feedDetails.getSubType()) && CHANNELS_ALLOWED_FOR_DIRECT_MESSAGE_NOTIFICATION.contains(feedDetails.getChannel())) {
                  receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_direct_message_email()) && user.getSocialNotification().getEnable_direct_message_email()).collect(Collectors.toList());
                  guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_direct_message_email()) && user.getSocialNotification().getEnable_direct_message_email()).collect(Collectors.toList());
                  if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
                  {
                      nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());
                      guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());

                  }
                  preparePayloadForDirectMessage(feedDetails,payload,business.getBusinessNumber());
                  subject=String.format(DIRECT_MESSAGE_SUBJECT,isChannelTwitter
                          ? SocialChannel.getXAndTwitterMerged()
                          :org.apache.commons.lang3.StringUtils.capitalize(feedDetails.getChannel()));
              } else if (feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.MENTION.name()) && CHANNELS_ALLOWED_FOR_STORY_MENTION_NOTIFICATION.contains(feedDetails.getChannel())){
                  receiverUsers=receiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_story_mentions_email()) && user.getSocialNotification().getEnable_story_mentions_email()).collect(Collectors.toList());
                  guestReceiverUsers=guestReceiverUsers.stream().filter(user->Objects.nonNull(user.getSocialNotification().getEnable_story_mentions_email()) && user.getSocialNotification().getEnable_story_mentions_email()).collect(Collectors.toList());
                  if(CollectionUtils.isNotEmpty(receiverUsers) || CollectionUtils.isNotEmpty(guestReceiverUsers))
                  {
                      nonGuestUsers=receiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());
                      guestUsers=guestReceiverUsers.stream().map(SocialNotificationUsers::getEmailId)
                              .collect(Collectors.toList());

                  }
                  preparePayloadForStoryMention(feedDetails,payload,business.getBusinessNumber());
                  subject=STORY_MENTION_SUBJECT;
              }
          }
          else
          {
              return;
          }
          LOGGER.info("Email alert notification with businessId {} and payload {} is being sent to guestUsers {} and adminUsers {}",smallEnterpriseId, payload,guestUsers,nonGuestUsers);
         /* removing hardcoding if(!CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialEngageEmailTestBusinessList().contains(smallEnterpriseId))
          {
              LOGGER.info("Not sending email as the businessId {} Notifications are not permitted",smallEnterpriseId);
              return;
          }*/
          LOGGER.info("Guest Users {}",guestUsers);
          LOGGER.info("NonGuest Users {}",nonGuestUsers);
          sendEmailAlert(emailDTO,payload,nonGuestUsers,channel,businessDetails,feedId,isReseller,subject);
          payload.put("guestUser",true);
          emailDTO.setExternalUuid(StringUtils.join(Constants.ENGAGE_EMAIL_ALERT, "-G-",businessDetails.getEnterpriseId().toString()));
          sendEmailAlert(emailDTO,payload,guestUsers,channel,businessDetails,feedId,isReseller,subject);
          
      } catch (Exception ex) {
          engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(), businessDetails.getBusinessIds().get(0),feedId,new ArrayList<>(),true,null,"Exception occurred "+ex.toString(),"Failed");
          LOGGER.info("exception occurred ",ex);

      }

    }

    private void sendEmailAlert(EmailDTO emailDTO, Map<String, Object> payload, List<String> receiverUsers, String channel, EngageBusinessDetails businessDetails, String feedId,Boolean isReseller,String subject) {
        if(receiverUsers.isEmpty())
        {
            engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(),businessDetails.getBusinessIds().get(0), feedId,receiverUsers,true,payload,"No receiver for this alert","Failed");
        }else{
            emailDTO.setTo(receiverUsers);
            emailDTO.setSubject(subject);
            payload.put("isReseller",isReseller);
            nexusService.sendEmailV2(emailDTO,payload,isReseller);
            engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(),businessDetails.getBusinessIds().get(0), feedId,receiverUsers,true,payload,"Pushed to Nexus kafka","Success");

        }
//        emailDTO.setTo(CacheManager.getInstance()
//                .getCache(SystemPropertiesCache.class).getSocialEngageEmailTestAccounts());
    }

    private void preparePayloadForStoryMention(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber) {
        payload.put("type", EngageEmailAlertTypeEnum.storyMention);
        if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage()))
        {
            payload.put(AUTHOR_PROFILE_IMAGE,feedDetails.getAuthorProfileImage());
        }
        else {
            payload.put("authorInitial",commonService.getInitials(feedDetails.getAuthorName()));
        }
        payload.put(AUTHOR_NAME,feedDetails.getAuthorName());
        payload.put(FEED_DATE,relativeDate(feedDetails.getFeedDate()));
        payload.put(FEED_TEXT,feedDetails.getText());
        payload.put(FEED_IMAGES,getSingleUrlForMultipleMedia(feedDetails,businessNumber,payload));
    }

    private void preparePayloadForDirectMessage(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber) {
       payload.put("type",EngageEmailAlertTypeEnum.directMessage);
       if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage()))
       {
           payload.put(AUTHOR_PROFILE_IMAGE,feedDetails.getAuthorProfileImage());
       }
       else {
           payload.put("authorInitial",commonService.getInitials(feedDetails.getAuthorName()));
       }
       payload.put(AUTHOR_NAME,feedDetails.getAuthorName());
       preparePayloadForPost(feedDetails,payload,businessNumber);
    }

    private void preparePayloadForNewFollower(EngageNotificationDetails feedDetails, Map<String,Object> payload) {
        payload.put("type",EngageEmailAlertTypeEnum.following);
        payload.put("followersCount",feedDetails.getFollowers());
        payload.put("followsCount",feedDetails.getFollows());
        if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage())) {
            payload.put(AUTHOR_NAME, feedDetails.getAuthorName());
        }
        else {
            payload.put("authorInitial", feedDetails.getAuthorName());
        }
        payload.put(AUTHOR_PROFILE_IMAGE,feedDetails.getAuthorProfileImage());
        payload.put(FEED_DATE,relativeDate(feedDetails.getFeedDate()));
    }

    private void preparePayloadForTweetQuote(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber) {

        payload.put("type",EngageEmailAlertTypeEnum.quoteTweet);
        if(Objects.nonNull(feedDetails.getSubEvent()) && Objects.nonNull(feedDetails.getSubEvent().getAuthorName())
        && Objects.nonNull(feedDetails.getSubEvent().getAuthorProfileImage())) {
            preparePayloadForQuoteTweet(feedDetails, payload,businessNumber);
        }
        else {
            LOGGER.info("Incomplete info of User whose tweet is quoted");
        }
    }

    private void preparePayloadForQuoteTweet(EngageNotificationDetails postFeedDetails, Map<String, Object> payload, Long businessNumber) {
        if(Objects.nonNull(postFeedDetails.getAuthorProfileImage()) && commonService.isValidUrl(postFeedDetails.getAuthorProfileImage())){
            payload.put(PAGE_PROFILE_IMAGE,postFeedDetails.getSubEvent().getAuthorProfileImage());
        }
        else {
            payload.put("pageInitial",commonService.getInitials(postFeedDetails.getSubEvent().getAuthorName()));
        }
        payload.put(PAGE_NAME,postFeedDetails.getSubEvent().getAuthorName());
        payload.put(FEED_DATE,relativeDate(postFeedDetails.getSubEvent().getFeedDate()));
        payload.put(FEED_TEXT,postFeedDetails.getParentPostText());
        payload.put(FEED_IMAGES,getSingleUrlForMultipleMedia(postFeedDetails.getSubEvent(),businessNumber, payload));
        if(Objects.nonNull(postFeedDetails.getAuthorProfileImage()) && commonService.isValidUrl(postFeedDetails.getAuthorProfileImage())) {
            payload.put("quotedProfileImage", postFeedDetails.getAuthorProfileImage());
        }
        else{
            payload.put("quotedAuthorInitial",commonService.getInitials(postFeedDetails.getAuthorName()));
        }
        payload.put("quotedAuthorName",postFeedDetails.getAuthorName());
        payload.put("quotedDate",relativeDate(postFeedDetails.getFeedDate()));
        payload.put("quotedText",postFeedDetails.getText());
        payload.put("quotedImage",getSingleUrlForMultipleMedia(postFeedDetails,businessNumber, payload));

    }

    private void preparePayloadForRetweet(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber) {
        payload.put("type",EngageEmailAlertTypeEnum.retweet);
        payload.put(AUTHOR_NAME,feedDetails.getSubEvent().getAuthorName());
        if(Objects.nonNull(feedDetails.getSubEvent().getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getSubEvent().getAuthorProfileImage()))
        {
            payload.put(AUTHOR_PROFILE_IMAGE, feedDetails.getSubEvent().getAuthorProfileImage());
        }
        else {
            payload.put("authorInitial", commonService.getInitials(feedDetails.getSubEvent().getAuthorName()));
        }
        preparePayloadForPost(feedDetails.getSubEvent(), payload,businessNumber);

    }


    private void preparePayloadForPostMention(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber) {
        payload.put("type",EngageEmailAlertTypeEnum.postMention);
        if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage()))
        {
            payload.put(AUTHOR_PROFILE_IMAGE, feedDetails.getAuthorProfileImage());
        }
        else
        {
            payload.put("authorInitial",commonService.getInitials(feedDetails.getAuthorName()));
        }
        payload.put(AUTHOR_NAME,feedDetails.getAuthorName());
        preparePayloadForPost(feedDetails,payload,businessNumber);
    }

    private void preparePayloadForPost(EngageNotificationDetails postFeedDetails, Map<String, Object> payload,Long businessNumber) {
        if(Objects.nonNull(postFeedDetails)) {
            if(Objects.nonNull(postFeedDetails.getAuthorProfileImage()) && commonService.isValidUrl(postFeedDetails.getAuthorProfileImage())) {
                payload.put(PAGE_PROFILE_IMAGE, postFeedDetails.getAuthorProfileImage());
            }
            else {
                payload.put("pageInitial",commonService.getInitials(postFeedDetails.getAuthorName()));
            }
            payload.put(PAGE_NAME, postFeedDetails.getAuthorName());
            payload.put(FEED_DATE, relativeDate(postFeedDetails.getFeedDate()));
            payload.put(FEED_TEXT, postFeedDetails.getText());
            payload.put(FEED_IMAGES, getSingleUrlForMultipleMedia(postFeedDetails, businessNumber, payload));
            if(!(payload.containsKey(AUTHOR_PROFILE_IMAGE) || payload.containsKey("authorInitial"))) {
                payload.put(AUTHOR_PROFILE_IMAGE, postFeedDetails.getAuthorProfileImage());
            }
        }
        else
        {
            LOGGER.info("postFeedDetail or subEvent is null");
        }
    }

    private String preparePayloadForTypeComment(EngageNotificationDetails feedDetails,Map<String,Object> payload,Long businessNumber, boolean isChannelTwitter) {
        String subject="";
        if(Objects.nonNull(feedDetails.getSubType()) && feedDetails.getSubType().equalsIgnoreCase(EngageV2FeedSubTypeEnum.REPLY.name())) {
            payload.put("type",EngageEmailAlertTypeEnum.replyComment);
            preparePayloadForSubComment(feedDetails,payload,businessNumber);
            subject=String.format(REPLY_COMMENT_SUBJECT, isChannelTwitter
                    ? SocialChannel.getXAndTwitterMerged()
                    :org.apache.commons.lang3.StringUtils.capitalize(feedDetails.getChannel()));
        }
        else {
            payload.put("type",EngageEmailAlertTypeEnum.comment);
            preparePayloadForComment(feedDetails,payload,businessNumber);
            subject=String.format(COMMENT_SUBJECT, isChannelTwitter
                    ? SocialChannel.getXAndTwitterMerged()
                    :org.apache.commons.lang3.StringUtils.capitalize(feedDetails.getChannel()));
        }
        preparePayloadForPost(feedDetails.getPostId(), payload,businessNumber);
        return subject;
    }

    private void addHeaderTokensToPayload(BusinessLiteDTO businessLiteDTO, EngageNotificationDetails feedDetails, Map<String, Object> payload,Integer businessId, boolean isChannelTwitter) {
      Boolean isSmb =checkBusinessSMB(businessLiteDTO);
      if(isSmb) {
          payload.put("businessName", Objects.nonNull(businessLiteDTO.getBusinessAlias()) ?
                  businessLiteDTO.getBusinessAlias(): businessLiteDTO.getBusinessName());
          payload.put("locationName","");
      } else {
          try {
              BusinessLiteDTO businessLiteDTO1 = businessCoreService.getBusinessLite(businessId, false);
              payload.put("businessName", Objects.nonNull(businessLiteDTO.getBusinessAlias()) ?
                      businessLiteDTO.getBusinessAlias(): businessLiteDTO.getBusinessName());
              payload.put("locationName",Objects.nonNull(businessLiteDTO1.getBusinessAlias()) ?
                      businessLiteDTO1.getBusinessAlias(): businessLiteDTO1.getBusinessName());
          }
          catch(Exception e)
          {
              engageAlertAuditService.saveAudit(businessLiteDTO.getEnterpriseNumber(),businessId,"",new ArrayList<>(),true,null,String.format("Exception occurred while getting businessDetails for businessId %s :%s",businessId,e.toString()),"Failed");
              LOGGER.info("Exception occurred while getting info from core for businessId {} is {}",businessId,e);

          }
      }
        payload.put("isSmb", isSmb);
        payload.put("userName",feedDetails.getAuthorName());
        payload.put("channelName",isChannelTwitter?SocialChannel.getXAndTwitterMerged(): org.apache.commons.lang3.StringUtils.capitalize(feedDetails.getChannel()));
        payload.put("redirectCTA",getRedirectUrl(businessLiteDTO,feedDetails.getFeedId()));
    }

    private Boolean checkBusinessReseller(BusinessLiteDTO business) {
        Boolean isReseller=false;
        if(business.getResellerId()!=null && !BusinessAccountTypeEnum.DIRECT.getName().equalsIgnoreCase(business.getAccountType())){
            isReseller=true;
        }
        return isReseller;
    }

    public boolean checkBusinessSMB(BusinessLiteDTO business) {
        return ("Business".equals(business.getType()) || "Product".equals(business.getType()) && business.getEnterpriseId()==null);
    }

    private Map<String,List<SocialNotificationUsers>> prepareGenericReceiverLists(EngageBusinessDetails businessDetails, Integer smallEnterpriseId) {
        BusinessSocialNotificationDTO businessSocialNotificationDTO=businessCoreService.getSocialNotificationUsersList(smallEnterpriseId);
        Map returnMap=new HashMap();
        List<SocialNotificationUsers> userNotificationMap=new ArrayList<>();
        List<SocialNotificationUsers> guestUserNotificationMap=new ArrayList<>();
        if(Objects.isNull(businessSocialNotificationDTO) || CollectionUtils.isEmpty(businessSocialNotificationDTO.getUsers())){
            engageAlertAuditService.saveAudit(businessDetails.getEnterpriseId(),smallEnterpriseId,"",new ArrayList<>(),true,null,String.format("Received null users for social notification for enterpriseId %s and businessId %s",businessDetails.getEnterpriseId(),businessDetails.getBusinessIds().get(0)),"Failed");
            LOGGER.info("Received null users for SocialNotificationEnabled");
            returnMap.put("userList",userNotificationMap);
            returnMap.put("guestUserList",guestUserNotificationMap);
            return returnMap;
        }
        BusinessLiteUserDTO businessLiteUserDTO = businessCoreService.getUserDetailsWithRole(smallEnterpriseId);

        if(Objects.isNull(businessLiteUserDTO) || CollectionUtils.isEmpty(businessDetails.getBusinessIds())
                || CollectionUtils.isEmpty(businessLiteUserDTO.getUserDetailsList())) {
            LOGGER.info("Received empty/null userDetails for enterpriseId {} and BusinessLiteUserDTO : {}",businessDetails, businessLiteUserDTO.getUserDetailsList());
            returnMap.put("userList",userNotificationMap);
            returnMap.put("guestUserList",guestUserNotificationMap);
            return returnMap;
        }

        Map<Integer, String> usersMap = businessLiteUserDTO.getUserDetailsList().stream().filter(user->(Objects.nonNull(user) &&
                        Objects.nonNull(user.getEmailId()) && Objects.nonNull(user.getRole()) && !"guest".equalsIgnoreCase(user.getRole()) &&
                        Objects.nonNull(user.getLocationIds()) && user.getLocationIds().contains(businessDetails.getBusinessIds().get(0)) &&
                        (!user.getEmailId().contains("@birdeye.com") )))
                .collect(Collectors.toMap(User::getId, user -> user.emailId));
        Map<Integer,String> guestUserMap=businessLiteUserDTO.getUserDetailsList().stream().filter(user->(Objects.nonNull(user) &&
                        Objects.nonNull(user.getEmailId()) && Objects.nonNull(user.getRole()) && "guest".equalsIgnoreCase(user.getRole())
                        && Objects.nonNull(user.getLocationIds()) && user.getLocationIds().contains(businessDetails.getBusinessIds().get(0)) &&
                        (!user.getEmailId().contains("@birdeye.com") )))
                .collect(Collectors.toMap(User::getId, user -> user.emailId));
        if(usersMap!=null) {
            userNotificationMap = businessSocialNotificationDTO.getUsers().stream().filter(user -> (Objects.nonNull(user.getEmailId()) && StringUtils.isNotEmpty(user.getEmailId()) && usersMap.containsKey(user.getUserId()))).collect(Collectors.toList());
        }
        if(guestUserMap!=null) {
            guestUserNotificationMap = businessSocialNotificationDTO.getUsers().stream().filter(user -> (Objects.nonNull(user.getEmailId()) && StringUtils.isNotEmpty(user.getEmailId()) && guestUserMap.containsKey(user.getUserId()))).collect(Collectors.toList());
        }
        LOGGER.info("admin Users {}",usersMap);
        LOGGER.info("guest Users {}",guestUserMap);
       returnMap.put("userList",userNotificationMap);
       returnMap.put("guestUserList",guestUserNotificationMap);
       return returnMap;
    }

    private void preparePayloadForPost(String postId, Map<String, Object> payload, Long businessNumber) {
        List<EngageNotificationDetails> postFeedDetails;
        try {
            postFeedDetails = engageConverterService.fetchEsDocByFeedId(postId);
            if (CollectionUtils.isEmpty(postFeedDetails)) {
                LOGGER.info("No record found in ES for raw feed id {}", postId);
                return;
            }

            preparePayloadForPost(postFeedDetails.get(0), payload, businessNumber);
        } catch (Exception ex) {
            LOGGER.info("exception occurred", ex);
        }
    }

    private void preparePayloadForSubComment(EngageNotificationDetails feedDetails, Map<String,Object> payload,Long businessNumber) {
        payload.put("subAuthorName",feedDetails.getAuthorName());
        if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage()))
        {
            payload.put("subAuthorProfileImage",feedDetails.getAuthorProfileImage());
        }
        else {
            payload.put("subAuthorInitial",commonService.getInitials(feedDetails.getAuthorName()));
        }
        payload.put("subDate",relativeDate(feedDetails.getFeedDate()));
        payload.put("subText",feedDetails.getText());
        payload.put("subImage",getSingleUrlForMultipleMedia(feedDetails,businessNumber, payload));
        try {
            EngageNotificationDetails parentFeedDetails = engageConverterService.fetchEsDocByFeedIdAndPageId(feedDetails.getEventParentId(), feedDetails.getPageId());
            if(Objects.nonNull(parentFeedDetails)) {
                preparePayloadForComment(parentFeedDetails, payload,businessNumber);
            }
        }
        catch(Exception ex)
        {
            LOGGER.info("Exception occurred",ex);
        }

    }

    private EmailDTO prepareMetadataForSendEmail(EngageBusinessDetails businessDetails) {
        EmailDTO emailDto = new EmailDTO();
        emailDto.setBusinessId(businessDetails.getBusinessIds().get(0));
        emailDto.setRequestType(Constants.ENGAGE_EMAIL_ALERT);
        emailDto.setExternalUuid( StringUtils.join(Constants.ENGAGE_EMAIL_ALERT, "-",businessDetails.getEnterpriseId().toString()));
        return emailDto;
    }

    private void preparePayloadForComment(EngageNotificationDetails feedDetails, Map<String, Object> payload,long businessNumber) {
        payload.put(AUTHOR_NAME,feedDetails.getAuthorName());
        if(Objects.nonNull(feedDetails.getAuthorProfileImage()) && commonService.isValidUrl(feedDetails.getAuthorProfileImage()))
        {
            payload.put(AUTHOR_PROFILE_IMAGE,feedDetails.getAuthorProfileImage());
        }
        else {
            payload.put("authorInitial", commonService.getInitials(feedDetails.getAuthorName()));
        }
        payload.put("commentDate",relativeDate(feedDetails.getFeedDate()));
        payload.put("commentText",feedDetails.getText());
        payload.put("commentImage",getSingleUrlForMultipleMedia(feedDetails,businessNumber, payload));

    }


    private EngageNotificationDetails getFeedDocumentFromEs(Integer rawFeedId) throws IOException {
        GetResponse doc = esService.fetchEsDocumentByDocId(rawFeedId, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName() );
        if(doc.isExists()) {
            return JSONUtils.fromJSON(doc.getSourceAsString(), EngageNotificationDetails.class);
        }
        return null;
    }

    public static String relativeDate(Date date){
        Date now=new Date();
        if(date.before(now)){
            int hoursPassed=(int) TimeUnit.MILLISECONDS.toHours(now.getTime() - date.getTime());
            if(hoursPassed>1 && hoursPassed < 23 )return hoursPassed+" hours ago";
            else{
                int minutesPassed=(int) TimeUnit.MILLISECONDS.toMinutes(now.getTime() - date.getTime());
                if(minutesPassed>1 && minutesPassed < 60 )return minutesPassed+" minutes ago";
                int secondsPassed=(int) TimeUnit.MILLISECONDS.toSeconds(now.getTime() - date.getTime());
                if(secondsPassed > 1 && secondsPassed < 60) 	return secondsPassed +" seconds ago";
                else{
                    return new SimpleDateFormat(DATE_FORMAT).format(date);
                }
            }
        }
        else
        {
            return new SimpleDateFormat(DATE_FORMAT).format(date);
        }
    }

    public String getSingleUrlForMultipleMedia(EngageNotificationDetails feedDetails, Long businessNumber, Map<String, Object> payload) {
        List<Picturesque> picturesqueList = new ArrayList<>();
        PicturesqueRequest picturesqueRequest = new PicturesqueRequest();
        if (Objects.nonNull(feedDetails.getImageUrlsMetaData()) && !feedDetails.getImageUrlsMetaData().isEmpty()) {
            for (MediaData mediaFile : feedDetails.getImageUrlsMetaData()) {
                if(Objects.nonNull(mediaFile) && Objects.nonNull(mediaFile.getMediaUrl())) {
                    Picturesque picturesque = new Picturesque();
                    picturesque.setType(PICTURESQUE_TYPE_IMAGE);
                    picturesque.setCompleteURL(mediaFile.getMediaUrl());
                    if(StringUtils.isNotEmpty(mediaFile.getMediaMetaData()))
                    {
                        List<String> mediaMetaList= Arrays.asList(mediaFile.getMediaMetaData().split(","));
                        List<String> aspectRatioIdentifier= Arrays.asList(mediaMetaList.get(mediaMetaList.size() - 1).split(":"));
                        picturesque.setAspectRatio((aspectRatioIdentifier.get(aspectRatioIdentifier.size()-1)).split("}")[0]);
                    }
                    picturesqueList.add(picturesque);
                }
            }
        }
        if (Objects.nonNull(feedDetails.getVideoUrlsMetaData()) && !feedDetails.getVideoUrlsMetaData().isEmpty()) {
            for (MediaData mediaFile : feedDetails.getVideoUrlsMetaData()) {
                if(Objects.nonNull(mediaFile) && Objects.nonNull(mediaFile.getMediaUrl())) {
                    Picturesque picturesque = new Picturesque();
                    picturesque.setType(PICTURESQUE_TYPE_VIDEO);
                    picturesque.setCompleteURL(mediaFile.getMediaUrl());
                    if(StringUtils.isNotEmpty(mediaFile.getMediaMetaData()))
                    {
                        List<String> mediaMetaList= Arrays.asList(mediaFile.getMediaMetaData().split(","));
                        List<String> aspectRatioIdentifier= Arrays.asList(mediaMetaList.get(mediaMetaList.size() - 1).split(":"));

                        picturesque.setAspectRatio((aspectRatioIdentifier.get(aspectRatioIdentifier.size()-1)).split("}")[0]);
                    }
                    picturesqueList.add(picturesque);
                }
            }
        }

        if (!picturesqueList.isEmpty()) {
            picturesqueRequest.setBusinessNumber(businessNumber);
            picturesqueRequest.setAttachments(picturesqueList);
            PicturesqueResponse response = picturesqueGen.getSingleMediaData(picturesqueRequest);
            if (response.isSuccess())
                return response.getS3Url();
            else {
                LOGGER.info("collage cannot be generated for the multimediaFile {}",picturesqueList);
                engageAlertAuditService.saveAudit(businessNumber,null, feedDetails.getFeedId(),new ArrayList<>(),true,payload,"Could not generate collage for the images and videos","Pending");
            }
        }
        return null;
    }

    private String getRedirectUrl(BusinessLiteDTO businessLiteDTO,String feedId) {
        StringBuilder connectCTA = new StringBuilder();
        WebsiteDomainInfo businessDomain = businessService.getBusinessDomain(businessLiteDTO.getBusinessId());
        if(Objects.nonNull(businessDomain)) {
            connectCTA.append(getDomainBaseURL(businessDomain));
//        connectCTA.append("https://preprod8app.birdeye.com");
            connectCTA.append(DASHBOARD_ENGAGE_SOCIAL_CONNECT_URL);
            connectCTA.append("?businessId=").append(businessLiteDTO.getBusinessId());
            connectCTA.append("&feedId=").append(feedId);
            LOGGER.info("Redirection Url for feedId {} is {}", feedId, connectCTA);
            return connectCTA.toString();
        }
        return null;
    }
    private String getDomainBaseURL(WebsiteDomainInfo domainMessage) {
        String protocol = domainMessage.getSecureEnabled() != null && domainMessage.getSecureEnabled() == 1 ? "https" : "http";
        return protocol + "://" + domainMessage.getDomainName();
    }

}
