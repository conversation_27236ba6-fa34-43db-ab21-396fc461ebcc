package com.birdeye.social.service.drafts;

import com.birdeye.social.entities.SocialPostDraftInfo;

import java.util.List;

public interface SocialPostDraftInfoService {
    List<SocialPostDraftInfo> findByMasterPostIdIn(List<Integer> masterPostIds);

    List<SocialPostDraftInfo> findOneByMasterPostIdIn(List<Integer> socialPostIds);

    List<SocialPostDraftInfo> findBySocialPostIdIn(List<Integer> socialPostIds);

    void save(SocialPostDraftInfo draft);

    boolean existsByMasterPostIdAndSourceId(Integer masterPostId, Integer sourceId);

    List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdIn(List<Integer> masterPostIds, List<Integer> sourceIds);
    List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdInAndEnterpriseId(List<Integer> masterPostIds, List<Integer> sourceIds, Integer enterpriseId);

    void deleteById(Integer id);
}
