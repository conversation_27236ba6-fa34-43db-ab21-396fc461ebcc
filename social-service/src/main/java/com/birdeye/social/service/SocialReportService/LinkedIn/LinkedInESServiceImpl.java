package com.birdeye.social.service.SocialReportService.LinkedIn;

import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.Facebook.PagePostData;
import com.birdeye.social.service.EsService;
import com.birdeye.social.utils.JSONUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

@Service
public class LinkedInESServiceImpl implements LinkedInESService{

    @Autowired
    private EsService esCommonService;

    private static final Logger LOG = LoggerFactory.getLogger(LinkedInESServiceImpl.class);

    @Override
    public void bulkPostPageInsights(List<ESPageRequest> esPageRequests, String index) {
        try {
            BulkRequest bulkRequest = prepareBulkRequestForPage(esPageRequests,index);
            esCommonService.addBulkDocument(bulkRequest);
            LOG.info("Successfully uploaded document to ES");
        }catch (IOException e){
            LOG.error("Exception for getting the posting page insights data to elastic search error: {}", e.getMessage());
        }
    }

    @Override
    public List<PagePostData> getDataPointsForPost(InsightsESRequest request) {
        return null;
    }

    private BulkRequest prepareBulkRequestForPage(List<ESPageRequest> esPageRequests, String index) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        BulkRequest bulkRequest = new BulkRequest();
        esPageRequests.forEach(esPageRequest -> {
            String s = JSONUtils.toJSON(esPageRequest);
            try {
                bulkRequest.add(new IndexRequest(index).id(dateFormat.parse(esPageRequest.getDay()).getTime()+"_"+esPageRequest.getPage_id()).source(s, XContentType.JSON));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        });
        return bulkRequest;
    }
}
