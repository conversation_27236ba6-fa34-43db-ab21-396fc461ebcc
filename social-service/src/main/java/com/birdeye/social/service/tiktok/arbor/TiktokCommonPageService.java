package com.birdeye.social.service.tiktok.arbor;

import com.birdeye.social.dto.TiktokLiteDTO;
import com.birdeye.social.entities.BusinessGetPageOpenUrlRequest;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.entities.BusinessTiktokAccounts;
import com.birdeye.social.model.Validity;
import org.springframework.data.domain.PageRequest;
import com.birdeye.social.model.tiktok.TiktokAccessTokenDataResponse;

import java.util.List;

public interface TiktokCommonPageService {

    void fetchPages(BusinessGetPageRequest request, TiktokAccessTokenDataResponse user, String type, Long parentId);

    void fetchPagesOpenUrl(BusinessGetPageOpenUrlRequest request, TiktokAccessTokenDataResponse user, Long parentId, String key);

    Validity fetchValidityAndErrorMessage(BusinessTiktokAccounts page);

    void saveTiktokLocationMapping(Integer locationId, String pageId, Integer userId, String type);

    void fetchPagesReconnect(TiktokAccessTokenDataResponse user, String pageId, String type, Long parentId, Integer userId);

    boolean checkPermission(List<BusinessTiktokAccounts> tiktokAccounts, List<String> modules);

    TiktokLiteDTO findByRequestId(String requestId, PageRequest pageRequest);

    TiktokLiteDTO findByRequestIdAndPageName(String search, PageRequest pageRequest, String requestId);
}
