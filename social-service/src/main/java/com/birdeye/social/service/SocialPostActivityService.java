package com.birdeye.social.service;

import com.birdeye.social.constant.PostActivityType;
import com.birdeye.social.model.*;

public interface SocialPostActivityService {

    PostActivityResponse getPostActivityData(Integer postId, String timezone, Integer pageNo, Integer size, Boolean applyPagination) throws Exception;
    void saveCreateActivity(Integer postId, Integer userId);

    void saveDuplicateActivity(Integer postId, Integer userId);

    void saveDeleteActivity(Integer postId, Integer userId, PostActivityDelete postActivityDelete);

    void saveDeleteFailedActivity(Integer postId, Integer userId);

    void saveQuoteTweetActivity(Integer postId, Integer userId);

    void saveSendNowActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled);

    void saveRescheduledActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled);

    void saveEditActivity(Integer postId, Integer userId, Integer childPostId);

    void savePublishedActivity(Integer postId, Integer userId);

    void saveEditRescheduledActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled);

    void saveApprovalActivity(Integer postId, Integer userId, String guestUserEmailId,ApprovalActivityData approvalActivityData, PostActivityType postActivityType);

    void saveActivity(SavePostActivityRequest request);

    void sendEventToSaveApprovalActivity(Integer postId, String userId, String activity, Object metadata);

    void sendEventToSaveActivity(Integer postId, Integer userId, String activity, Object metadata, Integer enterpriseId, Integer... childPostId);
}
