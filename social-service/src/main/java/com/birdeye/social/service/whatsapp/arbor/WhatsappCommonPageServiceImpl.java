package com.birdeye.social.service.whatsapp.arbor;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.BusinessWhatsappAccountsRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.WhatsappLiteDTO;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.ChannelAuthRequest;
import com.birdeye.social.model.DebugTokenResponse;
import com.birdeye.social.model.FbUserProfileInfo;
import com.birdeye.social.model.Validity;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.IPermissionMappingService;
import com.birdeye.social.service.ISocialModulePermissionService;
import com.birdeye.social.service.SocialAccountSetupCommonService;
import com.birdeye.social.service.whatsapp.dto.*;
import com.birdeye.social.service.whatsapp.dto.WABA.WABADetails;
import com.birdeye.social.service.whatsapp.external.WhatsappExternalService;
import com.birdeye.social.specification.WhatsappSpecification;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;

@Service
public class WhatsappCommonPageServiceImpl extends SocialAccountSetupCommonService implements WhatsappCommonPageService {

    private static final Logger LOG	= LoggerFactory.getLogger(WhatsappCommonPageServiceImpl.class);

    @Autowired
    private WhatsappExternalService whatsappExternalService;

    @Autowired
    private BusinessWhatsappAccountsRepository whatsappAccountsRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private KafkaProducerService kafkaProducer;
    @Autowired
    private IRedisLockService redisService;

    @Autowired
    private BusinessGetPageReqRepo businessGetPageReqRepo;

    @Autowired
    private IPermissionMappingService permissionMappingService;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private ISocialModulePermissionService socialModulePermissionService;

    @Autowired
    private WhatsappSpecification whatsappSpecification;

    @Autowired
    private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

    private static final String WHATSAPP_MAPPING_TOPIC = "social-whatsapp-mapping-event";

    @Async
    @Override
    public void fetchWABADetails(ChannelAuthRequest authRequest,BusinessGetPageRequest request, String extendedToken, FbUserProfileInfo user,
                                 String type, Long parentId, BusinessLiteDTO businessLiteDTO) {
        String key = SocialChannel.WHATSAPP.getName().toLowerCase().concat(String.valueOf(parentId));
        try {

            String wabaId=authRequest.getWabaId();
            String metaBusinessId=authRequest.getMetaBusinessId();
            String phoneNumberId=authRequest.getPhoneNumberId();
            Integer locationId= authRequest.getLocationId();

            //Get WABA information by WABAID
            WABADetails wabaDetails= whatsappExternalService.getWabaInformationByWABAId(extendedToken,wabaId);
            if(Objects.isNull(wabaDetails)){
                throw new BirdeyeSocialException(ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND, "WABA details not found");
            }
            //get PhoneNumber Data through WABA ID
            PhoneNumberInformation phoneNumberDetailsByWABAId= whatsappExternalService.getPhoneNumberDetailsByWABAId(extendedToken,phoneNumberId);
            //Get Business Verification Status
            MetaBusinessVerificationStatus status=whatsappExternalService.businessVerificationStatus(metaBusinessId,extendedToken);

            if(Objects.isNull(phoneNumberDetailsByWABAId)){
                throw new BirdeyeSocialException(ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND, "Phone number details not found");
            }
            WhatsappAccountDetails whatsappAccountDetails= mergeData(phoneNumberDetailsByWABAId,wabaDetails,metaBusinessId,status);
            saveWhatsappAccounts(whatsappAccountDetails, request, user, type, parentId,businessLiteDTO,locationId);
        } catch (Exception ex) {
            redisService.release(key);
            LOG.error("[Redis Lock] (Whatsapp) Lock released for business {}, error {}", parentId, ex);
            BusinessGetPageRequest req = checkInProgressBusinessRequests(parentId, type);
            if(req!=null){
                req.setStatus(Status.CANCEL.getName());
                req.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0, Math.min(ex.getMessage().length(), 4000)) : null);
                businessGetPageReqRepo.saveAndFlush(req);
                pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(),req.getRequestType(),Status.COMPLETE.getName(), parentId,true);
            }else{
                pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(),"connect",Status.COMPLETE.getName(), parentId,true);
            }
        }
    }


    private WhatsappAccountDetails mergeData(PhoneNumberInformation phoneNumberDetailsByWABAId, WABADetails wabaDetails,
                                             String metaBusinessId,MetaBusinessVerificationStatus status) {
        WhatsappAccountDetails whatsappAccountDetails = new WhatsappAccountDetails();
        if(Objects.nonNull(phoneNumberDetailsByWABAId)){
            whatsappAccountDetails.setPhoneNumberid(phoneNumberDetailsByWABAId.getId());
            whatsappAccountDetails.setDisplay_phone_number(phoneNumberDetailsByWABAId.getDisplay_phone_number());
            whatsappAccountDetails.setQuality_rating(QualityRating.getValueByCode(
                    phoneNumberDetailsByWABAId.getQuality_rating()));
            whatsappAccountDetails.setVerified_name(phoneNumberDetailsByWABAId.getVerified_name());
            whatsappAccountDetails.setStatus(phoneNumberDetailsByWABAId.getStatus());
            whatsappAccountDetails.setCode_verification_status(phoneNumberDetailsByWABAId.getCode_verification_status());
            LOG.info("Message Tier :{}",phoneNumberDetailsByWABAId.getMessaging_limit_tier());
            if(Objects.isNull(phoneNumberDetailsByWABAId.getMessaging_limit_tier())){
                whatsappAccountDetails.setMessagingLimit("250");
            }
            else {
                whatsappAccountDetails.setMessagingLimit(MessagingLimit.getLimitByDescription(phoneNumberDetailsByWABAId.getMessaging_limit_tier()));
            }
        }

        if(Objects.nonNull(wabaDetails)){
            whatsappAccountDetails.setWabaId(wabaDetails.getId());
            whatsappAccountDetails.setName(wabaDetails.getName());
            whatsappAccountDetails.setCurrency(wabaDetails.getCurrency());
            if (Objects.nonNull(wabaDetails.getTimezoneId())) {
                whatsappAccountDetails.setTimezoneId(CountryDetails.getValueById(Integer.valueOf(wabaDetails.getTimezoneId())));
            } else {
                LOG.warn("TimezoneId is null for WABA details with ID: {}", wabaDetails.getId());
                whatsappAccountDetails.setTimezoneId(null); // or set a default value if applicable
            }            whatsappAccountDetails.setAccountReviewStatus(wabaDetails.getAccountReviewStatus());
            if(Objects.nonNull(wabaDetails.getBusinessInfo()))
                whatsappAccountDetails.setMetaBusinessName(wabaDetails.getBusinessInfo().getName());

        }
        whatsappAccountDetails.setMetaBusinessId(metaBusinessId);
        whatsappAccountDetails.setMetaBusinessStatus(status.getVerification_status());
        return whatsappAccountDetails;


    }

    private void saveWhatsappAccounts(WhatsappAccountDetails data, BusinessGetPageRequest request,
                                      FbUserProfileInfo user, String type, Long parentId,BusinessLiteDTO business,Integer businessId) {
        Integer pageCount = 0;
        Integer totalCount = 0;
        String key = SocialChannel.WHATSAPP.getName().toLowerCase().concat(String.valueOf(parentId));
        try {
            if (data != null) {
                // get Existing accounts form DB
                totalCount++;
                BusinessWhatsappAccounts existingAccount = whatsappAccountsRepository.findByPhoneNumberId(data.getPhoneNumberid());
                if (Objects.nonNull(existingAccount)) {
                    updateExistingAccountsDetails(existingAccount, request.getId().toString(), data, user,
                            request.getEmail(), request.getUserAccessToken());
                } else {
                    LOG.info("New page found");
                    saveNewWhatsappAccount(request, data, user,parentId,business.getAccountId(),businessId);
                    pageCount++;
                }
            }
            request.setPageCount(pageCount);
            request.setTotalPages(totalCount);
            if (Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0) {
                request.setStatus(Status.NO_PAGES_FOUND.getName());
            } else {
                request.setStatus(Status.COMPLETE.getName());
            }
            LOG.info("Saving info for reqId: {}", request.getId());
            redisService.release(key);
            businessGetPageReqRepo.saveAndFlush(request);
            pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), request.getRequestType(), request.getStatus(), parentId);
        } catch (Exception ex) {
            redisService.release(key);
            LOG.error("[Redis Lock] (WHatsapp) Lock released for business {}, error {}", parentId, ex);
            BusinessGetPageRequest req = checkInProgressBusinessRequests(parentId, type);
            if (req != null) {
                req.setStatus(Status.CANCEL.getName());
                req.setErrorLog(ex.getMessage() != null ? ex.getMessage().substring(0, Math.min(ex.getMessage().length(), 4000)) : null);
                businessGetPageReqRepo.saveAndFlush(req);
                pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), req.getRequestType(), Status.COMPLETE.getName(), parentId, true);
            } else {
                pushCheckStatusInFirebase(SocialChannel.WHATSAPP.getName().toLowerCase(), "connect", Status.COMPLETE.getName(), parentId, true);
            }
        }
    }



    private void saveNewWhatsappAccount( BusinessGetPageRequest request, WhatsappAccountDetails accountDetails, FbUserProfileInfo user,
                                         Long enterpriseId, Integer accountId,Integer businessId) {
        BusinessWhatsappAccounts businessWhatsappAccount= new BusinessWhatsappAccounts();
        businessWhatsappAccount.setRequestId(request.getId().toString());

        businessWhatsappAccount.setWabaId(accountDetails.getWabaId());
        businessWhatsappAccount.setWabaStatus(accountDetails.getAccountReviewStatus());
        businessWhatsappAccount.setCountry(accountDetails.getTimezoneId());
        businessWhatsappAccount.setWabaName(accountDetails.getName());
        businessWhatsappAccount.setMetaBusinessName(accountDetails.getMetaBusinessName());
        businessWhatsappAccount.setMetaBusinessId(accountDetails.getMetaBusinessId());
        businessWhatsappAccount.setIsBusinessVerified("VERIFIED".equalsIgnoreCase(accountDetails.getMetaBusinessStatus())?1:0);

        businessWhatsappAccount.setPhoneNumberId(accountDetails.getPhoneNumberid());
        businessWhatsappAccount.setPhoneNumberStatus(accountDetails.getStatus());
        businessWhatsappAccount.setMessagingLimit(accountDetails.getMessagingLimit());
        businessWhatsappAccount.setPhoneNumber(accountDetails.getDisplay_phone_number());
        businessWhatsappAccount.setVerifiedName(accountDetails.getVerified_name());
        businessWhatsappAccount.setQualityRating(accountDetails.getQuality_rating());

        businessWhatsappAccount.setScopes(null);

        businessWhatsappAccount.setAccountId(accountId);
        businessWhatsappAccount.setEnterpriseId(enterpriseId);
        businessWhatsappAccount.setUpdatedBy(request.getBirdeyeUserId());
        businessWhatsappAccount.setIsValid(1);
        businessWhatsappAccount.setValidityType(ValidTypeEnum.VALID.getId());
        businessWhatsappAccount.setIsSelected(1);
        businessWhatsappAccount.setAccessToken(request.getUserAccessToken());
        businessWhatsappAccount.setCreatedBy(request.getBirdeyeUserId());
        businessWhatsappAccount.setExpiresOn(null);
        businessWhatsappAccount.setPagePermission(null);
        businessWhatsappAccount.setUserEmailId(request.getEmail());
        businessWhatsappAccount.setBusinessId(businessId);
        whatsappAccountsRepository.save(businessWhatsappAccount);
        whatsappAccountsRepository.flush();


        commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(businessWhatsappAccount),
                user.getId(), businessId, businessWhatsappAccount.getEnterpriseId());

        LOG.info("[Arbor Whatsapp] Page with phoneNumber: {} successfully mapped to locationId: {}", businessWhatsappAccount.getPhoneNumberId(),
                businessId);
        sendWhatsappMappingEvent(businessWhatsappAccount,businessId,"MAPPED");

    }

    private void sendWhatsappMappingEvent(BusinessWhatsappAccounts data, Integer locationId,String status) {
        WhatsappMappingEvent whatsappMappingEvent = new WhatsappMappingEvent();
        whatsappMappingEvent.setWabaId(data.getWabaId());
        whatsappMappingEvent.setPhoneNumberId(data.getPhoneNumberId());
        whatsappMappingEvent.setBusinessId(locationId);
        whatsappMappingEvent.setMappingStatus(status);
        kafkaProducer.sendObjectV1(WHATSAPP_MAPPING_TOPIC,whatsappMappingEvent);
    }

    private void updateExistingAccountsDetails(BusinessWhatsappAccounts existingAccount,  String requestId,
                                               WhatsappAccountDetails data, FbUserProfileInfo user,
                                               String email, String userAccessToken) {
        LOG.info("inside updateExistingAccountsDetails: {}",requestId);
        Set<String> oldRequestIds = new HashSet<>();

        oldRequestIds.add(existingAccount.getRequestId());
        existingAccount.setRequestId(requestId);
        existingAccount.setUpdatedBy(null);
        if(existingAccount.getEnterpriseId() == null) {
            existingAccount.setVerifiedName(data.getVerified_name());
            existingAccount.setWabaName(data.getName());
            existingAccount.setPhoneNumber(data.getDisplay_phone_number());
            existingAccount.setIsValid(1);
            existingAccount.setAccessToken(userAccessToken);
            existingAccount.setUserEmailId(email);
            existingAccount.setMetaBusinessId(data.getMetaBusinessId());
            existingAccount.setMetaBusinessName(data.getMetaBusinessName());
            existingAccount.setIsBusinessVerified("VERIFIED".equalsIgnoreCase(data.getMetaBusinessStatus())?1:0);
        }


        String tokenPermissions = null;
        DebugTokenResponse tokenResponse = null;
        LOG.info("Fetching token permission for userAccessToken: {}",userAccessToken);
        try {
            tokenResponse = getWhatsappPageToken(userAccessToken);
            if (Objects.nonNull(tokenResponse) && Objects.nonNull(tokenResponse.getData())) {
                tokenPermissions = String.join(",", tokenResponse.getData().getScopes());
            }
        } catch (Exception e) {
            LOG.error("For page id {} Error while saving the data ", userAccessToken, e);
        }
        String finalTokenPermissions = tokenPermissions;
        // DebugTokenResponse finalTokenResponse = tokenResponse;

        existingAccount.setPagePermission(finalTokenPermissions);
        //Granular scope needs to be checked
             /*   if(Objects.nonNull(finalTokenResponse) && Objects.nonNull(finalTokenResponse.getData())) {
                    List<String> granularPermissions = commonService.getFilteredScopeForPage(finalTokenResponse, Collections.singletonList(c.get()));
                    c.setGranularPagePermissions(String.join(",", granularPermissions));
                }*/
        commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(existingAccount),
                String.valueOf(user.getId()), existingAccount.getBusinessId(), existingAccount.getEnterpriseId());


        LOG.info("Existing page data being saved for pages: {}",existingAccount);
        whatsappAccountsRepository.save(existingAccount);
        //NOT IN SCOPE
        // existingAccounts.forEach(accounts -> commonService.uploadPageImageToCDN(accounts));
        // List fbPagesIds = existingFBPages.stream().map(BusinessFBPage::getFacebookPageId).collect(Collectors.toList());
        // pushToKafkaForValidity(Constants.FACEBOOK, fbPagesIds);
        whatsappAccountsRepository.flush();
        //Not in scope as of now
        /*  if(CollectionUtils.isNotEmpty(oldRequestIds)) { // send event to check invalid get page requests and mark them as cancelled
                kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.WHATSAPP, oldRequestIds));
            }*/
    }

    @Override
    public boolean checkPermission(List<BusinessWhatsappAccounts> whatsappAccountsList, List<String> modules) {
        List<String> tiktokPermissions = new ArrayList<>();
        if(CollectionUtils.isEmpty(whatsappAccountsList)){
            LOG.info("whatsappAccounts list is empty");
            return true;
        }

        if (CollectionUtils.isNotEmpty(modules)) {
            for (String module : modules) {

                SocialModulePermission socialModulePermission = socialModulePermissionService
                        .getPermissionsForChannelAndModule(SocialChannel.WHATSAPP.getId(), module);
                if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
                    List<String> modulePermissions = Arrays
                            .asList(socialModulePermission.getPermissionsNeeded().split(","));
                    tiktokPermissions.addAll(modulePermissions);
                }
            }

            for(BusinessWhatsappAccounts whatsappAccounts : whatsappAccountsList) {
                String permission = whatsappAccounts.getScopes();
                if (whatsappAccounts.getIsValid() == 0 || StringUtils.isEmpty(permission)) {
                    return false;
                }
                List<String> permissions = Arrays.stream(permission.split(",")).collect(Collectors.toList());
                if(!new HashSet<>(permissions).containsAll(tiktokPermissions)){
                    return false;
                }
            }
        }
        return true;
    }


    @Override
    public Validity fetchValidityAndErrorMessage(BusinessWhatsappAccounts page) {
        Validity validity = new Validity();
        if(page.getIsValid() == 1) {
            List<PermissionMapping> permissionMappings = permissionMappingService.
                    getDataByChannelAndPermissionNameNotNull(SocialChannel.WHATSAPP.getName().toLowerCase()).
                    stream().filter(permissionMapping -> Objects.nonNull(permissionMapping.getPermissionName())).collect(Collectors.toList());

            String errorMessage = Constants.DEFAULT_ERROR_WHATSAPP_MESSAGE_START;
            Set<String > errorMessageSet = new HashSet<>();
            int numberOfErrors = 0;
            for(PermissionMapping permission : permissionMappings){
                if( StringUtils.isNotEmpty(page.getScopes()) &&
                        Objects.nonNull(permission) &&
                        StringUtils.isNotEmpty(permission.getPermissionName()) &&
                        !page.getScopes().contains(permission.getPermissionName())){
                    errorMessageSet.add(permission.getErrorMessage());
                    numberOfErrors++;
                }
            }
            errorMessage += String.join(", ", errorMessageSet);
            if(numberOfErrors > 0) {
                validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
                validity.setErrorCode(PERMISSION_MISSING);
                validity.setErrorMessage(errorMessage);
            }
        } else {
            validity.setValidType(ValidTypeEnum.INVALID.getName());
            validity.setErrorCode(Constants.INTEGRATION);
            PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
                    SocialChannel.WHATSAPP.getName().toLowerCase(),Constants.INTEGRATION);
            if(Objects.nonNull(permissionMapping)) {
                validity.setErrorMessage(permissionMapping.getErrorMessage());
            }
        }
        if(Objects.isNull(validity.getValidType()) && page.getIsValid() == 1) {
            validity.setValidType(ValidTypeEnum.VALID.getName());
        }

        return validity;
    }

    @Override
    public WhatsappLiteDTO findByRequestId(String requestId, PageRequest pageRequest) {
        Page<BusinessWhatsappAccounts> page = whatsappAccountsRepository.findByRequestId(requestId, pageRequest);
        return WhatsappLiteDTO.convertToWhatsappLiteDTO(page);
    }

    @Override
    public WhatsappLiteDTO findByRequestIdAndPageName(String search, PageRequest pageRequest, String requestId) {
        Page<BusinessWhatsappAccounts> page = searchWithRequestIdAndPageName(requestId, search, pageRequest);
        return WhatsappLiteDTO.convertToWhatsappLiteDTO(page);
    }

    @Override
    public void saveWhatsappLocationMapping(Integer locationId, String pageId, Integer userId, String type) {
        LOG.info("[Whatsapp Arbor] Tiktok page Id {} mapping with location Id {}", pageId, locationId);

        BusinessWhatsappAccounts whatsappAccount = whatsappAccountsRepository.findByPhoneNumberId(pageId);
        if (Objects.isNull(whatsappAccount)) {
            LOG.error("[Whatsapp Arbor] For Whatsapp page id {} no data found ", pageId);
            throw new BirdeyeSocialException(ErrorCodes.SOCIAL_RAW_PAGE_NOT_FOUND, "whatsapp account data not found");
        }
        if(whatsappAccountsRepository.existsByBusinessId(locationId)){
            throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS, MAPPING_ALREADY_EXIST_ERROR);
        }

        if (!Objects.isNull(whatsappAccount.getBusinessId())) {
            LOG.error("[Whatsapp Arbor] Whatsapp page id {} is already mapped ", whatsappAccount);
            throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS, "whatsapp account is already mapped");
        }
        whatsappAccount.setBusinessId(locationId);
        if(Objects.isNull(whatsappAccount.getCreatedBy())) {
            whatsappAccount.setCreatedBy(userId);
        }
        whatsappAccount.setUpdatedBy(userId);
        updateWhatsappAccountForReseller(whatsappAccount, type, locationId);
        whatsappAccountsRepository.save(whatsappAccount);

        commonService.sendWhatsappSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), Arrays.asList(whatsappAccount), userId.toString(), locationId,
                RESELLER.equals(type)?whatsappAccount.getResellerId():whatsappAccount.getEnterpriseId());

        LOG.info("[Whatsapp Arbor] Account with profileId: {} successfully mapped to locationId: {}", whatsappAccount, locationId);
    }


    private void updateWhatsappAccountForReseller(BusinessWhatsappAccounts accountDetails, String type, Integer locationId) {
        if (Constants.RESELLER.equals(type)) {
            BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(locationId, false);
            accountDetails.setAccountId(businessLiteDTO.getAccountId());

            if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
                accountDetails.setEnterpriseId(businessLiteDTO.getBusinessNumber());
            } else {
                accountDetails.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
            }
        }
    }
    private List<BusinessGetPageRequest> getRequestForBusiness(Long parentId, String status, String type) {

        return type.equals(RESELLER) ? businessGetPageReqRepo.findByResellerIdAndStatusAndChannel(parentId, status, SocialChannel.WHATSAPP.getName().toLowerCase())
                : businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannel(parentId, status, SocialChannel.WHATSAPP.getName().toLowerCase());
    }

    private BusinessGetPageRequest checkInProgressBusinessRequests(Long parentId, String type) {
        LOG.info("Exception occurred in connect whatsapp, Checking for in progress request for business id {}", parentId);
        List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(parentId, Status.INITIAL.getName(), type);
        if (CollectionUtils.isNotEmpty(underProcessRequests)) {
            return underProcessRequests.get(0);
        }
        return null;
    }


    private Page<BusinessWhatsappAccounts> searchWithRequestIdAndPageName(String requestId, String search, PageRequest pageRequest) {
        Specification<BusinessWhatsappAccounts> specification = Specifications.where(whatsappSpecification.hasPageName(search))
                .and(whatsappSpecification.hasRequestId(requestId));

        return whatsappAccountsRepository.findAll(specification, pageRequest);
    }
}