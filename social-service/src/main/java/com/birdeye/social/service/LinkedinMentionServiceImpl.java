package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.MentionRepository;
import com.birdeye.social.dto.EsMentionMetaData;
import com.birdeye.social.dto.EsMentionReviewerDataPoint;
import com.birdeye.social.dto.MentionEsRequest;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.Mention;
import com.birdeye.social.external.request.linkedin.*;
import com.birdeye.social.linkedin.LinkedinOrganizationInfo;
import com.birdeye.social.linkedin.response.*;
import com.birdeye.social.utils.LinkedinUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.linkedin.LinkedinProfileDetails;
import com.birdeye.social.linkedin.LinkedinService;
import com.birdeye.social.model.*;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.service.SocialLinkedinServiceImpl.LINKEDIN_COMPANY_URL;
import static com.birdeye.social.service.SocialLinkedinServiceImpl.LINKEDIN_PROFILE_URL;

@Service
public class LinkedinMentionServiceImpl implements LinkedinMentionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LinkedinMentionServiceImpl.class);

    private static final String pattern = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private BusinessLinkedinPageRepository businessLinkedinPageRepository;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private MentionRepository mentionRepository;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private LinkedinService linkedinService;

    @Autowired
    private SocialMentionService socialMentionService;


   /* @Override
    public void fetchLinkedinPosts(String accountId) {
        LOGGER.info("Request received to get Tagged posts for IG accountId: {}", accountId);
        try {
            BusinessLinkedinPage businessLinkedinPage = businessLinkedinPageRepository.findByProfileId(accountId);
            if (Objects.nonNull(businessLinkedinPage)) {
                LinkedinPostResponse linkedPost = linkedinService.getSocialPosts(businessLinkedinPage.getUrn(),businessLinkedinPage.getAccessToken());
                if (CollectionUtils.isNotEmpty(linkedPost.getElements())) {
                    Integer businessId = businessCoreService.getBusinessId(businessLinkedinPage.getEnterpriseId());
                    List<String> previousTagged = mentionRepository.findPostIdByPageIdAndTypeIn(accountId, Arrays.asList("tagged","post","comment","admin_comment"));
                    linkedPost.getElements().forEach(timelineResponse -> {
                        if (previousTagged.contains(timelineResponse.getNotificationId())) {
                            return;
                        }
                        MentionEsRequest mentionEsRequest = convertManualPostInfoToEsRequest(timelineResponse, businessLinkedinPage);
                        Mention mention = null;
                        try {
                            mention = DtoToEntityConverter.forLinkedin(mentionEsRequest,businessId);
                        } catch (ParseException e) {
                            throw new RuntimeException(e);
                        }
                        mention = mentionRepository.save(mention);
                        socialMentionService.publishMentionToES(mention);

                    });
                }
            }
        } catch (Exception e) {
            LOGGER.info("Error fetching tagged iG posts for accountId: {}", accountId, e);
        }
    }*/

    private MentionEsRequest convertManualPostInfoToEsRequest(LinkedinPostNotification linkedinNotification, BusinessLinkedinPage businessLinkedinPage) {
        DateFormat df = new SimpleDateFormat(pattern);
        MentionEsRequest mentionEsRequest= new MentionEsRequest();
        mentionEsRequest.setType(LinkedinEventMapping.getEventActionTypeByName(linkedinNotification.getAction()).getId());
        mentionEsRequest.setSourceId(SocialChannel.LINKEDIN.getId());
        EsMentionReviewerDataPoint mentionReviewerDataPoint = new EsMentionReviewerDataPoint();
        EsMentionMetaData mentionMetaData = new EsMentionMetaData();
        return mentionEsRequest;
    }

    @Override
    public void consumeLinkedinMentionEvent(LinkedinMentionEventRequest linkedinMentionEventRequest) {
        LOGGER.info("Request received to get mentions for Linkedin request: {}", linkedinMentionEventRequest);
       try{
            if (CollectionUtils.isNotEmpty(linkedinMentionEventRequest.getNotifications())) {
                linkedinMentionEventRequest.getNotifications().forEach(linkedinEntry -> {
                    try {
                        if (linkedinEntry.getAction().equalsIgnoreCase("COMMENT")
                                || linkedinEntry.getAction().equalsIgnoreCase("ADMIN_COMMENT"))
                        {
                            //Comment portion needs to be handled in Alert Will be doing later
                            LOGGER.info("Request received for comment and admin comment with notification id : {}", linkedinEntry.getNotificationId());
                            return;
                        }
                        Long notificationId = linkedinEntry.getNotificationId();
                        String linkedinProfileId = String.valueOf(linkedinEntry.getOrganizationalEntity().split("urn:li:organization:")[1]);
                        Mention mentionNotification = mentionRepository.findByNotificationId(notificationId.toString());
                        if (Objects.nonNull(mentionNotification)) {
                            LOGGER.info("Request received for duplicate notification id : {}", linkedinMentionEventRequest);
                            return;
                        }
                        BusinessLinkedinPage businessLinkedinPage = businessLinkedinPageRepository.findByProfileId(linkedinProfileId);
                        if (Objects.nonNull(businessLinkedinPage)) {
                             Integer businessId = businessCoreService.getBusinessId(businessLinkedinPage.getEnterpriseId());
                            MentionEsRequest mentionEsRequest = convertListenInfoToEsRequest(linkedinEntry, businessLinkedinPage, linkedinProfileId);
                            Mention mention = DtoToEntityConverter.forLinkedin(mentionEsRequest, businessId);
                            mentionRepository.save(mention);
                            socialMentionService.publishMentionToES(mention);
                        }
                    } catch (Exception e) {
                        LOGGER.info("Error fetching Linkedin event response for payload: {}", linkedinEntry, e);
                    }
                });

            }
        } catch (Exception e) {
            LOGGER.info("Error fetching Linkedin event response for payload: {}", linkedinMentionEventRequest, e);
        }
    }

    public final String LINKEDIN_SHARE_URL = "https://www.linkedin.com/feed/update/";

    private MentionEsRequest convertListenInfoToEsRequest(LinkedinNotification linkedinNotification, BusinessLinkedinPage businessLinkedinPage,String profileId) throws Exception {

        DateFormat df = new SimpleDateFormat(pattern);
        MentionEsRequest mentionEsRequest = new MentionEsRequest();
        mentionEsRequest.setPageId(profileId);
        mentionEsRequest.setEnterpriseId(businessLinkedinPage.getEnterpriseId());
        mentionEsRequest.setType(LinkedinEventMapping.getEventActionTypeByName(linkedinNotification.getAction()).getId());
        mentionEsRequest.setSourceId(SocialChannel.LINKEDIN.getId());

        EsMentionReviewerDataPoint mentionReviewerDataPoint = new EsMentionReviewerDataPoint();
        EsMentionMetaData mentionMetaData = new EsMentionMetaData();
        mentionEsRequest.setNotificationId(linkedinNotification.getNotificationId().toString());

        String owner;
        String activityURN;

        owner = Objects.nonNull(linkedinNotification.getDecoratedGeneratedActivity())?linkedinNotification.getDecoratedGeneratedActivity().getShare().getOwner():null;
        activityURN = Objects.nonNull(linkedinNotification.getDecoratedGeneratedActivity())?linkedinNotification.getDecoratedGeneratedActivity().getShare().getEntity()
                :linkedinNotification.getGeneratedActivity();

        SharePostRequestLinkedin postDetails = linkedinPostDetails(activityURN, businessLinkedinPage.getAccessToken());
        owner = Objects.isNull(owner)?postDetails.getAuthor() : owner;
        LinkedinProfileDetails profileDetails = linkedinProfileInformation(owner == null ? postDetails.getAuthor() : owner, businessLinkedinPage.getAccessToken());

        if ( Objects.nonNull(profileDetails.getVanityName())) {
            String urlPrefix =  isValid(owner,"person") ? LINKEDIN_PROFILE_URL : LINKEDIN_COMPANY_URL;
            mentionReviewerDataPoint.setProfile(urlPrefix.concat(profileDetails.getVanityName()));
        }


        if (Objects.nonNull(profileDetails.getFirstName()) && Objects.nonNull(profileDetails.getFirstName().getLocalized())) {
            mentionReviewerDataPoint.setNick(profileDetails.getFirstName().getLocalized().getEn_US().concat(" "));
            mentionReviewerDataPoint.setFirst(profileDetails.getFirstName().getLocalized().getEn_US().concat(" "));
        }

        if (Objects.nonNull(profileDetails.getLastName()) && Objects.nonNull(profileDetails.getLastName().getLocalized())) {
            mentionReviewerDataPoint.setNick(mentionReviewerDataPoint.getNick() == null ? profileDetails.getLastName().getLocalized().getEn_US()
                    : mentionReviewerDataPoint.getFirst().concat(profileDetails.getLastName().getLocalized().getEn_US()));
            mentionReviewerDataPoint.setLast(profileDetails.getLastName().getLocalized().getEn_US());
        }

        if(Objects.isNull(mentionReviewerDataPoint.getNick())){
            mentionReviewerDataPoint.setNick(profileDetails.getLocalizedName());
        }

        if (StringUtils.isNotEmpty(profileDetails.getProfilePictureUrl())) {
            mentionReviewerDataPoint.setImg(profileDetails.getProfilePictureUrl());
        }

        if (StringUtils.isNotEmpty(profileDetails.getLogoUrl())) {
            mentionReviewerDataPoint.setImg(profileDetails.getLogoUrl());
        }

        if (linkedinNotification.getAction().equalsIgnoreCase("PHOTO_MENTION")
                || linkedinNotification.getAction().equalsIgnoreCase("SHARE_MENTION")) {
            mentionMetaData=null;
            mentionEsRequest.setUrl(LINKEDIN_SHARE_URL + (Objects.nonNull(linkedinNotification.getDecoratedGeneratedActivity())?linkedinNotification.getDecoratedGeneratedActivity().getShare().getEntity():linkedinNotification.getGeneratedActivity()));
            mentionEsRequest.setText(Objects.nonNull(linkedinNotification.getDecoratedGeneratedActivity())?linkedinNotification.getDecoratedGeneratedActivity().getShare().getText(): LinkedinUtils.removeMentionsHashtag(postDetails.getCommentary()));
            mentionEsRequest.setPostId(Objects.nonNull(linkedinNotification.getDecoratedGeneratedActivity())?linkedinNotification.getDecoratedGeneratedActivity().getShare().getEntity():linkedinNotification.getGeneratedActivity());
           mentionEsRequest.setDate(df.format(postDetails.getPublishedAt()));

           if(Objects.nonNull(postDetails.getVideoUrl()))
                 mentionEsRequest.setVideo(postDetails.getVideoUrl().get(0));
            if(Objects.nonNull(postDetails.getImageUrl()))
               mentionEsRequest.setImagesList(postDetails.getImageUrl());

        }
        mentionEsRequest.setMentionMetaData(mentionMetaData);
        mentionEsRequest.setReviewerData(mentionReviewerDataPoint);
        return mentionEsRequest;
    }


    public LinkedinProfileDetails linkedinProfileInformation(String owner, String accessToken) throws IOException {
        String personId = getIdFromUrn(owner);
        LinkedinProfileDetails personIdProfileDetails = null;
        if (Objects.nonNull(personId)) {
            if(isValid(owner,"person")){
                 personIdProfileDetails=  linkedinService.getPersonProfileDetailsWithImageUrl(personId, accessToken);
            } else {
                LinkedinOrganizationInfo response= linkedinService.fetchOrganizationLookupDetailsWithLogoUrl(personId, accessToken);
                personIdProfileDetails = response.getResults().get(personId);
            }
            if (Objects.nonNull(personIdProfileDetails))
                return personIdProfileDetails;
        }

        return null;
    }

    public static String getIdFromUrn(String urn) {
        try {
            return urn.substring(urn.lastIndexOf(":") + 1);
        } catch (Exception e) {
            LOGGER.error("[Linkedin] Error while getting id from urn {}", e);
        }
        return null;
    }

    private SharePostRequestLinkedin linkedinPostDetails(String activityURN, String accessToken) throws Exception {

            SharePostRequestLinkedin shareResponse = linkedinService.fetchUGCPostByUrn(accessToken, activityURN);

            //part is commented to skip shopwing media info
           /* if(isValid(activityURN,"share")) {
                if (Objects.nonNull(shareResponse) && Objects.nonNull(shareResponse.getContent())) {
                    if (Objects.nonNull(shareResponse.getContent().getMedia())||Objects.nonNull(shareResponse.getContent().getMultiImage())) {
                        LinkedinMediaNotificationResposne data = linkedinService.getLinkedinShareMediaUrl(activityURN, accessToken);
                        if (Objects.nonNull(data) && Objects.nonNull(data.getContent())&& Objects.nonNull(data.getContent().getContentEntities())) {
                            if (Objects.nonNull(shareResponse.getContent().getMedia()) && isValid(shareResponse.getContent().getMedia().getId(), "video"))
                                shareResponse.setVideoUrl(Arrays.asList(data.getContent().getContentEntities().get(0).getEntityLocation()));
                            else
                                shareResponse.setImageUrl(data.getContent().getContentEntities().stream().map(LinkedinContentEntities::getEntityLocation).collect(Collectors.toList()));
                        }
                    }
                }
            }
            else if(isValid(activityURN,"ugcPost")) {
                if (Objects.nonNull(shareResponse) && Objects.nonNull(shareResponse.getContent())) {
                    if (Objects.nonNull(shareResponse.getContent().getMedia())) {
                        SharePostRequest data = linkedinService.getLinkedinUGCMediaUrl(activityURN, accessToken);

                        if (Objects.nonNull(data) && Objects.nonNull(data.getSpecificContent())
                                && Objects.nonNull(data.getSpecificContent().getShareContent())) {

                            List<Media> mediaList = data.getSpecificContent().getShareContent().getMedia();
                            List<String> videoUrls = new ArrayList<>();
                            for (Media media : mediaList) {
                                if (CollectionUtils.isNotEmpty(media.getMediaProjection().getElements())) {
                                    List<LinkedinUGCPostElement> elements = media.getMediaProjection().getElements();
                                    for (LinkedinUGCPostElement element : elements) {
                                        if (CollectionUtils.isNotEmpty(element.getIdentifiers()) && element.getIdentifiers().get(0).getMediaType().equals("video/mp4")) {
                                            videoUrls.add(element.getIdentifiers().get(0).getIdentifier());
                                            // contains multiple same extension urls, skipping after picking first one
                                            shareResponse.setVideoUrl(videoUrls);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }*/
            return shareResponse;

                }

    private boolean isValid(String owner,String object) {
        if (Objects.nonNull(owner) && owner.contains(object))
            return true;
        return false;
    }


}
