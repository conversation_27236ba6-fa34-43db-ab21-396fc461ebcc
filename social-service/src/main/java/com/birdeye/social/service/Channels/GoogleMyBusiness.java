package com.birdeye.social.service.Channels;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.ProcessingPost;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.LocationReportRequest;
import com.birdeye.social.insights.ES.ReportSortingCriteria;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.facebook.NewFbPostData;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.GMBReportInsightsResponse;
import com.birdeye.social.model.LocationPagePair;
import com.birdeye.social.model.PageInfoDto;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.service.GoogleSocialAccountService;
import com.birdeye.social.service.SocialPostOperationService.GMB.GMBPostOperationService;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialReportService.GMB.GmbInsights;
import com.birdeye.social.service.SocialReportService.GMB.GmbReportService;
import com.birdeye.social.service.SocialReportService.SocialInsights;
import com.birdeye.social.service.notification.NotificationAuditService;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service("GoogleMyBusiness")
public class GoogleMyBusiness implements SocialInsights, NotificationAuditService, PostOperation {

    @Autowired
    private GmbInsights gmbInsights;

    @Autowired
    private GmbReportService gmbReportService;

    @Autowired
    private GoogleSocialAccountService googleSocialAccountService;

    @Autowired
    private GMBPostOperationService postOperationService;
    @Autowired
    private BusinessGMBLocationRawRepository googleLocationRawRepository;

    @Autowired
    private BusinessGMBLocationRawRepository businessGMBLocationRawRepository;

    private static final Logger log = LoggerFactory.getLogger(GoogleMyBusiness.class);


    @Override
    public String channelName() {
        return SocialChannel.GMB.getName();
    }

    @Override
    public List<SocialNotificationAudit> putNotificationForChannel(Object notificationObject) {
        return googleSocialAccountService.auditNotifications(notificationObject);
    }

    @Override
    public Object getPageInsightsFromES(InsightsRequest insightsRequest) throws Exception {
        log.info("Request for template for page insights with enterpriseId: {}", insightsRequest.getEnterpriseId());
        if(Objects.isNull(insightsRequest)){
            return null;
        }
        SearchTemplate searchTemplate = SearchTemplate.searchTemplate(insightsRequest.getReportType());
        log.info("Template is {}",searchTemplate);
        if(Objects.nonNull(searchTemplate)) {
            return gmbInsights.getGmbInsightsForPage(insightsRequest);
        }
        return null;
    }

    @Override
    public Object getPageInsightsESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Object getPageInsightsFromESByLocation(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public void postPageInsightsToEs(PageInsights pageInsights) {
        if(Objects.isNull(pageInsights)){
            return;
        }
        gmbInsights.postGmbPageInsightToES(pageInsights);
    }

    @Override
    public void getPostInsights(List<BusinessPosts> businessPosts, Boolean isFreshRequest) {
        if(CollectionUtils.isEmpty(businessPosts)){
            return;
        }
        gmbInsights.getPostInsights(businessPosts.get(0),isFreshRequest);
    }

    @Override
    public void getPageInsightsFromSocialChannel(SocialScanEventDTO getPageInsights) {
        gmbReportService.getPageInsightsFromGMB(getPageInsights);
//        gmbInsights.getPageInsightsFromGmb(getPageInsights);
    }

    @Override
    public void postPostDataAndInsightsToEs(PostData postData) {
        gmbInsights.postGmbPostInsightsToEs(postData);
    }

    @Override
    public Object getPostDataAndInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return gmbInsights.getGmbInsightsForPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void updateToPostAndPageIndexEs(NewFbPostData newFbPostData) {

    }

    @Override
    public void updatePageInsightsDb(String pageId, Integer businessId, Long enterpriseId) {

    }

    @Override
    public void updatePageInsightsPostCount(PageInsights pageInsights) {

    }

    @Override
    public String getPageInsightIndexName() {
        return ElasticConstants.GMB_REPORT_PAGE_INSIGHTS.getName();
    }

    @Override
    public void startScanForPosts(String pageId) {

    }

	@Override
	public void getGMBPageAnalytics(String pageId, Integer businessId) throws Exception {
		gmbReportService.getGMBPageAnalytics(pageId,businessId);

	}

    @Override
    public void saveCDNPostToES(BusinessPosts businessPosts) {
        if(Objects.isNull(businessPosts)){
            return;
        }
        gmbInsights.saveCDNPostToES(businessPosts);
    }

    @Override
    public void getGMBKeywordAnalytics(Integer businessId) throws Exception {
        gmbReportService.getGMBKeywordAnalytics(businessId);
    }

    @Override
    public PerformanceSummaryResponse getPerformanceData(InsightsRequest insightsRequest) {
        return null;
    }

    @Override
    public boolean backfillProfilePagesToEs(PageInsightsRequest pageInsights) {
        return false;
    }
    public Object getPageReportESData(InsightsRequest insightsRequest) throws Exception {
        return null;
    }

    @Override
    public Map<String, Integer> getBusinessIdPageIdMapping(List<Integer> businessIds) {
        return null;
    }

    public PostData createPostData(BusinessPosts businessPosts) throws Exception {
        return gmbInsights.createPostData(businessPosts, new GMBReportInsightsResponse());
    }

    @Override
    public void backfillEngagementBreakdown(BackfillInsightReq backfillInsightReq) {

    }

    @Override
    public void backfillEngagementBreakdownPageWise(BackfillRequest backfillRequest) {

    }

    @Override
    public List<String> getPageIdsFromBusinessIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) {
        postOperationService.editPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        postOperationService.deletePost(publishedPost);
    }

    @Override
    public void processPendingPosts(ProcessingPost processingPost, PageInfoDto pageInfoDto, SocialPostPublishInfo publishInfo) {

    }

    @Override
    public Map<String, PageInfoDto> getPageInfoDto(List<String> pageIds) {
        return Collections.emptyMap();
    }

    @Override
    public Object getTopPostsInsightsFromES(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder, boolean excelDownload) {
        return gmbInsights.getGmbInsightsForTopPost(insightsRequest, startIndex, pageSize, sortParam, sortOrder, excelDownload);
    }

    @Override
    public void getBusinessIdPageIdPairFromBusinessId(List<Integer> businessIds,List<LocationPagePair> locationPagePairList) {
        List<BusinessGoogleMyBusinessLocation> gmbLocations = businessGMBLocationRawRepository.findByBusinessIdIn(businessIds);

        if (CollectionUtils.isNotEmpty(gmbLocations)) {
            gmbLocations.forEach(location ->
                    locationPagePairList.add(
                            LocationPagePair.builder()
                                    .pageId(location.getLocationId())
                                    .locationId(location.getBusinessId())
                                    .pageName(location.getLocationName())
                                    .profilePictureUrl(location.getPictureUrl())
                                    .pagePermission(Objects.equals(location.getCanPost(), 1))
                                    .build()
                    )
            );
        }
    }

    @Override
    public void getBusinessIdPageIdPairFromPageIds(List<String> pageIds, List<LocationPagePair> locationPagePairList) {
        List<BusinessGoogleMyBusinessLocation> gmbLocations = businessGMBLocationRawRepository.findAllByLocationIdIn(pageIds);

        if (CollectionUtils.isNotEmpty(gmbLocations)) {
            gmbLocations.forEach(location ->
                    locationPagePairList.add(
                        LocationPagePair.builder()
                                .pageId(location.getLocationId())
                                .locationId(location.getBusinessId())
                                .pageName(location.getLocationName())
                                .profilePictureUrl(location.getPictureUrl())
                                .pagePermission(Objects.equals(location.getCanPost(), 1))
                                .build()
                    )
            );
        }
    }

    @Override
    public List<String> getPageIds(List<Integer> businessIds) {
        return Collections.emptyList();
    }

    @Override
    public LeadershipByPostsDataPoints getPostLeadershipReport(LocationReportRequest insightsRequest, ReportSortingCriteria postSortingCriteria, SortOrder order, Integer startIndex, Integer pageSize) {
        return null;
    }

    @Override
    public List<String> getAllPageIds(int page, int size, Long enterpriseId) {
        return Objects.nonNull(enterpriseId)? googleLocationRawRepository.findAllLocationIdsByEnterpriseId(enterpriseId):
                googleLocationRawRepository.findPageIdsWithPagination(new PageRequest(page,size));
    }
}
