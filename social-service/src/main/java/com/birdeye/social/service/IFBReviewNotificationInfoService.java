package com.birdeye.social.service;

import com.birdeye.social.entities.FBReviewNotificationInfo;
import com.birdeye.social.model.FacebookEventRequest;

import java.util.List;

public interface IFBReviewNotificationInfoService {

    void saveNotificationRequest(List<Integer> businessFacebookPageNews,
                                 FacebookEventRequest facebookEventRequest,
                                 String verb,
                                 String pageId,
                                 String status,
                                 String comment,
                                 String fbReviewId,
                                 String fbReviewerId);

    FBReviewNotificationInfo findFirstByFacebookReviewIdAndNotificationTypeAndStatus(String facebookReviewId,
                                                                                     String notificationType,
                                                                                     String status);
}
