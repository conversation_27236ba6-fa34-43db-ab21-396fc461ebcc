package com.birdeye.social.service.SocialPostOperationService.LinkedIn;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.service.SocialPostLinkedinService;
import com.birdeye.social.service.SocialPostOperationService.PostOperationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class LinkedInPostOperationServiceImpl implements LinkedInPostOperationService{

    @Autowired
    private BusinessLinkedinPageRepository linkedinPageRepository;

    @Autowired
    private SocialPostLinkedinService socialPostLinkedinService;

    @Autowired
    SocialPostInfoRepository publishInfoRepository;

    @Autowired
    private PostOperationUtil postOperationUtil;
    private static final Logger LOGGER = LoggerFactory.getLogger(LinkedInPostOperationServiceImpl.class);

    @Override
    public void editPost(SocialPostPublishInfo publishInfo) {
        socialPostLinkedinService.updatePostTextById(publishInfo);
        postOperationUtil.markOriginalPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        BusinessLinkedinPage businessLinkedinPage = linkedinPageRepository.findByProfileIdAndIsValid(publishedPost.getExternalPageId(), 1);
        if(Objects.isNull(businessLinkedinPage)) {
            LOGGER.info("Page not found for page id: {}", publishedPost.getExternalPageId());
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, "Page not found");
        }
        try {
            boolean success = socialPostLinkedinService.deletePost(businessLinkedinPage, publishedPost.getPostId());
            if(!success) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_DELETE_LINKEDIN_OBJECT, "post delete failed on linkedin");
            }
            LOGGER.info("Post with publish info id: {} is deleted successfully from LinkedIn", publishedPost.getId());
        } catch (BirdeyeSocialException e) {
            if(e.getCode() == ErrorCodes.CLIENT_ERROR_404.value()) {
                LOGGER.info("Seems like {} post with publish info id: {} is already deleted, marking it as deleted in the system", SocialChannel.LINKEDIN.getName(), publishedPost.getId());
            } else {
                throw e;
            }
        }
    }
}
