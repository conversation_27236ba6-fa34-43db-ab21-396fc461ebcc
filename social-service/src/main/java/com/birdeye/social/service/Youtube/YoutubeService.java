package com.birdeye.social.service.Youtube;

import com.birdeye.social.dto.TokensAndUrlAuthData;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategoryItems;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylistItems;
import com.birdeye.social.youtube.YoutubeActivity;
import com.birdeye.social.youtube.YoutubeChannelStatisticsResponse;
import com.birdeye.social.youtube.YoutubeCommentReply;
import com.birdeye.social.youtube.YoutubeCommentThreadResponse;
import com.google.api.services.youtube.YouTube;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public interface YoutubeService {
	
	public List<YoutubeActivity> getActivities(String keyword);
	
	public Map<?,?> getChannelInformation(String channelId);

	YouTube getService(TokensAndUrlAuthData tokensAndUrlAuthData);

	YoutubeCategoryItems getCategoriesByRegion(String accessToken, String regionCode) throws Exception;

    YoutubePlaylistItems getPlaylistForChannelId(String channelId, String accessToken) throws Exception;

	YoutubeCommentThreadResponse getYTComments(String accessToken, String channelId, String nextToken) throws Exception;

	YoutubeCommentThreadResponse getVideoInfo(String accessToken, List<String> videoIds) throws Exception;

	void hideUnhideComment(BusinessYoutubeChannel youtubeChannel, String commentId, Boolean hide);

	YoutubeCommentReply.Comment replyToComment(BusinessYoutubeChannel youtubeChannel, String commentId, String replyMessage);

	YoutubeCommentReply.Comment commentOnVideo(BusinessYoutubeChannel youtubeChannel, String videoId, String message);

	void banAuthor(BusinessYoutubeChannel youtubeChannel, String commentId);

	YoutubeChannelStatisticsResponse getChannelInformation(String channelId, String accessToken);
}
