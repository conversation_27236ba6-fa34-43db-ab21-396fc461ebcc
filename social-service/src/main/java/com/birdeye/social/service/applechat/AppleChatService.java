package com.birdeye.social.service.applechat;

import java.io.IOException;
import java.util.List;

import com.birdeye.social.apple.AppleSendRequest;
import com.birdeye.social.apple.AttachmentPreDownloadRequest;
import com.birdeye.social.apple.AttachmentPreUploadRequest;
import com.birdeye.social.apple.AttachmentPreUploadResponse;
import com.birdeye.social.model.AutoSuggesterPagesResponse;
import com.birdeye.social.model.ChannelSetupStatus;
import com.birdeye.social.model.ChannelSetupStatus.PageSetupStatus;
import com.birdeye.social.model.SocialPageListInfo;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.sro.AppleAddLocationMappingRequest;
import com.birdeye.social.sro.AppleDisconnectRequest;
import com.birdeye.social.sro.AppleLocationPageMappingRequest;
import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.LocationPageMapping;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.applechat.*;
import com.birdeye.social.sro.applechat.AppleConnectPageRequest;
import com.birdeye.social.sro.applechat.ConnectPageResponse;

public interface AppleChatService {
    ChannelPageInfo connectPage(AppleConnectPageRequest request);

    ConnectPageResponse getPages(Long enterpriseId,String type);

    void removePage(List<LocationPageMappingRequest> businessId, boolean isSMB, long enterpriseId);

	void removeAccount(AppleDisconnectRequest appleDisconnectRequest);

	void removePageMappings(AppleLocationPageMappingRequest input);

	AutoSuggesterPagesResponse findPagesWithMappingStatus(Long businessId);

	LocationPageMapping getLocationMappingPages(LocationMappingRequest request);

	List<SocialPageListInfo> findPagesSocialList(Long enterpriseId);
	
	void saveLocationAndMapping(AppleAddLocationMappingRequest appleAddLocationMappingRequest,long enterpriseId);

	PageSetupStatus getPageSetupStatus(Long enterpriseId);

	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId);

	SocialLocationResponse getAllMappedAndUnMappedLocations(Long enterpriseId) throws Exception;

	AppleResponseStatusDTO addMappingForEnterprise(AppleBulkImport request);

    AppleChatAccountStatus getAccountStatus(Long enterpriseId);

	Integer getBusinessIdForPageId(String destinationId, String intent, boolean isSMB);

	String getAppleIntegrationStatus(Integer businessId);

	String getApplePageIdByBusinessId(Integer businessId, Long enterpriseNumber, boolean isSMB);

	void sendMessage(AppleSendRequest request) throws IOException, Exception;

	String attachmentPreDownload(AttachmentPreDownloadRequest request) throws Exception;

	AttachmentPreUploadResponse attachmentPreUpload(AttachmentPreUploadRequest request) throws Exception;

	List<SocialNotificationAudit> auditNotification(Object notificationObject);
}
