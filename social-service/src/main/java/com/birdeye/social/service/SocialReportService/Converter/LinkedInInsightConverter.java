package com.birdeye.social.service.SocialReportService.Converter;

import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.SearchTemplate;
import com.birdeye.social.insights.InsightsRequest;
import com.birdeye.social.insights.LinkedIn.LinkedInPageInsightsResponse;
import com.birdeye.social.insights.PageInsights;

import java.util.List;

public interface LinkedInInsightConverter {

    InsightsESRequest createESRequestForPage(InsightsRequest insightsRequest);

    InsightsESRequest createESRequestForPost(InsightsRequest insightsRequest, int startIndex, int pageSize, String sortParam, String sortOrder);

    List<ESPageRequest> createPageInsightsObject(PageInsights pageInsights);
}
