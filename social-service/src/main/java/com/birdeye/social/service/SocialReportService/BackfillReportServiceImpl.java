package com.birdeye.social.service.SocialReportService;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.insights.PageInsightsRequest;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.service.IRedisExternalService;
import com.birdeye.social.service.SocialReportService.Facebook.FacebookReportService;
import com.birdeye.social.service.SocialReportService.Instagram.InstagramReportService;
import com.birdeye.social.service.SocialReportService.LinkedIn.LinkedInInsights;
import com.birdeye.social.service.SocialReportService.Twitter.TwitterReportService;
import com.birdeye.social.service.Youtube.YoutubeReportService;
import com.birdeye.social.twitter.HistoricalTweetInsight;
import com.birdeye.social.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BackfillReportServiceImpl implements BackfillReportService{
    @Autowired
    private InsightsFactory socialInsights;
    @Autowired
    private InstagramReportService instagramReportService;
    @Autowired
    private FacebookReportService facebookReportService;
    @Autowired
    private TwitterReportService xReportService;
    @Autowired
    private YoutubeReportService youtubeReportService;
    @Autowired
    private LinkedInInsights linkedInInsights;
    @Autowired
    private IRedisExternalService redisExternalService;
    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepo;
    @Autowired
    PicturesqueGen picturesqueGen;

    @Override
    public boolean backfillProfilePages(PageInsightsRequest pageInsights, String channel) {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));

        return execute.backfillProfilePagesToEs(pageInsights);
    }
    @Override
    public  void backfillIgInsight(BackfillInsightReq igBackFillInsightReq) {
        instagramReportService.backfillIgInsight(igBackFillInsightReq);
    }

    @Override
    public void backfillXInsight(BackfillInsightReq xBackfillInsightReq) {
        xReportService.backfillXInsight(xBackfillInsightReq);
    }

    @Override
    public void backfillFbInsight(BackfillInsightReq fbBackfillInsightReq) {
        facebookReportService.backfillFbInsight(fbBackfillInsightReq);
    }

    @Override
    public void backfillYtInsight(BackfillInsightReq ytBackfillInsightReq) {
        youtubeReportService.backfillYtInsight(ytBackfillInsightReq);
    }

    @Override
    public void backfillLnInsight(BackfillInsightReq lnBackfillInsightReq) {
        linkedInInsights.backfillLnInsight(lnBackfillInsightReq);
    }

    @Override
    public void backFillXHistorical(HistoricalTweetInsight historicalTweetInsight) {
        xReportService.backFillXHistorical(historicalTweetInsight);
    }

    @Override
    public void engagementBreakdownBackfill(String channel , BackfillInsightReq backfillInsightReq) {
        String ch = SocialChannel.getSocialChannelByName(channel).getName();
        SocialInsights execute = socialInsights
                .getSocialInsightsChannel(ch)
                .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
        execute.backfillEngagementBreakdown(backfillInsightReq);
    }

    @Override
    public void engagementBreakdown(String channel, BackfillRequest backfillRequest) {
        try {
            log.info("Request received with data :{}",backfillRequest);
            String ch = SocialChannel.getSocialChannelByName(channel).getName();
            SocialInsights execute = socialInsights
                    .getSocialInsightsChannel(ch)
                    .orElseThrow(() -> new IllegalArgumentException("Invalid Request"));
            execute.backfillEngagementBreakdownPageWise(backfillRequest);
        }catch (Exception e){
            log.info("Exception handler and add page to redis",e);
            String pageId = getString(backfillRequest);
            Optional<Object> object = redisExternalService.get(Constants.BACKFILL_REDIS_KEY);
            List<String> stringList = new ArrayList<>();
            if(object.isPresent()){
                stringList = Arrays.stream(String.valueOf(object.get()).split(",")).collect(Collectors.toList());
            }
            stringList.add(pageId);
            String commaSeparatedString = String.join(",", stringList);
            redisExternalService.setKeyAndValue(Constants.BACKFILL_REDIS_KEY, commaSeparatedString);
            log.info("Get key FB_FAILURE_REDIS_KEY: {}", redisExternalService.get(Constants.BACKFILL_REDIS_KEY));
        }

    }

    @Override
    public void backfillSocialPostsAssetsThumbnails(Integer count, String businessNumber) {
        PageRequest pageRequest = new PageRequest(0, count);
        List<SocialPostsAssets> socialPostsAssetsList;
        if(Objects.nonNull(businessNumber)){
            socialPostsAssetsList = socialPostsAssetsRepo.findByVideoUrlNotNullAndVideoThumbnailIsNullOrVideoThumbnailEqualsAndBucketIdOrderByIdDesc(businessNumber, pageRequest);
        }else{
            socialPostsAssetsList = socialPostsAssetsRepo.findByVideoUrlNotNullAndVideoThumbnailIsNullOrVideoThumbnailEqualsOrderByIdDesc(pageRequest);
        }
        if (socialPostsAssetsList.isEmpty()) {
            log.info("No social posts assets found to backFill thumbnails.");
            return;
        }
        String cdnUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCdnImageBaseUrl();
        socialPostsAssetsList.parallelStream().forEach(videoAsset -> {
            try {
                String videoThumbnail = videoAsset.getVideoThumbnail();
                if (StringUtils.isEmpty(videoThumbnail)) {
                    String videoUrl = cdnUrl + "/" + videoAsset.getBucketId() + "/" + videoAsset.getVideoUrl();
                    String thumbnailUrl = picturesqueGen.getVideoThumbnailUrl(videoUrl, videoAsset.getBucketId());
                    videoAsset.setDefaultVideoThumbnail(thumbnailUrl);
                } else {
                    videoAsset.setDefaultVideoThumbnail(videoThumbnail);
                }
                log.info("BackFilled thumbnail for video ID: {}", videoAsset.getId());
            } catch (Exception e) {
                log.error("Error backFilling thumbnail for post ID: {}", videoAsset.getId(), e);
            }
        });
        socialPostsAssetsRepo.save(socialPostsAssetsList);
    }

    private static String getString(BackfillRequest backfillRequest) {
        String pageId = "";
        if(Objects.nonNull(backfillRequest.getBusinessFBPage())){
            pageId = backfillRequest.getBusinessFBPage().getFacebookPageId();
        }
        if(Objects.nonNull(backfillRequest.getBusinessInstagramAccount())){
            pageId = backfillRequest.getBusinessInstagramAccount().getInstagramAccountId();
        }
        if(Objects.nonNull(backfillRequest.getBusinessLinkedinPage())){
            pageId = backfillRequest.getBusinessLinkedinPage().getProfileId();
        }
        return pageId;
    }
}
