package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.BusinessTypeEnum;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.sro.GMBConnectionStatusResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("gmbSetupService")
public class GMBSetupServiceImpl implements GMBSetupService{
    @Autowired
    private BusinessGMBLocationRawRepository socialGMBRepo;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IBusinessCoreService businessCoreService;

    private static final Logger LOG = LoggerFactory.getLogger(GMBSetupServiceImpl.class);
    @Override
    public GMBConnectionStatusResponse getGMBConnectionStatus(Integer businessId) {
        LOG.info("businessId received in header: {}",businessId);
        BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId,false);
        //commonService.checkBusinessValidation(businessLiteDTO);
        GMBConnectionStatusResponse gmbConnectionStatusResponse=new GMBConnectionStatusResponse();
        List<BusinessGoogleMyBusinessLocation> gmbLocations = socialGMBRepo.findAllByBusinessId(businessId);
        if(CollectionUtils.isEmpty(gmbLocations)) {
            gmbConnectionStatusResponse.setStatus("Not Connected");
            gmbConnectionStatusResponse.setSubStatus("GMB_PAGE_NOT_FOUND");
        }
        else if(gmbLocations.size()>1){
            throw new BirdeyeSocialException(ErrorCodes.GMB_MULTIPLE_MAPPING_FOUND, ("Multiple mappings found for businessId :" + businessId));
        }
        else if(gmbLocations.get(0).getIsValid()!=1){
            gmbConnectionStatusResponse.setStatus("Not Connected");
            gmbConnectionStatusResponse.setSubStatus("GMB_PAGE_INVALID");
        }
        else{
            gmbConnectionStatusResponse.setStatus("Connected");
            gmbConnectionStatusResponse.setSubStatus(null);
        }
        return gmbConnectionStatusResponse;
    }
}
