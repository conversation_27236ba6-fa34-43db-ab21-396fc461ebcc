package com.birdeye.social.service.SocialPostOperationService.Apple;

import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.service.applePost.SocialPostAppleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApplePostOperationServiceImpl implements ApplePostOperationService{

    @Autowired
    private SocialPostAppleService socialPostAppleService;
    @Override
    public void editPost() {

    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception {
        socialPostAppleService.deletePostDataFromApple(publishedPost);
    }
}
