package com.birdeye.social.service.drafts;

import com.birdeye.social.dao.SocialPostDraftInfoRepository;
import com.birdeye.social.entities.SocialPostDraftInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SocialPostDraftInfoServeImpl implements SocialPostDraftInfoService{

    @Autowired
    private SocialPostDraftInfoRepository socialPostDraftInfoRepository;

    @Override
    public List<SocialPostDraftInfo> findByMasterPostIdIn(List<Integer> socialPostIds) {
        return socialPostDraftInfoRepository.findByMasterPostIdIn(socialPostIds);
    }
    @Override
    public List<SocialPostDraftInfo> findOneByMasterPostIdIn(List<Integer> socialPostIds) {
        return socialPostDraftInfoRepository.findOneByMasterPostIdIn(socialPostIds);
    }

    @Override
    public List<SocialPostDraftInfo> findBySocialPostIdIn(List<Integer> socialPostIds) {
        return socialPostDraftInfoRepository.findBySocialPostIdIn(socialPostIds);
    }

    @Override
    public void save(SocialPostDraftInfo draft) {
        socialPostDraftInfoRepository.save(draft);
    }

    @Override
    public boolean existsByMasterPostIdAndSourceId(Integer masterPostId, Integer sourceId) {
        return socialPostDraftInfoRepository.existsByMasterPostIdAndSourceId(masterPostId,sourceId);
    }

    @Override
    public List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdIn(List<Integer> masterPostIds, List<Integer> sourceIds) {
        return socialPostDraftInfoRepository.findByMasterPostIdInAndSourceIdIn(masterPostIds,sourceIds);
    }

    @Override
    public List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdInAndEnterpriseId(List<Integer> masterPostIds, List<Integer> sourceIds, Integer enterpriseId) {
        return socialPostDraftInfoRepository.findByMasterPostIdInAndSourceIdInAndEnterpriseId(masterPostIds,sourceIds, enterpriseId);
    }

    @Override
    public void deleteById(Integer id) {
        socialPostDraftInfoRepository.deleteById(id);
    }

}
