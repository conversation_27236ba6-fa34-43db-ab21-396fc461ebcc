package com.birdeye.social.service;

import com.birdeye.social.dao.DisconnectedMailAuditRepo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.DisconnectedMailAudit;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
@Service
public class DisconnectedMailAuditServiceImpl implements DisconnectedMailAuditService {
    private static final Logger LOG = LoggerFactory.getLogger(DisconnectedMailAuditServiceImpl.class);

    @Autowired
    private DisconnectedMailAuditRepo disconnectedMailAuditRepo;

    @Override
    public void saveAudit(BusinessLiteDTO business, String channel, List<String> users, String status, String comment) {
        DisconnectedMailAudit audit = new DisconnectedMailAudit();
        audit.setBusinessId(business.getBusinessId());
        audit.setBusinessName(business.getBusinessName());
        audit.setChannel(channel);
        String userSub = null;
        if (!CollectionUtils.isEmpty(users)) {
        	List<String>  subList = users.subList(0, users.size() > 100 ? 100 : users.size());
        	userSub = subList.toString();
        }
        audit.setUsers(userSub);
        audit.setUserCount(users!=null?users.size():0);
        audit.setComment(comment);
        audit.setStatus(status);
        disconnectedMailAuditRepo.saveAndFlush(audit);
    }
}
