package com.birdeye.social.service;

import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.model.ChannelAuthOpenUrlRequest;
import com.birdeye.social.model.ChannelAuthRequest;
import com.birdeye.social.model.EnterpriseDetailsResponse;

/**
 *
 * <AUTHOR>
 *
 */

public interface GenerateExternalUrlService {

    public String generateUniqueUrl(String channel, Long enterpriseId);

    public ChannelPageInfo getPagesFromExternalUrl(String channel, Long enterpriseId) throws Exception;


    public EnterpriseDetailsResponse getBusinessDetailsUsingToken(String channel, String token);

	public void submitFetchPageRequestForOpenURL(String channel, ChannelAuthOpenUrlRequest authRequest, String sessionToken) throws Exception;

	void updateFirebaseForOpenUrl();

}
