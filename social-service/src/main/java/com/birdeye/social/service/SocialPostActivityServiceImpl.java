package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.PostActivityType;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.ActivityMessageRepo;
import com.birdeye.social.dao.PostActivityRepo;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.entities.ActivityMessage;
import com.birdeye.social.entities.PostActivity;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.birdeye.social.utils.TimeZoneUtil;
import com.google.api.client.json.Json;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.text.StringSubstitutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SocialPostActivityServiceImpl implements SocialPostActivityService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostActivityServiceImpl.class);

    @Autowired
    private PostActivityRepo activityRepo;

    @Autowired
    private ActivityMessageRepo activityMessageRepo;

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final String SAVE_POST_ACTIVITY_EVENT = "save-post-activity";

    private static final String ACTIVITY_DATE_FORMAT = "MMM dd, hh:mm a";


    @Override
    public PostActivityResponse getPostActivityData(Integer postId, String timezone, Integer pageNo, Integer size, Boolean applyPagination) throws Exception {
        List<PostActivity> postActivities = new ArrayList<>();
        if(Objects.equals(applyPagination, Boolean.TRUE)) {
            Page<PostActivity> postActivityPage = activityRepo.findByPostIdOrderByIdDesc(postId, new PageRequest(pageNo, size));

            if(Objects.nonNull(postActivityPage)) {
                postActivities = postActivityPage.getContent();
            }
        } else {
            postActivities = activityRepo.findByPostIdOrderByIdDesc(postId);
        }

        List<PostActivity> postActivityList = postActivities;
        CompletableFuture<Map<Integer, BusinessCoreUser>> getUserFuture = CompletableFuture.supplyAsync(()-> CollectionUtils.isEmpty(postActivityList)?new HashMap<>():
                businessCoreService.getBusinessUserForUserId(postActivityList.stream().map(s->s.getUserId()).collect(Collectors.toList())));

        CompletableFuture<Map<String, ActivityMessage>> getActivityMessageFuture = CompletableFuture.supplyAsync(()-> getActivityMessageMap());

        CompletableFuture<Void> integrationCompletionFuture = CompletableFuture.allOf(getUserFuture, getActivityMessageFuture);
        integrationCompletionFuture.get(100, TimeUnit.SECONDS);
        Map<Integer, BusinessCoreUser> userIdVsUser = getUserFuture.get();
        Map<String, ActivityMessage> activityMessageMap = getActivityMessageFuture.get();
        List<PostActivityData> postActivityDataList = new ArrayList<>();
        for(PostActivity postActivity: postActivityList) {
            PostActivityType type = PostActivityType.getPostActivityTypeByName(postActivity.getActivity());
            if(Objects.isNull(type)) {
                LOGGER.info("Activity: {} is not present in the system", postActivity.getActivity());
                continue;
            }
            String userName = null;
            if(Objects.isNull(postActivity.getUserId())) {
                userName = postActivity.getGuestUserEmailId();
            } else {
                userName = businessCoreService.getFullUsername(postActivity.getUserId());
            }
            ActivityMessage activityMessage= activityMessageMap.get(type.getName());
            String message = activityMessage.getMessage();
            PostActivityRescheduled postActivityRescheduled;
            ApprovalActivityData approvalActivityData;
            String activityTime = postActivity.getCreatedAt().toInstant().toString();
            String scheduledTime;
            String rescheduledTime;
            Integer childPostId = postActivity.getChildPostId();
            timezone = StringUtils.isEmpty(timezone)?"UTC":timezone;
            Map<String, String> paramsMap = new HashMap<>();
            switch (type){
                case DELETE:
                    PostActivityDelete postActivityDelete = JSONUtils.fromJSON(postActivity.getMetadata(), PostActivityDelete.class);
                    if(Objects.isNull(postActivityDelete) || MapUtils.isEmpty(postActivityDelete.getDeletedPageChannelWiseDetails())) {
                        message = "deleted the post";
                        break;
                    }
                    Map<String, List<String>> deleteMap = postActivityDelete.getDeletedPageChannelWiseDetails();
                    String channelName ="";
                    Integer noOfPages = 0;
                    Integer noOfChannels = 0;
                    for(Map.Entry<String, List<String>> entry: deleteMap.entrySet()) {
                        channelName = SocialChannel.getSocialChannelByName(entry.getKey()).getLabel();
                        noOfChannels+=1;
                        if(CollectionUtils.isNotEmpty(entry.getValue())) {
                            noOfPages+=entry.getValue().size();
                        }
                    }
                    if(noOfChannels > 1) {
                        channelName = noOfChannels+" channels";
                    }
                    message = "deleted post from ${noOfPages} ${pageString}";
                    String pageString = noOfPages==1?"page":"pages";
                    paramsMap.put("channelName", channelName);
                    paramsMap.put("noOfPages", Integer.toString(noOfPages));
                    paramsMap.put("pageString", pageString);
                    message = StringSubstitutor.replace(message, paramsMap, "${", "}");
                    break;
                case SEND_NOW:
                    try {
                        postActivityRescheduled = JSONUtils.fromJSON(postActivity.getMetadata(), PostActivityRescheduled.class);
                        scheduledTime = new SimpleDateFormat(ACTIVITY_DATE_FORMAT).format(postActivityRescheduled.getScheduledTimeStamp());
                    } catch (Exception e) {
                        scheduledTime = null;
                    }
                    paramsMap.put("scheduledTime", scheduledTime);
                    message = StringSubstitutor.replace(message, paramsMap, "${", "}");
                    break;
                case RESCHEDULED:
                    try {
                        postActivityRescheduled = JSONUtils.fromJSON(postActivity.getMetadata(), PostActivityRescheduled.class);
                        //scheduledTime = new SimpleDateFormat(ACTIVITY_DATE_FORMAT).format(TimeZoneUtil.convertToSpecificTimeZone(postActivityRescheduled.getScheduledTimeStamp(),"Asia/Kolkata").getTime());
                        rescheduledTime = new SimpleDateFormat(ACTIVITY_DATE_FORMAT).format(TimeZoneUtil.convertToSpecificTimeZone(postActivityRescheduled.getRescheduledTimeStamp(),timezone).getTime());
                    } catch (Exception e) {
                        //scheduledTime=null;
                        rescheduledTime="";
                    }
                    paramsMap.put("scheduledTime", "");
                    paramsMap.put("rescheduledTime", rescheduledTime);
                    message = StringSubstitutor.replace(message, paramsMap, "${", "}");
                    break;
                case EDIT_RESCHEDULED:
                    try {
                        postActivityRescheduled = JSONUtils.fromJSON(postActivity.getMetadata(), PostActivityRescheduled.class);
                        //scheduledTime = new SimpleDateFormat(ACTIVITY_DATE_FORMAT).format(TimeZoneUtil.convertToSpecificTimeZone(postActivityRescheduled.getScheduledTimeStamp(),"Asia/Kolkata").getTime());
                        rescheduledTime = new SimpleDateFormat(ACTIVITY_DATE_FORMAT).format(TimeZoneUtil.convertToSpecificTimeZone(postActivityRescheduled.getRescheduledTimeStamp(),timezone).getTime());
                    } catch (Exception e) {
                        scheduledTime=null;
                        rescheduledTime="";
                    }

                    paramsMap.put("scheduledTime", "");
                    paramsMap.put("rescheduledTime", rescheduledTime);
                    message = StringSubstitutor.replace(message, paramsMap, "${", "}");
                    break;
                case REJECTED:
                    String rejectedReason;
                    try {
                        approvalActivityData = JSONUtils.fromJSON(postActivity.getMetadata(), ApprovalActivityData.class);
                        rejectedReason = approvalActivityData.getReason();
                    } catch (Exception e) {
                        rejectedReason=null;
                    }

                    paramsMap.put("reason", "\""+rejectedReason+"\"");
                    message = StringSubstitutor.replace(message, paramsMap, "${", "}");
                    break;
                default:
                    break;
            }

            postActivityDataList.add(new PostActivityData(activityTime, message, userName, childPostId));
        }
        return new PostActivityResponse(postActivityDataList);
    }

    Map<String, ActivityMessage> getActivityMessageMap() {
        List<ActivityMessage> activityMessages= activityMessageRepo.findAll();
        if(CollectionUtils.isNotEmpty(activityMessages)) {
            return activityMessages.stream().collect(Collectors.toMap(s->s.getActivity(), s->s));
        }
        return new HashMap<>();
    }

    @Override
    public void saveCreateActivity(Integer postId, Integer userId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.CREATE.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveDuplicateActivity(Integer postId, Integer userId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.DUPLICATE.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveDeleteActivity(Integer postId, Integer userId, PostActivityDelete postActivityDelete) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.DELETE.getName())
                .userId(userId)
                .metadata(JSONUtils.toJSON(postActivityDelete))
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        // TODO add delete metadata
        activityRepo.save(postActivity);
    }

    @Override
    public void saveDeleteFailedActivity(Integer postId, Integer userId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.DELETE_FAILED.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        // TODO add delete metadata
        activityRepo.save(postActivity);
    }

    @Override
    public void saveQuoteTweetActivity(Integer postId, Integer userId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.QUOTE_TWEET.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveSendNowActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled) {
        String metadata = JSONUtils.toJSON(postActivityRescheduled);
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.SEND_NOW.getName())
                .userId(userId)
                .metadata(metadata)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveRescheduledActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled) {
        String metadata = JSONUtils.toJSON(postActivityRescheduled);
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.RESCHEDULED.getName())
                .userId(userId)
                .metadata(metadata)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveEditActivity(Integer postId, Integer userId, Integer childPostId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.EDIT.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .childPostId(childPostId)
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void savePublishedActivity(Integer postId, Integer userId) {
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.PUBLISHED.getName())
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveEditRescheduledActivity(Integer postId, Integer userId, PostActivityRescheduled postActivityRescheduled) {
        String metadata = JSONUtils.toJSON(postActivityRescheduled);
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(PostActivityType.EDIT_RESCHEDULED.getName())
                .metadata(metadata)
                .userId(userId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveApprovalActivity(Integer postId, Integer userId, String guestUserEmailId,ApprovalActivityData approvalActivityData, PostActivityType postActivityType) {
        String metadata = JSONUtils.toJSON(approvalActivityData);
        PostActivity postActivity = PostActivity.builder()
                .postId(postId)
                .activity(postActivityType.getName())
                .metadata(metadata)
                .userId(userId)
                .guestUserEmailId(guestUserEmailId)
                .createdAt(new Date(approvalActivityData.getCreatedDate()))
                .updatedAt(new Date(approvalActivityData.getCreatedDate()))
                .build();
        activityRepo.save(postActivity);
    }

    @Override
    public void saveActivity(SavePostActivityRequest request) {
        Integer postId = request.getPostId();
        Integer userId = request.getUserId();
        String activity = request.getActivity();
        String metaData = request.getMetadata();
        String guestUserEmailId = request.getGuestUserEmailId();
        PostActivityType type = PostActivityType.getPostActivityTypeByName(activity);
        Integer childPostId = request.getChildPostId();
        PostActivityRescheduled postActivityRescheduled;
        ApprovalActivityData activityData;
        if(Objects.isNull(type)) {
            LOGGER.info("Activity: {} is not present in the system", activity);
            return;
        }
        switch (type){
            case CREATE:
                saveCreateActivity(postId, userId);
                break;
            case DUPLICATE:
                saveDuplicateActivity(postId, userId);
                break;
            case DELETE:
                PostActivityDelete postActivityDelete = JSONUtils.fromJSON(metaData, PostActivityDelete.class);
                saveDeleteActivity(postId, userId, postActivityDelete);
                break;
            case DELETE_FAILED:
                saveDeleteFailedActivity(postId, userId);
                break;
            case QUOTE_TWEET:
                saveQuoteTweetActivity(postId, userId);
                break;
            case SEND_NOW:
                postActivityRescheduled = JSONUtils.fromJSON(metaData, PostActivityRescheduled.class);
                saveSendNowActivity(postId, userId, postActivityRescheduled);
                break;
            case RESCHEDULED:
                postActivityRescheduled = JSONUtils.fromJSON(metaData, PostActivityRescheduled.class);
                saveRescheduledActivity(postId, userId, postActivityRescheduled);
                break;
            case EDIT:
                saveEditActivity(postId, userId, childPostId);
                break;
            case PUBLISHED:
                savePublishedActivity(postId, userId);
                break;
            case EDIT_RESCHEDULED:
                postActivityRescheduled = JSONUtils.fromJSON(metaData, PostActivityRescheduled.class);
                saveEditRescheduledActivity(postId, userId, postActivityRescheduled);
                break;
            case REJECTED:
                activityData = JSONUtils.fromJSON(metaData, ApprovalActivityData.class);
                saveApprovalActivity(postId, userId, guestUserEmailId,activityData, PostActivityType.REJECTED);
                break;
            case APPROVED:
                activityData = JSONUtils.fromJSON(metaData, ApprovalActivityData.class);
                saveApprovalActivity(postId, userId, guestUserEmailId, activityData, PostActivityType.APPROVED);
                break;
            case TERMINATED:
                activityData = JSONUtils.fromJSON(metaData, ApprovalActivityData.class);
                saveApprovalActivity(postId, userId, guestUserEmailId, activityData, PostActivityType.TERMINATED);
                break;
            default:
                break;
        }
    }

    @Override
    public void sendEventToSaveActivity(Integer postId, Integer userId, String activity, Object metadata, Integer enterpriseId, Integer... childPostId) {
        String metadataJson = JSONUtils.toJSON(metadata);
        SavePostActivityRequest savePostActivityRequest = SavePostActivityRequest.builder()
                .postId(postId)
                .userId(userId)
                .activity(activity)
                .metadata(metadataJson)
                .enterpriseId(enterpriseId)
                .childPostId(Objects.nonNull(childPostId)&& childPostId.length>0?childPostId[0]:null)
                .build();
        kafkaProducerService.sendObjectV1(SAVE_POST_ACTIVITY_EVENT, savePostActivityRequest);
    }

    @Override
    public void sendEventToSaveApprovalActivity(Integer postId, String userId, String activity, Object metadata) {
        String metadataJson = JSONUtils.toJSON(metadata);
        Integer userIdNumber = null;
        String guestUserEmailId = null;
        if(StringUtils.isNotEmpty(userId)) {
            try {
                userIdNumber = Integer.parseInt(userId);
            }catch (Exception e) {
                guestUserEmailId = userId;
            }
        }
        SavePostActivityRequest savePostActivityRequest = SavePostActivityRequest.builder()
                .postId(postId)
                .userId(userIdNumber)
                .guestUserEmailId(guestUserEmailId)
                .activity(activity)
                .metadata(metadataJson)
                .build();
        kafkaProducerService.sendObjectV1(SAVE_POST_ACTIVITY_EVENT, savePostActivityRequest);
    }
}
