package com.birdeye.social.service.SocialReportService.ExternalService;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.IgData;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramAccountDataResponse;
import com.birdeye.social.insights.Instagram.ExternalApiResponse.InstagramResponse;
import com.birdeye.social.insights.Instagram.InstagramInsightRequest;
import com.birdeye.social.insights.Instagram.InstagramPostInsightRequest;
import com.birdeye.social.instagram.response.InstagramBaseResponse;
import com.birdeye.social.instagram.response.InstagramErrorResponse;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import static com.birdeye.social.facebook.FacebookApis.GET_PAGE_INSIGHTS;
import static com.birdeye.social.facebook.FacebookApis.GRAPH_API_BASE_VERSION_DEFAULT_V21;

@Service
public class InstagramExternalApiServiceImpl implements InstagramExternalApiService{

    private static final Logger log = LoggerFactory.getLogger(InstagramExternalApiServiceImpl.class);

    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;
    @Override
    public List<IgData> getInstagramAccountInsights(InstagramInsightRequest instagramAccountData, boolean isEngReq) {
        if(Objects.isNull(instagramAccountData) || StringUtils.isEmpty(instagramAccountData.getAccessToken())) {
            return null;
        }

        String url = com.birdeye.social.utils.StringUtils.format(GET_PAGE_INSIGHTS, instagramAccountData.getPageId());
        ResponseEntity<InstagramResponse> response;
        MultiValueMap<String, String> queryParam = getStringStringMultiValueMap(instagramAccountData, isEngReq);
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        log.info("url for getInstagramAccountInsights {}",url);
        try {
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null, InstagramResponse.class);
            if (response.getBody().getData() != null ) {
                return response.getBody().getData();
            }
        }
        catch (HttpStatusCodeException e) {
            //LOGGER.error("HttpStatusCodeException while calling facebook debug token API for URL {} :: {}", url, e.getResponseBodyAsString());

            if (e.getStatusCode().is5xxServerError()) {
                //logger.error("InternalServerException while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, e.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                //logger.error("Exception while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),e.getMessage());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return null;
    }

    @NotNull
    private MultiValueMap<String, String> getStringStringMultiValueMap(InstagramInsightRequest instagramAccountData, boolean isEngReq) {
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("access_token", instagramAccountData.getAccessToken());
        queryParam.add("metric", instagramAccountData.getMetric());
        queryParam.add("period", instagramAccountData.getPeriod());
        queryParam.add("since",String.valueOf(instagramAccountData.getSince()));
        queryParam.add("until",String.valueOf(instagramAccountData.getUntil()));
        if(isEngReq)
            queryParam.add("metric_type","total_value");
        return queryParam;
    }

    @Override
    public InstagramAccountDataResponse getInstagramAccountData(InstagramInsightRequest instagramAccountData) {
        String url = com.birdeye.social.utils.StringUtils.format(GRAPH_API_BASE_VERSION_DEFAULT_V21, instagramAccountData.getPageId());
        url += "/"+instagramAccountData.getPageId();
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("fields", "media_count,followers_count");
        queryParam.add("access_token", instagramAccountData.getAccessToken());
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        ResponseEntity<InstagramAccountDataResponse> response;
        log.info("url for getInstagramAccountData {}",url);
        try {
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null, InstagramAccountDataResponse.class);
            if (response.getBody() != null) {
                return response.getBody();
            } else {
                return new InstagramAccountDataResponse(0,0,"", 0 ,0);
            }
        } catch (HttpStatusCodeException e) {
            //LOGGER.error("HttpStatusCodeException while calling facebook debug token API for URL {} :: {}", url, e.getResponseBodyAsString());

            if (e.getStatusCode().is5xxServerError()) {
                //logger.error("InternalServerException while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, e.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                //logger.error("Exception while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),e.getMessage());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return new InstagramAccountDataResponse(0,0,"", 0 , 0);
    }

    @Override
    public List<IgData> getInstagramPostInsights(InstagramPostInsightRequest instagramAccountData) {
        if(Objects.isNull(instagramAccountData) || StringUtils.isEmpty(instagramAccountData.getAccessToken())) {
            return null;
        }

        String url = com.birdeye.social.utils.StringUtils.format(GET_PAGE_INSIGHTS, instagramAccountData.getPostId());
        ResponseEntity<InstagramResponse> response;
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("access_token",instagramAccountData.getAccessToken());
        queryParam.add("metric",instagramAccountData.getMetric());
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        try {
            log.info("url for getInstagramPostInsights {}",url);
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null, InstagramResponse.class);
            if (response.getBody().getData() != null ) {
                return response.getBody().getData();
            }
        }
        catch (HttpStatusCodeException e) {
            log.info("HttpStatusCodeException while calling getInstagramPostInsights: {}",e.getResponseBodyAsString());
            InstagramBaseResponse instagramBaseResponse = JSONUtils.fromJSON(e.getResponseBodyAsString(), InstagramBaseResponse.class);
            if(Objects.nonNull(instagramBaseResponse) && instagramBaseResponse.getError().getCode() == 100
                    && (instagramBaseResponse.getError().getError_subcode() == 33 || instagramBaseResponse.getError().getError_subcode() == 2108006 )){
                throw new HttpStatusCodeException(e.getStatusCode(), ErrorCodes.UNABLE_TO_FETCH_FB_INSIGHTS.name()) {
                };
            }
            if(Objects.nonNull(instagramBaseResponse) && instagramBaseResponse.getError().getCode() == 10) {
                return Collections.emptyList();
            }
            boolean ackError = checkIfAcknowledgeException(instagramBaseResponse);
            if(ackError) {
                throw new BirdeyeSocialException(ErrorCodes.FB_ACKNOWLEDGE_ERROR, e.getMessage());
            }
            if (e.getStatusCode().is5xxServerError()) {
                //logger.error("InternalServerException while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, e.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                //logger.error("Exception while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),e.getMessage());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return null;
    }

    @Override
    public InstagramAccountDataResponse getInstagramEngagementData(String postId, String accessToken) {
        String url = com.birdeye.social.utils.StringUtils.format(GRAPH_API_BASE_VERSION_DEFAULT_V21, postId);
        url += "/"+ postId;
        MultiValueMap<String, String> queryParam = new LinkedMultiValueMap<>();
        queryParam.add("fields", "comments_count,like_count");
        queryParam.add("access_token", accessToken);
        url = UriComponentsBuilder.fromHttpUrl(url).queryParams(queryParam).build().encode().toUriString();
        ResponseEntity<InstagramAccountDataResponse> response;
        try {
            log.info("url for getInstagramEngagementData {}",url);
            response = socialRestTemplate.exchange(url, HttpMethod.GET,null, InstagramAccountDataResponse.class);
            if (response.getBody() != null) {
                return response.getBody();
            } else {
                return new InstagramAccountDataResponse(0,0,"", 0, 0);
            }
        } catch (HttpStatusCodeException e) {
            //LOGGER.error("HttpStatusCodeException while calling facebook debug token API for URL {} :: {}", url, e.getResponseBodyAsString());

            if (e.getStatusCode().is5xxServerError()) {
                //logger.error("InternalServerException while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, e.getMessage());
            }
            if(e.getStatusCode().is4xxClientError()){
                //logger.error("Exception while calling GMB message send for URL {} and exception {}", url, e.getResponseBodyAsString());
                throw new BirdeyeSocialException(e.getStatusCode().value(),e.getMessage());
            }
        }
        catch (Exception e) {
            throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS, ErrorCodes.UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS.name(), e);
        }
        return new InstagramAccountDataResponse(0,0,"", 0 ,0);
    }


    private boolean checkIfAcknowledgeException(InstagramBaseResponse errorResponse) {
        if(Objects.isNull(errorResponse) || Objects.isNull(errorResponse.getError()))
            return false;

        InstagramErrorResponse error = errorResponse.getError();

        if(Objects.nonNull(error.getCode()) && error.getCode().equals(190)
                && Objects.nonNull(error.getError_subcode()) && error.getError_subcode().equals(459)) {
            return true;
        }

        return false;
    }
}
