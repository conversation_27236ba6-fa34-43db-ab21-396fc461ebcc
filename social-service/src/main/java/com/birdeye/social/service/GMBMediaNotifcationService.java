package com.birdeye.social.service;

import com.birdeye.social.constant.GMBNotificationStatus;
import com.birdeye.social.dao.GMBMediaNotificationRepo;
import com.birdeye.social.entities.GMBMediaNotification;
import com.birdeye.social.model.GMBNotificationReviewRequest;
import com.birdeye.social.response.gmb.GMBCustomerMediaByIdResponse;
import com.birdeye.social.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
public class GMBMediaNotifcationService implements IGMBMediaNotificationService{

    @Autowired
    GMBMediaNotificationRepo gmbMediaNotificationRepo;

    @Override
    @Transactional
    public void createGMBMediaNotificationAsSuccess(GMBNotificationReviewRequest request, GMBCustomerMediaByIdResponse response,  String mediaName) {
        GMBMediaNotification gmbMediaNotification = new GMBMediaNotification();
        gmbMediaNotification.setLocationId(request.getLocationId());
        gmbMediaNotification.setEventData(request.getEventData());
        gmbMediaNotification.setDecodedEventData(request.getDecodedEventData());
        gmbMediaNotification.setNotificationType(request.getReviewNotificationType().toString());
        gmbMediaNotification.setMediaItemName(mediaName);
        gmbMediaNotification.setCreated(new Date());
        gmbMediaNotification.setUpdated(new Date());
        gmbMediaNotification.setBuisness_id(response.getBusinessId());
        gmbMediaNotification.setStatus(GMBNotificationStatus.SUCCESS.toString());
        gmbMediaNotification.setPlaceId(response.getPlaceId());
        gmbMediaNotificationRepo.save(gmbMediaNotification);
    }

    @Override
    @Transactional
    public void createGMBMediaNotificationAsFailed(GMBNotificationReviewRequest request,  String mediaName, Integer errorCode, String comment) {
        GMBMediaNotification gmbMediaNotification = new GMBMediaNotification();
        gmbMediaNotification.setLocationId(request.getLocationId());
        gmbMediaNotification.setEventData(request.getEventData());
        gmbMediaNotification.setDecodedEventData(request.getDecodedEventData());
        gmbMediaNotification.setNotificationType(request.getReviewNotificationType().toString());
        gmbMediaNotification.setMediaItemName(mediaName);
        gmbMediaNotification.setCreated(new Date());
        gmbMediaNotification.setUpdated(new Date());
        if (errorCode != null){
            gmbMediaNotification.setErrorCode(errorCode);
        }
        if (StringUtils.isNotEmpty(comment)){
            gmbMediaNotification.setComment(comment);
        }
        gmbMediaNotification.setStatus(GMBNotificationStatus.FAILED.toString());
        gmbMediaNotificationRepo.save(gmbMediaNotification);
    }
}
