package com.birdeye.social.service.impl;

import com.birdeye.social.businessCore.BusinessAppIdsResponse;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessCoreUserBulkRequest;
import com.birdeye.social.dto.BusinessCoreUsersBulk;
import com.birdeye.social.dto.BusinessDomainDTO;
import com.birdeye.social.service.BusinessService;
import com.birdeye.social.service.ISocialAppService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class BusinessServiceImpl implements BusinessService {

    private static final Logger LOG = LoggerFactory
            .getLogger(BusinessServiceImpl.class);

    @Autowired
    private IBusinessCoreService businessCoreService;

    @Autowired
    private ISocialAppService socialAppService;

    private ExecutorService executor = Executors.newFixedThreadPool(3);

    private static Logger logger = LoggerFactory.getLogger(BusinessServiceImpl.class);

    @Override
    @Cacheable(value = "businessDomain", key = "#businessId.toString()")
    public BusinessDomainDTO getBusinessDomain(Integer businessId) {
        logger.info("Fetching businessDomain for businessId: {}",businessId);
        return businessCoreService.getBusinessDomain(businessId);
    }

    @Override
    public BusinessAppIdsResponse getBusinessAppIds(Integer businessId, Long domainId) throws ExecutionException, InterruptedException {
        LOG.info("Received request to fetch business app ids for business id {} and domain Id {}",
                businessId, domainId);

        Future<String> googleAdminUser = executor.submit(() -> socialAppService.getGooglePlusCredentials(domainId).getToken());
        Future<String> facebookAppId = executor.submit(() -> socialAppService.getFacebookCredentials(domainId).getToken());
        Future<String> googleAppId = executor.submit(() -> socialAppService.getGoogleCreds(domainId).getToken());

        return new BusinessAppIdsResponse(googleAdminUser.get(), facebookAppId.get(), googleAppId.get());
    }

    @Override
    public Map<Integer, BusinessCoreUser> getBusinessUserForUserId(List<Integer> userIds) {
        Map<Integer, BusinessCoreUser> responseMap = new HashMap<>();
        if(CollectionUtils.isEmpty(userIds)) return responseMap;

        userIds = userIds.stream().filter(s-> Objects.nonNull(s)).distinct().collect(Collectors.toList());
        List<List<Integer>> userIdsList = Lists.partition(userIds, 50);

        List<BusinessCoreUser> businessCoreUsers = new ArrayList<>();
        for(List<Integer> userIdsSubList: userIdsList) {
            BusinessCoreUserBulkRequest request = BusinessCoreUserBulkRequest.builder()
                    .userIds(userIdsSubList)
                    .build();
            BusinessCoreUsersBulk businessCoreUsersBulk = businessCoreService.getUserInfoInBulk(request);
            if(Objects.nonNull(businessCoreUsersBulk) && CollectionUtils.isNotEmpty(businessCoreUsersBulk.getUsers())) {
                businessCoreUsers.addAll(businessCoreUsersBulk.getUsers());
            }
        }

        responseMap = businessCoreUsers.stream().collect(Collectors.toMap(s->s.getId(), s->s));
        return responseMap;
    }
}
