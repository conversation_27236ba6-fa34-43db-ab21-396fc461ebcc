package com.birdeye.social.service.SocialCompetitorService.Facebook;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialReservedAccountsRepo;
import com.birdeye.social.dao.competitor.CompetitorPostsRepo;
import com.birdeye.social.dao.competitor.CompetitorProfileStatusInfoRepo;
import com.birdeye.social.dao.competitor.FacebookCompetitorInfoRepo;
import com.birdeye.social.dao.competitor.FacebookCompetitorMappingRepo;
import com.birdeye.social.dto.PicturesqueMediaCallback;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.MediaAsset;
import com.birdeye.social.entities.competitor.CompetitorPosts;
import com.birdeye.social.entities.competitor.CompetitorProfileStatusInfo;
import com.birdeye.social.entities.competitor.FacebookCompetitorInfo;
import com.birdeye.social.entities.competitor.FacebookCompetitorMapping;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.*;
import com.birdeye.social.model.*;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetails;
import com.birdeye.social.model.competitorProfile.CompetitorPageDetailsDTO;
import com.birdeye.social.service.FacebookPageService;
import com.birdeye.social.service.MediaAssetRepoService;
import com.birdeye.social.service.SocialCompetitorService.SocialCompetitorHelperService;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class FacebookCompetitorServiceImpl  implements FacebookCompetitorService{

    @Autowired
    private FacebookCompetitorInfoRepo competitorInfoRepo;
    @Autowired
    private FacebookCompetitorMappingRepo competitorMappingRepo;
    @Autowired
    private FacebookPageService facebookPageService;
    @Autowired
    private CompetitorPostsRepo competitorPostsRepo;
    @Autowired
    private KafkaProducerService kafkaService;
    @Autowired
    private SocialReservedAccountsRepo reservedAccountsRepo;
    @Autowired
    private CompetitorProfileStatusInfoRepo competitorProfileStatusInfoRepo;
    @Autowired
    private FacebookService fbService;

    @Autowired
    private SocialCompetitorHelperService socialCompetitorHelperService;

    @Autowired
    private MediaAssetRepoService mediaAssetRepoService;

    @Autowired
    private SocialFBPageRepository fbPageRepository;

    private static final Logger LOG = LoggerFactory.getLogger(FacebookCompetitorServiceImpl.class);

    private static final String SHARE = "share";

    private String getInsightData(FacebookFeedV2 response) {
        Integer reactionCareCount = Objects.nonNull(response.getReactions_care()) && Objects.nonNull(response.getReactions_care().getSummary()) ?
                response.getReactions_care().getSummary().getTotal_count() : 0;

        Integer shareCount = Objects.nonNull(response.getShares())? Integer.parseInt(response.getShares().getCount()) : 0;
        Integer commentCount = Objects.nonNull(response.getComments()) && Objects.nonNull(response.getComments().getSummary()) ?
                response.getComments().getSummary().getTotal_count() : 0;
        Integer likeCount = Objects.nonNull(response.getReactions_like()) && Objects.nonNull(response.getReactions_like().getSummary()) ?
                response.getReactions_like().getSummary().getTotal_count() : 0;
        CompetitorInsightDTO competitorInsightDTO = CompetitorInsightDTO.builder()
                .likeCount(likeCount + reactionCareCount)
                .commentCount(commentCount)
                .shareCount(shareCount)
                .build();

        return JSONUtils.toJSON(competitorInsightDTO);
    }


    private CompetitorPosts convertToCompetitorPost(FacebookFeedV2 data, CompetitorRequestDTO competitorRequestDTO) {
        FbAttachement attachement = data.getAttachments();
        List<String> imageUrls = new ArrayList<>();
        List<String> videoUrls = new ArrayList<>();
        List<String> thumbnailUrls = new ArrayList<>();
        if(Objects.nonNull(attachement) && CollectionUtils.isNotEmpty(attachement.getData())) {
            for(Attachement attachmentEntry: attachement.getData()) {
                if(Objects.nonNull(attachmentEntry) && "photo".equals(attachmentEntry.getType())) {
                    FbMedia media = attachmentEntry.getMedia();
                    if(Objects.nonNull(media) && Objects.nonNull(media.getImage())) {
                        String imageSource = media.getImage().getSrc();
                        if(StringUtils.isNotEmpty(imageSource)) imageUrls.add(imageSource);
                    }
                } else if(Objects.nonNull(attachmentEntry) &&
                        "album".equals(attachmentEntry.getType()) &&
                        Objects.nonNull(attachmentEntry.getSubattachments()) &&
                        CollectionUtils.isNotEmpty(attachmentEntry.getSubattachments().getData())) {
                    List<Attachement> subAttachments = attachmentEntry.getSubattachments().getData();
                    for(Attachement subAttachmentEntry: subAttachments) {
                        if(Objects.nonNull(subAttachmentEntry) && "photo".equals(subAttachmentEntry.getType())) {
                            FbMedia media = subAttachmentEntry.getMedia();
                            if(Objects.nonNull(media) && Objects.nonNull(media.getImage())) {
                                String imageSource = media.getImage().getSrc();
                                if(StringUtils.isNotEmpty(imageSource)) imageUrls.add(imageSource);
                            }
                        } else if(Objects.nonNull(subAttachmentEntry) &&
                                 ("animated_image_video".equals(subAttachmentEntry.getType()) ||
                                         "video_inline".equals(subAttachmentEntry.getType()) ||
                                         "video".equals(subAttachmentEntry.getType()))) {
                            FbMedia media = subAttachmentEntry.getMedia();
                            if(Objects.nonNull(media) && Objects.nonNull(media.getImage())) {
                                String imageSource = media.getImage().getSrc();
                                if(StringUtils.isNotEmpty(imageSource)) thumbnailUrls.add(imageSource);
                            }
                        }
                    }
                } else if(Objects.nonNull(attachmentEntry) &&
                        ("animated_image_video".equals(attachmentEntry.getType()) ||
                                "video_inline".equals(attachmentEntry.getType()) ||
                                "video".equals(attachmentEntry.getType()))) {
                    FbMedia media = attachmentEntry.getMedia();
                    if(Objects.nonNull(media) && Objects.nonNull(media.getImage())) {
                        String imageSource = media.getImage().getSrc();
                        if(StringUtils.isNotEmpty(imageSource)) thumbnailUrls.add(imageSource);
                    }
                }
                else if(Objects.nonNull(attachmentEntry) && SHARE.equals(attachmentEntry.getType())) {
                    FbMedia media = attachmentEntry.getMedia();
                    if(Objects.nonNull(media) && Objects.nonNull(media.getImage())) {
                        String imageSource = media.getImage().getSrc();
                        if(StringUtils.isNotEmpty(imageSource)) imageUrls.add(imageSource);
                    }
                }
            }
        }
        return CompetitorPosts.builder()
                .rawCompetitorId(competitorRequestDTO.getRawId())
                .postId(data.getId())
                .sourceId(competitorRequestDTO.getSourceId())
                .postUrl(data.getPermalink_url())
                .postText(data.getMessage())
                .imageUrls(imageUrls)
                .videoUrls(videoUrls)
                .thumbnailUrls(thumbnailUrls)
                .userName(competitorRequestDTO.getUserName())
                .publishDate(data.getCreated_time())
                .insightData(getInsightData(data))
                .build();
    }

    // TODO: add dedupe for competitorPosts
    @Override
    public void fetchCompetitorPosts(CompetitorRequestDTO competitorRequestDTO) {
        LOG.info("[FB competitor posts] started processing for FB page: {}",competitorRequestDTO.getPageId());
        String accessToken = getAccessToken(competitorRequestDTO);
        if(StringUtils.isEmpty(accessToken)) {
            LOG.info("Unable to get system user token, exiting the flow!!");
            return;
        }
        CompetitorProfileStatusEnum status = CompetitorProfileStatusEnum.FETCHING_MEDIA_DATA;
        String nextToken = null;
        Integer daysSub = 0;
        if(Objects.nonNull(competitorRequestDTO) && Objects.nonNull(competitorRequestDTO.getIsDailyScan()) && competitorRequestDTO.getIsDailyScan()) {
            daysSub = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFacebookCompPostLimitDayForJob();
        } else {
            daysSub = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFacebookCompPostLimitDay();
        }
        Integer totalCount = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFacebookCompPostCountLimit();
        Date thresholdDate = DateUtils.addDays(new Date(), 0-daysSub);
        try {
            int count = 0;
            competitorProfileStatusInfoRepo.updateStatus(competitorRequestDTO.getUserName(), status);
            do {
                FacebookFeedDataV2 competitorPostData = fbService.getFacebookCompetitorFeed(competitorRequestDTO.getPageId(), accessToken, nextToken);
                if (Objects.nonNull(competitorPostData) && CollectionUtils.isNotEmpty(competitorPostData.getData())) {
                    boolean ifStopFetch = stopFetchingPosts(competitorPostData, thresholdDate);
                    count += competitorPostData.getData().size();
                    if(count >= totalCount) {
                        ifStopFetch = true;
                        competitorPostData.setData(competitorPostData.getData()
                                .subList(0, competitorPostData.getData().size()-(count-totalCount)));
                    }
                    if(CollectionUtils.isEmpty(competitorPostData.getData())) {
                        LOG.info("No data found to save in DB for request {}", competitorRequestDTO);
                        break;
                    }
                    List<CompetitorPosts> competitorPostsList = new ArrayList<>();
                    competitorPostData.getData().forEach(data -> competitorPostsList.add(convertToCompetitorPost(data, competitorRequestDTO)));
                    upsertCompetitorEntries(competitorPostsList, competitorRequestDTO.getSourceId());
                    nextToken = Objects.nonNull(competitorPostData.getPaging()) && Objects.nonNull(competitorPostData.getPaging().getCursors()) &&
                            StringUtils.isNotEmpty(competitorPostData.getPaging().getCursors().getAfter()) ? competitorPostData.getPaging().getCursors().getAfter() : null;
                    if(ifStopFetch) {
                        LOG.info("threshold reached breaking the loop");
                        break;
                    }
                } else {
                    nextToken=null;
                    LOG.info("No post data found for page: {}", competitorRequestDTO.getPageId());
                }
            } while (Objects.nonNull(nextToken));
            status = CompetitorProfileStatusEnum.SUCCESS;
        }catch(Exception e) {
            status = CompetitorProfileStatusEnum.MEDIA_FETCH_FAILED;
            LOG.error("Exception while fetching posts for page: {}", competitorRequestDTO.getPageId(), e);
        }
        updateCompPageScannedOnce(competitorRequestDTO);
        competitorProfileStatusInfoRepo.updateStatus(competitorRequestDTO.getUserName(), status);
    }

    private String getAccessToken(CompetitorRequestDTO competitorRequestDTO) {
        if(Objects.isNull(competitorRequestDTO)) return null;

        if(Objects.nonNull(competitorRequestDTO.getIsDailyScan()) && competitorRequestDTO.getIsDailyScan()) {
            String accessToken = null;
            List<FacebookCompetitorMapping> facebookCompetitorMappingList =  competitorMappingRepo.findByRawCompetitorId(competitorRequestDTO.getRawId());
            if(CollectionUtils.isNotEmpty(facebookCompetitorMappingList)) {
                List<Long> businessNumberList = facebookCompetitorMappingList.stream().map(FacebookCompetitorMapping::getEnterpriseId).collect(Collectors.toList());
                List<BusinessFBPage>  fbPages = fbPageRepository.findByEnterpriseIdInAndIsValidAndBusinessIdNotNull(businessNumberList);
                if(CollectionUtils.isNotEmpty(fbPages)) {
                    accessToken = fbPages.get(0).getPageAccessToken();
                }
            }
            return accessToken;
        } else {
            return facebookPageService.getPPCASystemUserToken();
        }
    }

    private void updateCompPageScannedOnce(CompetitorRequestDTO competitorRequestDTO) {
        competitorInfoRepo.updateScannedOnce(1, competitorRequestDTO.getRawId());
    }

    private boolean stopFetchingPosts(FacebookFeedDataV2 competitorPostData, Date thresholdDate) {
        try {
            List<FacebookFeedV2> facebookFeedV2List = competitorPostData.getData();
            if(CollectionUtils.isEmpty(facebookFeedV2List)) return true;
            int i = 0;
            while(i < facebookFeedV2List.size()) {
                FacebookFeedV2 competitorPosts = facebookFeedV2List.get(i);
                if(thresholdDate.after(competitorPosts.getCreated_time())) {
                    break;
                }
                i++;
            }
            if(i == facebookFeedV2List.size()) return false;
            else {
                competitorPostData.setData(facebookFeedV2List.subList(0, i));
                return true;
            }
        } catch (Exception e) {
            LOG.info("exception occurred while checking fetch posts stop: {}", e.getMessage());
            return true;
        }
    }

    private FacebookCompetitorMapping createCompMappingEntity(FacebookCompetitorInfo facebookCompetitorInfo, Long enterpriseId) {
        return FacebookCompetitorMapping.builder()
                .competitorId(facebookCompetitorInfo.getPageId())
                .enterpriseId(enterpriseId)
                .rawCompetitorId(facebookCompetitorInfo.getId())
                .build();
    }

    private void upsertCompetitorEntries(List<CompetitorPosts> competitorPostsList, Integer sourceId) {
        if(CollectionUtils.isEmpty(competitorPostsList)) return;
        List<String> postIds = competitorPostsList.stream().map(CompetitorPosts::getPostId).collect(Collectors.toList());
        List<CompetitorPosts> competitorPostsInDB = competitorPostsRepo.findByPostIdInAndSourceId(postIds, sourceId);
        Map<String, CompetitorPosts> postIdVsCompPosts = new HashMap<>();
        if(CollectionUtils.isNotEmpty(competitorPostsInDB)) {
            postIdVsCompPosts = competitorPostsInDB.stream().collect(Collectors.toMap(s->s.getPostId(), s->s));
        }
        List<CompetitorPosts> competitorPostsUpdatedList = new ArrayList<>();
        for(CompetitorPosts competitorPosts: competitorPostsList) {
            if(postIdVsCompPosts.containsKey(competitorPosts.getPostId())) {
                CompetitorPosts competitorPostsFromDB = postIdVsCompPosts.get(competitorPosts.getPostId());
                competitorPosts.setId(competitorPostsFromDB.getId());
            }
            competitorPosts.setMetaData(socialCompetitorHelperService.getCompetitorPostMetaData(competitorPosts));
            competitorPostsUpdatedList.add(competitorPosts);
        }
        // todo send event to call picturesque
        competitorPostsRepo.save(competitorPostsUpdatedList);
        competitorPostsRepo.flush();
        if(CollectionUtils.isNotEmpty(competitorPostsUpdatedList)) {
            competitorPostsUpdatedList.forEach(competitorEntity -> {
                socialCompetitorHelperService.sendEsSyncEvent(competitorEntity);
                socialCompetitorHelperService.sendEventToCallPicturesQueue(competitorEntity.getId(), MediaAssetEntityType.COMPETITOR_POST, null, null);
            });
        }
    }

    private void pushDtoForFetchingPosts(FacebookCompetitorInfo facebookCompetitorInfo) {
        CompetitorRequestDTO competitorRequestDTO = CompetitorRequestDTO.builder()
                .rawId(facebookCompetitorInfo.getId())
                .sourceId(SocialChannel.FACEBOOK.getId())
                .pageId(facebookCompetitorInfo.getPageId())
                .userName(facebookCompetitorInfo.getUserName())
                .isDailyScan(false)
                .build();
        kafkaService.sendObjectV1(KafkaTopicEnum.FACEBOOK_FETCH_COMP_POSTS.getName(), competitorRequestDTO);
    }

    private FacebookCompetitorInfo createProfileInfoEntity(FbPublicProfileInfo profileResponse) {
        String profilePictureUrl = null;
        try {
            Map<String, Object> picture = profileResponse.getPicture();
            if(MapUtils.isNotEmpty(picture) && picture.containsKey("data")) {
                Map<String, Object> pictureParam = (Map<String, Object>) picture.get("data");
                if(MapUtils.isNotEmpty(pictureParam) && pictureParam.containsKey("url")) {
                    profilePictureUrl = (String) pictureParam.get("url");
                }
            }
        } catch (Exception e) {
            LOG.info("exception while getting profile pic url in facebook");
        }
        Boolean isVerified = "blue_verified".equals(profileResponse.getVerification_status());
        String userName = profileResponse.getUsername() != null ? profileResponse.getUsername(): "";
        return FacebookCompetitorInfo.builder()
                .fullName(StringUtils.isEmpty(profileResponse.getName()) ? userName : profileResponse.getName())
                .pageId(profileResponse.getId())
                .userName(userName)
                .pageUrl(profileResponse.getLink())
                .profilePictureUrl(profilePictureUrl)
                .description(profileResponse.getAbout())
                .verified(isVerified?1:0)
                .scannedOnce(0)
                .build();
    }

    // TODO: add dedupe for competitorInfo, competitorMapping and competitorPostStatusInfo table
    @Override
    public void fetchCompetitorAccounts(List<String> accountIdentifier, Long enterpriseId) {
        LOG.info("Processing FB account info for competitors: {}",accountIdentifier.size());

        String accessToken = facebookPageService.getPPCASystemUserToken();
        if(StringUtils.isEmpty(accessToken)) {
            LOG.info("Unable to get PPCA token, exiting the flow!!");
            return;
        }

        accountIdentifier.forEach(pageIdentifier -> {
            CompetitorProfileStatusEnum status = CompetitorProfileStatusEnum.INIT;
            competitorProfileStatusInfoRepo.save(new CompetitorProfileStatusInfo(SocialChannelIcons.FACEBOOK.getId(), pageIdentifier, status));
            try {
                LOG.info("Processing FB page with pageIdentifier: {}", pageIdentifier);
                FbPublicProfileInfo profileResponse = fbService.getPublicProfileInfo(accessToken, pageIdentifier);
                if(Objects.nonNull(profileResponse) && Objects.nonNull(profileResponse.getId())) {
                    FacebookCompetitorInfo facebookCompetitorInfo = createProfileInfoEntity(profileResponse);
                    createAndSaveCompetitorInfoAndMapping(facebookCompetitorInfo, enterpriseId);
                    pushDtoForFetchingPosts(facebookCompetitorInfo);
                    socialCompetitorHelperService.sendEventToCallPicturesQueue(facebookCompetitorInfo.getId(), MediaAssetEntityType.COMPETITOR_PAGE, SocialChannel.FACEBOOK, null);
                    // todo push event to call picturesque
                    status = CompetitorProfileStatusEnum.PROFILE_DATA_FETCHED;
                    LOG.info("Profile data fetched for user: {}", pageIdentifier);
                } else {
                    status = CompetitorProfileStatusEnum.PROFILE_DATA_NOT_FOUND;
                    LOG.info("Page data not found for user: {}", pageIdentifier);
                }
            } catch (Exception e) {
                status = CompetitorProfileStatusEnum.PROFILE_FETCH_FAILED;
                LOG.info("Exception while getting account info for FB page: {}",pageIdentifier, e);
                throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
            }finally {
                competitorProfileStatusInfoRepo.updateStatus(pageIdentifier, status);
            }
        });
    }

    public void createAndSaveCompetitorInfoAndMapping(FacebookCompetitorInfo facebookCompetitorInfo, Long enterpriseId) {
        FacebookCompetitorInfo facebookCompetitorInfoFromDB = competitorInfoRepo.findByPageId(facebookCompetitorInfo.getPageId());

        if(Objects.nonNull(facebookCompetitorInfoFromDB)) {
            facebookCompetitorInfo.setId(facebookCompetitorInfoFromDB.getId());
            facebookCompetitorInfo.setLastScanDate(new Date());
            facebookCompetitorInfo.setScannedOnce(facebookCompetitorInfoFromDB.getScannedOnce());
        }
        competitorInfoRepo.saveAndFlush(facebookCompetitorInfo);

        FacebookCompetitorMapping competitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(facebookCompetitorInfo.getPageId(), enterpriseId);
        if(Objects.isNull(competitorMapping)) {
            competitorMappingRepo.saveAndFlush(createCompMappingEntity(facebookCompetitorInfo, enterpriseId));
        }
        socialCompetitorHelperService.clearCache(SocialChannel.FACEBOOK, enterpriseId);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.FACEBOOK, enterpriseId);
    }

    @Override
    public void updateCompCache(SocialChannel channel, Long businessNumber) {
        List<FacebookCompetitorMapping> mappings = competitorMappingRepo.findByEnterpriseId(businessNumber);

        if(CollectionUtils.isEmpty(mappings)) {
            socialCompetitorHelperService.clearCache(channel, businessNumber);
            return;
        }

        List<String> pageIds = mappings.stream().map(FacebookCompetitorMapping::getCompetitorId) .collect(Collectors.toList());
        List<FacebookCompetitorInfo> facebookCompetitorInfos = competitorInfoRepo.findByPageIdIn(pageIds);

        if(CollectionUtils.isEmpty(facebookCompetitorInfos)) {
            socialCompetitorHelperService.clearCache(channel, businessNumber);
            return;
        }

        socialCompetitorHelperService.updateFacebookCache(channel, businessNumber,facebookCompetitorInfos);
    }

    @Override
    public CompetitorListResponse getCompetitorList(Long businessNumber) {
        Object listObject = socialCompetitorHelperService.getCompetitorListFromCache(SocialChannel.FACEBOOK, businessNumber);
        CompetitorListResponse competitorListResponse = new CompetitorListResponse();
        try {
            if(Objects.nonNull(listObject)) {
                List<FacebookCompetitorInfo> facebookCompetitorInfoFromCache = (List<FacebookCompetitorInfo>) listObject;
                competitorListResponse.setSocialCompetitorDataList(createCompetitorResponseList(facebookCompetitorInfoFromCache));
            } else {
                List<FacebookCompetitorMapping> facebookCompetitorMappings = competitorMappingRepo.findByEnterpriseId(businessNumber);
                if(CollectionUtils.isEmpty(facebookCompetitorMappings)) {
                    LOG.info("empty mapping for channel facebook and business: {}", businessNumber);
                    return competitorListResponse;
                }

                List<String> pageIds = facebookCompetitorMappings.stream()
                        .map(FacebookCompetitorMapping::getCompetitorId).collect(Collectors.toList());

                List<FacebookCompetitorInfo> facebookCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
                if(CollectionUtils.isEmpty(facebookCompetitorInfoList)) {
                    LOG.info("no pages found for channel facebook and business: {}", businessNumber);
                    return competitorListResponse;
                }

                competitorListResponse.setSocialCompetitorDataList(createCompetitorResponseList(facebookCompetitorInfoList));
            }

            return competitorListResponse;
        } catch (Exception e) {
            LOG.info("error occured while fetching pages for business: {}, error: {}", businessNumber, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    private List<CompetitorBasicDetail> createCompetitorResponseList(List<FacebookCompetitorInfo> facebookCompetitorInfoList) {
        if(CollectionUtils.isEmpty(facebookCompetitorInfoList))
            return new ArrayList<>();

        List<CompetitorBasicDetail> competitorBasicDetailList = new ArrayList<>();
        for(FacebookCompetitorInfo facebookCompetitorInfo: facebookCompetitorInfoList) {
            Boolean isVerified = null;
            if(Objects.nonNull(facebookCompetitorInfo.getVerified())) {
                isVerified = facebookCompetitorInfo.getVerified()==1?true:false;
            }
            CompetitorBasicDetail competitorBasicDetail = CompetitorBasicDetail.builder()
                    .id(facebookCompetitorInfo.getPageId())
                    .name(facebookCompetitorInfo.getFullName())
                    .userName(facebookCompetitorInfo.getUserName())
                    .pageLink(facebookCompetitorInfo.getPageUrl())
                    .profilePictureUrl(facebookCompetitorInfo.getProfilePictureUrl())
                    .isVerified(isVerified)
                    .build();

            competitorBasicDetailList.add(competitorBasicDetail);
        }

        return competitorBasicDetailList;
    }

    @Override
    @Transactional
    public void deleteCompetitor(DeleteCompetitorDetailRequest deleteRequest, Long enterpriseId) {
        if (Objects.isNull(deleteRequest) || StringUtils.isEmpty(deleteRequest.getPageId())) {
            LOG.info("invalid request payload for enterprise id: {}", enterpriseId);
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "Invalid Request Payload");
        }

        String pageId = deleteRequest.getPageId();

        FacebookCompetitorInfo facebookCompetitorInfo = competitorInfoRepo.findByPageId(pageId);

        if (Objects.isNull(facebookCompetitorInfo)) {
            LOG.info("No page found with page id: {}", pageId);
            socialCompetitorHelperService.clearCache(SocialChannel.FACEBOOK, enterpriseId);
            socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.FACEBOOK, enterpriseId);
            return;
        }
        FacebookCompetitorMapping facebookCompetitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(pageId, enterpriseId);

        if (Objects.nonNull(facebookCompetitorMapping)) {
            competitorMappingRepo.delete(facebookCompetitorMapping);
        }

        socialCompetitorHelperService.clearCache(SocialChannel.FACEBOOK, enterpriseId);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.FACEBOOK, enterpriseId);
    }

    @Override
    public String getPageIdOnCompId(Integer rawCompId) {
        FacebookCompetitorInfo facebookCompetitorInfo = competitorInfoRepo.findOne(rawCompId);
        if(Objects.isNull(facebookCompetitorInfo)) {
            return null;
        }
        return facebookCompetitorInfo.getPageId();
    }

    @Override
    public void scanPages() {
        Integer numberOfRecords = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCompetitorPageScanCount();
        Date previousDayDate = DateUtils.addDays(new Date(), -1);
        List<FacebookCompetitorInfo> facebookCompetitorInfoList= competitorInfoRepo
                .findRecordsByLastScanDate(previousDayDate, numberOfRecords);

        if(CollectionUtils.isEmpty(facebookCompetitorInfoList)) {
            LOG.info("no records to scan for facebook");
            return;
        }

        List<Integer> ids = new ArrayList<>();

        facebookCompetitorInfoList.forEach(facebookCompetitorInfo -> {
            CompetitorRequestDTO competitorRequestDTO = CompetitorRequestDTO.builder()
                    .rawId(facebookCompetitorInfo.getId())
                    .pageId(facebookCompetitorInfo.getPageId())
                    .sourceId(SocialChannel.FACEBOOK.getId())
                    .userName(facebookCompetitorInfo.getUserName())
                    .isDailyScan(true)
                    .build();
            kafkaService.sendObjectV1(KafkaTopicEnum.FACEBOOK_FETCH_COMP_POSTS.getName(), competitorRequestDTO);
            ids.add(facebookCompetitorInfo.getId());
        });

        competitorInfoRepo.updateLastScanDate(new Date(), ids);
    }

    @Override
    public List<CompetitorBasicDetail> getCompetitorsBasicDetails(List<String> pageIds) {
        List<FacebookCompetitorInfo> facebookCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);

        List<CompetitorBasicDetail> competitorBasicDetailList = new ArrayList<>();
        if(CollectionUtils.isEmpty(facebookCompetitorInfoList)) {
            return competitorBasicDetailList;
        }

        facebookCompetitorInfoList.forEach(facebookCompetitorInfo -> {
            Boolean isVerified = null;
            if(Objects.nonNull(facebookCompetitorInfo.getVerified())) {
                isVerified = facebookCompetitorInfo.getVerified() == 1?true:false;
            }

            CompetitorBasicDetail competitorBasicDetail = CompetitorBasicDetail.builder()
                    .id(facebookCompetitorInfo.getPageId())
                    .name(facebookCompetitorInfo.getFullName())
                    .userName(facebookCompetitorInfo.getUserName())
                    .profilePictureUrl(facebookCompetitorInfo.getProfilePictureUrl())
                    .scannedOnce(facebookCompetitorInfo.getScannedOnce() == 1?true:false)
                    .isVerified(isVerified)
                    .build();
            competitorBasicDetailList.add(competitorBasicDetail);
        });
        return competitorBasicDetailList;
    }

    @Override
    public void updateProfilePictureUrl(PicturesqueMediaCallback picturesqueMediaCallback, Integer rawCompId) {
        if(Objects.isNull(picturesqueMediaCallback) || Objects.isNull(rawCompId)) {
            LOG.info("invalid upload profile picture request");
            return;
        }

        FacebookCompetitorInfo facebookCompetitorInfo = competitorInfoRepo.findOne(rawCompId);

        if(Objects.isNull(facebookCompetitorInfo)) {
            LOG.info("no page found with id: {}", rawCompId);
            return;
        }

        facebookCompetitorInfo.setProfilePictureUrl(picturesqueMediaCallback.getMediaUrl());
        competitorInfoRepo.saveAndFlush(facebookCompetitorInfo);

        List<FacebookCompetitorMapping> competitorMappings = competitorMappingRepo.findByRawCompetitorId(rawCompId);

        if(CollectionUtils.isNotEmpty(competitorMappings)) {
            for(FacebookCompetitorMapping mapping: competitorMappings) {
                socialCompetitorHelperService.clearCache(SocialChannel.FACEBOOK, mapping.getEnterpriseId());
                socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.FACEBOOK, mapping.getEnterpriseId());
            }
        }
    }

    @Override
    public void callPicturesQueForPage(PicturesqueCompRequest request) {
        if(Objects.isNull(request) || Objects.isNull(request.getId())) {
            LOG.info("invalid upload profile picture request");
            return;
        }

        FacebookCompetitorInfo facebookCompetitorInfo = competitorInfoRepo.findOne(request.getId());

        if(Objects.isNull(facebookCompetitorInfo) || StringUtils.isEmpty(facebookCompetitorInfo.getProfilePictureUrl())) {
            LOG.info("no page found with id: {}", request.getId());
            return;
        }
        MediaAsset mediaAsset = MediaAsset.builder()
                .mediaUrl(facebookCompetitorInfo.getProfilePictureUrl())
                .mediaType(MediaAssetMediaType.IMAGE.getName())
                .entityId(facebookCompetitorInfo.getId()+"_"+SocialChannel.FACEBOOK.getId())
                .entityType(MediaAssetEntityType.COMPETITOR_PAGE.getName())
                .build();
        mediaAssetRepoService.saveAndFlush(mediaAsset);
        String callBackUrl =  CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getUploadPicturesqueMediaUploadCallBackUrl();
        String businessNumber = Objects.isNull(request.getBusinessNumber())?"na":Long.toString(request.getBusinessNumber());
        socialCompetitorHelperService.picturesqueService(facebookCompetitorInfo.getProfilePictureUrl(), businessNumber, mediaAsset.getId(), callBackUrl);
    }

    @Override
    public CompetitorPageDetailResponse getPageSummary(String pageId, Long businessNumber) {
        if(StringUtils.isEmpty(pageId)) {
            throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, "Invalid Request");
        }
        String accessToken = facebookPageService.getPPCASystemUserToken();

        try {
            FbPublicProfileInfo profileResponse = fbService.getPublicProfileInfo(accessToken, pageId);
            Map<String, Object> picture = profileResponse.getPicture();
            String profilePictureUrl = null;
            if(MapUtils.isNotEmpty(picture) && picture.containsKey("data")) {
                Map<String, Object> pictureParam = (Map<String, Object>) picture.get("data");
                if(MapUtils.isNotEmpty(pictureParam) && pictureParam.containsKey("url")) {
                    profilePictureUrl = (String) pictureParam.get("url");
                }
            }
            String coverUrl = null;
            Map<String, Object> cover = profileResponse.getCover();
            if(MapUtils.isNotEmpty(cover) && cover.containsKey("source")) {
                coverUrl = (String) cover.get("source");
            }
            FacebookCompetitorMapping facebookCompetitorMapping = competitorMappingRepo.findByCompetitorIdAndEnterpriseId(profileResponse.getId(), businessNumber);
            Boolean isVerified = "blue_verified".equals(profileResponse.getVerification_status());

            CompetitorPageDetailResponse response = CompetitorPageDetailResponse.builder()
                    .profileImageUrl(profilePictureUrl)
                    .coverImageUrl(coverUrl)
                    .followerCount(profileResponse.getFollowers_count())
                    .isVerified(isVerified)
                    .pageName(profileResponse.getName())
                    .pageLink(profileResponse.getLink())
                    .description(profileResponse.getDescription())
                    .website(profileResponse.getWebsite())
                    .pagePresent(Objects.nonNull(facebookCompetitorMapping))
                    .build();

            return response;
        } catch (Exception e) {
            LOG.info("exception occurred while getting page summary for id: {}, error: {}", pageId, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }

    @Override
    public void proceedToUnmapPage(Long businessNumber) {
        List<FacebookCompetitorMapping> facebookCompetitorMappingList = competitorMappingRepo.findByEnterpriseId(businessNumber);
        if(CollectionUtils.isEmpty(facebookCompetitorMappingList)) {
            LOG.info("no facebook mapping found for : {}", businessNumber);
            return;
        }
        competitorMappingRepo.delete(facebookCompetitorMappingList);

        socialCompetitorHelperService.clearCache(SocialChannel.FACEBOOK, businessNumber);
        socialCompetitorHelperService.sendUpdateCacheEvent(SocialChannel.FACEBOOK, businessNumber);
    }
    @Override
    public Optional<Integer> fetchCompetitorProfileFollowerCount(CompetitorRequestDTO competitorRequestDTO) {
        LOG.info("Fetching Competitor Profile : {}",competitorRequestDTO);

        String accessToken = facebookPageService.getPPCASystemUserToken();
        if(StringUtils.isEmpty(accessToken)) {
            LOG.info("Unable to get system user token, exiting the flow!!");
            return Optional.empty();
        }
        String pageIdentifier = competitorRequestDTO.getPageId();
        try {
            LOG.info("Processing FB page with pageIdentifier: {}",pageIdentifier);
            FbPublicProfileInfo profileResponse = fbService.getPublicProfileInfo(accessToken, pageIdentifier);
            if(Objects.nonNull(profileResponse) && Objects.nonNull(profileResponse.getId())) {
                return Optional.of(profileResponse.getFollowers_count());
            } else {
                LOG.info("Page data not found for user: {}", pageIdentifier);
            }
        } catch (Exception e) {
            LOG.info("Exception while getting account info for FB page: {}",pageIdentifier, e);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, ErrorCodes.INTERNAL_SERVER_ERROR.name());
        }
        return Optional.empty();
    }
    @Override
    public Map<String, CompetitorPageDetails> getPageNameByPageId(List<String> pageIds){
        Map<String, CompetitorPageDetails> pageIdVsPageName = new HashMap<>();
        try {
            List<FacebookCompetitorInfo> facebookCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
            // Mapping pageIds with pageName
            facebookCompetitorInfoList.forEach(facebookCompetitorInfo ->
                    pageIdVsPageName.put(facebookCompetitorInfo.getPageId(),  CompetitorPageDetails.builder()
                            .pageName(StringUtils.isEmpty(facebookCompetitorInfo.getFullName()) ? facebookCompetitorInfo.getUserName() : facebookCompetitorInfo.getFullName())
                            .profilePictureUrl(facebookCompetitorInfo.getProfilePictureUrl())
                            .pageLink(facebookCompetitorInfo.getPageUrl())
                            .channel(SocialChannel.FACEBOOK.getName())
                            .pageId(facebookCompetitorInfo.getPageId())
                            .build()));
        }catch (Exception e){
            LOG.info("Exception while getting page name for pageIds: {} {}", pageIds, e.getMessage());
        }
        return pageIdVsPageName;
    }

    @Override
    public List<CompetitorPageDetailsDTO> getPageDetails(List<String> pageIds) {
        List<CompetitorPageDetailsDTO> response = new ArrayList<>();


        List<FacebookCompetitorInfo> facebookCompetitorInfoList = competitorInfoRepo.findByPageIdIn(pageIds);
        if(CollectionUtils.isEmpty(facebookCompetitorInfoList)) {
            return new ArrayList<>();
        }

        for(FacebookCompetitorInfo facebookCompetitorInfo: facebookCompetitorInfoList) {
            CompetitorPageDetailsDTO data =  CompetitorPageDetailsDTO.builder()
                    .pageName(facebookCompetitorInfo.getFullName())
                    .pageId(facebookCompetitorInfo.getPageId())
                    .pageProfileUrl(facebookCompetitorInfo.getProfilePictureUrl())
                    .build();
            response.add(data);
        }
        return response;
    }

    @Override
    public Integer getCompetitorAccounts(Long businessNumber) {
        Object listObject = socialCompetitorHelperService.getCompetitorListFromCache(SocialChannel.FACEBOOK, businessNumber);
        try {
            if (Objects.nonNull(listObject)) {
                List<FacebookCompetitorInfo> facebookCompetitorInfoFromCache = (List<FacebookCompetitorInfo>) listObject;
                return facebookCompetitorInfoFromCache.size();
            } else {
                List<FacebookCompetitorMapping> facebookCompetitorMappings = competitorMappingRepo.findByEnterpriseId(businessNumber);
                if (CollectionUtils.isEmpty(facebookCompetitorMappings)) {
                    LOG.info("empty mapping for channel facebook and business: {}", businessNumber);
                    return 0;
                }
                return facebookCompetitorMappings.size();

            }
        } catch (Exception e) {
            LOG.info("error occured while fetching pages for business: {}, error: {}", businessNumber, e.getMessage());
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
        }
    }
}
