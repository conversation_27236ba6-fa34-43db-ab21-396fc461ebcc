package com.birdeye.social.service;


import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.model.MentionData;

import java.util.List;

public interface SocialPostPublishInfoService {
    SocialPostPublishInfo findById(Integer publishInfoId);

    void save(SocialPostPublishInfo socialPostPublishInfo);

    void updateEnterpriseIdWhereEnterpriseId(Integer targetEnterpriseId, Integer sourceBusinessId,Integer targetBusinessId);

    void updateEnterpriseIdWhereEnterpriseIdAndApprovalIdIsNull(Integer targetEnterpriseId, Integer sourceBusinessId,Integer targetBusinessId);

    String getProfileData(String input, Integer businessId, List<MentionData> mentionData, Integer sourceId);
}
