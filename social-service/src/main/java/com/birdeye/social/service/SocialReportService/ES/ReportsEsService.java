package com.birdeye.social.service.SocialReportService.ES;

import com.birdeye.social.dto.EsPostDataPoint;
import com.birdeye.social.insights.*;
import com.birdeye.social.insights.ES.*;
import com.birdeye.social.insights.ES.Request.InsightsESRequest;
import com.birdeye.social.insights.ES.Request.InsightsEsRequestForCalendarPage;
import com.birdeye.social.insights.ES.Request.ESPageRequest;
import com.birdeye.social.insights.Facebook.ExternalAPIResponse.Data;
import com.birdeye.social.insights.Facebook.PostDataAndInsightResponse;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.model.ai_post.AiReasoningResponse;
import com.birdeye.social.model.ai_post.ReferencePostDetails;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import com.birdeye.social.trends.TrendsReportRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface ReportsEsService {

    PageInsightV2EsData getPageInsightDataFromEsChannelWise(InsightsESRequest request);

    PageInsightEsData getPageInsightDataFromEs(InsightsESRequest request);

    List<PageInsightDataPoint> getPageInsightHitsDataFromEs(InsightsESRequest request);

    Integer getSumEngagementOnPageId(InsightsESRequest request);

    Integer getSumImpressionOnPageId(InsightsESRequest request);

    Integer getSumClickCountOnPageId(InsightsESRequest request);

    PostInsightsDataPerPage getSumPostInsightsOnPageId(InsightsESRequest request);

    Integer getSumVideoViewsOnPageId(InsightsESRequest request);

    PostDataAndInsightResponse getPostDataFromEs(InsightsESRequest request, boolean excelDownload);

    void bulkPostPageInsights(List<ESPageRequest> esPageRequest,String index);

    Map<String, CalendarViewPagePostInsightsData> getCalendarViewPagePostDataFromEs(InsightsEsRequestForCalendarPage request);

    SearchRequest getCalendarPagePostInsightsResponse(List<String> pageIds, List<Integer> bePostIds, List<Integer> sourceIds);

    Map<Integer, Map<String, CalendarViewPagePostInsightsData>> getBulkCalendarViewPagePostDataFromEs(SearchRequest searchRequest);

    Map<String, List<CalendarViewPagePostInsightsData>> getCalendarViewPostDataFromEs(InsightsEsRequestForCalendarPage request);

    void bulkPostPagePostDataToES(List<EsPostDataPoint> esPostDataPoints, String index);

    Double calculateEngagementRate(Integer aggregatedEngagement, Integer aggregatedImpression);

    List<ESPageRequest> getPageDataForPageId(String pageId, Integer businessId, Date date, String index) throws IOException;

    List<EsPostDataPoint> getPostDataForPageId(String pageId, Integer businessId, String index,Integer sourceId) throws IOException;

    PageFollowersData getFollowersResponse(InsightsRequest insightsRequest) throws Exception;

    void updatePostCountForDates(InsightsESRequest request, PageInsightEsData pageInsightEsData, List<String> pageIds, Date queryStartDate) throws IOException;

    void updatePostCountForDates(InsightsESRequest request, PageInsightV2EsData pageInsightEsData, List<String> pageIds, Date queryStartDate) throws IOException;

    Date getFirstPostDate(InsightsRequest request, List<String> pageIds, Date queryStartDate) throws IOException;

    void bulkPageInsightsPostCountUpdate(List<ESPageRequest> esPageRequest, String index);

    List<PageLevelMetaData> mergeAndGetUpdatePageInsightList(List<PageLevelMetaData> pageLevelMetaDataList, Map<Date, Integer> postCountMap);

    void updatePageInsightsPostCount(PageInsights pageInsights, String index);

    String getStartDate(List<String> pageId,String index) throws IOException;

    long getTimeDifference(InsightsESRequest request);

    long getFeedTimeDifference(InsightsESRequest request);

    PageInsightsV2Response createResponseForPageInsight(Map<String, PageInsightV2EsData> map, InsightsRequest insightsRequest, int bucketSize);

    PageInsightV2EsData getTotalAudFromEsChannelWise(InsightsESRequest request, PageInsightV2EsData pageInsightDataFromEs);

    PerformanceSummaryResponse getPagePerformanceDataFromEsChannelWise(InsightsESRequest request);

    Integer getPostLinkClickCountTotal(InsightsESRequest request);

    Integer getPostOtherClickCountTotal(InsightsESRequest request);

    Map<String, Integer> getSummationOfFbStoryInsights(InsightsESRequest request);

    Integer prevLinkClickCount(InsightsESRequest request);

    Integer prevOtherClickCount(InsightsESRequest request);

    Integer prevPageStoryInsights(InsightsESRequest request, String insightName);

    PageInsightV2EsData getMessageVolumeFromEsChannelWise(InsightsESRequest request, PageInsightV2EsData pageInsightEsData);

    Map<Date, Integer> datevsClickCountMap (InsightsESRequest request);
    List<PagePostInsightsData> getDayWisePageInsightDataMapFromES(InsightsESRequest request);

    PageReportEsData getPageReportDataFromEsChannelWise(InsightsESRequest request);

    List<ProfilePerformanceExcelResponse> getProfileReportData(Map<String, PageReportEsData> map,InsightsRequest insightsRequest);

    BoolQueryBuilder prepareQueryToGetData(BackfillRequest backfillRequest,String pageId);

    List<ESPageRequest> getDataFromEsIndex(BoolQueryBuilder boolQueryBuilder, int size, String index);

    void bulkPostPageEngagementBreakDown(List<ESPageRequest> esPageDataPoints, String index);

    BoolQueryBuilder prepareQueryToGetCommentCount(String facebookPageId, BackfillInsightReq backfillInsightReq);

    SearchSourceBuilder prepareQueryForSeachSourceAndAggregate(BoolQueryBuilder queryBuilder);

    Map<String, Integer> getDataFromEs(SearchSourceBuilder sourceBuilder,String index);

    Map<String, Integer> getPostLikeCountFromFB(List<Data> facebookResponse);

    ExecutivePostDataResponse getPostData(InsightsRequest request, List<String> pageIds);

    SearchResponse getDataFromESPageIndex(LocationReportRequest insightsRequest, ReportSortingCriteria postSortingCriteria,
                                          SortOrder order, Integer startIndex, Integer pageSize, List<String> pageIds,String index);

    SearchResponse getDataFromESPostIndex(LocationReportRequest insightsRequest, ReportSortingCriteria postSortingCriteria,
                                          SortOrder order, Integer startIndex, Integer pageSize, List<String> pageIds, Integer sourceId);

    SearchResponse getDataFromESFollowers(LocationReportRequest insightsRequest, ReportSortingCriteria postSortingCriteria,
                                          SortOrder order, Integer startIndex, Integer pageSize, ArrayList<String> strings, String index);
    long getTimeDifference(TrendsReportRequest request);

    List<ESPageRequest> getPageWiseLatestData(String index, List<String> pageIds, Date from, Date end);

    Integer getPublishedPostData(String index, List<String> pageIds, Date from, Date end);

    List<ProfilePerformanceExcelResponse> getTiktokDemographicReportData(InsightsRequest insightsRequest,
                                                                         String channel);

    List<ProfilePerformanceExcelResponse> getChannelSpecificReportData(Map<String, PageReportEsData> map,InsightsRequest insightsRequest);

    ReferencePostViewPostInsightData getReferencePagePostDataFromEs(String postId, ReferencePostDetails referencePostDetails);
}