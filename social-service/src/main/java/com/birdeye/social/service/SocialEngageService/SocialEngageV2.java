package com.birdeye.social.service.SocialEngageService;

import com.birdeye.social.entities.EngageFeedDetails;
import com.birdeye.social.model.*;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.FbNotification.FreshPostNotificationRequest;
import com.birdeye.social.model.engageV2.EngageBusinessDetails;
import com.birdeye.social.model.engageV2.EngageCommentRequest;
import com.birdeye.social.model.engageV2.EngageWebhookSubscriptionRequest;
import com.birdeye.social.model.engageV2.PageDetailsData;
import com.birdeye.social.model.engageV2.message.ExternalServiceEvent;
import com.birdeye.social.model.engageV2.message.InboxMessageRequest;
import java.util.List;

public interface SocialEngageV2 {

    String channelName();

    SocialTimeline getFeedData(String pageId, EngageFeedDetails feedDetails, String type);
    List<EngageNotificationDetails> getFeedEngagement(String pageId);

    List<EngageNotificationDetails> getCommentData(EngageCommentRequest request);

    EngageNotificationDetails prepareAdditionalParamentToEs(EngageNotificationDetails request);

    Feed getPostDetails(FreshPostNotificationRequest request);

    void startBackFill(GenericScriptRequest scriptRequest);


    void saveMessages(InboxMessageRequest inboxMessageRequest);

    EngageBusinessDetails getChannelEnterpriseIdByPageId(String pageId);

    Feed getFeedDetails(String pageId, Feed feed);

    void hideWallPost(SocialEngageObjectRequest request);

    void hidePostComment(SocialEngageObjectRequest request);

    void blockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent);

    void unBlockUserFromPage(SocialEngageObjectRequest request, ExternalServiceEvent blockUserEvent);

    void likePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent);

    void unLikePageContent(SocialEngageObjectRequest request,ExternalServiceEvent externalServiceEvent);

    void commentPageContent(SocialEngageObjectRequest request) throws Exception;

    void deletePageContent(SocialEngageObjectRequest request) throws Exception;

    Boolean subscribeNotificationWebhook(String pageId);

    void unSubscribeNotificationWebhook(String pageId);

    void followUser(SocialEngageObjectRequest request);

    void unfollowUser(SocialEngageObjectRequest request);

    void shareComment(SocialEngageObjectRequest request);

    void unShareComment(SocialEngageObjectRequest request);

    void saveCommentInDbAndInES(SocialEngageObjectRequest request, EngageNotificationDetails documentFromEs);

    void likeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent);

    void unLikeMessage(SocialEngageObjectRequest request, ExternalServiceEvent externalServiceEvent);

    boolean isPageValid(String pageId);

    List<String> getChannelPageIdsByEnterpriseId(Long enterpriseId);

   PageDetailsData getChannelPageIdsByEnterpriseIds(List<Long> enterpriseId);

    void subscribeEngageNotificationWebhook(String pageId);

    void unSubscribeEngageNotificationWebhook(String pageId);

    void removePageNotificationWebhook(EngageWebhookSubscriptionRequest request);

    String getReviewerUrlByPageId(String pageId);

    EngagePageDetails getRawPageDetails(String pageId);

    boolean validateImageUrls(List<String> imageUrls);

    boolean validateCommentDetails(List<EngageNotificationDetails> comments, String commentId);
}

