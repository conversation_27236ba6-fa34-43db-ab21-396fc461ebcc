package com.birdeye.social.service;

import com.birdeye.social.model.ChannelPageRemoved;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.SocialChannelSyncRequest;
import com.birdeye.social.sro.SocialEsValidRequest;

import java.util.List;

public interface ISocialPlatformService {

    void addMapping(LocationPageMappingRequest locationPageMappingRequest, String channel);

    void removeMapping(List<LocationPageMappingRequest> input, String channel);

    void removePage(List<ChannelPageRemoved> input);

    void updatePageValidity(SocialEsValidRequest socialEsValidRequest);

    void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest);
}
