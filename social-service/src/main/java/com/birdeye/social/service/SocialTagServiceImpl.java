package com.birdeye.social.service;

import com.birdeye.social.AbstractSocialTagOperationService;
import com.birdeye.social.assetlibrary.SocialAssetLibraryDBService;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.constant.SocialTagOperation;
import com.birdeye.social.constant.SocialTagSortOption;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.SocialTagMappingInfo;
import com.birdeye.social.dto.assetlibrary.SocialAssetLibraryBizTagMappingInfo;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibrary;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.factory.SocialTagOperationServiceFactory;
import com.birdeye.social.service.calendar.SocialPostCalendarESService;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.SocialTagMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 21/12/23
 */
@Service
@Slf4j
public class SocialTagServiceImpl implements SocialTagService {

    @Autowired
    private SocialTagDBService socialTagDBService;

    @Autowired
    private SocialPostCalendarESService socialPostCalendarESService;

    @Autowired
    private SocialPostScheduleInfoRepo socialPostScheduleInfoRepo;

    @Autowired
    @Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
    private ThreadPoolTaskExecutor executor;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private SocialTagMapper socialTagMapper;

    @Autowired
    private SocialTagEventService socialTagEventService;

    @Autowired
    private SocialPostRepository socialPostRepo;

    @Autowired
    private SocialMasterPostRepository socialMasterPostRepo;

    @Autowired
    private EngageFeedDetailsRepo engageFeedDetailsRepo;

    @Autowired
    private PostLibMasterRepo postLibMasterRepo;

    @Autowired
    private SocialAssetLibraryDBService socialAssetLibraryDBService;

    @Autowired
    private IBusinessCoreService businessCoreService;
    @Autowired
    private SocialPostInfoRepository socialPostPublishInfoRepository;

    private static final String TAG_FETCH_DATE_FORMAT = "MMM dd, yyyy";
    private static final String PST_TIMEZONE_ID = "PST";

    private List<SocialTagOperation> ENTITY_TAG_MAPPING_ACCEPTED_OPERATIONS = Arrays.asList(SocialTagOperation.CREATE, SocialTagOperation.DELETE);

    @Override
    public SocialTagFetchAllResponse getAllTags(SocialTagFetchAllRequest socialTagFetchAllRequest, Integer accountId, String timezoneId,
                                                Integer startIndex, Integer pageSize, String sortBy, Integer sortOrder, Boolean liteVersion) {
        List<SocialTagFetchAllResponse.SocialTagFetchResponse> fetchTagResponses = getFilteredTags(socialTagFetchAllRequest, accountId, timezoneId, liteVersion);

        Comparator<SocialTagFetchAllResponse.SocialTagFetchResponse> comparator = getFetchAllTagComparator(sortBy, sortOrder);
        fetchTagResponses.sort(comparator);

        List<SocialTagFetchAllResponse.SocialTagFetchResponse> fetchTagPaginatedResponses = fetchPaginatedResponse(fetchTagResponses, startIndex, pageSize);
        return new SocialTagFetchAllResponse(CollectionUtils.size(fetchTagResponses), fetchTagPaginatedResponses);
    }

    private List<SocialTagFetchAllResponse.SocialTagFetchResponse> getFilteredTags(SocialTagFetchAllRequest socialTagFetchAllRequest, Integer accountId,
                                                                                   String timezoneId, Boolean liteVersion) {
        List<SocialTag> allSocialTags = socialTagDBService.findAllSocialTagsByAccountId(accountId);
        List<SocialTag> filteredSocialTags = allSocialTags;
        if (Objects.nonNull(socialTagFetchAllRequest) && Objects.nonNull(socialTagFetchAllRequest.getNameSearch()) && StringUtils.isNotBlank(socialTagFetchAllRequest.getNameSearch().getQuery())) {
            SocialTagFetchAllRequest.TagNameSearchFilter tagNameSearchFilter = socialTagFetchAllRequest.getNameSearch();
            String searchText = StringUtils.lowerCase(tagNameSearchFilter.getQuery());
            Boolean exactMatch = tagNameSearchFilter.getExactMatch();
            if (BooleanUtils.isTrue(exactMatch)) {
                filteredSocialTags = allSocialTags.stream().filter(tag -> StringUtils.equalsIgnoreCase(tag.getName(), searchText)).collect(Collectors.toList());
            } else {
                String[] splitSearchText = StringUtils.split(searchText);
                filteredSocialTags = allSocialTags.stream().filter(tag -> StringUtils.containsAny(StringUtils.lowerCase(tag.getName()), splitSearchText))
                        .collect(Collectors.toList());
            }
        }
        List<SocialTagFetchAllResponse.SocialTagFetchResponse> fetchTagResponses =
                filteredSocialTags.stream().map(tag -> convert(tag, timezoneId, liteVersion)).collect(Collectors.toList());
        return fetchTagResponses;
    }

//    @Override
//    public List<SocialTagOperationResponse> performTagOperations(List<SocialTagOperationRequest> tagOperationRequests, Integer accountId, Long accountNum, Long userId, Boolean throwError) {
//        // For UI facing API we need error to be thrown right away
//        return performTagOperations(tagOperationRequests, accountId, accountNum, userId, true);
//    }

    @Override
    public List<SocialTagOperationResponse> performTagOperations(List<SocialTagOperationRequest> tagOperationRequests, Integer accountId, Long accountNum, Long userId, boolean throwError) {
        if (CollectionUtils.isEmpty(tagOperationRequests)) {
            return Collections.emptyList();
        }
        // Validate the request before proceeding
        validateSocialTagOperationRequest(accountId, tagOperationRequests);

        List<Future<SocialTagOperationResponse>> socialTagOperationResponseFutures = new LinkedList<>();
        for (SocialTagOperationRequest tagOperationRequest : tagOperationRequests) {
            Set<AbstractSocialTagOperation.SocialTagOperationDetail> tags = tagOperationRequest.getTags();
            if (CollectionUtils.isEmpty(tags)) {
                continue;
            }
            SocialTagOperation operation = tagOperationRequest.getOperation();
            AbstractSocialTagOperationService tagOperationService = SocialTagOperationServiceFactory.byOperation(operation);
            // This is done so that in case of all type of operations are performed at
            // once, we can do them in parallel
            Future<SocialTagOperationResponse> tagOperationResponseFuture =
                    executor.submit(() -> tagOperationService.performOperation(accountId, accountNum, userId, tags, throwError));
            socialTagOperationResponseFutures.add(tagOperationResponseFuture);
        }

        List<SocialTagOperationResponse> tagOperationResponses = new LinkedList<>();
        Boolean isTagOperationExecuted = false;
        for (Future<SocialTagOperationResponse> socialTagOperationResponseFuture : socialTagOperationResponseFutures) {
            try {
                SocialTagOperationResponse socialTagOperationResponse = socialTagOperationResponseFuture.get(10, TimeUnit.SECONDS);
                isTagOperationExecuted = isTagOperationExecuted || socialTagOperationResponse.getIsOperationExecuted();
                tagOperationResponses.add(socialTagOperationResponse);
                log.info("[SocialTagServiceImpl] performTagOperations fetched SUCCESSFULLY for operation :{} and account:{}",
                        socialTagOperationResponse.getOperation(), accountId);
            }  catch (Exception e) {
                String errorMessage = Objects.nonNull(e.getCause()) ? e.getCause().getMessage() : e.getMessage();
                log.error("[SocialTagServiceImpl] performTagOperations failed for accountId:{} with error:{}", accountId, errorMessage);
                if(throwError){
                    if(Objects.isNull(errorMessage))
                        throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, "Something went wrong");
                    else {
                        throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, errorMessage);
                    }
                }
            }
        }
        // Evict the account level created cache for all tags for this account
        // Only if tag operation(s) are actually executed
        if (isTagOperationExecuted) {
            evictAllTagsCacheByAccountId(accountId);
        }
        return tagOperationResponses;
    }

    private void updateEditorInfoPostLib(Long entityId, Long userId) {
        PostLibMaster postLibMaster = postLibMasterRepo.findById(Math.toIntExact(entityId));
        if (Objects.nonNull(postLibMaster)) {
            postLibMaster.setLastEditedAt(new Date());
            postLibMaster.setEditedBy(Math.toIntExact(userId));
            postLibMasterRepo.saveAndFlush(postLibMaster);
        }
    }

    @Override
    public void performTagEntityMappingOperations(SocialTagEntityMappingRequest socialTagEntityMappingRequest, SocialTagEntityType entityType,
                                                  Long entityId, Integer accountId, Long userId, Long businessNumber, Boolean isFreshRequest) {

        if (Objects.isNull(entityId)) {
            log.error("[SocialTagServiceImpl] performTagEntityMappingOperations cannot proceed for a NULL entityId");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Non existent " + entityType + " Cannot be mapped with a tag");
        }
        // Validate the entity before proceeding to tag mapping
        validateEntity(entityId, entityType);

        Set<SocialTagMappingOperationRequest> tagMappings = socialTagEntityMappingRequest.getTagMappings();
        if (CollectionUtils.isEmpty(tagMappings)) {
            log.info("[SocialTagServiceImpl] performTagEntityMappingOperations cannot proceed for empty tag mappings for accountId:{}", tagMappings);
            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Invalid Tag Mapping request");
        }

        // Validate the tag-mapping action
        validateEntityBizTagMappingOperations(entityId, entityType, tagMappings);

        Map<Long, SocialTag> allExistingTagIdToSocialTagMap = socialTagDBService.getTagIdToSocialTagMap(accountId);
        // If there exists no TAG(s) for this account there is no point of any action
        // over the tags itself.
        if (MapUtils.isEmpty(allExistingTagIdToSocialTagMap)) {
            log.info("[SocialTagServiceImpl] performTagEntityMappingOperations cannot proceed for no existing tags for accountId:{}", accountId);
            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Kindly create a tag before proceeding to map it to the " + entityType);
        }

        List<SocialTagMappingInfo> formerTagMappingInfos = socialTagDBService.findTagMappingInfoByEntityIdsAndEntityType(Collections.singletonList(entityId), entityType);
        Set<Long> formerMappedTagIds = formerTagMappingInfos.stream().map(SocialTagMappingInfo::getTagId).collect(Collectors.toSet());

        // This is used to compute the overall tagIds mapped to the entity
        Set<Long> overallEntityMappedTagIds = new HashSet<>(formerMappedTagIds);

        boolean isActionExecuted = false;
        for (SocialTagMappingOperationRequest tagMapping : tagMappings) {
            SocialTagOperation tagOperation = tagMapping.getOperation();
            Set<Long> toBeOperatedOnTagIds = tagMapping.getTagIds();
            if (CollectionUtils.isEmpty(toBeOperatedOnTagIds)) {
                continue;
            }
            // Filter the thus passed tagIds whether these exist for this account or NOT
            toBeOperatedOnTagIds = toBeOperatedOnTagIds.stream().filter(allExistingTagIdToSocialTagMap::containsKey).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(toBeOperatedOnTagIds)) {
                continue;
            }

            switch (tagOperation) {
                case CREATE:
                    // This ensures that we only create the newly mapped tags and
                    // NOT duplicate the mapping
                    toBeOperatedOnTagIds = new HashSet<>(CollectionUtils.removeAll(toBeOperatedOnTagIds, formerMappedTagIds));
                    boolean isEligible4TagMappingCreation = CollectionUtils.isNotEmpty(toBeOperatedOnTagIds);
                    if (isEligible4TagMappingCreation) {
                        createEntityTagMapping(accountId, entityId, entityType, toBeOperatedOnTagIds, userId);
                    }
                    // Adding all the newly created tag IDs to know the new state of mapping
                    overallEntityMappedTagIds.addAll(toBeOperatedOnTagIds);
                    isActionExecuted = isActionExecuted || isEligible4TagMappingCreation;
                    socialTagDBService.clearSocialTagsCacheForAccount(accountId);
                    break;
                case UPDATE:
                    throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, tagOperation + " operation cannot be performed for tag mapping");
                case DELETE:
                    // If none of the operable tagIds were mapped onto this entity then
                    // no point deleting either
                    boolean isEligible4TagMappingDeletion = CollectionUtils.containsAny(toBeOperatedOnTagIds, formerMappedTagIds);
                    if (isEligible4TagMappingDeletion) {
                        deleteEntityTagMapping(entityId, entityType, toBeOperatedOnTagIds);
                    }
                    // Removing all the newly deleted tag IDs to know the new state of mapping
                    overallEntityMappedTagIds.removeAll(toBeOperatedOnTagIds);
                    isActionExecuted = isActionExecuted || isEligible4TagMappingDeletion;
                    socialTagDBService.clearSocialTagsCacheForAccount(accountId);
                    break;
            }
            try {
                boolean isPostFlag = socialPostCalendarESService.isPostFlag(accountId);
                if(isPostFlag) {
                    log.info("calendarRevampBusinessIds found so staring saving tags data on ES");
                    CompletableFuture<Boolean> updateTagsOnES = CompletableFuture.supplyAsync(() -> {
                        List<Integer> socialPostScheduleInfoIds = socialPostScheduleInfoRepo.findIdsBySocialPostId(Math.toIntExact(entityId));
                        if (CollectionUtils.isNotEmpty(socialPostScheduleInfoIds) || SocialTagEntityType.AI_POST.equals(entityType) || SocialTagEntityType.DRAFT.equals(entityType)) {
                            socialPostCalendarESService.updateTagMappings(overallEntityMappedTagIds,
                                    allExistingTagIdToSocialTagMap,
                                    entityId, socialPostScheduleInfoIds,entityType);
                            return Boolean.TRUE;
                        }
                        return Boolean.FALSE;
                    });
                    CompletableFuture<Void> integrationCompletionFuture = CompletableFuture.allOf(updateTagsOnES);
                    integrationCompletionFuture.get(100, TimeUnit.SECONDS);
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.info("Exception occurred while updating record on ES for post Id {} :: {}", entityId, e.getMessage(), e);
            }
        }
        if (isActionExecuted) {
            publishTagEntityMappingActionEvent(accountId, entityId, entityType, userId, businessNumber, isFreshRequest);
            if(SocialTagEntityType.POST_LIB.equals(entityType) && Boolean.TRUE.equals(isFreshRequest)) {
                updateEditorInfoPostLib(entityId,userId);
            }
        }
    }

    private void validateEntity(Long entityId, SocialTagEntityType entityType) {
        switch (entityType) {
            case POST:
                if (!socialPostRepo.exists(Math.toIntExact(entityId))) {
                    throw new BirdeyeSocialException(ErrorCodes.POST_DNE, "Non existent Post cannot be mapped with a Tag.");
                }
                break;
            case DRAFT:
                if (!socialMasterPostRepo.exists(Math.toIntExact(entityId))) {
                    throw new BirdeyeSocialException(ErrorCodes.DRAFT_DNE, "Non existent Draft cannot be mapped with a Tag.");
                }
                break;
            case POST_LIB:
                if (!postLibMasterRepo.exists(Math.toIntExact(entityId))) {
                    throw new BirdeyeSocialException(ErrorCodes.POST_LIB_DNE, "Non existent Post library cannot be mapped with a Tag.");
                }
                break;
            case ENGAGE:
                if (!engageFeedDetailsRepo.exists(Math.toIntExact(entityId))) {
                    throw new BirdeyeSocialException(ErrorCodes.ENGAGE_DNE, "Non existent Engage cannot be mapped with a Tag.");
                }
                break;
            case ASSET:
                List<SocialAssetLibrary> socialAssets = socialAssetLibraryDBService.getActiveAssetByAssetIds(Collections.singletonList(entityId));
                if (CollectionUtils.isEmpty(socialAssets)) {
                    throw new BirdeyeSocialException(ErrorCodes.ASSET_LIB_ASSET_DNE, "Non existent Asset cannot be mapped with a Tag.");
                }
                break;
            case AI_POST:
                if(!postLibMasterRepo.exists(Math.toIntExact(entityId))) {
                    throw new BirdeyeSocialException(ErrorCodes.AI_POST_DNE, "Non existent Engage cannot be mapped with a Tag.");
                }
                break;
        }
    }

    private void publishTagEntityMappingActionEvent(Integer accountId, Long entityId, SocialTagEntityType entityType, Long userId, Long businessNumber, Boolean isFreshRequest) {
        SocialTagEntityMappingActionEvent actionEvent = new SocialTagEntityMappingActionEvent();
        actionEvent.setAccountId(accountId);
        actionEvent.setBusinessNumber(businessNumber);
        actionEvent.setEntityId(entityId);
        actionEvent.setEntityType(entityType);
        actionEvent.setBy(userId);
        actionEvent.setAt(System.currentTimeMillis());
        actionEvent.setIsFreshRequest(isFreshRequest);
        socialTagEventService.publishTagEntityMappingActionEvent(entityId, actionEvent, entityType);
    }

    private void deleteEntityTagMapping(Long entityId, SocialTagEntityType entityType, Set<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            log.info("[SocialTagServiceImpl] deleteEntityTagMapping cannot proceed for empty tagIds");
            return;
        }
        socialTagDBService.deleteByEntityIdsEntityTypeAndTagIds(Collections.singletonList(entityId), entityType, tagIds);
        log.info("[SocialTagServiceImpl] deleteEntityTagMapping executed successfully for entityId:{} entityType:{} and tagIds:{}", entityId, entityType, tagIds);
    }

    private void createEntityTagMapping(Integer accountId, Long entityId, SocialTagEntityType entityType, Set<Long> tagIds, Long userId) {
        if (CollectionUtils.isEmpty(tagIds)) {
            log.info("[SocialTagServiceImpl] createEntityTagMapping cannot proceed for empty tagIds");
            return;
        }
        List<SocialTagMapping> socialTagMappings = new LinkedList<>();
        tagIds.forEach(tagId -> {
            SocialTagMapping socialTagMapping = new SocialTagMapping();
            socialTagMapping.setEntityId(entityId);
            socialTagMapping.setEntityType(entityType);
            socialTagMapping.setTagId(tagId);
            socialTagMapping.setAccountId(accountId);
            socialTagMapping.setCreatedBy(userId);
            socialTagMapping.setUpdatedBy(userId);
            socialTagMappings.add(socialTagMapping);

        });
        socialTagDBService.saveAllAndFlushSocialTagMappings(socialTagMappings);
        log.info("[SocialTagServiceImpl] createEntityTagMapping successful for entityId:{} entityType:{} and tagIds:{}", entityId, entityType, tagIds);
    }

    private void validateEntityBizTagMappingOperations(Long entityId, SocialTagEntityType entityType, Set<SocialTagMappingOperationRequest> tagMappings) {
        Optional<SocialTagOperation> invalidTagMappingOperation =
                tagMappings.stream().map(SocialTagMappingOperationRequest::getOperation)
                        .filter(not(ENTITY_TAG_MAPPING_ACCEPTED_OPERATIONS::contains)).findFirst();
        if (invalidTagMappingOperation.isPresent()) {
            SocialTagOperation invalidTagOperation = invalidTagMappingOperation.get();
            log.error("[SocialTagServiceImpl] validateEntityBizTagMappingOperations cannot proceed for a :{} tag-operation for entityId:{}",
                    invalidTagOperation, entityId);
            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, invalidTagOperation + " " + entityType + " tag mapping operation cannot be performed");
        }
    }

    public static <T> Predicate<T> not(Predicate<T> t) {
        return t.negate();
    }


    private List<SocialTagMappingInfo> findTagMappingInfoByEntityIdsAndEntityType(Collection<Long> entityIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isEmpty(entityIds)) {
            return Collections.emptyList();
        }
        return socialTagDBService.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);
    }
    @Override
    public Map<Long, List<SocialTagBasicDetail>> getEntityIdToBasicTagDetailListMap(Collection<Long> entityIds, SocialTagEntityType entityType) {
        List<SocialTagMappingInfo> tagMappingInfoList = findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);

        return tagMappingInfoList
                .stream()
                .collect(Collectors.groupingBy(SocialTagMappingInfo::getEntityId, Collectors.mapping(socialTagMapper::convertTagMappingInfoToBasicTagDetails, Collectors.toList())));
    }

    @Override
    public Map<Long, List<SocialTagBasicDetail>> getEntityIdToBasicTagDetailListMap(Collection<Long> entityIds, List<SocialTagEntityType> entityType) {
        List<SocialTagMappingInfo> tagMappingInfoList = socialTagDBService.findTagMappingInfoByEntityIdsAndEntityType(entityIds, entityType);

        return tagMappingInfoList
                .stream()
                .collect(Collectors.groupingBy(SocialTagMappingInfo::getEntityId, Collectors.mapping(socialTagMapper::convertTagMappingInfoToBasicTagDetails, Collectors.toList())));

    }

    @Override
    public List<SocialTagBasicDetail> getBasicTagDetailForSingleEntityId(Long entityId, SocialTagEntityType entityType) {
        Map<Long, List<SocialTagBasicDetail>> basicDetailMap = getEntityIdToBasicTagDetailListMap(Collections.singletonList(entityId),
                entityType);
        if(MapUtils.isNotEmpty(basicDetailMap) && basicDetailMap.containsKey(entityId)) {
            return basicDetailMap.get(entityId);
        }
        return null;
    }

    @Override
    public Map<Long, SocialTagBasicDetail> getTagIdsBasicDetailsFilteredOnEnterpriseId(Set<Long> tagIds, Integer enterpriseId) {
        if(CollectionUtils.isEmpty(tagIds)) return new HashMap<>();
//        List<SocialTagRepository.TagBasicDetails> basicDetails = socialTagDBService.findByTagIdInAndAccountId(tagIds, enterpriseId);
        List<SocialTag> socialTagList = socialTagDBService.findAllSocialTagsByAccountId(enterpriseId);

        if(CollectionUtils.isEmpty(socialTagList)) return new HashMap<>();
        List<SocialTagBasicDetail> basicTagDetails = socialTagList.stream().filter(s->tagIds.contains(s.getId()))
                .map(s->new SocialTagBasicDetail(s.getId(), s.getName()))
                .collect(Collectors.toList());
        return basicTagDetails.stream().collect(Collectors.toMap(SocialTagBasicDetail::getId, Function.identity()));
    }

    @Override
    public void performTagEntityMappingBulkOperations(SocialTagEntityBulkMappingRequest socialTagEntityBulkMappingRequest, SocialTagEntityType entityType,
                                                      Integer accountId, Long userId, Long businessNumber) {
        if (Objects.nonNull(socialTagEntityBulkMappingRequest) && CollectionUtils.isNotEmpty(socialTagEntityBulkMappingRequest.getEntityIds()) && CollectionUtils.isNotEmpty(socialTagEntityBulkMappingRequest.getTagMappings())) {
            // Publish events for processing in async for bulk tag mapping action
            socialTagEntityBulkMappingRequest.getEntityIds().forEach(entityId -> {
                log.info("[SocialTagServiceImpl] performTagEntityMappingBulkOperations publishing an event for entityId:{}", entityId);
                socialTagEventService.publishTagBulkEntityMappingActionEvent(entityId, entityType, socialTagEntityBulkMappingRequest, accountId, userId, businessNumber);
            });
        }
    }


    @Override
    public void performTagEntityMappingBulkOperationsTemp(SocialTagEntityBulkMappingRequest socialTagEntityBulkMappingRequest, SocialTagEntityType entityType, Integer accountId, Long userId, Long businessNumber) {
        if (Objects.nonNull(socialTagEntityBulkMappingRequest) && CollectionUtils.isNotEmpty(socialTagEntityBulkMappingRequest.getEntityIds()) && CollectionUtils.isNotEmpty(socialTagEntityBulkMappingRequest.getTagMappings())) {
            // Publish events for processing in async for bulk tag mapping action
            socialTagEntityBulkMappingRequest.getEntityIds().forEach(entityId -> {
                log.info("[SocialTagServiceImpl] performTagEntityMappingBulkOperations publishing an event for entityId:{}", entityId);
                socialTagEventService.publishTagBulkEntityMappingActionEventTemp(entityId, entityType, socialTagEntityBulkMappingRequest, accountId, userId, businessNumber);
            });
        }
    }

    @Override
    public void deleteAllTagEntityMapping(Long entityId, SocialTagEntityType entityType, Integer accountId) {
        if (Objects.isNull(entityId)) {
            log.error("[SocialTagServiceImpl] deleteAllTagEntityMapping cannot proceed for a NULL entityId");
            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Non existent " + entityType + " Cannot be mapped with a tag");
        }
        if(SocialTagEntityType.POST.equals(entityType)) {
            List<SocialPostPublishInfo> publishInfoList = socialPostPublishInfoRepository.findBySocialPostId(Math.toIntExact(entityId));
            if(CollectionUtils.isNotEmpty(publishInfoList)) {
                log.info("Post is not completely deleted, exiting the flow!!");
                return;
            }
        }

        Map<Long, SocialTag> allExistingTagIdToSocialTagMap = socialTagDBService.getTagIdToSocialTagMap(accountId);
        // If there exists no TAG(s) for this account there is no point of any action
        // over the tags itself.
        if (MapUtils.isEmpty(allExistingTagIdToSocialTagMap)) {
            log.info("[SocialTagServiceImpl] deleteAllTagEntityMapping cannot proceed for no existing tags for accountId:{}", accountId);
            return;
        }

        socialTagDBService.deleteByEntityIdsEntityTypeAndAccountId(Collections.singletonList(entityId), entityType, accountId);
    }

    @Override
    public Boolean isUntaggedRequest(Collection<Long> tagIds) {
        if(CollectionUtils.isEmpty(tagIds)) return false;
        return tagIds.contains(-1l);
    }

    @Override
    public List<Long> findEntityIdByEntityTypeAndTagIdIn(Collection<Long> tagIds, SocialTagEntityType entityType) {
        if (CollectionUtils.isNotEmpty(tagIds)) {
            return socialTagDBService.findEntityIdByEntityTypeAndTagIdIn(tagIds, entityType);
        }
        return Collections.emptyList();
    }

    @Override
    public List<Long> findEntityIdByEntityTypeAndAccountId(Integer accountId, SocialTagEntityType entityType) {
        return  socialTagDBService.findEntityIdByEntityTypeAndAccountId(accountId, entityType);
    }

    @Override
    public void evictAllTagsCacheByAccountId(Integer accountId) {
        cacheService.clearCacheByKey(Constants.ACCOUNT_ALL_SOCIAL_TAGS_CACHE, String.valueOf(accountId));
    }

    @Override
    public void migrateAssetLibraryTagAsSocialTag() {
        // Find ALL the distinct accounts where we need to perform the migration
        List<Integer> allAccountsWithTagMapping = socialAssetLibraryDBService.findDistinctAccountsWithTagMapping();
        if (CollectionUtils.isNotEmpty(allAccountsWithTagMapping)) {
            allAccountsWithTagMapping.forEach(accountId -> socialTagEventService.publishAssetLibTagMappingMigrationEvent(accountId));
        }
        log.info("[SocialTagServiceImpl] migrateAssetLibraryTagAsSocialTag pushed events for {} accounts to migrate", CollectionUtils.size(allAccountsWithTagMapping));
    }

    @Override
    public void migrateAssetLibraryTagAsSocialTag(Integer accountId) {
        BusinessLiteDTO business = businessCoreService.getBusinessLite(accountId, false);
        if (Objects.isNull(business)) {
            log.info("[SocialAssetLibraryServiceImpl] migrateLegacyTemplates cannot proceed for a non-existent business:{}", accountId);
            return;
        }
        Long accountNumber = business.getBusinessNumber();
        List<SocialAssetLibraryBizTagMappingInfo> socialAssetLibraryBizTagMappingInfos = socialAssetLibraryDBService.findTagAndMappingInfoByAccountId(accountId);
        if (CollectionUtils.isEmpty(socialAssetLibraryBizTagMappingInfos)) {
            log.info("[SocialTagServiceImpl] migrateAssetLibraryTagAsSocialTag NOT proceeding for empty mapping");
            return;
        }

        Map<Long, List<SocialAssetLibraryBizTagMappingInfo>> tagUpdatedByToTagMappingInfoMap = new HashMap<>();
        Map<String, List<SocialAssetLibraryBizTagMappingInfo>> tagNameToTagMappingInfoMap = new HashMap<>();
        for (SocialAssetLibraryBizTagMappingInfo socialAssetLibraryBizTagMappingInfo : socialAssetLibraryBizTagMappingInfos) {
            Long tagUpdatedBy = socialAssetLibraryBizTagMappingInfo.getTagUpdatedBy();
            String tagName = StringUtils.trim(socialAssetLibraryBizTagMappingInfo.getTagName());

            List<SocialAssetLibraryBizTagMappingInfo> tagMappingInfosByTagName = tagNameToTagMappingInfoMap.get(tagName);
            List<SocialAssetLibraryBizTagMappingInfo> tagMappingInfosByUpdatedBy = tagUpdatedByToTagMappingInfoMap.get(tagUpdatedBy);
            // If this key DNE, create a new entry
            if (CollectionUtils.isEmpty(tagMappingInfosByUpdatedBy)) {
                tagMappingInfosByUpdatedBy = new LinkedList<>();
            }
            tagMappingInfosByUpdatedBy.add(socialAssetLibraryBizTagMappingInfo);
            tagUpdatedByToTagMappingInfoMap.put(tagUpdatedBy, tagMappingInfosByUpdatedBy);

            // If this key DNE, create a new entry
            if (CollectionUtils.isEmpty(tagMappingInfosByTagName)) {
                tagMappingInfosByTagName = new LinkedList<>();
            }
            tagMappingInfosByTagName.add(socialAssetLibraryBizTagMappingInfo);
            tagNameToTagMappingInfoMap.put(tagName, tagMappingInfosByTagName);
        }
        tagUpdatedByToTagMappingInfoMap.forEach((userId, tagMappingInfos) -> {
            // Set of tagNames is populated to ensure unique tag names are ONLY
            // propagated for creation
            Set<AbstractSocialTagOperation.SocialTagOperationDetail> socialTagOperationDetails =
                    tagMappingInfos.stream().map(SocialAssetLibraryBizTagMappingInfo::getTagName).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).stream()
                            .map(this::convert).collect(Collectors.toSet());
            SocialTagOperationRequest socialTagOperationRequest = new SocialTagOperationRequest();
            if (CollectionUtils.isEmpty(socialTagOperationDetails)) {
                return;
            }
            socialTagOperationRequest.setOperation(SocialTagOperation.CREATE);
            socialTagOperationRequest.setTags(socialTagOperationDetails);
            // While migration, we do not need error to be thrown for duplicate tag creation
            List<SocialTagOperationResponse> socialTagOperationResponses =
                    performTagOperations(Collections.singletonList(socialTagOperationRequest), accountId, accountNumber, userId, false);
            if (CollectionUtils.isNotEmpty(socialTagOperationResponses)) {
                Map<String, Long> tagNameToSocialTagIdMap =
                        socialTagOperationResponses.stream().filter(tor -> Objects.equals(tor.getOperation(), SocialTagOperation.CREATE)).flatMap(stor -> stor.getTags().stream()).collect(Collectors.toMap(AbstractSocialTagOperation.SocialTagOperationDetail::getName, AbstractSocialTagOperation.SocialTagOperationDetail::getId, (first, second) -> second));
                tagNameToSocialTagIdMap.forEach((tagName, tagId) -> {
                    List<SocialAssetLibraryBizTagMappingInfo> assetTagMappingInfos = tagNameToTagMappingInfoMap.get(tagName);
                    if (CollectionUtils.isNotEmpty(assetTagMappingInfos)) {
                        assetTagMappingInfos.forEach(assetTagMappingInfo -> {
                            Long assetId = assetTagMappingInfo.getAssetId();
                            Long tagMappingUpdatedBy = assetTagMappingInfo.getTagMappingUpdatedBy();

                            // We are creating it as a bulk request to allow all the
                            // servers to take part of this migration by throwing an
                            // event for each mapping request
                            SocialTagEntityBulkMappingRequest socialTagEntityBulkMappingRequest = new SocialTagEntityBulkMappingRequest();
                            socialTagEntityBulkMappingRequest.setEntityIds(new HashSet<>(Collections.singletonList(assetId)));

                            SocialTagMappingOperationRequest socialTagMappingOperationRequest = new SocialTagMappingOperationRequest();
                            socialTagMappingOperationRequest.setOperation(SocialTagOperation.CREATE);
                            socialTagMappingOperationRequest.setTagIds(new HashSet<>(Collections.singletonList(tagId)));
                            socialTagEntityBulkMappingRequest.setTagMappings(new HashSet<>(Arrays.asList(socialTagMappingOperationRequest)));

                            performTagEntityMappingBulkOperations(socialTagEntityBulkMappingRequest, SocialTagEntityType.ASSET, accountId, tagMappingUpdatedBy, business.getBusinessNumber());
                        });
                    }
                });
            }
        });
    }

    private AbstractSocialTagOperation.SocialTagOperationDetail convert(String tagName) {
        AbstractSocialTagOperation.SocialTagOperationDetail socialTagOperationDetail = new AbstractSocialTagOperation.SocialTagOperationDetail();
        socialTagOperationDetail.setName(StringUtils.trim(tagName));
        return socialTagOperationDetail;
    }

    private void validateSocialTagOperationRequest(Integer accountId, List<SocialTagOperationRequest> tagOperationRequests) {
        // We cannot have the same Tags IDs to be passed in any other operation, it
        // should be unique across all operations
        Set<Long> alreadyParsedTagIds = new HashSet<>();
        // We cannot have the tagName duplicated for any operation and even within a
        // single operation either
        Set<String> alreadyParsedTagName = new HashSet<>();

        for (SocialTagOperationRequest tagOperationRequest : tagOperationRequests) {
            Set<AbstractSocialTagOperation.SocialTagOperationDetail> tagDetails = tagOperationRequest.getTags();
            if (CollectionUtils.isNotEmpty(tagDetails)) {
                for (AbstractSocialTagOperation.SocialTagOperationDetail tagDetail : tagDetails) {
                    Long tagId = tagDetail.getId();
                    String tagName = StringUtils.trim(StringUtils.lowerCase(tagDetail.getName()));
                    SocialTagOperation operation = tagOperationRequest.getOperation();

                    if (Objects.isNull(tagId) && !Objects.equals(operation, SocialTagOperation.CREATE)) {
                        throw new BirdeyeSocialException(ErrorCodes.SOCIAL_TAG_DNE, "Invalid Tag ID provided");
                    }
                    // every encountered tagId in each operation and within each
                    // operation should always be unique. If not, it is a corrupted request
                    if (Objects.nonNull(tagId) && alreadyParsedTagIds.contains(tagId)) {
                        log.error("[SocialTagServiceImpl] validateBusinessTagOperationRequest cannot be performed for accountId:{} with action:{} "
                                + "for duplicate tagId:{}", accountId, operation, tagId);
                        throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Unique operation can only be performed at once for every Tag");
                    }

                    if (!Objects.equals(operation, SocialTagOperation.DELETE)) {
                        if (StringUtils.isBlank(tagName)) {
                            log.error("[SocialTagServiceImpl] validateBusinessTagOperationRequest cannot be performed for accountId:{} with action:{}"
                                    + " for empty tagName", accountId, operation);
                            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, operation + " operation can only be performed for non-empty Tag(s)");
                        }

                        if (StringUtils.length(tagName) > Constants.SOCIAL_TAG_NAME_MAX_LENGTH) {
                            log.error("[SocialTagServiceImpl] validateBusinessTagOperationRequest cannot be performed for accountId:{} with action:{}"
                                    + " for too long tagName:{} ", accountId, operation, tagName);
                            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, operation + " operation cannot be performed for too long Tag name(s)");
                        }

                        if (alreadyParsedTagName.contains(tagName)) {
                            log.error("[SocialTagServiceImpl] validateBusinessTagOperationRequest cannot be performed for accountId:{} with action:{}"
                                    + " for duplicate tagName:{}", accountId, operation, tagName);
                            throw new BirdeyeSocialException(ErrorCodes.INVALID_TAG_REQUEST, "Provide a unique Tag name for all operations");
                        }
                    }

                    alreadyParsedTagIds.add(tagId);
                    alreadyParsedTagName.add(tagName);
                }
            }
        }
    }

    private SocialTagFetchAllResponse.SocialTagFetchResponse convert(SocialTag socialTag, String timezoneId, Boolean liteVersion) {
        SocialTagFetchAllResponse.SocialTagFetchResponse fetchTagResponse = new SocialTagFetchAllResponse.SocialTagFetchResponse();
        fetchTagResponse.setId(socialTag.getId());
        fetchTagResponse.setName(StringUtils.trim(socialTag.getName()));


        Date createdAt = socialTag.getCreatedAt();
        fetchTagResponse.setCreatedAtDt(createdAt);
        // If it is not a liteVersion API, then only populate the extra fields
        if (!liteVersion) {
            fetchTagResponse.setCreatedAt(getTagFetchFormattedDate(createdAt, timezoneId));
            //TODO: Usage Count is yet to be decided
        }

        return fetchTagResponse;
    }

    private String getTagFetchFormattedDate(Date date, String timezoneId) {
        timezoneId = StringUtils.isBlank(timezoneId) ? PST_TIMEZONE_ID : timezoneId;
        SimpleDateFormat sdf = new SimpleDateFormat(TAG_FETCH_DATE_FORMAT);
        sdf.setTimeZone(TimeZone.getTimeZone(timezoneId));
        return sdf.format(date);
    }

    private Comparator<SocialTagFetchAllResponse.SocialTagFetchResponse> getFetchAllTagComparator(String sortBy, Integer sortOrder) {
        SocialTagSortOption sortByOption = SocialTagSortOption.byName(sortBy);
        // Default Comparator on tag creation date
        Comparator<SocialTagFetchAllResponse.SocialTagFetchResponse> comparator =
                Comparator.comparing(SocialTagFetchAllResponse.SocialTagFetchResponse::getCreatedAtDt);

        switch (sortByOption) {
            case NAME:
                comparator = Comparator.comparing(SocialTagFetchAllResponse.SocialTagFetchResponse::getName, String.CASE_INSENSITIVE_ORDER);
                break;
            case DATE_ADDED:
                break;
        }

        // For descending order
        if (Objects.equals(sortOrder, 1)) {
            comparator = comparator.reversed();
        }

        // Comparator tie-breaker logic
        if (!Objects.equals(sortByOption, SocialTagSortOption.DATE_ADDED)) {
            comparator = comparator.thenComparing(Comparator.comparing(SocialTagFetchAllResponse.SocialTagFetchResponse::getCreatedAtDt).reversed());
        }
        return comparator;
    }

    private <T> List<T> fetchPaginatedResponse(List<T> list, Integer startIndex, Integer pageSize) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        int toIndex = (startIndex + pageSize); // toIndex is exclusive
        toIndex = Math.min(toIndex, CollectionUtils.size(list));
        List<T> pagedList;
        if (startIndex > toIndex) {
            pagedList = Collections.emptyList();
        } else {
            pagedList = list.subList(startIndex, toIndex);
        }
        return pagedList;
    }

    @Override
    public Set<Long> getTagIdsFromEntityId(Long entityId, SocialTagEntityType socialTagEntityType) {
        List<SocialTagBasicDetail> socialTagBasicDetailList = getBasicTagDetailForSingleEntityId(Long.valueOf(entityId), socialTagEntityType);
        return CollectionUtils.isEmpty(socialTagBasicDetailList)?null:
                socialTagBasicDetailList.stream().map(SocialTagBasicDetail::getId).collect(Collectors.toSet());
    }
}
