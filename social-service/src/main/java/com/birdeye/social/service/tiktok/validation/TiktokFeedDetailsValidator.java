package com.birdeye.social.service.tiktok.validation;

import com.birdeye.social.utils.StringUtils;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.function.Function;

public interface TiktokFeedDetailsValidator extends Function<List<String>, TiktokFeedDetailsValidator.ValidationResult> {

    static TiktokFeedDetailsValidator isImageUrlValid() {
        return imageUrls -> {
            if (CollectionUtils.isEmpty(imageUrls)) {
                return ValidationResult.THUMBNAIL_URL;
            }

            boolean allValid = imageUrls.stream().allMatch(image -> StringUtils.contains(image, "tiktokcdn"));
            return allValid ? ValidationResult.SUCCESS : ValidationResult.THUMBNAIL_URL;
        };
    }

    default TiktokFeedDetailsValidator and(TiktokFeedDetailsValidator other) {
        return feedDetails ->
                this.apply(feedDetails).equals(TiktokFeedDetailsValidator.ValidationResult.SUCCESS)
                        ? other.apply(feedDetails)
                        : this.apply(feedDetails);
    }

    @Getter
    enum ValidationResult {
        SUCCESS,
        THUMBNAIL_URL
    }
}
