package com.birdeye.social.service.whatsapp.impl;

import com.birdeye.social.dao.BusinessWhatsappAccountsRepository;
import com.birdeye.social.doup.DoupRequestBody;
import com.birdeye.social.doup.DoupService;
import com.birdeye.social.entities.BusinessWhatsappAccounts;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.service.SocialAccountSetupCommonService;
import com.birdeye.social.service.whatsapp.SocialWhatsappService;
import com.birdeye.social.service.whatsapp.dto.*;
import com.birdeye.social.service.whatsapp.dto.messages.*;
import com.birdeye.social.service.whatsapp.dto.messages.response.MessageResponse;
import com.birdeye.social.service.whatsapp.dto.messages.type.ParameterType;
import com.birdeye.social.service.whatsapp.dto.request.WhatsappMessageRequest;
import com.birdeye.social.service.whatsapp.dto.templates.MessageTemplateResponse;
import com.birdeye.social.service.whatsapp.dto.templates.type.ComponentType;
import com.birdeye.social.service.whatsapp.dto.templates.type.LanguageType;
import com.birdeye.social.service.whatsapp.external.WhatsappExternalService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.ws.rs.BadRequestException;
import java.security.SecureRandom;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialWhatsappServiceImpl extends SocialAccountSetupCommonService implements SocialWhatsappService {

    private static final Logger LOGGER	= LoggerFactory.getLogger(SocialWhatsappServiceImpl.class);
    @Autowired
    private BusinessWhatsappAccountsRepository businessWhatsappAccountsRepository;

    @Autowired
    private WhatsappExternalService whatsappExternalService;

    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private DoupService doupService;

    @Override
    public WabaDetails getWabaDetailsByWabaId(String wabaId) {

        List<BusinessWhatsappAccounts> businessWhatsappAccounts= businessWhatsappAccountsRepository.findByWabaId(wabaId);
        if (Objects.isNull(businessWhatsappAccounts)|| businessWhatsappAccounts.isEmpty()) {
            LOGGER.info("No WhatsApp accounts found for wabaId: {}", wabaId);
            return null;
        }

        return WabaDetails.builder()
                .wabaId(businessWhatsappAccounts.get(0).getWabaId())
                .wabaName(businessWhatsappAccounts.get(0).getWabaName())
                .build();
    }

    @Override
    public WabaDetails getWabaDetailsByBusinessId(Integer businessId) {
        BusinessWhatsappAccounts businessWhatsappAccounts= businessWhatsappAccountsRepository.findByBusinessId(businessId);
        if (Objects.isNull(businessWhatsappAccounts)) {
            LOGGER.info("No WhatsApp accounts found for businessId: {}", businessId);
            return null;
        }

        return WabaDetails.builder()
                .wabaId(businessWhatsappAccounts.getWabaId())
                .wabaName(businessWhatsappAccounts.getWabaName())
               .build();
    }

    @Override
    public MessageTemplateResponse getTemplateDetailsByWabaId(String wabaId) {
        List<BusinessWhatsappAccounts> businessWhatsappAccounts= businessWhatsappAccountsRepository.findByWabaId(wabaId);
        if (Objects.isNull(businessWhatsappAccounts)|| businessWhatsappAccounts.isEmpty()) {
            LOGGER.info("No WhatsApp accounts found for wabaId: {}", wabaId);
            return null;
        }
        MessageTemplateResponse messageTemplate= whatsappExternalService.getTemplateDetailsByWabaId(wabaId,businessWhatsappAccounts.get(0).getAccessToken());
        return messageTemplate;
    }


    public MessageRequest buildFromWhatsappMessageRequest(WhatsappMessageRequest request) {
        MessageRequest.MessageBuilder builder = MessageRequest.MessageBuilder.builder()
                .setTo(request.getReceiverPhoneNumber())
                .setContext(null); // Set context if applicable

        String type = request.getType();
        if (type == null) {
            throw new IllegalArgumentException("Message type cannot be null");
        }

        switch (type.toUpperCase()) {
            case "TEXT":
                WhatsappMessageRequest.Text text = request.getText();
                if (text == null) throw new IllegalArgumentException("Text payload is missing");
                TextMessage textMessage = new TextMessage();
                textMessage.setBody(text.getBody());
                textMessage.setPreviewUrl(text.isPreviewUrl());
                return builder.buildTextMessage(textMessage);

            case "IMAGE":
                WhatsappMessageRequest.Image img = request.getImage();
                if (img == null) throw new IllegalArgumentException("Image payload is missing");
                ImageMessage imageMessage = mapImage(img); // helper method below
                return builder.buildImageMessage(imageMessage);

            case "VIDEO":
                WhatsappMessageRequest.Video vid = request.getVideo();
                if (vid == null) throw new IllegalArgumentException("Video payload is missing");
                VideoMessage videoMessage = mapVideo(vid); // helper method below
                return builder.buildVideoMessage(videoMessage);

            case "TEMPLATE":
                WhatsappMessageRequest.Template tpl = request.getTemplate();
                if (tpl == null) throw new IllegalArgumentException("Template payload is missing");
                TemplateMessage templateMessage = mapTemplate(tpl); // helper method below
                return builder.buildTemplateMessage(templateMessage);

            case "CONTACTS":
                // Stub example – adjust per your actual ContactMessage implementation
                ContactMessage contactMessage = new ContactMessage();
                // contactMessage.setContacts(...) from request (if present)
                return builder.buildContactMessage(contactMessage);

            default:
                throw new UnsupportedOperationException("Unsupported message type: " + type);
        }
    }

    private VideoMessage mapVideo(WhatsappMessageRequest.Video vid) {
        VideoMessage videoMessage = new VideoMessage();
        videoMessage.setLink(vid.getVideoUrl());
        videoMessage.setCaption(vid.getCaption());
        return videoMessage;
    }

    private ImageMessage mapImage(WhatsappMessageRequest.Image img) {
        ImageMessage imageMessage = new ImageMessage();
        imageMessage.setLink(img.getImageUrl());
        imageMessage.setCaption(img.getCaption());
        return imageMessage;
    }


    private TemplateMessage mapTemplate(WhatsappMessageRequest.Template source) {
        TemplateMessage template = new TemplateMessage();

        // Map template name
        template.setName(source.getName());
        // Map language
        if (source.getLanguage() != null) {
            Language language = new Language();
            language.setCode(source.getLanguage().getCode());
            template.setLanguage(language);
        }

        // Map components
        if (source.getComponents() != null) {
            List<Component<?>> components = source.getComponents().stream()
                    .map(this::mapComponent)
                    .collect(Collectors.toList());

            template.setComponents(components);
        }

        return template;
    }

    private Component<?> mapComponent(WhatsappMessageRequest.Template.Component srcComponent) {
        ComponentType type = ComponentType.valueOf(srcComponent.getType().toUpperCase());

        Component<?> targetComponent;
        switch (type) {
            case HEADER:
                targetComponent = new HeaderComponent();
                break;
            case BODY:
                targetComponent = new BodyComponent();
                break;
            case BUTTONS:
                targetComponent = new ButtonComponent();
                break;
            case FOOTER:
                targetComponent = new FooterComponent();
                break;
            default:
                throw new IllegalArgumentException("Unsupported component type: " + srcComponent.getType());
        }

        // Map parameters
        if (srcComponent.getParameters() != null) {
            List<Parameter> parameters = srcComponent.getParameters().stream()
                    .map(p -> mapParameter(p, type))
                    .collect(Collectors.toList());

            targetComponent.setParameters(parameters);
        }
return targetComponent;

    }

    private Parameter mapParameter(WhatsappMessageRequest.Template.Component.Parameter src,ComponentType componentType) {
        ParameterType type = ParameterType.valueOf(src.getType().toUpperCase());
        switch (type) {
            case TEXT:
                if (componentType == ComponentType.BUTTONS) {
                    return new ButtonTextParameter(src.getText());
                } else {
                    return new TextParameter(src.getText());
                }
            case PAYLOAD:
                return new ButtonPayloadParameter(src.getText());
            case VIDEO:
                return new VideoParameter();
            case IMAGE:
                return new ImageParameter(src.getImage());

            case DOCUMENT:
                return new DocumentParameter();
            case DATE_TIME:
                DateTime dateTime = new DateTime();
                dateTime.setFallbackValue(src.getText());
                return new DateTimeParameter().setDateTime(dateTime);
            case CURRENCY:
                return new CurrencyParameter();
            // Add cases for other Parameter subclasses as needed
            default:
                throw new IllegalArgumentException("Unsupported parameter type: " + src.getType());
        }
    }


            @Override
    public WhatsappMessageResponse sendWhatsappMessage(WhatsappMessageRequest whatsappMessageRequest) {
        BusinessWhatsappAccounts businessWhatsappAccount= businessWhatsappAccountsRepository.findByBusinessId(whatsappMessageRequest.getBusinessId());
        if (Objects.isNull(businessWhatsappAccount)) {
            LOGGER.info("No WhatsApp accounts found for location: {}", whatsappMessageRequest.getBusinessId());
            return null;
        }

        MessageRequest message= buildFromWhatsappMessageRequest(whatsappMessageRequest);
        MessageResponse messageResponse=
                whatsappExternalService.sendWhatsappMesaage(businessWhatsappAccount.getAccessToken(),
                        businessWhatsappAccount.getPhoneNumberId(),message);
        WhatsappMessageResponse whatsappMessageResponse= new WhatsappMessageResponse();
        if(messageResponse!=null && CollectionUtils.isNotEmpty(messageResponse.getMessages()))
        {
            List<WhatsappMessageResponse.Message> messageList= new ArrayList<>();
            messageResponse.getMessages().forEach(message1 ->
            {

                if(message1.getId()!=null)
                {
                    WhatsappMessageResponse.Message messageData= new WhatsappMessageResponse.Message();
                    messageData.setId(message1.getId());
                    messageList.add(messageData);
                }
            });
            whatsappMessageResponse.setMessages(messageList);

        }
        return whatsappMessageResponse;

    }

    @Override
    public void subscription(WhatsappMappingEvent request) {
        try {
            BusinessWhatsappAccounts businessWhatsappAccount= businessWhatsappAccountsRepository.findByPhoneNumberId(request.getPhoneNumberId());
            if (Objects.isNull(businessWhatsappAccount)) {
                LOGGER.info("No WhatsApp accounts found for phoneNumberId: {}", request.getPhoneNumberId());
                return ;
            }
            if("MAPPED".equalsIgnoreCase(request.getMappingStatus())) {
                 FacebookBaseResponse response=
                         whatsappExternalService.subscribeNotificationWebhook(request.getWabaId(),businessWhatsappAccount.getAccessToken());
                 RegisterPhoneRequest registerPhoneRequest= new RegisterPhoneRequest();
                 registerPhoneRequest.setPin(generatePin());
                FacebookBaseResponse response2= whatsappExternalService.registerPhoneNumber(request.getPhoneNumberId(),businessWhatsappAccount.getAccessToken(),registerPhoneRequest);
                if(Boolean.TRUE.equals(response.isSuccess()) && Boolean.TRUE.equals(response2.isSuccess())){
                            WhatsappMappingEvent whatsappMappingEvent = new WhatsappMappingEvent();
                    whatsappMappingEvent.setWabaId(businessWhatsappAccount.getWabaId());
                    whatsappMappingEvent.setWabaName(businessWhatsappAccount.getWabaName());
                    whatsappMappingEvent.setPhoneNumberId(businessWhatsappAccount.getPhoneNumberId());
                    whatsappMappingEvent.setBusinessId(businessWhatsappAccount.getBusinessId());
                    whatsappMappingEvent.setAccountId(businessWhatsappAccount.getAccountId());
                    whatsappMappingEvent.setStatus(true);
                    whatsappMappingEvent.setWabaStatus(businessWhatsappAccount.getWabaStatus());
                    kafkaProducerService.sendObject("whatsapp_account_onboarded",whatsappMappingEvent);
                    businessWhatsappAccount.setEncryptedPin(registerPhoneRequest.getPin());
                    businessWhatsappAccountsRepository.saveAndFlush(businessWhatsappAccount);
                }
            } else {
               // whatsappExternalService.unSubscribeNotificationWebhook(request.getWabaId(),businessWhatsappAccount.getAccessToken());
                WhatsappMappingEvent whatsappMappingEvent = new WhatsappMappingEvent();
                whatsappMappingEvent.setWabaId(businessWhatsappAccount.getWabaId());
                whatsappMappingEvent.setPhoneNumberId(businessWhatsappAccount.getPhoneNumberId());
                whatsappMappingEvent.setBusinessId(businessWhatsappAccount.getBusinessId());
                whatsappMappingEvent.setAccountId(businessWhatsappAccount.getAccountId());
                whatsappMappingEvent.setWabaName(businessWhatsappAccount.getWabaName());
                whatsappMappingEvent.setStatus(false);
                whatsappMappingEvent.setWabaStatus(businessWhatsappAccount.getWabaStatus());
                    kafkaProducerService.sendObject("whatsapp_account_onboarded",whatsappMappingEvent);
                }
        } catch (Exception ex) {
            LOGGER.info("Something went wrong while subscription handler for request {} with error", request, ex );
        }
    }

    @Override
    public Map<String, Object> downloadMedia(String mediaId, String phoneNumberId) {
        Map<String, Object> map = new HashMap<>();
        BusinessWhatsappAccounts businessWhatsappAccount= businessWhatsappAccountsRepository.findByPhoneNumberId(phoneNumberId);
        if (Objects.isNull(businessWhatsappAccount)) {
            LOGGER.info("No WhatsApp accounts found for phoneNumberId: {}", phoneNumberId);
            return map;
        }
        WhatsappMediaDataResponse mediaDataResponse=
                whatsappExternalService.retrieveMediaUrl(businessWhatsappAccount.getAccessToken(),mediaId);
        if(mediaDataResponse==null || mediaDataResponse.getUrl()==null)
        {
            LOGGER.info("No Media URL identified for phoneNumber: {}", phoneNumberId);
            throw new BadRequestException("Not able to generate media URL");
        }

        byte[] data = whatsappExternalService.downloadMedia(businessWhatsappAccount.getAccessToken(),mediaDataResponse.getUrl());
        if(data != null){
            String base64Data = Base64.getEncoder().encodeToString(data);
            String mimeType = mediaDataResponse.getMime_type();
            if(StringUtils.isNotEmpty(mimeType)){

                    map.put("mediaData",base64Data);
                    map.put("mimeType", mimeType);
                }
            }
        return map;

    }

    private String generatePin() {
        SecureRandom random = new SecureRandom();
        int pin = 100000 + random.nextInt(900000); // ensures a 6-digit number
        return String.valueOf(pin);
    }

}
