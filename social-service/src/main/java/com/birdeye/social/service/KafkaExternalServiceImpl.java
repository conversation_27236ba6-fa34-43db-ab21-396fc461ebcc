package com.birdeye.social.service;


import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.SocialTokenInvalidDTO;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.linkinbio.LinkInfoEventRequest;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.internal.http2.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.ws.rs.BadRequestException;
import java.io.IOException;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@Service
public class KafkaExternalServiceImpl implements KafkaExternalService {

    @Autowired
    private KafkaProducerService kafkaProducerService;


    private static final Logger LOGGER = LoggerFactory.getLogger(KafkaExternalServiceImpl.class);

    private static final String REVIEW_SAVE_TYPE = "review_share";
    private static final String POST_SAVE_TYPE = "post";
    private static final String SOCIAL_POST_STATUS_TOPIC = "social-post-status-event";
    private static final String TRAFFIC_TOPIC = "traffic_topic";
    private static final String TEMPLATE_DELETION_TOPIC = "template-delete-event";
    private static final String FAILED_EVENT_TOPIC = "notification-schedule";
    private static final String APPLE_EMAIL_EVENT_TOPIC = "apple_showcase_status_email";

    private static final SimpleDateFormat FULL_DAY_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat DAY_FORMAT = new SimpleDateFormat("yyyy-MM-dd");


    private void publishStatusToKafka(SocialPostStatusInfo statusInfo) {
        LOGGER.info("Payload for status info: {}",statusInfo);
        String topic = SOCIAL_POST_STATUS_TOPIC;
        String key = statusInfo.getSaveType().concat("_").concat(statusInfo.getId().toString());
        kafkaProducerService.sendWithKey(topic, key, statusInfo);
    }

    private void pushTrafficReportToES(TrafficReportKafkaMessage messages) {
        LOGGER.info("Payload for TrafficReportKafkaMessage: {}",messages);
        String topic = TRAFFIC_TOPIC;
        String key = messages.getBiz_id().toString();
//        kafkaProducerService.sendWithKey(topic, key, messages);
    }

    private void pushFailTrafficReportToES(ReviewSocialPostingLogDTO messages, Exception e) {
        LOGGER.info("Payload for TrafficReportKafkaMessage: {}",messages);
        String topic = TRAFFIC_TOPIC;
        String key = messages.toString();
//        kafkaProducerService.sendWithKey(topic, key, e.getMessage());
    }

    @Override
    public void publishSocialPostEvent(SocialPostPublishInfo publishInfo) {
        LOGGER.info("Inside publishSocialPostEvent for id: {}",publishInfo.getId());
        publishFailedSocialPostEvent(publishInfo);
        Integer id = null;
        if(Objects.isNull(publishInfo.getSocialPost()) || Objects.isNull(publishInfo.getSocialPost().getSaveType())){
            return;
        }
        if(publishInfo.getSocialPost().getSaveType().equalsIgnoreCase(REVIEW_SAVE_TYPE)) {
            id = publishInfo.getSocialPost().getReviewId();
        } else if(publishInfo.getSocialPost().getSaveType().equalsIgnoreCase(POST_SAVE_TYPE)) {
            id = publishInfo.getSocialPost().getId();
        }
        publishSocialPostEvent(id,publishInfo.getSocialPost().getCreatedBy(),publishInfo.getIsPublished(), publishInfo.getFailureReason(),
                publishInfo.getSocialPost().getSaveType(),publishInfo.getPublishDate(),publishInfo.getSocialPost().getAutoShare(),
                publishInfo.getPostUrl(),publishInfo.getSourceId(),publishInfo.getBusinessId());
    }

    @Override
    public void publishSocialPostEvent(Integer id, Integer userId, Integer status, String failureReason, String saveType,
                                       Date publishDate, Boolean autoShare, String postUrl,Integer sourceId, Integer businessId) {
        SocialPostStatusInfo statusInfo = new SocialPostStatusInfo();
        statusInfo.setId(id);
        statusInfo.setUserId(userId);
        statusInfo.setStatus(status);
        statusInfo.setFailureReason(failureReason);
        statusInfo.setPostedOn(publishDate);
        statusInfo.setSaveType(saveType);
        statusInfo.setAutoShare(autoShare);
        statusInfo.setPostUrl(postUrl);
        statusInfo.setSourceId(sourceId);
        statusInfo.setBusinessId(businessId);
        publishStatusToKafka(statusInfo);
    }

    @Override
    public void pushSocialPostingToES(Integer followerCount,Integer source, Date postedOn,Integer id, Integer enterpriseId, Integer reviewId,Integer postedBy) {
        if(Objects.nonNull(followerCount) && followerCount>0) {
            try {
                TrafficReportKafkaMessage kafkaMessage = new TrafficReportKafkaMessage();
                String conversionFactor = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("follower-conversion-factor");
                Double factor = (StringUtils.isBlank(conversionFactor)) ? 0.1 : Double.parseDouble(conversionFactor);
                kafkaMessage.setBiz_id(Long.valueOf(id));
                kafkaMessage.setEnterprise_id(Long.valueOf(Objects.nonNull(enterpriseId) ? enterpriseId : id));
                kafkaMessage.setSrc_id(Long.valueOf(source));
                kafkaMessage.setSrc_name(SocialChannel.getSocialChannelNameById(source));
                kafkaMessage.setUpdated_at(FULL_DAY_FORMAT.format(postedOn));
                kafkaMessage.setVisit_day(DAY_FORMAT.format(postedOn));
                kafkaMessage.setBadge_type(-1);
                if (followerCount != null) {
                    if (SocialChannel.TWITTER.getId() == source || SocialChannel.GOOGLE.getId() == source) {
                        followerCount = new Double(followerCount * factor).intValue();
                    }
                    kafkaMessage.setVisitor_count(followerCount);
                }
                pushTrafficReportToES(kafkaMessage);
            } catch (Exception e) {
                LOGGER.info("Failed to send traffic report: {}",e.getMessage());
                ReviewSocialPostingLogDTO reviewSocialPostingLogDTO = new ReviewSocialPostingLogDTO();
                reviewSocialPostingLogDTO.setReviewId(reviewId);
                reviewSocialPostingLogDTO.setSource(SocialChannel.getSocialChannelNameById(source));
                reviewSocialPostingLogDTO.setFollowerCount(followerCount);
                reviewSocialPostingLogDTO.setPostedOn(postedOn);
                reviewSocialPostingLogDTO.setPostedBy(postedBy);
                pushFailTrafficReportToES(reviewSocialPostingLogDTO,e);
            }
        }

    }

    @Override
    public void publishDeleteTemplateEvent(TemplateDeleteEventDTO deleteEventDTO) {
        LOGGER.info("Payload for TemplateDeleteEvent: {}",deleteEventDTO);
        String topic = TEMPLATE_DELETION_TOPIC;
        String key = deleteEventDTO.getTemplateId().toString();
        kafkaProducerService.sendWithKey(topic, key, deleteEventDTO);
    }
    private String writeAsString(Serializable input) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(input);
        } catch (IOException e) {
            LOGGER.error("Unable to serialize Object {} to String", input, e);
            throw new BadRequestException(ErrorCode.INTERNAL_ERROR.toString(), e);
        }
    }


    @Override
    public void publishToNexusKafkaAsync(String topic, Serializable key, Serializable value) {
        String sKey = writeAsString(key);
        String sValue = writeAsString(value);
        kafkaProducerService.sendWithKey(topic, sKey, sValue);

    }

    @Override
    public void sendAppleStatusEmailEvent(AppleShowcaseEmail showcaseEmail) {
        kafkaProducerService.sendObjectV1(APPLE_EMAIL_EVENT_TOPIC, showcaseEmail);
    }

    public void publishFailedSocialPostEvent(SocialPostPublishInfo publishInfo) {
        LOGGER.info("inside emailAlertNotification : {}",publishInfo.getId());
        kafkaProducerService.sendObject(FAILED_EVENT_TOPIC, publishInfo);
    }

    @Override
    public void pushLinkInBio(LinkInfoEventRequest linkInfoEventRequest) {
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.CREATE_LINKS.getName(), linkInfoEventRequest);
    }

    @Override
    public void markPageInvalid(String channel, String pageId) {
        SocialTokenInvalidDTO payload = SocialTokenInvalidDTO.builder()
                .channel(channel)
                .pageId(pageId)
                .build();
        LOGGER.info("sending payload to mark {} pageId: {} as invalid", channel, pageId);
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_PAGE_INVALID.getName(), payload);
    }

    @Override
    public void pushIgContainerCheck(IgContainerCheckDTO igContainerCheckDTO) {
        kafkaProducerService.sendObjectV1(KafkaTopicEnum.IG_CONTAINER_CHECK.getName(), igContainerCheckDTO);
    }

}

