package com.birdeye.social.platform.messages;

/**
 * <AUTHOR>
 *
 */
public class EmailMetaData {

	private String  emailType;

	private String emailSubType;

	private String businessType;

	private String externalUid;

	private Long businessId;

	private String htmlTemplateType;
	
	private String sendgridAPIKey;

	public String getEmailType() {
		return emailType;
	}

	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}

	public String getEmailSubType() {
		return emailSubType;
	}

	public void setEmailSubType(String emailSubType) {
		this.emailSubType = emailSubType;
	}

	public String getBusinessType() {
		return businessType;
	}

	public void setBusinessType(String businessType) {
		this.businessType = businessType;
	}

	public String getExternalUid() {
		return externalUid;
	}

	public void setExternalUid(String externalUid) {
		this.externalUid = externalUid;
	}

	public Long getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Long businessId) {
		this.businessId = businessId;
	}

	public String getHtmlTemplateType() {
		return htmlTemplateType;
	}

	public void setHtmlTemplateType(String htmlTemplateType) {
		this.htmlTemplateType = htmlTemplateType;
	}

	public String getSendgridAPIKey() {
		return sendgridAPIKey;
	}

	public void setSendgridAPIKey(String sendgridAPIKey) {
		this.sendgridAPIKey = sendgridAPIKey;
	}
	
}
