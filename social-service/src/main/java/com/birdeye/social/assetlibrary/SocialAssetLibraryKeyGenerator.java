package com.birdeye.social.assetlibrary;

import com.birdeye.social.utils.HashingUtils;
import com.birdeye.social.utils.SocialKeyGenerator;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;

/**
 * <AUTHOR> on 20/10/23
 */
@Service("assetLibKeyGenerator")
public class SocialAssetLibraryKeyGenerator implements SocialKeyGenerator, KeyGenerator {

    @Override
    public String generate(String keyType, Object... params) {
        switch (keyType) {
            case SocialKeyGenerator.ASSET_LIB_ALL_ASSET_FETCH_KEY:
            default:
                StringBuilder customKey = new StringBuilder();
                for (Object param : params) {
                    customKey.append(param);
                }
                return HashingUtils.getMD5Hex(customKey.toString());
        }
    }

    @Override
    public Object generate(Object target, Method method, Object... params) {
        return generate(SocialKeyGenerator.ASSET_LIB_ALL_ASSET_FETCH_KEY, params);
    }
}
