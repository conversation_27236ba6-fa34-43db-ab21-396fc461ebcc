/**
 *
 *
 */
package com.birdeye.social.twitter.test;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.test.context.junit4.SpringRunner;

import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.ConsumerTokenAndSecret;
import com.birdeye.social.platform.dao.BusinessTwitterPageRepository;
import com.birdeye.social.platform.dao.SessionTokenRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialPostTwitterService;
import com.birdeye.social.service.SocialPostTwitterServiceImpl;
import com.birdeye.social.service.TwitterSocialAccountService;
import com.birdeye.social.twitter.TwitterService;

import twitter4j.TwitterException;

/**
 * <AUTHOR>
 *
 */
@RunWith(SpringRunner.class)
public class TestTwitterPages {
	
	@MockBean
	SocialPostInfoRepository				socialPostInfoRepository;
	
	@MockBean
	SocialPostsAssetsRepository				socialPostsAssetsRepository;
	
	@MockBean
	BusinessTwitterPageRepository			twitterRepo;
	
	@MockBean
	CommonService							commonService;
	
	@MockBean
	SessionTokenRepository					sessionTokenRepository;
	
	@MockBean
	BusinessGetPageReqRepo					businessGetPageReqRepo;
	
	@MockBean
	IRedisLockService						redisService;
	
	@MockBean
	SocialTwitterAccountRepository	socialTwitterRepo;
	
	@MockBean
	TwitterService							twitterService;
	
	@MockBean
	TwitterSocialAccountService				twitterAccountService;
	
	@Autowired
	SocialPostTwitterService socialPostTwitterService;
	
	
	@TestConfiguration
	static class TestSocialPostTwitterServiceConfiguration {
		@Bean
		public SocialPostTwitterService socialPostTwitterService() {
			return new SocialPostTwitterServiceImpl();
		}
	}
	
	@Test(expected=BirdeyeSocialException.class)
	public void testSubmitFetchPageRequestNullBusiness() throws TwitterException, Exception {

		socialPostTwitterService.submitFetchPageRequest(1L,1,"requestToken","requestSecret","oauthVerifier","ENTERPRISE");
	}
	
	@Test
	public void testSubmitFetchPageRequestNotNullBusinessLockFalse() throws TwitterException, Exception {
		Mockito.when(redisService.tryToAcquireLock(Mockito.anyString())).thenReturn(false);
		
		socialPostTwitterService.submitFetchPageRequest(1L,1,"requestToken","requestSecret","oauthVerifier","ENTERPRISE");
	}
	
	@Test(expected=BirdeyeSocialException.class)
	public void testSubmitFetchPageRequestNotNullBusinessLockTrueNullConsumer() throws TwitterException, Exception {
		Mockito.when(redisService.tryToAcquireLock(Mockito.anyString())).thenReturn(true);
		Mockito.when(commonService.getAppKeyAndToken(Mockito.any(), Mockito.any())).thenReturn(null);
		
		socialPostTwitterService.submitFetchPageRequest(1L,1,"requestToken","requestSecret","oauthVerifier","ENTERPRISE");
	}
	
	@Test(expected=TwitterException.class)
	public void testSubmitFetchPageRequestNotNullBusinessLockTrueNotNullConsumer() throws TwitterException, Exception {
		Mockito.when(redisService.tryToAcquireLock(Mockito.anyString())).thenReturn(true);
		Mockito.when(commonService.getAppKeyAndToken(Mockito.any(), Mockito.any())).thenReturn(getConsumerTokenAndSecret());
		
		socialPostTwitterService.submitFetchPageRequest(1L,1,"requestToken","requestSecret","oauthVerifier","ENTERPRISE");
	}
	
	private Business getBusiness() {
		Business business = new Business();
		business.setId(1);
		business.setBusinessId(12345L);
		business.setEnterpriseId(123);
		business.setName("Twitter Test Business");
		business.setReseller(business);
		return business;
	}
	
	private ConsumerTokenAndSecret getConsumerTokenAndSecret() {
		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		consumerToken.setToken("con token");
		consumerToken.setSecret("con secret");
		consumerToken.setAppAccessToken("con app access token");
		consumerToken.setDomainName("con domain name");
		return consumerToken;
	}
	
}
