package com.birdeye.social.service;

import com.birdeye.social.dao.BusinessAppleLocationRepo;
import com.birdeye.social.entities.BusinessAppleLocation;
import com.birdeye.social.sro.AppleLocationData;
import com.birdeye.social.sro.AppleLocationDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class AppleSetupServiceImplTest {

    @Mock
    private BusinessAppleLocationRepo appleLocationRepo;
    @Mock
    private AppleConnectService appleConnectService;

    @InjectMocks
    private AppleSetupServiceImpl appleSetupService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUpdateAppleLocationLinks_linkIsNull_shouldUpdateLink() {
        String locationId = "loc123";
        String companyId = "comp1";
        String businessId = "biz1";
        String placeCardUrl = "https://apple.com/place/loc123";

        BusinessAppleLocation appleLocation = new BusinessAppleLocation();
        appleLocation.setAppleLocationId(locationId);
        appleLocation.setAppleCompanyId(companyId);
        appleLocation.setAppleBusinessId(businessId);
        appleLocation.setLink(null);

        AppleLocationDTO dto = new AppleLocationDTO();
        dto.setId(locationId);
        dto.setPlaceCardUrl(placeCardUrl);

        AppleLocationData data = new AppleLocationData();
        data.setData(Collections.singletonList(dto));

        when(appleLocationRepo.findByAppleLocationId(locationId)).thenReturn(appleLocation);
        when(appleConnectService.getLocationForBusiness(companyId, businessId, null)).thenReturn(data);

        appleSetupService.updateAppleLocationLinks(Collections.singletonList(locationId));

        assertEquals(placeCardUrl, appleLocation.getLink());
        verify(appleLocationRepo, times(1)).saveAndFlush(appleLocation);
    }

    @Test
    public void testUpdateAppleLocationLinks_linkIsNotNull_shouldNotUpdate() {
        String locationId = "loc456";
        BusinessAppleLocation appleLocation = new BusinessAppleLocation();
        appleLocation.setAppleLocationId(locationId);
        appleLocation.setLink("already_set");

        when(appleLocationRepo.findByAppleLocationId(locationId)).thenReturn(appleLocation);

        appleSetupService.updateAppleLocationLinks(Collections.singletonList(locationId));

        verify(appleLocationRepo, never()).saveAndFlush(any());
    }

    @Test
    public void testUpdateAppleLocationLinks_noMatchingDTO_shouldNotUpdate() {
        String locationId = "loc789";
        String companyId = "comp2";
        String businessId = "biz2";

        BusinessAppleLocation appleLocation = new BusinessAppleLocation();
        appleLocation.setAppleLocationId(locationId);
        appleLocation.setAppleCompanyId(companyId);
        appleLocation.setAppleBusinessId(businessId);
        appleLocation.setLink(null);

        AppleLocationDTO dto = new AppleLocationDTO();
        dto.setId("other_id");
        dto.setPlaceCardUrl("https://apple.com/place/other");

        AppleLocationData data = new AppleLocationData();
        data.setData(Collections.singletonList(dto));

        when(appleLocationRepo.findByAppleLocationId(locationId)).thenReturn(appleLocation);
        when(appleConnectService.getLocationForBusiness(companyId, businessId, null)).thenReturn(data);

        appleSetupService.updateAppleLocationLinks(Collections.singletonList(locationId));

        assertNull(appleLocation.getLink());
        verify(appleLocationRepo, never()).saveAndFlush(any());
    }
}