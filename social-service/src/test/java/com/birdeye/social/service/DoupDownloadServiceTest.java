package com.birdeye.social.service;

import com.birdeye.social.model.CalendarPostsExportRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class DoupDownloadServiceTest {

    @Mock
    private DoupDownloadService doupDownloadService;

    @InjectMocks
    private DoupDownloadServiceTest doupDownloadServiceTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDownloadPdfCalendarReport() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = 1;
        String timeZoneId = "UTC";
        Integer accountId = 123;
        String sortOrder = "desc";
        Integer pageSize = 100;
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "success");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }

    @Test
    public void testDownloadPdfCalendarReport_NullParameters() {
        // Arrange
        Map<String, Object> expectedResponse = new HashMap<>();

        when(doupDownloadService.downloadPdfCalendarReport(null, null, null, null, null, null, null))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(null, null, null, null, null, null, null);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(null, null, null, null, null, null, null);
    }

    @Test
    public void testDownloadPdfCalendarReport_EmptyExportFilter() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = 1;
        String timeZoneId = "UTC";
        Integer accountId = 123;
        String sortOrder = "asc";
        Integer pageSize = 50;
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "empty");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }

    @Test
    public void testDownloadPdfCalendarReport_InvalidUserId() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = -1; // Invalid user ID
        String timeZoneId = "UTC";
        Integer accountId = 123;
        String sortOrder = "desc";
        Integer pageSize = 100;
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "error");
        expectedResponse.put("message", "Invalid user ID");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }

    @Test
    public void testDownloadPdfCalendarReport_LargePageSize() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = 1;
        String timeZoneId = "UTC";
        Integer accountId = 123;
        String sortOrder = "asc";
        Integer pageSize = 10000; // Large page size
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "success");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }

    @Test
    public void testDownloadPdfCalendarReport_InvalidTimeZone() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = 1;
        String timeZoneId = "INVALID_TIMEZONE";
        Integer accountId = 123;
        String sortOrder = "desc";
        Integer pageSize = 100;
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "error");
        expectedResponse.put("message", "Invalid time zone");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }

    @Test
    public void testDownloadPdfCalendarReport_ZeroStartIndex() {
        // Arrange
        CalendarPostsExportRequest exportFilter = new CalendarPostsExportRequest();
        Integer userId = 1;
        String timeZoneId = "UTC";
        Integer accountId = 123;
        String sortOrder = "asc";
        Integer pageSize = 50;
        Integer startIndex = 0;

        Map<String, Object> expectedResponse = new HashMap<>();
        expectedResponse.put("status", "success");

        when(doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId))
                .thenReturn(expectedResponse);

        // Act
        Map<String, Object> actualResponse = doupDownloadService.downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);

        // Assert
        assertEquals(expectedResponse, actualResponse);
        verify(doupDownloadService, times(1)).downloadPdfCalendarReport(exportFilter, userId, timeZoneId, startIndex, sortOrder, pageSize, accountId);
    }


}