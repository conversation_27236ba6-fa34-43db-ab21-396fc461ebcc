package com.birdeye.social.service.impl;

import com.birdeye.social.constant.IntegrationStatusEnum;
import com.birdeye.social.model.CheckIntegrationRequest;
import com.birdeye.social.model.CheckIntegrationRequest.CheckIntegrationInputDTO;
import com.birdeye.social.model.FBCheckIntegrationResponse;
import com.birdeye.social.model.GMBCheckIntegrationResponse;
import com.birdeye.social.service.IAggregationService;
import com.birdeye.social.utils.TestUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static com.birdeye.social.utils.TestUtil.createCheckIntegrationInputDTO;
import static org.junit.Assert.*;
import static org.mockito.Matchers.anyListOf;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BusinessAggregationCheckServiceTest {


    @Mock
    private IAggregationService aggregationService;

    @InjectMocks
    private BusinessAggregationCheckService aggCheckService;

    @Before
    public void setUp() throws Exception {
    }

    @Test
    public void checkIntegration_SuccessCase() {
        CheckIntegrationInputDTO fbInput = createCheckIntegrationInputDTO("page1", 1,
                11, "facebook");
        CheckIntegrationInputDTO gmbInput = createCheckIntegrationInputDTO("place1", 2,
                21, "gmb");

        List<CheckIntegrationInputDTO> inputDTOS = Arrays.asList(fbInput, gmbInput);
        FBCheckIntegrationResponse fbResponse = new FBCheckIntegrationResponse();
        fbResponse.setBusinessAggregationId(11);
        fbResponse.setIntegrationDetail(IntegrationStatusEnum.getIntegrationStatusByName("valid").toString());

        GMBCheckIntegrationResponse gmbResponse = new GMBCheckIntegrationResponse();
        gmbResponse.setBusinessAggregationId(21);
        gmbResponse.setIntegrationDetail(IntegrationStatusEnum.getIntegrationStatusByName("valid").toString());

        when(aggregationService.checkFBIntegration(anyListOf(CheckIntegrationInputDTO.class),anyBoolean()))
                .thenReturn(Arrays.asList(fbResponse));
        when(aggregationService.checkGMBIntegration(anyListOf(CheckIntegrationInputDTO.class),anyBoolean()))
                .thenReturn(Arrays.asList(gmbResponse));

        Map<Integer, String> map = aggCheckService.checkIntegration(new HashSet<>(inputDTOS));
        assertTrue(map.size() ==2);
        assertTrue(IntegrationStatusEnum.getIntegrationStatusByName("valid").toString().equalsIgnoreCase(map.get(11)));
    	assertTrue(IntegrationStatusEnum.getIntegrationStatusByName("valid").toString().equalsIgnoreCase(map.get(21)));
        verify(aggregationService,times(1))
                .checkFBIntegration(anyListOf(CheckIntegrationInputDTO.class),anyBoolean());
        verify(aggregationService,times(1))
                .checkGMBIntegration(anyListOf(CheckIntegrationInputDTO.class),anyBoolean());

    }
}