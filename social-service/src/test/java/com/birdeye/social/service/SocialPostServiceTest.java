package com.birdeye.social.service;

import com.birdeye.social.model.SocialMasterPostInputMessages;
import com.birdeye.social.model.SocialGenericResponse;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class SocialPostServiceTest {

    @Mock
    private SocialPostService socialPostService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // saveMasterDraft tests

    @Test
    public void saveMasterDraft_HappyPath() {
        SocialMasterPostInputMessages input = new SocialMasterPostInputMessages();
        long businessNumber = 123L;
        boolean isReseller = false;
        String requestSource = "test";
        SocialGenericResponse expectedResponse = new SocialGenericResponse();

        when(socialPostService.saveMasterDraft(input, businessNumber, isReseller, requestSource))
                .thenReturn(expectedResponse);

        SocialGenericResponse response = socialPostService.saveMasterDraft(input, businessNumber, isReseller, requestSource);

        assertEquals(expectedResponse, response);
        verify(socialPostService, times(1)).saveMasterDraft(input, businessNumber, isReseller, requestSource);
    }

    @Test
    public void saveMasterDraft_NullInput() {
        long businessNumber = 123L;
        boolean isReseller = false;
        String requestSource = "test";
        when(socialPostService.saveMasterDraft(null, businessNumber, isReseller, requestSource))
                .thenReturn(null);

        SocialGenericResponse response = socialPostService.saveMasterDraft(null, businessNumber, isReseller, requestSource);

        assertNull(response);
        verify(socialPostService, times(1)).saveMasterDraft(null, businessNumber, isReseller, requestSource);
    }

    @Test
    public void saveMasterDraft_ExceptionScenario() {
        SocialMasterPostInputMessages input = new SocialMasterPostInputMessages();
        long businessNumber = 123L;
        boolean isReseller = false;
        String requestSource = "test";

        when(socialPostService.saveMasterDraft(input, businessNumber, isReseller, requestSource))
                .thenThrow(new RuntimeException("DB error"));

        try {
            socialPostService.saveMasterDraft(input, businessNumber, isReseller, requestSource);
            fail("Expected RuntimeException");
        } catch (RuntimeException ex) {
            assertEquals("DB error", ex.getMessage());
        }
        verify(socialPostService, times(1)).saveMasterDraft(input, businessNumber, isReseller, requestSource);
    }

    // editMasterDraft tests

    @Test
    public void editMasterDraft_HappyPath() {
        SocialMasterPostInputMessages input = new SocialMasterPostInputMessages();
        boolean isReseller = true;
        String requestSource = "edit";
        SocialGenericResponse expectedResponse = new SocialGenericResponse();

        when(socialPostService.editMasterDraft(input, isReseller, requestSource))
                .thenReturn(expectedResponse);

        SocialGenericResponse response = socialPostService.editMasterDraft(input, isReseller, requestSource);

        assertEquals(expectedResponse, response);
        verify(socialPostService, times(1)).editMasterDraft(input, isReseller, requestSource);
    }

    @Test
    public void editMasterDraft_NullInput() {
        boolean isReseller = true;
        String requestSource = "edit";
        when(socialPostService.editMasterDraft(null, isReseller, requestSource))
                .thenReturn(null);

        SocialGenericResponse response = socialPostService.editMasterDraft(null, isReseller, requestSource);

        assertNull(response);
        verify(socialPostService, times(1)).editMasterDraft(null, isReseller, requestSource);
    }

    @Test
    public void editMasterDraft_ExceptionScenario() {
        SocialMasterPostInputMessages input = new SocialMasterPostInputMessages();
        boolean isReseller = true;
        String requestSource = "edit";

        when(socialPostService.editMasterDraft(input, isReseller, requestSource))
                .thenThrow(new NullPointerException("Null pointer"));

        try {
            socialPostService.editMasterDraft(input, isReseller, requestSource);
            fail("Expected NullPointerException");
        } catch (NullPointerException ex) {
            assertEquals("Null pointer", ex.getMessage());
        }
        verify(socialPostService, times(1)).editMasterDraft(input, isReseller, requestSource);
    }

    // deleteMasterDraft tests

    @Test
    public void deleteMasterDraft_HappyPath() {
        Integer id = 1;
        Integer businessId = 2;
        boolean isReseller = false;
        SocialGenericResponse expectedResponse = new SocialGenericResponse();

        when(socialPostService.deleteMasterDraft(id, businessId, isReseller))
                .thenReturn(expectedResponse);

        SocialGenericResponse response = socialPostService.deleteMasterDraft(id, businessId, isReseller);

        assertEquals(expectedResponse, response);
        verify(socialPostService, times(1)).deleteMasterDraft(id, businessId, isReseller);
    }

    @Test
    public void deleteMasterDraft_NullIds() {
        boolean isReseller = false;
        when(socialPostService.deleteMasterDraft(null, null, isReseller))
                .thenReturn(null);

        SocialGenericResponse response = socialPostService.deleteMasterDraft(null, null, isReseller);

        assertNull(response);
        verify(socialPostService, times(1)).deleteMasterDraft(null, null, isReseller);
    }

    @Test
    public void deleteMasterDraft_ExceptionScenario() {
        Integer id = 1;
        Integer businessId = 2;
        boolean isReseller = false;

        when(socialPostService.deleteMasterDraft(id, businessId, isReseller))
                .thenThrow(new RuntimeException("DB failure"));

        try {
            socialPostService.deleteMasterDraft(id, businessId, isReseller);
            fail("Expected RuntimeException");
        } catch (RuntimeException ex) {
            assertEquals("DB failure", ex.getMessage());
        }
        verify(socialPostService, times(1)).deleteMasterDraft(id, businessId, isReseller);
    }
}