package com.birdeye.social.service;

import com.birdeye.social.constant.GoogleResponseStatus;
import com.birdeye.social.google.GoogleApiKeys;
import com.birdeye.social.response.GoogleReviewResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.*;

import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(MockitoJUnitRunner.class)
public class TestGoogleReviewService
{
    @InjectMocks
    private GoogleReviewServiceImpl googleReviewService;

    @Mock
    private RestTemplate socialRestTemplate;

    @Mock
    private GoogleApiKeys googleApiKeys;

    @Test
    public void testFetchGoogleReviews()
    {
        String placeId = "placeId";
        Map<String, List<String>> apiKeys = new HashMap<>();
        apiKeys.put("place-details", Arrays.asList("mapApiKey1", "mapApiKey2"));
        when(googleApiKeys.getApiKeys()).thenReturn(apiKeys);

        URI expectedUri = UriComponentsBuilder.fromHttpUrl("https://maps.googleapis" +
                                                                   ".com/maps/api/place/details/json?key=mapApiKey1" +
                                                                   "&placeid=placeId&fields=reviews,rating,url").build()
                .encode().toUri();

        when(socialRestTemplate.getForObject(expectedUri, GoogleReviewResponse.class)).thenReturn(getGoogleResponse());
        GoogleReviewResponse googleReviewResponse = googleReviewService.fetchGoogleReviews(placeId);
        Assert.assertEquals(GoogleResponseStatus.OK, googleReviewResponse.getStatus());
        GoogleReviewResponse.Result result = googleReviewResponse.getResult();
        Assert.assertNotNull(result);
        Assert.assertEquals("https://maps.google.com/?cid=9933292786101903570", result.getUrl());
        List<GoogleReviewResponse.Result.Review> reviews = result.getReviews();
        Assert.assertNotNull(reviews);
        Assert.assertEquals("Brat", reviews.get(0).getAuthorName());
    }

    @Test
    public void testFetchGoogleReviewsInvalid()
    {
        String placeId = "placeId";
        Map<String, List<String>> apiKeys = new HashMap<>();
        apiKeys.put("place-details", Arrays.asList("mapApiKey1", "mapApiKey2"));
        when(googleApiKeys.getApiKeys()).thenReturn(apiKeys);

        URI expectedUri = UriComponentsBuilder.fromHttpUrl("https://maps.googleapis" +
                                                                   ".com/maps/api/place/details/json?key=mapApiKey1" +
                                                                   "&placeid=placeId&fields=reviews,rating,url").build()
                .encode().toUri();

        GoogleReviewResponse negativeGoogleReviewResponse = new GoogleReviewResponse();
        negativeGoogleReviewResponse.setStatus(GoogleResponseStatus.REQUEST_DENIED);
        negativeGoogleReviewResponse.setErrorMessage("The provided API key is invalid.");

        when(socialRestTemplate.getForObject(expectedUri, GoogleReviewResponse.class))
                .thenReturn(negativeGoogleReviewResponse);
        GoogleReviewResponse googleReviewResponse = googleReviewService.fetchGoogleReviews(placeId);
        Assert.assertEquals(GoogleResponseStatus.REQUEST_DENIED, googleReviewResponse.getStatus());
        Assert.assertEquals("The provided API key is invalid.", googleReviewResponse.getErrorMessage());
        Assert.assertNull(googleReviewResponse.getResult());
    }

    private GoogleReviewResponse getGoogleResponse()
    {
        GoogleReviewResponse googleReviewResponse = new GoogleReviewResponse();

        googleReviewResponse.setStatus("OK");

        GoogleReviewResponse.Result result = new GoogleReviewResponse.Result();
        result.setRating(5.0);
        result.setUrl("https://maps.google.com/?cid=9933292786101903570");

        List<GoogleReviewResponse.Result.Review> reviews = new ArrayList<>();

        GoogleReviewResponse.Result.Review review = new GoogleReviewResponse.Result.Review();
        review.setAuthorName("Brat");
        review.setAuthorUrl("https://maps.google.com/?cid=9933292786101903570");
        review.setLanguage("en");
        review.setProfilePhotoUrl(
                "https://lh6.ggpht.com/-eqmnv3IgDgI/AAAAAAI/AAAAAAAAAAA/bI2HuYaIXio/s128-c0x00000000-cc-rp-mo/photo" +
                        ".jpg");
        review.setRating(5);
        review.setRelativeTime("a month ago");
        review.setText("All is well2!!!");
        review.setTime(1570852720L);

        reviews.add(review);

        result.setReviews(reviews);

        googleReviewResponse.setResult(result);

        return googleReviewResponse;
    }
}
