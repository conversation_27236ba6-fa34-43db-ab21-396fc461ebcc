<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_PuEngWLnEeePML3co2BKSQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_PuEngmLnEeePML3co2BKSQ" bindingContexts="_PuEnhGLnEeePML3co2BKSQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList/>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_PuEngmLnEeePML3co2BKSQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.platform" selectedElement="_QNixwWLnEeePML3co2BKSQ" label="%trimmedwindow.label.eclipseSDK" x="26" y="26" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_QNixwWLnEeePML3co2BKSQ" selectedElement="_QNixwmLnEeePML3co2BKSQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_QNixwmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_RBpvQGLnEeePML3co2BKSQ">
        <children xsi:type="advanced:Perspective" xmi:id="_RBpvQGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.j2ee.J2EEPerspective" selectedElement="_RBpvQWLnEeePML3co2BKSQ" label="Java EE" iconURI="platform:/plugin/org.eclipse.jst.j2ee.ui/icons/full/cview16/j2ee_perspective.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:org.eclipse.jdt.ui.actions.OpenProjectWizard,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.mylyn.doc.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.jst.j2ee.J2eeMainActionSet</tags>
          <tags>persp.actionSet:org.eclipse.jdt.ui.JavaActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.wst.server.ui.ServersView</tags>
          <tags>persp.viewSC:org.eclipse.datatools.connectivity.DataSourceExplorerNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.wst.common.snippets.internal.ui.SnippetsView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.AllMarkersView</tags>
          <tags>persp.viewSC:org.eclipse.mylyn.tasks.ui.views.tasks</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.newWizSC:oracle.eclipse.tools.glassfish.v3.wizards.AddGenericResourceWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.jpa.ui.wizard.newJpaProject</tags>
          <tags>persp.perspSC:org.eclipse.jpt.ui.jpaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.jdt.ui.JavaPerspective</tags>
          <tags>persp.perspSC:org.eclipse.ui.resourcePerspective</tags>
          <tags>persp.perspSC:org.eclipse.wst.web.ui.webDevPerspective</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.EarProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.project.facet.WebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.project.facet.EjbProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.jca.ui.internal.wizard.ConnectorProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.j2ee.ui.project.facet.appclient.AppClientProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.wst.web.ui.internal.wizards.SimpleWebProjectWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newJpaProject</tags>
          <tags>persp.newWizSC:org.eclipse.jst.servlet.ui.internal.wizard.AddServletWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddSessionBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ejb.ui.internal.wizard.AddMessageDrivenBeanWizard</tags>
          <tags>persp.newWizSC:org.eclipse.jpt.ui.wizard.newEntity</tags>
          <tags>persp.newWizSC:org.eclipse.jst.ws.creation.ui.wizard.serverwizard</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.actionSet:org.eclipse.wst.server.ui.internal.webbrowser.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.m2e.core.wizards.Maven2ProjectWizard</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.actionSet:org.eclipse.wst.ws.explorer.explorer</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_RBpvQWLnEeePML3co2BKSQ" selectedElement="_RBpvQmLnEeePML3co2BKSQ" horizontal="true">
            <children xsi:type="basic:PartStack" xmi:id="_RBpvQmLnEeePML3co2BKSQ" elementId="topLeft" containerData="2500" selectedElement="_RBpvQ2LnEeePML3co2BKSQ">
              <tags>active</tags>
              <children xsi:type="advanced:Placeholder" xmi:id="_RBpvQ2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_RAX84GLnEeePML3co2BKSQ"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RBpvRGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_RAZLAGLnEeePML3co2BKSQ"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RBpvRWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" toBeRendered="false" ref="_RAZLAWLnEeePML3co2BKSQ"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_RBpvRmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.PackagesView" toBeRendered="false" ref="_RBjooGLnEeePML3co2BKSQ"/>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_RBpvR2LnEeePML3co2BKSQ" containerData="7500">
              <children xsi:type="basic:PartSashContainer" xmi:id="_RBpvSGLnEeePML3co2BKSQ" containerData="7000" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvSWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.editorss" containerData="7000" ref="_RAOy8GLnEeePML3co2BKSQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_RBpvSmLnEeePML3co2BKSQ" elementId="topRight" containerData="3000" selectedElement="_RBpvS2LnEeePML3co2BKSQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_RBpvS2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_RBmr8mLnEeePML3co2BKSQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_RBpvTGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" ref="_RBnTAGLnEeePML3co2BKSQ"/>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_RBpvTWLnEeePML3co2BKSQ" elementId="bottomRight" containerData="3000" selectedElement="_RBpvTmLnEeePML3co2BKSQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvTmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.AllMarkersView" ref="_RBjooWLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvT2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.PropertySheet" ref="_RBkPsGLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvUGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.ServersView" ref="_RBk2wGLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvUWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" ref="_RBk2wWLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvUmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" ref="_RBld0GLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvU2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProblemView" toBeRendered="false" ref="_RBmE4GLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvVGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.TaskList" toBeRendered="false" ref="_RBmE4WLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvVWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.console.ConsoleView" toBeRendered="false" ref="_RBmE4mLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvVmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_RBmE42LnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvV2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_RBmr8GLnEeePML3co2BKSQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_RBpvWGLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.views.SearchView" toBeRendered="false" ref="_RBmr8WLnEeePML3co2BKSQ"/>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_QNixw2LnEeePML3co2BKSQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_QNixxGLnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_QNg8kGLnEeePML3co2BKSQ"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_QNixxWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_QNiKsGLnEeePML3co2BKSQ"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_QNixxmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_QNixwGLnEeePML3co2BKSQ"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_QNg8kGLnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_QNiKsGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_Rh4ugGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_Rh4ugWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_QNixwGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_RAOy8GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.editorss">
      <children xsi:type="basic:PartStack" xmi:id="_RAOy8WLnEeePML3co2BKSQ" elementId="org.eclipse.e4.primaryDataStack">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RAX84GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_RCjuMGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RCjuMWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RAZLAGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RAZLAWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBjooGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.PackagesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Java Browsing</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBjooWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.AllMarkersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.allSeverityField&quot; categoryGroup=&quot;org.eclipse.ui.ide.type&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.allMarkersGenerator&quot; partName=&quot;Markers&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.allSeverityField=&quot;300&quot; org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.allSeverityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RKT8AGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.AllMarkersView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RKT8AWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.AllMarkersView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBkPsGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBk2wGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.ServersView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Server</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBk2wWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Data Management</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBld0GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmE4GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmE4WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmE4mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmE42LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmr8GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmr8WLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBmr8mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_RIk2oGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_RIk2oWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_RBnTAGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" closeable="true">
      <tags>View</tags>
      <tags>categoryTag:Mylyn</tags>
    </sharedElements>
    <trimBars xmi:id="_QNpfcGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.main.toolbar">
      <children xsi:type="menu:ToolBar" xmi:id="_QveaIGLnEeePML3co2BKSQ" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_QvfBMGLnEeePML3co2BKSQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvfoQGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_QvoyMGLnEeePML3co2BKSQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" enabled="false" command="_PvnfwWLnEeePML3co2BKSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvfoQWLnEeePML3co2BKSQ" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_QvfoQmLnEeePML3co2BKSQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RM59AGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMUHIGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMmbAGLnEeePML3co2BKSQ" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMaNwGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_RMu94GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.ws.explorer.explorer">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvfoQ2LnEeePML3co2BKSQ" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_QvfoRGLnEeePML3co2BKSQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvgPUGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_QvqAUGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_PvmRr2LnEeePML3co2BKSQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvgPUWLnEeePML3co2BKSQ" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_QvgPUmLnEeePML3co2BKSQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvgPU2LnEeePML3co2BKSQ" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_QvgPVGLnEeePML3co2BKSQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_QvgPVWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Q8uzAGLnEeePML3co2BKSQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Q8wBIGLnEeePML3co2BKSQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Q87nUGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.trim.status" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_Q9Bt8GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Q9K34GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_Q9aIcGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Q9uRgGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.trim.vertical1" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_Rlc_4GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_Q9u4kGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.trim.vertical2" side="Right"/>
  </children>
  <bindingTables xmi:id="_PuEng2LnEeePML3co2BKSQ" contributorURI="platform:/plugin/org.eclipse.platform" bindingContext="_PuEnhGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwTcJWLnEeePML3co2BKSQ" keySequence="CTRL+A" command="_PvjOS2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUDMmLnEeePML3co2BKSQ" keySequence="CTRL+SPACE" command="_Pvm4rWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqRmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+SPACE" command="_PvhZMGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXGhGLnEeePML3co2BKSQ" keySequence="CTRL+1" command="_PvhZCWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtkGLnEeePML3co2BKSQ" keySequence="ALT+PAGE_DOWN" command="_PvlqtWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtk2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+D" command="_PvoG7mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ1mLnEeePML3co2BKSQ" keySequence="SHIFT+INSERT" command="_Pve85mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwb_AGLnEeePML3co2BKSQ" keySequence="CTRL+Z" command="_PviAKmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQcWLnEeePML3co2BKSQ" keySequence="CTRL+Y" command="_Pvj1XGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3iWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+F1" command="_PvgLA2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwhel2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+I" command="_PvgyCmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwhem2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+F2" command="_Pvlqm2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFoGLnEeePML3co2BKSQ" keySequence="CTRL+INSERT" command="_PvkccmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFqGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+F3" command="_Pvnfy2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTyWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+L" command="_PvoGwmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj622LnEeePML3co2BKSQ" keySequence="CTRL+V" command="_Pve85mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMQ2LnEeePML3co2BKSQ" keySequence="CTRL+X" command="_PviAMmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMRmLnEeePML3co2BKSQ" keySequence="CTRL+F10" command="_Pvfj4mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozUmLnEeePML3co2BKSQ" keySequence="ALT+PAGE_UP" command="_Pvj1bGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozVmLnEeePML3co2BKSQ" keySequence="CTRL+C" command="_PvkccmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBdGLnEeePML3co2BKSQ" keySequence="SHIFT+DEL" command="_PviAMmLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwK5QGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.javaEditorScope" bindingContext="_PvsYKmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwPKsGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+C" command="_PvlDYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwQ_42LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_Pvfj9WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwRm9mLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+B" command="_Pvot42LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwSOAmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_PvgLAWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1EmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_Pvj1aWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqQmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Pvj1QGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwY7sWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F" command="_PvoGz2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwcmEmLnEeePML3co2BKSQ" keySequence="CTRL+F3" command="_PvoHBGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQdGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+M" command="_PvhZFGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQe2LnEeePML3co2BKSQ" keySequence="CTRL+/" command="_PvlDYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFoWLnEeePML3co2BKSQ" keySequence="CTRL+I" command="_PvinImLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh4GLnEeePML3co2BKSQ" keySequence="CTRL+O" command="_PvjOTGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh6GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+U" command="_PvmRrmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI8GLnEeePML3co2BKSQ" keySequence="CTRL+T" command="_PvlDamLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwB2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+/" command="_Pvj1VGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-J2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+O" command="_PviAJWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlNWLnEeePML3co2BKSQ" keySequence="CTRL+7" command="_PvlDYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlO2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+P" command="_PvmRimLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozWmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+\" command="_Pvfj-2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqogmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_PvinK2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqohWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_PvjOTmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqoimLnEeePML3co2BKSQ" keySequence="CTRL+2 F" command="_PvoG8mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPk2LnEeePML3co2BKSQ" keySequence="CTRL+2 L" command="_Pvfj7WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwsdsGLnEeePML3co2BKSQ" keySequence="CTRL+2 R" command="_Pvm4m2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwuS42LnEeePML3co2BKSQ" keySequence="CTRL+2 M" command="_PvinMGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwPxwGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" bindingContext="_PvsYVWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwPxwWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+C" command="_PvlDYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQfGLnEeePML3co2BKSQ" keySequence="CTRL+/" command="_PvlDYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlNmLnEeePML3co2BKSQ" keySequence="CTRL+7" command="_PvlDYGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwPxwmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_PvsYIGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQY0GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+C" command="_PvmRtGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwRm8GLnEeePML3co2BKSQ" keySequence="ALT+ARROW_RIGHT" command="_PvoG4WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ1WLnEeePML3co2BKSQ" keySequence="SHIFT+INSERT" command="_PvinPmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwd0MGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+V" command="_PvinPmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhenGLnEeePML3co2BKSQ" keySequence="CTRL+INSERT" command="_PvmRtGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaY2LnEeePML3co2BKSQ" keySequence="ALT+ARROW_UP" command="_PveVtGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQY0WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" bindingContext="_PvsYI2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQY0mLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+C" command="_PvlDhmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwQ_5GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_PvlqpGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwSOBWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_PvmRrGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1E2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_Pvot_GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqRGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Pve88WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtl2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+E" command="_PvkcX2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwY7tGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F" command="_Pvfj8GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwcmE2LnEeePML3co2BKSQ" keySequence="CTRL+F3" command="_Pvkcd2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNJmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+T" command="_Pvj1SmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQeGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+M" command="_Pvfj2mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQfWLnEeePML3co2BKSQ" keySequence="CTRL+/" command="_PvlDhmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFomLnEeePML3co2BKSQ" keySequence="CTRL+I" command="_PvjONWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh5WLnEeePML3co2BKSQ" keySequence="CTRL+O" command="_Pve8zmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh62LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+U" command="_PviAL2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI8WLnEeePML3co2BKSQ" keySequence="CTRL+T" command="_PvlqcGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwCGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+/" command="_Pvj1R2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-KGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+O" command="_PvinNmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlN2LnEeePML3co2BKSQ" keySequence="CTRL+7" command="_PvlDhmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMQGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+P" command="_PvlqhmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozW2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+\" command="_PvgK82LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwqog2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_PvoG0WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqohmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_Pvgx9GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwqoi2LnEeePML3co2BKSQ" keySequence="CTRL+2 F" command="_PvinQGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPlGLnEeePML3co2BKSQ" keySequence="CTRL+2 L" command="_PvoHBWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwsdsWLnEeePML3co2BKSQ" keySequence="CTRL+2 R" command="_PvhZF2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQY02LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" bindingContext="_PvsYLGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQY1GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+C" command="_PvoGzWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwQ_5WLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_PvlqdmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwSOBmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_PvmRl2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1EWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+A" command="_Pvot9GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1FGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_PvgyA2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqRWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Pvlqu2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwY7tWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F" command="_Pvot0GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFo2LnEeePML3co2BKSQ" keySequence="CTRL+I" command="_PvnfxGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwistWLnEeePML3co2BKSQ" keySequence="F3" command="_PvlDaWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh5mLnEeePML3co2BKSQ" keySequence="CTRL+O" command="_PvlDeWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwCWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+/" command="_PvkchGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMQWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+P" command="_PvlDbGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozXGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+\" command="_PvlqmmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaYGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+>" command="_PvnfpmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqohGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_Pvfj1mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwqoh2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_Pvj1SGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQY1WLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" bindingContext="_PvsYJ2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQY1mLnEeePML3co2BKSQ" keySequence="ALT+CTRL+D" command="_PviAHWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcI2LnEeePML3co2BKSQ" keySequence="ALT+C" command="_PvhZHWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwbX82LnEeePML3co2BKSQ" keySequence="ALT+CTRL+X" command="_Pvj1YWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwd0MmLnEeePML3co2BKSQ" keySequence="ALT+Q" command="_PvnfuGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpY2LnEeePML3co2BKSQ" keySequence="ALT+S" command="_PvjOU2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQemLnEeePML3co2BKSQ" keySequence="CTRL+/" command="_Pve8wGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXF2LnEeePML3co2BKSQ" keySequence="ALT+CTRL+R" command="_Pvnf6WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlOWLnEeePML3co2BKSQ" keySequence="ALT+X" command="_Pvot5mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlOmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+P" command="_PvmRpmLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQY12LnEeePML3co2BKSQ" elementId="org.eclipse.ui.serverViewScope" bindingContext="_PvsYQ2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQ_4GLnEeePML3co2BKSQ" keySequence="ALT+CTRL+D" command="_PvlqtGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhelGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+P" command="_PvmRkGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj61mLnEeePML3co2BKSQ" keySequence="ALT+CTRL+S" command="_PvgyGmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXGGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+R" command="_PvotwGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQ_4WLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_PvsYSmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQ_4mLnEeePML3co2BKSQ" keySequence="ALT+D" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcJGLnEeePML3co2BKSQ" keySequence="ALT+C" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUDMWLnEeePML3co2BKSQ" keySequence="ALT+B" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXGhWLnEeePML3co2BKSQ" keySequence="ALT+F" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwYUomLnEeePML3co2BKSQ" keySequence="ALT+H" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwY7tmLnEeePML3co2BKSQ" keySequence="ALT+G" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwbX9GLnEeePML3co2BKSQ" keySequence="ALT+R" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwcmEWLnEeePML3co2BKSQ" keySequence="ALT+P" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNImLnEeePML3co2BKSQ" keySequence="ALT+V" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwebRWLnEeePML3co2BKSQ" keySequence="ALT+L" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpZGLnEeePML3co2BKSQ" keySequence="ALT+S" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTw2LnEeePML3co2BKSQ" keySequence="ALT+Y" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI9mLnEeePML3co2BKSQ" keySequence="ALT+W" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-JWLnEeePML3co2BKSQ" keySequence="ALT+N" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-LGLnEeePML3co2BKSQ" keySequence="ALT+T" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMS2LnEeePML3co2BKSQ" keySequence="ALT+E" command="_PvkcgmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaYWLnEeePML3co2BKSQ" keySequence="ALT+A" command="_PvkcgmLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwQ_5mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_PuEnhWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwQ_52LnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_PvlDh2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwRm8WLnEeePML3co2BKSQ" keySequence="ALT+ARROW_RIGHT" command="_PvinJGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwRm8mLnEeePML3co2BKSQ" keySequence="CTRL+B" command="_Pve87mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1EGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+A" command="_Pvlqs2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwS1FWLnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_PvlDnGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcIGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+A" command="_PvmRh2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcImLnEeePML3co2BKSQ" keySequence="ALT+C" command="_PviAGGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcJmLnEeePML3co2BKSQ" keySequence="ALT+ARROW_LEFT" command="_Pvfj5mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwVRUWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D A" command="_PvnfpWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwVRUmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D J" command="_PvmRqWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwVRU2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D Q" command="_Pvj1QWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwV4YGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D O" command="_PvkceGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwV4YWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D R" command="_Pvj1XmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwV4YmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D X" command="_Pvj1UGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwV4Y2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D E" command="_Pvot5GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwWfcGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D P" command="_Pvnfu2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwWfcWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+D T" command="_PveVwmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXGgGLnEeePML3co2BKSQ" keySequence="CTRL+{" command="_PvhZNGLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwXGgWLnEeePML3co2BKSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_PwYUoGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+G" command="_PvlqemLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwYUoWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+F" command="_Pvnf1mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwYUo2LnEeePML3co2BKSQ" keySequence="CTRL+F" command="_PvgK5GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwYUpWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+E" command="_PvgLDmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwZiwGLnEeePML3co2BKSQ" keySequence="CTRL+E" command="_PviAIWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwZiwWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+H" command="_PvkcZmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwZixGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+I" command="_Pvfj5GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ0WLnEeePML3co2BKSQ" keySequence="ALT+CTRL+H" command="_Pvfj2GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ02LnEeePML3co2BKSQ" keySequence="CTRL+H" command="_Pvm4rGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw4GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+H" command="_PviAGmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw4WLnEeePML3co2BKSQ" keySequence="ALT+CTRL+G" command="_PvlqmGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw4mLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+G" command="_Pvotz2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw52LnEeePML3co2BKSQ" keySequence="CTRL+G" command="_PveVtmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwb_AWLnEeePML3co2BKSQ" keySequence="F11" command="_PvoG4mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwb_BGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+S" command="_PvkcWWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwcmEGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+M" command="_PvoG5WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNIGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+W" command="_PvoHAWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNIWLnEeePML3co2BKSQ" keySequence="ALT+V" command="_PvjOUGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNJWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+T" command="_PviAMWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwebQGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+P" command="_Pvlqn2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwebQ2LnEeePML3co2BKSQ" keySequence="CTRL+N" command="_Pvot1GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCUWLnEeePML3co2BKSQ" keySequence="CTRL+U" command="_Pvj1U2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCUmLnEeePML3co2BKSQ" keySequence="CTRL+F4" command="_Pvj1Z2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCVmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F9" command="_PvjOP2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCV2LnEeePML3co2BKSQ" keySequence="CTRL+F9" command="_Pvgx8WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpYGLnEeePML3co2BKSQ" keySequence="CTRL+P" command="_PvnfwWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpYWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Z" command="_PvkcYGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpZWLnEeePML3co2BKSQ" keySequence="F2" command="_Pve87WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQcmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+N" command="_PvkchmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQeWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+W" command="_PviAMGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQfmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_PvhZNmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3gWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F6" command="_PvlqjmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3gmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+S" command="_PvlqhGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3g2LnEeePML3co2BKSQ" keySequence="CTRL+M" command="_Pvm4qGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3hGLnEeePML3co2BKSQ" keySequence="F12" command="_Pvm4sGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3hWLnEeePML3co2BKSQ" keySequence="SHIFT+F5" command="_PvjOWWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3hmLnEeePML3co2BKSQ" keySequence="CTRL+F11" command="_Pvnf6mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3h2LnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+T" command="_Pvfj4WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhekGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+V" command="_PvjOM2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwhek2LnEeePML3co2BKSQ" keySequence="ALT+CTRL+P" command="_PvgK7WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhelWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+J" command="_PviAHmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhemWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_Pvlqg2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFpWLnEeePML3co2BKSQ" keySequence="CTRL+W" command="_Pvj1Z2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFpmLnEeePML3co2BKSQ" keySequence="ALT+F7" command="_PvlDc2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFp2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F7" command="_PvoG5mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwiss2LnEeePML3co2BKSQ" keySequence="F3" command="_PvhZA2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwistmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+O" command="_PvoG6WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwisumLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F8" command="_PvhZM2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTxWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+U" command="_Pvgx82LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTx2LnEeePML3co2BKSQ" keySequence="ALT+F5" command="_PvjOQmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTymLnEeePML3co2BKSQ" keySequence="ALT+-" command="_PvkchWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj60WLnEeePML3co2BKSQ" keySequence="CTRL+-" command="_PvoGxGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj60mLnEeePML3co2BKSQ" keySequence="DEL" command="_PvgK9WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj602LnEeePML3co2BKSQ" keySequence="CTRL+3" command="_PvhZEmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj61WLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+F7" command="_PvmRi2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj63mLnEeePML3co2BKSQ" keySequence="ALT+CR" command="_Pvm4mGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh52LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F4" command="_PviAMGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI8mLnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+F12" command="_PvoG9mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI82LnEeePML3co2BKSQ" keySequence="CTRL+F6" command="_PvgK7mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI9WLnEeePML3co2BKSQ" keySequence="ALT+CTRL+T" command="_PvkcbmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI-GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+N" command="_PviAK2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwAGLnEeePML3co2BKSQ" keySequence="SHIFT+F2" command="_Pvlqc2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwAmLnEeePML3co2BKSQ" keySequence="CTRL+BREAK" command="_Pvfj9GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwBWLnEeePML3co2BKSQ" keySequence="CTRL+F12" command="_PvgK_2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwBmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+R" command="_Pvot-WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwCmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+L" command="_PvhZBWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXGmLnEeePML3co2BKSQ" keySequence="F5" command="_PvinLWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-IGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+T" command="_PvlDbWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-ImLnEeePML3co2BKSQ" keySequence="F4" command="_Pve89WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-KWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+M" command="_PvoG9GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-K2LnEeePML3co2BKSQ" keySequence="CTRL+." command="_PvoHBmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlMGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_Pvj1YmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlOGLnEeePML3co2BKSQ" keySequence="ALT+X" command="_PvkcXmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMQmLnEeePML3co2BKSQ" keySequence="CTRL+F8" command="_PvhZC2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMRWLnEeePML3co2BKSQ" keySequence="CTRL+S" command="_PvinTWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMSGLnEeePML3co2BKSQ" keySequence="CTRL+," command="_Pve862LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMSmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F12" command="_Pve8xmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozUWLnEeePML3co2BKSQ" keySequence="CTRL+#" command="_Pvfj42LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozV2LnEeePML3co2BKSQ" keySequence="CTRL+Q" command="_Pvnf0WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozWGLnEeePML3co2BKSQ" keySequence="CTRL+F7" command="_Pvkcc2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaZWLnEeePML3co2BKSQ" keySequence="CTRL+_" command="_PvhZNGLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwpaZmLnEeePML3co2BKSQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_PwqBcGLnEeePML3co2BKSQ" keySequence="CTRL+DEL" command="_PvoGyGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBcmLnEeePML3co2BKSQ" keySequence="CTRL+=" command="_PvinJ2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBc2LnEeePML3co2BKSQ" keySequence="SHIFT+DEL" command="_PvhZLWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBdWLnEeePML3co2BKSQ" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_Pvot22LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBdmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+B" command="_PvgK_GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBd2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+C" command="_Pvm4s2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqoiGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+B" command="_PvlqrmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPkGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X P" command="_PvoG_GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPkWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q O" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwrPkmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_PwrPlWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q Q" command="_PvlqpmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPlmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X X" command="_PvlDYmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwrPl2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q J" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwrPmGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.JavadocView"/>
    </bindings>
    <bindings xmi:id="_Pwr2oWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q V" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwr2omLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_Pwr2o2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q Y" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwr2pGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_Pwr2pWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q L" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwr2pmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_PwsdsmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X A" command="_Pve86mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwsds2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q D" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwsdtGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.SourceView"/>
    </bindings>
    <bindings xmi:id="_PwtEwGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q B" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwtEwWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_PwtEwmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q Z" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwtEw2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_PwtExGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q H" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwtExWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_PwtExmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X J" command="_PvlquWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwtEx2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q T" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwtEyGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.TypeHierarchy"/>
    </bindings>
    <bindings xmi:id="_Pwtr0GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q K" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwtr0WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_Pwtr0mLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q X" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwtr02LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_Pwtr1GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X R" command="_Pvj1WmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwtr1WLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q S" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_Pwtr1mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_Pwtr12LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X E" command="_Pvlql2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwtr2GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X T" command="_PvjOSGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwuS4GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q P" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwuS4WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.jdt.ui.PackageExplorer"/>
    </bindings>
    <bindings xmi:id="_PwuS5GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X Q" command="_PvgyB2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwuS5WLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q C" command="_PvlqpmLnEeePML3co2BKSQ">
      <parameters xmi:id="_PwuS5mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_PwuS52LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X M" command="_PvjOUWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwuS6GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+X O" command="_PvlqdGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwRm82LnEeePML3co2BKSQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_PvsYImLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwRm9GLnEeePML3co2BKSQ" keySequence="CTRL+ARROW_DOWN" command="_Pvot82LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwRm9WLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_PvjONGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwTcIWLnEeePML3co2BKSQ" keySequence="CTRL+ARROW_UP" command="_PvgLAGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUDMGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+ARROW_UP" command="_PvoHAmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqQGLnEeePML3co2BKSQ" keySequence="ALT+ARROW_DOWN" command="_PvmRhGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqQWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+DEL" command="_Pvm4tGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwVRUGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+A" command="_PvlDa2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtkWLnEeePML3co2BKSQ" keySequence="CTRL+D" command="_Pvfj-WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtkmLnEeePML3co2BKSQ" keySequence="CTRL+ARROW_RIGHT" command="_PvgyD2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwXtlmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_Pvgx-2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwYUpGLnEeePML3co2BKSQ" keySequence="SHIFT+HOME" command="_PvinNGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ12LnEeePML3co2BKSQ" keySequence="ALT+CTRL+J" command="_PvhZAWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ2GLnEeePML3co2BKSQ" keySequence="SHIFT+END" command="_PvjOMWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw5GLnEeePML3co2BKSQ" keySequence="CTRL+HOME" command="_Pve85WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwbX8WLnEeePML3co2BKSQ" keySequence="CTRL+BS" command="_PveVtWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwbX8mLnEeePML3co2BKSQ" keySequence="CTRL+END" command="_PvmRiWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNJGLnEeePML3co2BKSQ" keySequence="INSERT" command="_PvlDi2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwd0MWLnEeePML3co2BKSQ" keySequence="END" command="_Pvnf5GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwd0M2LnEeePML3co2BKSQ" keySequence="CTRL+NUMPAD_ADD" command="_PvoG82LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwebRGLnEeePML3co2BKSQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_PvmRm2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwebRmLnEeePML3co2BKSQ" keySequence="CTRL+J" command="_Pvfj6WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCUGLnEeePML3co2BKSQ" keySequence="CTRL+K" command="_PvlqsGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfCU2LnEeePML3co2BKSQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_PvgLBWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpZmLnEeePML3co2BKSQ" keySequence="F2" command="_PvhZE2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3gGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_PvlDjWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwg3iGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+INSERT" command="_Pvgx9WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhemGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+J" command="_PvgyBWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhemmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_PvmRo2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwissGLnEeePML3co2BKSQ" keySequence="SHIFT+CR" command="_PvoG0GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTwWLnEeePML3co2BKSQ" keySequence="CTRL+L" command="_PvnfomLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj62GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+Y" command="_PvinRmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI9GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+Q" command="_PvgyEmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI92LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+K" command="_PvgK-2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI-WLnEeePML3co2BKSQ" keySequence="ALT+/" command="_PvoG12LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXEGLnEeePML3co2BKSQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_PvnfyWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXFGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+CR" command="_PvnfymLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMRGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+X" command="_Pvkce2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMR2LnEeePML3co2BKSQ" keySequence="CTRL+F10" command="_PvnfxmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozUGLnEeePML3co2BKSQ" keySequence="CTRL+ARROW_LEFT" command="_PvkcbWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozWWLnEeePML3co2BKSQ" keySequence="HOME" command="_PvoG0mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaZGLnEeePML3co2BKSQ" keySequence="ALT+ARROW_UP" command="_Pvot32LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBcWLnEeePML3co2BKSQ" keySequence="CTRL+DEL" command="_PviAI2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqoiWLnEeePML3co2BKSQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_PvjOVGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwSOAGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" bindingContext="_PvsYKWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwSOAWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+B" command="_Pvot42LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwSOA2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_PvsYTmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwSOBGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_PvinU2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUDM2LnEeePML3co2BKSQ" keySequence="ALT+ARROW_DOWN" command="_PviAHGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwUqQ2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+ARROW_UP" command="_Pvm4oGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwZixWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+I" command="_PvinTGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ1GLnEeePML3co2BKSQ" keySequence="SHIFT+INSERT" command="_PvgK8GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwdNI2LnEeePML3co2BKSQ" keySequence="INSERT" command="_PvinQ2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh6WLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+U" command="_PvmRlWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwA2LnEeePML3co2BKSQ" keySequence="CTRL+CR" command="_PviAFmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-I2LnEeePML3co2BKSQ" keySequence="F4" command="_Pvfj3mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlMmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_PvjOMmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwpaYmLnEeePML3co2BKSQ" keySequence="ALT+ARROW_UP" command="_Pvlqo2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqBeGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+C" command="_PvkcV2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwXGgmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" bindingContext="_PvsYKGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwXGg2LnEeePML3co2BKSQ" keySequence="CTRL+1" command="_PvotymLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwXtlGLnEeePML3co2BKSQ" elementId="org.eclipse.core.runtime.xml" bindingContext="_PvsYMGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwXtlWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+D" command="_Pvj1UmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlPGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+P" command="_Pvm4sWLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwYUpmLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.AntEditorScope" bindingContext="_PvsYNmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwY7sGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F" command="_PvoGz2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwissmLnEeePML3co2BKSQ" keySequence="F3" command="_PveVxWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlI-mLnEeePML3co2BKSQ" keySequence="SHIFT+F2" command="_PvlDfWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-JmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+O" command="_PveVvmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-LWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_Pve89GLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwY7smLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.pdeEditorContext" bindingContext="_PvsYQWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwY7s2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F" command="_Pvfj82LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh5GLnEeePML3co2BKSQ" keySequence="CTRL+O" command="_PvgK6mLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwZiwmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" bindingContext="_PvsYUmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwZiw2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+H" command="_Pvot9mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ0GLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+I" command="_PvlDemLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwaJ0mLnEeePML3co2BKSQ" keySequence="ALT+CTRL+H" command="_Pvm4oWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwaw42LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+G" command="_PvlDcGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwbX8GLnEeePML3co2BKSQ" keySequence="CTRL+G" command="_PvlqsWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwb_BmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+S" command="_Pvlqf2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwfpYmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Z" command="_PvjOQWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhekmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+V" command="_PvgyEWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhelmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+J" command="_PvlDnWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwistGLnEeePML3co2BKSQ" keySequence="F3" command="_PvmRj2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwisuWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+O" command="_Pvfj-mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTxmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+U" command="_PvgyDmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwAWLnEeePML3co2BKSQ" keySequence="SHIFT+F2" command="_Pvm4u2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwC2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+L" command="_Pvj1SWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-IWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+T" command="_PvgK-mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-JGLnEeePML3co2BKSQ" keySequence="F4" command="_PvjOWGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwm-KmLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+M" command="_Pvnf5mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlNGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_PvlqiWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqogWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+C" command="_PvhZJWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwr2oGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q J" command="_PvoGvWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwsdtWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q D" command="_PvmRhWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwtEyWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q T" command="_PvlDmWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwuS4mLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+Q P" command="_PvlqkmLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwZixmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_PvsYT2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwZix2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+I" command="_PvinTGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwb_BWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+S" command="_PviAN2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQd2LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+M" command="_Pve8zWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh4WLnEeePML3co2BKSQ" keySequence="CTRL+O" command="_PvoG_mLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh6mLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+U" command="_PvmRlWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlM2LnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_PvjOMmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwqogGLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+C" command="_PvkcV2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwaw5WLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_PvsYRWLnEeePML3co2BKSQ">
    <bindings xmi:id="_Pwaw5mLnEeePML3co2BKSQ" keySequence="CTRL+G" command="_Pvm4p2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFqWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+." command="_Pvm4pmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj60GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+," command="_Pvnf1WLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwbX9WLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.console" bindingContext="_PvsYPmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwbX9mLnEeePML3co2BKSQ" keySequence="CTRL+Z" command="_PvoG_2LnEeePML3co2BKSQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_Pwb_AmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_PvsYRGLnEeePML3co2BKSQ">
    <bindings xmi:id="_Pwb_A2LnEeePML3co2BKSQ" keySequence="F6" command="_PvjOOGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTwmLnEeePML3co2BKSQ" keySequence="CTRL+R" command="_PvkcdmLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTxGLnEeePML3co2BKSQ" keySequence="F7" command="_Pvotw2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj612LnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+3" command="_Pvfj0GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXEWLnEeePML3co2BKSQ" keySequence="CTRL+F5" command="_PvoG3WLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXFWLnEeePML3co2BKSQ" keySequence="CTRL+F2" command="_Pvm4t2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXFmLnEeePML3co2BKSQ" keySequence="F8" command="_PvlDhWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXGWLnEeePML3co2BKSQ" keySequence="F5" command="_Pvfj1GLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwebQWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_PvsYJmLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwebQmLnEeePML3co2BKSQ" keySequence="CTRL+N" command="_PvlqlGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwgQc2LnEeePML3co2BKSQ" keySequence="ALT+CTRL+M" command="_PvjORWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwiFpGLnEeePML3co2BKSQ" keySequence="CTRL+W" command="_PvlDk2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTyGLnEeePML3co2BKSQ" keySequence="ALT+CTRL+N" command="_PvoG92LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwkh7GLnEeePML3co2BKSQ" keySequence="CTRL+T" command="_PvhZGGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwfCVGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.editorContext" bindingContext="_PvsYOWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwfCVWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F9" command="_PvjON2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwissWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F10" command="_PvoHAGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwjTwGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F5" command="_PvmRi2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_Pwj61GLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F11" command="_PvoG4GLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXE2LnEeePML3co2BKSQ" keySequence="CTRL+F5" command="_PvlDc2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwoMSWLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F12" command="_Pvotx2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwfpZ2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_PvsYUGLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwgQcGLnEeePML3co2BKSQ" keySequence="F1" command="_PveVw2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwgQdWLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" bindingContext="_PvsYO2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwgQdmLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+M" command="_PvhZJGLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwhekWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+V" command="_PvjOTWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwnlMWLnEeePML3co2BKSQ" keySequence="ALT+SHIFT+R" command="_PvoG-mLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwist2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_PvsYU2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwisuGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+O" command="_Pve83WLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwisu2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" bindingContext="_PvsYV2LnEeePML3co2BKSQ">
    <bindings xmi:id="_PwisvGLnEeePML3co2BKSQ" keySequence="CTRL+SHIFT+F5" command="_PvmRi2LnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwmXEmLnEeePML3co2BKSQ" keySequence="CTRL+F5" command="_PvlDc2LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwj62WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_PvsYVmLnEeePML3co2BKSQ">
    <bindings xmi:id="_Pwj62mLnEeePML3co2BKSQ" keySequence="CTRL+V" command="_PvkceWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwozU2LnEeePML3co2BKSQ" keySequence="CTRL+C" command="_PvhZIGLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwj63GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.BreakpointView" bindingContext="_PvsYIWLnEeePML3co2BKSQ">
    <bindings xmi:id="_Pwj63WLnEeePML3co2BKSQ" keySequence="ALT+CR" command="_PvkcZWLnEeePML3co2BKSQ"/>
    <bindings xmi:id="_PwlwBGLnEeePML3co2BKSQ" keySequence="CTRL+CR" command="_Pvfj32LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_Pwkh4mLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_PvsYUWLnEeePML3co2BKSQ">
    <bindings xmi:id="_Pwkh42LnEeePML3co2BKSQ" keySequence="CTRL+O" command="_Pve83WLnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_PwozVGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_PvsYSWLnEeePML3co2BKSQ">
    <bindings xmi:id="_PwozVWLnEeePML3co2BKSQ" keySequence="CTRL+C" command="_PvgK72LnEeePML3co2BKSQ"/>
  </bindingTables>
  <bindingTables xmi:id="_RAQBEWLnEeePML3co2BKSQ" bindingContext="_RAQBEGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQBE2LnEeePML3co2BKSQ" bindingContext="_RAQBEmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQoIWLnEeePML3co2BKSQ" bindingContext="_RAQoIGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQoI2LnEeePML3co2BKSQ" bindingContext="_RAQoImLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQoJWLnEeePML3co2BKSQ" bindingContext="_RAQoJGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQoJ2LnEeePML3co2BKSQ" bindingContext="_RAQoJmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAQoKWLnEeePML3co2BKSQ" bindingContext="_RAQoKGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPMGLnEeePML3co2BKSQ" bindingContext="_RAQoKmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPMmLnEeePML3co2BKSQ" bindingContext="_RARPMWLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPNGLnEeePML3co2BKSQ" bindingContext="_RARPM2LnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPNmLnEeePML3co2BKSQ" bindingContext="_RARPNWLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPOGLnEeePML3co2BKSQ" bindingContext="_RARPN2LnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RARPOmLnEeePML3co2BKSQ" bindingContext="_RARPOWLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2QWLnEeePML3co2BKSQ" bindingContext="_RAR2QGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2Q2LnEeePML3co2BKSQ" bindingContext="_RAR2QmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2RWLnEeePML3co2BKSQ" bindingContext="_RAR2RGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2R2LnEeePML3co2BKSQ" bindingContext="_RAR2RmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2SWLnEeePML3co2BKSQ" bindingContext="_RAR2SGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2S2LnEeePML3co2BKSQ" bindingContext="_RAR2SmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAR2TWLnEeePML3co2BKSQ" bindingContext="_RAR2TGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdUWLnEeePML3co2BKSQ" bindingContext="_RASdUGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdU2LnEeePML3co2BKSQ" bindingContext="_RASdUmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdVWLnEeePML3co2BKSQ" bindingContext="_RASdVGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdV2LnEeePML3co2BKSQ" bindingContext="_RASdVmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdWWLnEeePML3co2BKSQ" bindingContext="_RASdWGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdW2LnEeePML3co2BKSQ" bindingContext="_RASdWmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RASdXWLnEeePML3co2BKSQ" bindingContext="_RASdXGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATEYWLnEeePML3co2BKSQ" bindingContext="_RATEYGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATEY2LnEeePML3co2BKSQ" bindingContext="_RATEYmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATEZWLnEeePML3co2BKSQ" bindingContext="_RATEZGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATEZ2LnEeePML3co2BKSQ" bindingContext="_RATEZmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATEaWLnEeePML3co2BKSQ" bindingContext="_RATEaGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATrcGLnEeePML3co2BKSQ" bindingContext="_RATEamLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATrcmLnEeePML3co2BKSQ" bindingContext="_RATrcWLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATrdGLnEeePML3co2BKSQ" bindingContext="_RATrc2LnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATrdmLnEeePML3co2BKSQ" bindingContext="_RATrdWLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RATreGLnEeePML3co2BKSQ" bindingContext="_RATrd2LnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAUSgWLnEeePML3co2BKSQ" bindingContext="_RAUSgGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAUSg2LnEeePML3co2BKSQ" bindingContext="_RAUSgmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAUShWLnEeePML3co2BKSQ" bindingContext="_RAUShGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAUSh2LnEeePML3co2BKSQ" bindingContext="_RAUShmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAU5kWLnEeePML3co2BKSQ" bindingContext="_RAU5kGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAU5k2LnEeePML3co2BKSQ" bindingContext="_RAU5kmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAU5lWLnEeePML3co2BKSQ" bindingContext="_RAU5lGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAU5l2LnEeePML3co2BKSQ" bindingContext="_RAU5lmLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAVgoWLnEeePML3co2BKSQ" bindingContext="_RAVgoGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAVgo2LnEeePML3co2BKSQ" bindingContext="_RAVgomLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAVgpWLnEeePML3co2BKSQ" bindingContext="_RAVgpGLnEeePML3co2BKSQ"/>
  <bindingTables xmi:id="_RAVgp2LnEeePML3co2BKSQ" bindingContext="_RAVgpmLnEeePML3co2BKSQ"/>
  <rootContext xmi:id="_PuEnhGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_PuEnhWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.platform" name="In Windows" description="A window is open">
      <children xmi:id="_PuEnhmLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.platform" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_PvsYIGLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_PvsYIWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_PvsYImLnEeePML3co2BKSQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_PvsYI2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.javaEditorScope" name="Editing JavaScript Source" description="Editing JavaScript Source Context">
          <children xmi:id="_PvsYUmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.javascriptViewScope" name="JavaScript View" description="JavaScript View Context"/>
        </children>
        <children xmi:id="_PvsYJ2LnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.SQLEditorScope" name="Editing SQL" description="Editing SQL Context"/>
        <children xmi:id="_PvsYKGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.classFileEditorScope" name="Browsing attached Java Source" description="Browsing attached Java Source Context"/>
        <children xmi:id="_PvsYKmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.javaEditorScope" name="Editing Java Source" description="Editing Java Source Context"/>
        <children xmi:id="_PvsYLGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structuredTextEditorScope" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors">
          <children xmi:id="_PvsYLWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.cleanup" name="XML Source Cleanup" description="XML Source Cleanup"/>
          <children xmi:id="_PvsYLmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.comments" name="Source Comments in Structured Text Editors" description="Source Comments in Structured Text Editors"/>
          <children xmi:id="_PvsYL2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.core.jspsource" name="JSP Source" description="JSP Source"/>
          <children xmi:id="_PvsYMGLnEeePML3co2BKSQ" elementId="org.eclipse.core.runtime.xml" name="Editing XML Source" description="Editing XML Source"/>
          <children xmi:id="_PvsYMWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.occurrences" name="XML Source Occurrences" description="XML Source Occurrences"/>
          <children xmi:id="_PvsYMmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.grammar" name="XML Source Grammar" description="XML Source Grammar"/>
          <children xmi:id="_PvsYM2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.comments" name="XML Source Comments" description="XML Source Comments"/>
          <children xmi:id="_PvsYNWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.expand" name="XML Source Expand/Collapse" description="XML Source Expand/Collapse"/>
          <children xmi:id="_PvsYN2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.hideFormat" name="Editing in Structured Text Editors" description="Editing in Structured Text Editors"/>
          <children xmi:id="_PvsYOmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.selection" name="XML Source Selection" description="XML Source Selection"/>
          <children xmi:id="_PvsYO2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.ui.structured.text.editor.jsp.scope" name="Editing JSP Source" description="Editing JSP Source"/>
          <children xmi:id="_PvsYQGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.navigation" name="XML Source Navigation" description="XML Source Navigation"/>
          <children xmi:id="_PvsYS2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.css.core.csssource" name="Editing CSS Source" description="Editing CSS Source"/>
          <children xmi:id="_PvsYTWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.html.core.htmlsource" name="Editing HTML Source" description="Editing HTML Source"/>
          <children xmi:id="_PvsYVGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.dependencies" name="XML Source Dependencies" description="XML Source Dependencies"/>
          <children xmi:id="_PvsYWGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.html.occurrences" name="HTML Source Occurrences" description="HTML Source Occurrences"/>
        </children>
        <children xmi:id="_PvsYNmLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.AntEditorScope" name="Editing Ant Buildfiles" description="Editing Ant Buildfiles Context"/>
        <children xmi:id="_PvsYOWLnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.editorContext" name="Using Web Page Editor" description="Key binding context when using the web page editor"/>
        <children xmi:id="_PvsYQWLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.pdeEditorContext" name="PDE editor" description="The context used by PDE editors"/>
        <children xmi:id="_PvsYTGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.text.editor.context" name="Editing XSD context"/>
        <children xmi:id="_PvsYT2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_PvsYUGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_PvsYUWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_PvsYU2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_PvsYVWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.propertiesEditorScope" name="Editing Properties Files" description="Editing Properties Files Context"/>
        <children xmi:id="_PvsYV2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsf.facesconfig.editorContext" name="In Faces Config Editor" description="Key binding context when using the Faces Config Editor"/>
      </children>
      <children xmi:id="_PvsYJGLnEeePML3co2BKSQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_PvsYJmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_PvsYK2LnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.schemaobjecteditor.schemaediting" name="Schema Object Editor" description="Schema Object Editor"/>
      <children xmi:id="_PvsYPmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_PvsYP2LnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_PvsYQmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_PvsYQ2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.serverViewScope" name="In Servers View" description="In Servers View"/>
      <children xmi:id="_PvsYRGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_PvsYRWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_PvsYRmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsl.debug.ui.context" name="XSLT Debugging" description="Context for debugging XSLT"/>
        <children xmi:id="_PvsYR2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.debugging" name="Debugging Java" description="Debugging Java programs"/>
      </children>
      <children xmi:id="_PvsYSWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_PvsYSmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_PvsYTmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_PvsYVmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_PuEnh2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.platform" name="In Dialogs" description="A dialog is open"/>
  </rootContext>
  <rootContext xmi:id="_PvsYJWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_PvsYKWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.breadcrumbEditorScope" name="Editor Breadcrumb Navigation" description="Editor Breadcrumb Navigation Context"/>
  <rootContext xmi:id="_PvsYNGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.editor.sourceView" name="XSD Editor Source View" description="XSD Editor Source View"/>
  <rootContext xmi:id="_PvsYOGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.wsdl.ui.editor.sourceView" name="WSDL Editor Source View" description="WSDL Editor Source View"/>
  <rootContext xmi:id="_PvsYPGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.editor.designView" name="XSD Editor Design View" description="XSD Editor Design View"/>
  <rootContext xmi:id="_PvsYPWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_PvsYSGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.wsdl.ui.editor.designView" name="WSDL Editor Design View" description="WSDL Editor Design View"/>
  <rootContext xmi:id="_RAQBEGLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.actionSet.presentation" name="Auto::org.eclipse.ant.ui.actionSet.presentation"/>
  <rootContext xmi:id="_RAQBEmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.actionSet" name="Auto::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet"/>
  <rootContext xmi:id="_RAQoIGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_RAQoImLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_RAQoJGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_RAQoJmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_RAQoKGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_RAQoKmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_RARPMWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.JDTDebugActionSet" name="Auto::org.eclipse.jdt.debug.ui.JDTDebugActionSet"/>
  <rootContext xmi:id="_RARPM2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.JUnitActionSet" name="Auto::org.eclipse.jdt.junit.JUnitActionSet"/>
  <rootContext xmi:id="_RARPNWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.jdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_RARPN2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.jdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_RARPOWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavaActionSet" name="Auto::org.eclipse.jdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_RAR2QGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.A_OpenActionSet" name="Auto::org.eclipse.jdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_RAR2QmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.CodingActionSet" name="Auto::org.eclipse.jdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_RAR2RGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.SearchActionSet" name="Auto::org.eclipse.jdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_RAR2RmLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation" name="Auto::org.eclipse.jpt.jpa.ui.actionSet.jpaElementCreation"/>
  <rootContext xmi:id="_RAR2SGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.j2ee.J2eeMainActionSet" name="Auto::org.eclipse.jst.j2ee.J2eeMainActionSet"/>
  <rootContext xmi:id="_RAR2SmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_RAR2TGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.java.actionSet" name="Auto::org.eclipse.mylyn.java.actionSet"/>
  <rootContext xmi:id="_RASdUGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.java.actionSet.browsing" name="Auto::org.eclipse.mylyn.java.actionSet.browsing"/>
  <rootContext xmi:id="_RASdUmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.doc.actionSet" name="Auto::org.eclipse.mylyn.doc.actionSet"/>
  <rootContext xmi:id="_RASdVGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_RASdVmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_RASdWGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.SearchActionSet" name="Auto::org.eclipse.pde.ui.SearchActionSet"/>
  <rootContext xmi:id="_RASdWmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_RASdXGLnEeePML3co2BKSQ" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_RATEYGLnEeePML3co2BKSQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_RATEYmLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_RATEZGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_RATEZmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_RATEaGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_RATEamLnEeePML3co2BKSQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_RATrcWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_RATrc2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_RATrdWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_RATrd2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_RAUSgGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_RAUSgmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_RAUShGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation" name="Auto::org.eclipse.wst.jsdt.ui.text.java.actionSet.presentation"/>
  <rootContext xmi:id="_RAUShmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet"/>
  <rootContext xmi:id="_RAU5kGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavaActionSet" name="Auto::org.eclipse.wst.jsdt.ui.JavaActionSet"/>
  <rootContext xmi:id="_RAU5kmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.A_OpenActionSet" name="Auto::org.eclipse.wst.jsdt.ui.A_OpenActionSet"/>
  <rootContext xmi:id="_RAU5lGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.CodingActionSet" name="Auto::org.eclipse.wst.jsdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_RAU5lmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.SearchActionSet" name="Auto::org.eclipse.wst.jsdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_RAVgoGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.new.actionSet" name="Auto::org.eclipse.wst.server.ui.new.actionSet"/>
  <rootContext xmi:id="_RAVgomLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.internal.webbrowser.actionSet" name="Auto::org.eclipse.wst.server.ui.internal.webbrowser.actionSet"/>
  <rootContext xmi:id="_RAVgpGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.web.ui.wizardsActionSet" name="Auto::org.eclipse.wst.web.ui.wizardsActionSet"/>
  <rootContext xmi:id="_RAVgpmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.ws.explorer.explorer" name="Auto::org.eclipse.wst.ws.explorer.explorer"/>
  <descriptors xmi:id="_P0nUYGLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_P0qXsGLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.views.AntView" label="Ant" iconURI="platform:/plugin/org.eclipse.ant.ui/icons/full/eview16/ant_view.png" tooltip="" category="Ant" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Ant</tags>
  </descriptors>
  <descriptors xmi:id="_P0xFYGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.DataSourceExplorerNavigator" label="Data Source Explorer" iconURI="platform:/plugin/org.eclipse.datatools.connectivity.ui.dse/icons/full/cview16/enterprise_explorer.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_P0yTgGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.plan.planView" label="Execution Plan" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.plan/icons/sqlplan.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_P09SoGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.result.resultView" label="SQL Results" iconURI="platform:/plugin/org.eclipse.datatools.sqltools.result.ui/icons/sqlresult.gif" tooltip="" category="Data Management" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Data Management</tags>
  </descriptors>
  <descriptors xmi:id="_P1EnYGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1F1gGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1GckGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1HDoGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1IRwGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1I40GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1Jf4GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1KG8GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_P1LVEGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_P1L8IGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_P1MjMGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_P1NKQGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_P1NKQWLnEeePML3co2BKSQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P1Sp0GLnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_P1XiUGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.DisplayView" label="Display" iconURI="platform:/plugin/org.eclipse.jdt.debug.ui/icons/full/etool16/disp_sbook.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P1YJYGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.ResultView" label="JUnit" iconURI="platform:/plugin/org.eclipse.jdt.junit/icons/full/eview16/junit.gif" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1ca0GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.PackageExplorer" label="Package Explorer" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/package.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1do8GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.TypeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/class_hi.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1eQAGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.ProjectsView" label="Projects" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/projects.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_P1e3EGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.PackagesView" label="Packages" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/packages.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_P1feIGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.TypesView" label="Types" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/types.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_P1gFMGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.MembersView" label="Members" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/members.png" tooltip="" category="Java Browsing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java Browsing</tags>
  </descriptors>
  <descriptors xmi:id="_P1gsQGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/call_hierarchy.png" tooltip="" allowMultiple="true" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1hTUGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/templates.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P1h6YGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/source.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1ihcGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavadocView" label="Javadoc" iconURI="platform:/plugin/org.eclipse.jdt.ui/icons/full/eview16/javadoc.png" tooltip="" category="Java" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Java</tags>
  </descriptors>
  <descriptors xmi:id="_P1jIgGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.ui.jpaStructureView" label="JPA Structure" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-structure.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_P1jvkGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.ui.jpaDetailsView" label="JPA Details" iconURI="platform:/plugin/org.eclipse.jpt.jpa.ui/images/views/jpa-details.gif" tooltip="" category="JPA" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JPA</tags>
  </descriptors>
  <descriptors xmi:id="_P1kWoGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsf.ui.component.ComponentTreeView" label="JSF Component Tree" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_P1k9sGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsf.ui.tagregistry.TagRegistryView" label="Tag Registry" iconURI="platform:/plugin/org.eclipse.jst.jsf.ui/icons/obj16/library_obj.gif" tooltip="" category="JavaServer Faces" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaServer Faces</tags>
  </descriptors>
  <descriptors xmi:id="_P1rEUGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.ws.jaxws.ui.views.AnnotationsView" label="Annotation Properties" iconURI="platform:/plugin/org.eclipse.jst.ws.jaxws.ui/icons/eview16/prop_ps.gif" tooltip="" category="Web Services" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Web Services</tags>
  </descriptors>
  <descriptors xmi:id="_P1xyAGLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.views.MavenRepositoryView" label="Maven Repositories" iconURI="platform:/plugin/org.eclipse.m2e.core.ui/icons/maven_indexes.gif" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_P1yZEGLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.views.MavenBuild" label="Maven Workspace Build" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Maven" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Maven</tags>
  </descriptors>
  <descriptors xmi:id="_P1zAIGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_P2BpoGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.gif" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_P2CQsGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_P2C3wGLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.p2.ui.RepositoryExplorer" label="Repository Explorer" iconURI="platform:/plugin/org.eclipse.oomph.p2.ui/icons/obj16/repository.gif" tooltip="" category="Oomph" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Oomph</tags>
  </descriptors>
  <descriptors xmi:id="_P2De0GLnEeePML3co2BKSQ" elementId="org.eclipse.pde.api.tools.ui.views.apitooling.views.apitoolingview" label="API Tools" iconURI="platform:/plugin/org.eclipse.pde.api.tools.ui/icons/full/obj16/api_tools.gif" tooltip="" category="API Tools" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:API Tools</tags>
  </descriptors>
  <descriptors xmi:id="_P2Es8GLnEeePML3co2BKSQ" elementId="org.eclipse.pde.runtime.RegistryBrowser" label="Plug-in Registry" iconURI="platform:/plugin/org.eclipse.pde.runtime/icons/eview16/registry.gif" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_P2JlcGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.PluginsView" label="Plug-ins" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/eview16/plugin_depend.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_P2KzkGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.DependenciesView" label="Plug-in Dependencies" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/req_plugins_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_P2KzkWLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.TargetPlatformState" label="Target Platform State" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/target_profile_xml_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_P2LaoGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.ImageBrowserView" label="Plug-in Image Browser" iconURI="platform:/plugin/org.eclipse.pde.ui/icons/obj16/psearch_obj.png" tooltip="" category="Plug-in Development" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Plug-in Development</tags>
  </descriptors>
  <descriptors xmi:id="_P2LaoWLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.apidocs.rcp.views.apidocs" label="API Docs" iconURI="platform:/plugin/org.eclipse.recommenders.apidocs.rcp/icons/view16/apidocs.png" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_P2VLoGLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.models.rcp.views.projectCoordinates" label="Project Coordinates" iconURI="platform:/plugin/org.eclipse.recommenders.coordinates.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_P2bSQGLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.models.rcp.views.modelRepositories" label="Model Repositories" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_P2hY4GLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.models.rcp.views.dependencyOverview" label="Dependency Overview" iconURI="platform:/plugin/org.eclipse.recommenders.models.rcp/icons/view16/depinsp.gif" tooltip="" category="Code Recommenders" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Code Recommenders</tags>
  </descriptors>
  <descriptors xmi:id="_P2hY4WLnEeePML3co2BKSQ" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2rJ4GLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2rw8GLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2sYAGLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2sYAWLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2s_EGLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2tmIGLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_P2uNMGLnEeePML3co2BKSQ" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P2u0QGLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P2vbUGLnEeePML3co2BKSQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_P20T0GLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_P2064GLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_P2-E0GLnEeePML3co2BKSQ" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_P2-r4GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P2_S8GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P2_6AGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_P3FZkGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3GnsGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3HOwGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3H10GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3H10WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3Ic4GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3JD8GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3JrAGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3KSEGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3K5IGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3LgMGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3ebIGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.common.snippets.internal.ui.SnippetsView" label="Snippets" iconURI="platform:/plugin/org.eclipse.wst.common.snippets/icons/snippets_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_P3fCMGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.internet.monitor.view" label="TCP/IP Monitor" iconURI="platform:/plugin/org.eclipse.wst.internet.monitor.ui/icons/cview16/monitorView.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_P3mW8GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" label="Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/class_hi.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_P3nlEGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" label="Script Explorer" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/package.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_P3nlEWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.callhierarchy.view" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/call_hierarchy.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_P3oMIGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.SourceView" label="Declaration" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/source.gif" tooltip="" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_P3ozMGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavadocView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.jsdt.ui/icons/full/eview16/javadoc.gif" tooltip="JavaScript Documentation" category="JavaScript" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:JavaScript</tags>
  </descriptors>
  <descriptors xmi:id="_P3ozMWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui.ServersView" label="Servers" iconURI="platform:/plugin/org.eclipse.wst.server.ui/icons/cview16/servers_view.gif" tooltip="" category="Server" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:Server</tags>
  </descriptors>
  <descriptors xmi:id="_P3qBUGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.views.annotations.XMLAnnotationsView" label="Documentation" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/obj16/comment_obj.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_P3qoYGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.contentmodel.view" label="Content Model" iconURI="platform:/plugin/org.eclipse.wst.xml.ui/icons/full/view16/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_P3rPcGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.views.XPathView" label="XPath" iconURI="platform:/plugin/org.eclipse.wst.xml.xpath.ui/icons/full/xpath.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_P3r2gGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsl.jaxp.debug.ui.resultview" label="Result" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <descriptors xmi:id="_P3sdkGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsl.ui.view.outline" label="Stylesheet Model" iconURI="platform:/plugin/org.eclipse.wst.xsl.ui/icons/full/hierarchy.gif" tooltip="" category="XML" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <tags>View</tags>
    <tags>categoryTag:XML</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_PvduoGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvduoWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVsGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PveVsWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_PveVsmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to field" description="Invokes quick assist and selects 'Convert local variable to field'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVs2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVtGLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_Pvb5l2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVtWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVtmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVt2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInTypeHierarchyView" commandName="Show JavaScript Element Type Hierarchy" description="Show a JavaScript element in the Type Hierarchy view" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PveVuGLnEeePML3co2BKSQ" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_PveVuWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVumLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVu2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVvGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVvWLnEeePML3co2BKSQ" elementId="org.eclipse.epp.mpc.ui.command.showMarketplaceWizard" commandName="Eclipse Marketplace" description="Show the Eclipse Marketplace wizard" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVvmLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.toggleMarkOccurrences" commandName="Toggle Ant Mark Occurrences" description="Toggles mark occurrences in Ant editors" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVv2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVwGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.callhierarchy.view" commandName="JavaScript Call Hierarchy" description="Show the Call Hierarchy view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVwWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.commands.export" commandName="Export Profiles Command" description="Command to export connection profiles" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVwmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.junitShortcut.debug" commandName="Debug JUnit Test" description="Debug JUnit Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVw2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVxGLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVxWLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.open.declaration.command" commandName="Open Declaration" description="Opens the Ant editor on the referenced element" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVxmLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.commands.openBrowserDialog" commandName="Open a Web browser" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PveVx2LnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_PveVyGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Methods" description="Add delegate methods for a type's fields" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVyWLnEeePML3co2BKSQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PveVymLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.views.XPathView.prefixes" commandName="&amp;Edit Namespace Prefixes" category="_Pvb5fmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8wGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.toggleCommentAction" commandName="Toggle Comment" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8wWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8wmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8w2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8xGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8xWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8xmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.searchForTask" commandName="Search Repository for Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8x2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8yGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8yWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8ymLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8y2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8zGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8zWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_Pvb5iWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8zmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve8z2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.commands.openElementInEditor" commandName="Open JavaScript Element" description="Open a JavaScript element in its editor" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pve80GLnEeePML3co2BKSQ" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_Pve80WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.TypesView" commandName="JavaScript Types" description="Show the Types view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve80mLnEeePML3co2BKSQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve802LnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.importProjects" commandName="Import Projects" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve81GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve81WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Fields" description="Choose fields to initialize and constructor from superclass to call " category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve81mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve812LnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.disable.grammar.constraints" commandName="Turn off Grammar Constraints" description="Turn off grammar Constraints" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve82GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.commands.showElementInTypeHierarchyView" commandName="Show Java Element Type Hierarchy" description="Show a Java element in the Type Hierarchy view" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pve82WLnEeePML3co2BKSQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_Pve82mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve822LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve83GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve83WLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve83mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve832LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.MembersView" commandName="JavaScript Members" description="Show the Members view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve84GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve84WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve84mLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.commands.openBrowser" commandName="Open a Web browser" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pve842LnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_Pve85GLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve85WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve85mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve852LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve86GLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.refreshCache" commandName="Refresh Remote Cache" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve86WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify var access" description="Invokes quick assist and selects 'Qualify var access'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve86mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.run" commandName="Run Java Applet" description="Run Java Applet" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve862LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve87GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve87WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve87mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve872LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve88GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve88WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve88mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleBreadcrumb" commandName="Toggle Java Editor Breadcrumb" description="Toggle the Java editor breadcrumb" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve882LnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve89GLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.renameInFile" commandName="Rename In File" description="Renames all references within the same buildfile" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pve89WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj0GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.debug.ui.script.opensource" commandName="Open Source" description="Shows the JavaScript source for the selected script element" category="_Pvb5jmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj0WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj0mLnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_Pvb5nGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj02LnEeePML3co2BKSQ" elementId="org.eclipse.m2e.actions.LifeCycleGenerateSources.run" commandName="Run Maven Generate Sources" description="Run Maven Generate Sources" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj1GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj1WLnEeePML3co2BKSQ" elementId="org.eclipse.jst.ws.jaxws.ui.configure.handlers" commandName="Configure Handlers" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj1mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structure.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj12LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.AddExceptionBreakpoint" commandName="Add Java Exception Breakpoint" description="Add a Java exception breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj2GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj2WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj2mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj22LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvfj3GLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_Pvfj3WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj3mLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj32LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.debug.ui.breakpoint.properties" commandName="JavaScript Breakpoint Properties" description="View and edit the properties for a given JavaScript breakpoint" category="_Pvb5jmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj4GLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj4WLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Terminal" category="_Pvb5j2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj4mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj42LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj5GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or method" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj5WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj5mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj52LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj6GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj6WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj6mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.inlineLocal.assist" commandName="Quick Assist - Inline local variable" description="Invokes quick assist and selects 'Inline local variable'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj62LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj7GLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.newEntity" commandName="JPA Entity" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj7WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj7mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj72LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.return.continue.targets" commandName="Search break/continue Target Occurrences in File" description="Search for break/continue target occurrences of a selected target name" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj8GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj8WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj8mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter methods for type's fields" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj82LnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.edit.text.format" commandName="Format Source" description="Format a PDE Source Page" category="_Pvb5gWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj9GLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.result.terminate" commandName="Terminate Result" category="_Pvb5eWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj9WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj9mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj92LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() functions for the type" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj-GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj-WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj-mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj-2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj_GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.implementation" commandName="Open Implementation" description="Opens the Implementations of a method in its hierarchy" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj_WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.gotoTest" commandName="Referring Tests" description="Referring Tests" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj_mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvfj_2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.commands.showElementInPackageView" commandName="Show Java Element in Package Explorer" description="Select Java element in the Package Explorer view" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvfkAGLnEeePML3co2BKSQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_PvfkAWLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.performDropdown" commandName="Perform Dropdown" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK4GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.InstanceCount" commandName="Instance Count" description="View the instance count of the selected type loaded in the target VM" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK4WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.getter.setter" commandName="Generate Getters and Setters" description="Generate Getter and Setter functions for type's vars" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK4mLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.OpenUrlCommand" commandName="OpenUrlCommand" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvgK42LnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.urlParam" name="url" optional="false"/>
  </commands>
  <commands xmi:id="_PvgK5GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK5WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK5mLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.addAllPluginsToJavaSearch" commandName="Add All Plug-ins to Java Search" description="Adds all plug-ins in the target platform to java search" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK52LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvgK6GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_PvgK6WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.convert.anonymous.to.nested" commandName="Convert Anonymous Class to Nested" description="Convert an anonymous class to a nested class" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK6mLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.quickOutline" commandName="Quick Outline" description="Open a quick outline popup dialog for a given editor input" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK62LnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK7GLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_Pvb5hmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK7WLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.profiles.ui.commands.selectMavenProfileCommand" commandName="Select Maven Profiles" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK7mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK72LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK8GLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK8WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavaBrowsingPerspective" commandName="JavaScript Browsing" description="Show the JavaScript Browsing perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK8mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK82LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.remove.block.comment" commandName="Remove Block Comment" description="Remove the block comment enclosing the selection" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK9GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK9WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK9mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK92LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK-GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK-WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.working.set" commandName="Declaration in Working Set" description="Search for declarations of the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK-mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK-2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK_GLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK_WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK_mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgK_2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLAGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLAWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLAmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.java.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_Pvb5jWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLA2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.runtime.spy.commands.spyCommand" commandName="Plug-in Selection Spy" description="Show the Plug-in Spy" category="_Pvb5n2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLBGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLBWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLBmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLB2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLCGLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.ui.ToggleOfflineMode" commandName="Toggle Offline Mode" category="_Pvb5gmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLCWLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.openLog" commandName="Open Setup Log" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLCmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLC2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_Pvb5d2LnEeePML3co2BKSQ">
    <parameters xmi:id="_PvgLDGLnEeePML3co2BKSQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_PvgLDWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLDmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLD2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.find.broken.nls.keys" commandName="Find Broken Externalized Strings" description="Finds undefined, duplicate and unused externalized string keys in property files" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgLEGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Dynamic Help" description="Open the dynamic help" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx8GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into Java comments" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx8WLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx8mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx82LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx9GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the JavaScript file" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx9WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx9mLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx92LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx-GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx-WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx-mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx-2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx_GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavaBrowsingPerspective" commandName="Java Browsing" description="Show the Java Browsing perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx_WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx_mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvgx_2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyAGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyAWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyAmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.ProjectsView" commandName="JavaScript Projects" description="Show the Projects view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyA2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.previousSibling" commandName="Previous Sibling" description="Go to Previous Sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyBGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyBWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyBmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory function to encapsulate invocation of the selected constructor" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyB2LnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.antShortcut.run" commandName="Run Ant Build" description="Run Ant Build" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyCGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyCWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyCmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.Inspect" commandName="Inspect" description="Inspect result of evaluating selected text" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyC2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyDGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyDWLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyDmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file.quickMenu" commandName="Show Occurrences in File Quick Menu" description="Shows the Occurrences in File quick menu" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyD2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyEGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.convertLocalToField.assist" commandName="Quick Assist - Convert local variable to var" description="Invokes quick assist and selects 'Convert local variable to var'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyEWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyEmLnEeePML3co2BKSQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyE2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyFGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyFWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyFmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.Watch" commandName="Watch" description="Create new watch expression" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyF2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyGGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_Pvb5dmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyGWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyGmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.stop" commandName="Stop" description="Stop the server" category="_Pvb5dWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvgyG2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_Pvb5lWLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvgyHGLnEeePML3co2BKSQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_PvgyHWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZAGLnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZAWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZAmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.saveAsTemplateAction" commandName="Save as Template" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZA2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZBGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZBWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZBmLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.p2.ui.ExploreRepository" commandName="Explore Repository" category="_Pvb5fGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZB2LnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="Open Refactoring History " description="Opens the refactoring history" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZCGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.hierarchy" commandName="Read Access in Hierarchy" description="Search for read references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZCWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZCmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZC2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZDGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZDWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZDmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.working.set" commandName="Write Access in Working Set" description="Search for write references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZD2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvhZEGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_PvhZEWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZEmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZE2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZFGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.import" commandName="Add Import" description="Create import statement on selection" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZFWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_Pvb5iWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZFmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZF2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZGGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZGWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZGmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.working.set" commandName="Implementors in Working Set" description="Search for implementors of the selected interface in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZG2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZHGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.refreshFromDatabaseAction" commandName="Refresh from Database" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZHWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteAsOneStatementAction" commandName="Execute Selected Text As One Statement" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZHmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZH2LnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.commands.addrepository" commandName="New Repository Profile Command" description="Command to create a new repository profile" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZIGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZIWLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.perform.startup" commandName="Perform Setup Tasks (Startup)" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZImLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavaPerspective" commandName="JavaScript" description="Show the JavaScript perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZI2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZJGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.ui.add.imports" commandName="Add Im&amp;port" description="Create import declaration for selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZJWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.modify.method.parameters" commandName="Change Function Signature" description="Change function signature includes parameter names and parameter order" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZJmLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.upgradeToEclipseLinkMappingFile" commandName="Upgrade to EclipseLink Mapping File" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZJ2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_Pvb5eGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvhZKGLnEeePML3co2BKSQ" elementId="url" name="URL"/>
    <parameters xmi:id="_PvhZKWLnEeePML3co2BKSQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_PvhZKmLnEeePML3co2BKSQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_PvhZK2LnEeePML3co2BKSQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_PvhZLGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static var and uses the new static var" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZLWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.result.removeAllInstances" commandName="Remove All Visible Results" category="_Pvb5eWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZLmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZL2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZMGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZMWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZMmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZM2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZNGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_Pvb5eGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvhZNWLnEeePML3co2BKSQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_PvhZNmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZN2LnEeePML3co2BKSQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvhZOGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAEGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAEWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAEmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.interface" commandName="Extract Interface" description="Extract a set of members into a new interface and try to use the new interface" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAE2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAFGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAFWLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.createAntBuildFile" commandName="Create Ant Build File" description="Creates an Ant build file for the current project" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAFmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAF2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAGGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.copycolumn" commandName="Copy" category="_Pvb5kmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAGWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAGmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAG2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAHGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAHWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.debugAction" commandName="Debug" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAHmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.javadoc.comment" commandName="Add Javadoc Comment" description="Add a Javadoc comment stub to the member element" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAH2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAIGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAIWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAImLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jaxb.ui.generateJaxbClasses" commandName="JAXB Classes..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAI2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAJGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.openDependencies" commandName="Open Plug-in Dependencies" description="Opens the plug-in dependencies view for the current plug-in" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAJWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in Java editors" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAJmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAJ2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected method" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAKGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAKWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAKmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAK2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviALGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviALWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviALmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAL2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAMGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAMWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a Java editor" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAMmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAM2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected method" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviANGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviANWLnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviANmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAN2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_Pvb5iWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAOGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAOWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.reload.dependencies" commandName="Reload Dependencies" description="Reload Dependencies" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAOmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.generate.xml" commandName="&amp;XML File..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAO2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAPGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addImport" commandName="Quick Fix - Add import" description="Invokes quick assist and selects 'Add import'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAPWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunFailedFirst" commandName="Rerun JUnit Test - Failures First" description="Rerun JUnit Test - Failures First" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAPmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_Pvb5d2LnEeePML3co2BKSQ">
    <parameters xmi:id="_PviAP2LnEeePML3co2BKSQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_PviAQGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.refactor.makeTypeGlobal" commandName="Make &amp;Anonymous Type Global" description="Promotes anonymous type to global level and replaces its references" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PviAQWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.project" commandName="Implementors in Project" description="Search for implementors of the selected interface in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinIGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinIWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavaPerspective" commandName="Java" description="Show the Java perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinImLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.indent" commandName="Correct Indentation" description="Corrects the indentation of the selected lines" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinI2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinJGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinJWLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinJmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinJ2LnEeePML3co2BKSQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_Pvb5nWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinKGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinKWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinKmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinK2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinLGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.externalizeStrings" commandName="Externalize Strings in Plug-ins" description="Extract translatable strings from plug-in files" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinLWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinLmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.commands.showcategory" commandName="Show Category" description="Show Category" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinL2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinMGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.extractMethodInplace.assist" commandName="Quick Assist - Extract method" description="Invokes quick assist and selects 'Extract to method'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinMWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinMmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addThrowsDecl" commandName="Quick Fix - Add throws declaration" description="Invokes quick assist and selects 'Add throws declaration'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinM2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinNGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinNWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinNmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in JavaScript editors" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinN2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinOGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinOWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeMapAs" commandName="Map As" category="_Pvb5f2LnEeePML3co2BKSQ">
    <parameters xmi:id="_PvinOmLnEeePML3co2BKSQ" elementId="specifiedPersistentAttributeMappingKey" name="specified mapping key" optional="false"/>
    <parameters xmi:id="_PvinO2LnEeePML3co2BKSQ" elementId="defaultPersistentAttributeMappingKey" name="default mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_PvinPGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.index.ui.command.ResetIndex" commandName="Refresh Search Index" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinPWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinPmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_Pvb5l2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinP2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinQGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to var" description="Invokes quick assist and selects 'Assign to var'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinQWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.convertJavaGenerators" commandName="Move Java Generators to XML..." category="_Pvb5fWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinQmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinQ2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinRGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinRWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinRmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinR2LnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.commands.addprofile" commandName="New Connection Profile Command" description="Command to create a new connection profile" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvinSGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.ui.ignoreCategory" name="ignoreCategory"/>
    <parameters xmi:id="_PvinSWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.ui.useSelection" name="useSelection"/>
  </commands>
  <commands xmi:id="_PvinSmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinS2LnEeePML3co2BKSQ" elementId="org.eclipse.m2e.discovery.ui" commandName="m2e Marketplace" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinTGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinTWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinTmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.project" commandName="References in Project" description="Search for references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinT2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.indirection" commandName="Introduce Indirection" description="Introduce an indirection to encapsulate invocations of a selected function" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinUGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinUWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_PvbSYGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvinUmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_PvinU2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvinVGLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_Pvb5hmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOMGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.generateEntities" commandName="Generate Entities from Tables..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOMWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOMmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOM2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.element" commandName="Move - Refactoring " description="Move the selected element to a new location" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjONGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjONWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.indent" commandName="Indent Line" description="Indents the current line" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjONmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.quick.format" commandName="Format Element" description="Format enclosing text element" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjON2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.vertical" commandName="Vertical Layout" category="_Pvb5iGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOOGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOOWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.addToPersistenceUnit" commandName="Add to Persistence Unit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOOmLnEeePML3co2BKSQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOO2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_Pvb5d2LnEeePML3co2BKSQ">
    <parameters xmi:id="_PvjOPGLnEeePML3co2BKSQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_PvjOPWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOPmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOP2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOQGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOQWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOQmLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.ui.command.updateProject" commandName="Update Project" description="Update Maven Project configuration and dependencies" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOQ2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjORGLnEeePML3co2BKSQ" elementId="refresh.schema.editor.action.id" commandName="Refresh from Server" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjORWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjORmLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.organizeManifest" commandName="Organize Manifests" description="Cleans up plug-in manifest files" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOR2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOSGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.junitShortcut.run" commandName="Run JUnit Test" description="Run JUnit Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOSWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.convertJavaQueries" commandName="Move Java Queries to XML..." category="_Pvb5fWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOSmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOS2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOTGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.show.outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOTWLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.ui.refactor.move" commandName="Move" description="Move a Java Element to another package" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOTmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the compilation unit" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOT2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOUGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.pastecolumn" commandName="Paste" category="_Pvb5kmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOUWLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.pomFileAction.run" commandName="Run Maven Build" description="Run Maven Build" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOUmLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.actions.LifeCycleInstall.run" commandName="Run Maven Install" description="Run Maven Install" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOU2LnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteCurrentAction" commandName="Execute Current Text" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOVGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOVWLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.perform" commandName="Perform Setup Tasks" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOVmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOV2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.refactor.create.refactoring.script" commandName="Create Script" description="Create a refactoring script from refactorings on the local workspace" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOWGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOWWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOWmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOW2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOXGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvjOXWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_Pvb5gGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1QGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1QWLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.antShortcut.debug" commandName="Debug Ant Build" description="Debug Ant Build" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1QmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1Q2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1RGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1RWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_Pvb5eGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvj1RmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_Pvj1R2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1SGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.nextSibling" commandName="Next Sibling" description="Go to Next Sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1SWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.local.variable" commandName="Extract Local Variable" description="Extracts an expression into a new local variable and uses the new local variable" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1SmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type" commandName="Open Type" description="Open a type in a JavaScript editor" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1S2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_Pvb5lWLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvj1TGLnEeePML3co2BKSQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_Pvj1TWLnEeePML3co2BKSQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_Pvj1TmLnEeePML3co2BKSQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_Pvj1T2LnEeePML3co2BKSQ" elementId="revert.schema.editor.action.id" commandName="Revert Object" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1UGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.debug" commandName="Debug XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1UWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1UmLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.ui.command.addDependency" commandName="Add Maven Dependency" description="Add Maven Dependency" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1U2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.Execute" commandName="Execute" description="Evaluate selected text" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1VGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.block.comment" commandName="Add Block Comment" description="Enclose the selection with a block comment" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1VWLnEeePML3co2BKSQ" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_Pvb5kWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1VmLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.core.synchronizeClasses" commandName="Synchronize Class List" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1V2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.navigate.gotopackage" commandName="Go to Folder" description="Go to Folder" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1WGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1WWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1WmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.launchShortcut.run" commandName="Run on Server" description="Run the current selection on a server" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1W2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.move.inner.to.top.level" commandName="Convert Member Type to Top Level" description="Convert member type to top level" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1XGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1XWLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.UpdateCenterCommand" commandName="GlassFish Update Center" description="GlassFish Update Center" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1XmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.launchShortcut.debug" commandName="Debug on Server" description="Debug the current selection on a server" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1X2LnEeePML3co2BKSQ" elementId="org.eclipse.m2e.editor.RenameArtifactAction" commandName="Rename Maven Artifact..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1YGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1YWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSQLAction" commandName="Execute All" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1YmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1Y2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.api.tools.ui.remove.filters" commandName="Remove &amp;API Problem Filters..." description="Remove API problem filters for this project" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1ZGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1ZWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1ZmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1Z2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1aGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1aWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the compilation unit" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1amLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvj1a2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_Pvj1bGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1bWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.hashcode.equals" commandName="Generate hashCode() and equals()" description="Generates hashCode() and equals() methods for the type" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1bmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvj1b2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_Pvj1cGLnEeePML3co2BKSQ" elementId="sed.tabletree.collapseAll" commandName="Collapse All" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvj1cWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcUGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_Pvb5e2LnEeePML3co2BKSQ">
    <parameters xmi:id="_PvkcUWLnEeePML3co2BKSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_PvkcUmLnEeePML3co2BKSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_PvkcU2LnEeePML3co2BKSQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_PvkcVGLnEeePML3co2BKSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_PvkcVWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcVmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.add.unimplemented.constructors" commandName="Generate Constructors from Superclass" description="Evaluate and add constructors from superclass" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcV2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcWGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcWWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcWmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcW2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcXGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcXWLnEeePML3co2BKSQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcXmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaeditor.cutcolumn" commandName="Cut" category="_Pvb5kmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcX2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.debug.ui.evaluate.command" commandName="Evaluate" description="Evaluates the selected text in the JavaScript editor" category="_Pvb5jmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcYGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcYWLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcYmLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.OpenDomainHomeCommand" commandName="OpenDomainHomeCommand" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcY2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcZGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcZWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.breakpoint.properties" commandName="Java Breakpoint Properties" description="View and edit the properties for a given Java breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcZmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcZ2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcaGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcaWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcamLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkca2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.views.XPathView.processor.xpathprocessor" commandName="XPath Processor" category="_Pvb5fmLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvkcbGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_PvkcbWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcbmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_Pvb5j2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkcb2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_Pvb5eGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvkccGLnEeePML3co2BKSQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_PvkccWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkccmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkcc2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcdGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcdWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcdmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkcd2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkceGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.debug" commandName="Debug OSGi Framework" description="Debug OSGi Framework" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkceWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcemLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkce2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcfGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcfWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcfmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkcf2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcgGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcgWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkcgmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_Pvb5l2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkcg2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkchGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.add.block.comment" commandName="Add Block Comment" description="Add Block Comment" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkchWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvkchmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.AllInstances" commandName="All Instances" description="View all instances of the selected type loaded in the target VM" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvkch2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDYGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDYWLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.actions.LifeCycleTest.run" commandName="Run Maven Test" description="Run Maven Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDYmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsl.debug.ui.launchshortcut.run" commandName="Run XSLT Transformation" description="Create a configuration to debug an XSLT transformation" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDY2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDZGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDZWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to field" description="Invokes quick assist and selects 'Assign parameter to field'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDZmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDZ2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.move.inner.to.top.level" commandName="Move Type to New File" description="Move Type to New File" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDaGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor " description="Toggles linking of a view's selection with the active editor's selection" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDaWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.open.file.from.source" commandName="Open Selection" description="Open an editor on the selected link" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDamLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDa2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDbGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.goto.matching.bracket" commandName="Matching Character" description="Go to Matching Character" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDbWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.refactor.quickMenu" commandName="Show Refactor Quick Menu" description="Shows the refactor quick menu" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDbmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDb2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.override.methods" commandName="Override/Implement Methods" description="Override or implement methods from super types" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDcGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDcWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jaxb.eclipselink.ui.command.addEclipseLinkJaxbProperty" commandName="Add EclipseLink JAXB property" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDcmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDc2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDdGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.change.type" commandName="Generalize Declared Type" description="Change the declaration of a selected variable to a more general type consistent with usage" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDdWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDdmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDd2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDeGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDeWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.quick_outline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDemLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.inline" commandName="Inline" description="Inline a constant, local variable or function" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDe2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDfGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.PackagesView" commandName="JavaScript Folders" description="Show the Folders view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDfWLnEeePML3co2BKSQ" elementId="org.eclipse.ant.ui.openExternalDoc" commandName="Open External Documentation" description="Open the External documentation for the current task in the Ant editor" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDfmLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.ViewAdminConsoleCommand" commandName="View Admin Console" description="View Admin Console" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDf2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDgGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.hierarchy" commandName="Write Access in Hierarchy" description="Search for write references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDgWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDgmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDg2LnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDhGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDhWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDhmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDh2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDiGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDiWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.project" commandName="Read Access in Project" description="Search for read references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDimLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDi2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDjGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.pull.up" commandName="Pull Up" description="Move members to a superclass" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDjWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDjmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.surround.with.try.catch" commandName="Surround with try/catch Block" description="Surround the selected text with a try/catch block" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDj2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDkGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDkWLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvlDkmLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_PvlDk2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDlGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDlWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new function parameter based on the selected expression" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDlmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDl2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDmGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDmWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.TypeHierarchy" commandName="JavaScript Type Hierarchy" description="Show the Type Hierarchy view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDmmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDm2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addCast" commandName="Quick Fix - Add cast" description="Invokes quick assist and selects 'Add cast'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDnGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlDnWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.add.javadoc.comment" commandName="Add JSDoc Comment" description="Add a JSDoc comment stub to the member element" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqcGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.hierarchy" commandName="Quick Hierarchy" description="Show the quick hierarchy of the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqcWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqcmLnEeePML3co2BKSQ" elementId="org.eclipse.pde.api.tools.ui.convert.javadocs" commandName="Convert API Tools &amp;Javadoc Tags..." description="Starts a wizard that will allow you to convert existing Javadoc tags to annotations" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqc2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.open.external.javadoc" commandName="Open Attached Javadoc" description="Open the attached Javadoc of the selected element in a browser" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqdGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.EquinoxLaunchShortcut.run" commandName="Run OSGi Framework" description="Run OSGi Framework" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqdWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqdmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structure.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqd2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqeGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.AddClassPrepareBreakpoint" commandName="Add Class Load Breakpoint" description="Add a class load breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqeWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.convertJavaProjectToJpa" commandName="Convert to JPA Project..." category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqemLnEeePML3co2BKSQ" elementId="org.eclipse.emf.codegen.ecore.ui.Generate" commandName="Generate Code" description="Generate code for the EMF models in the workspace" category="_Pvb5lGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqe2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.generate.javadoc" commandName="Generate JSDoc" description="Generates JSDoc for a selectable set of JavaScript resources" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqfGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqfWLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqfmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqf2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqgGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.specific_content_assist.command" commandName="Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_PvbSYGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvlqgWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_PvlqgmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqg2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqhGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqhWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.method.exits" commandName="Search Method Exit Occurrences in File" description="Search for method exit occurrences of a selected return type" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqhmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqh2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseMembers" commandName="Collapse Members" description="Collapse all members" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqiGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqiWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.rename.element" commandName="Rename - Refactoring " description="Rename the selected element" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqimLnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqi2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqjGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.generateDynamicEntities" commandName="Generate Dynamic Entities from Tables..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqjWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqjmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqj2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqkGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Field" description="Convert a local variable to a field" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqkWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqkmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.PackageExplorer" commandName="JavaScript Script Explorer" description="Show the Script Explorer" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqk2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.changeToStatic" commandName="Quick Fix - Change to static access" description="Invokes quick assist and selects 'Change to static access'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqlGLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqlWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqlmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Field" description="Create getting and setting methods for the field and use only those to access the field" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlql2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.run" commandName="Run Eclipse Application" description="Run Eclipse Application" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqmGLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqmWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.superclass" commandName="Extract Superclass" description="Extract a set of members into a new superclass and try to use the new superclass" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqmmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.remove.block.comment" commandName="Remove Block Comment" description="Remove Block Comment" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqm2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.runtime.spy.commands.menuSpyCommand" commandName="Plug-in Menu Spy" description="Show the Plug-in Spy" category="_Pvb5n2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqnGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.entityMappingsAddPersistentClass" commandName="Add Class..." category="_Pvb5f2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqnWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.generate.constructor.using.fields" commandName="Generate Constructor using Vars" description="Choose vars to initialize and constructor from superclass to call " category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqnmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.navigate.gototype" commandName="Go to Type" description="Go to Type" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqn2LnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.ui.command.openPom" commandName="Open Maven POM" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqoGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqoWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.refactor.rename.element" commandName="&amp;Rename XSD element" description="Rename XSD element" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqomLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqo2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqpGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.next" commandName="Select Next Element" description="Expand selection to include next sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqpWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqpmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_Pvb5h2LnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvlqp2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_PvlqqGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_PvlqqWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_PvlqqmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqq2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqrGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvlqrWLnEeePML3co2BKSQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_PvlqrmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqr2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqsGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqsWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.workspace" commandName="Declaration in Workspace" description="Search for declarations of the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqsmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.super.implementation" commandName="Open Super Implementation" description="Open the Implementation in the Super Type" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqs2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.openPluginArtifact" commandName="Open Plug-in Artifact" description="Open a plug-in artifact in the manifest editor" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqtGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.debug" commandName="Debug" description="Debug server" category="_Pvb5dWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqtWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqtmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter" commandName="Introduce Parameter" description="Introduce a new method parameter based on the selected expression" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqt2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_Pvb5dGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvlquGLnEeePML3co2BKSQ" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_PvlquWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.run" commandName="Run Java Application" description="Run Java Application" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvlqumLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvlqu2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structure.select.enclosing" commandName="Select Enclosing Element" description="Expand selection to include enclosing element" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRgGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.wsdl.ui.refactor.rename.element" commandName="Rename WSDL component" description="Renames WSDL component" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRgWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_Pvb5lWLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvmRgmLnEeePML3co2BKSQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_PvmRg2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRhGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRhWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.SourceView" commandName="JavaScript Declaration" description="Show the Declaration view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRhmLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRh2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.searchTargetRepositories" commandName="Add Artifact to Target Platform" description="Add an artifact to your target platform" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvmRiGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.searchTargetRepositories.term" name="The initial search pattern for the artifact search dialog"/>
  </commands>
  <commands xmi:id="_PvmRiWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRimLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRi2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRjGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.override.methods" commandName="Override/Implement Functions" description="Override or implement functions from super types" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRjWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRjmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.refactor.migrate.jar" commandName="Migrate JAR File" description="Migrate a JAR File to a new version" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRj2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.editor" commandName="Open Declaration" description="Open an editor on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRkGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.publish" commandName="Publish" description="Publish to server" category="_Pvb5dWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRkWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.factory" commandName="Introduce Factory" description="Introduce a factory method to encapsulate invocation of the selected constructor" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRkmLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.updateClasspath" commandName="Update Classpath" description="Updates the plug-in classpath from latest settings" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRk2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRlGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.use.supertype" commandName="Use Supertype Where Possible" description="Change occurrences of a type to use a supertype instead" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRlWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRlmLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRl2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.structure.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRmGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.sort.members" commandName="Sort Members" description="Sort all members using the member order preference" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRmWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRmmLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_Pvb5j2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRm2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRnGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.commands.showElementInPackageView" commandName="Show JavaScript Element in Script Explorer" description="Select JavaScript element in the Script Explorer view" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvmRnWLnEeePML3co2BKSQ" elementId="elementRef" name="JavaScript element reference" typeId="org.eclipse.wst.jsdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_PvmRnmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.refactor.makeElementGlobal" commandName="Make Local Element &amp;Global" description="Promotes local element to global level and replaces its references" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRn2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.encapsulateField.assist" commandName="Quick Assist - Create getter/setter for field" description="Invokes quick assist and selects 'Create getter/setter for field'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRoGLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.openEditorDropdown" commandName="Open Setup Editor" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRoWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRomLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRo2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRpGLnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRpWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.clean.up" commandName="Clean Up" description="Solve problems and improve code style on selected resources" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRpmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.GotoMatchingTokenAction" commandName="Goto Matching Token" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRp2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.api.tools.ui.setup.projects" commandName="API &amp;Tools Setup..." description="Configure projects for API usage and compatibility checks" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRqGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.create.delegate.methods" commandName="Generate Delegate Functions" description="Add delegate functions for a type's vars" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRqWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.localJavaShortcut.debug" commandName="Debug Java Application" description="Debug Java Application" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRqmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRq2LnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.convertJavaConverters" commandName="Move Java Converters to XML..." category="_Pvb5fWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRrGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.last" commandName="Restore Last Selection" description="Restore last selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRrWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRrmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.remove.occurrence.annotations" commandName="Remove Occurrence Annotations" description="Removes the occurrence annotations from the current editor" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRr2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRsGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.show.in.package.view" commandName="Show in Script Explorer" description="Show the selected element in the Script Explorer" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRsWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRsmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRs2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRtGLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_Pvb5l2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvmRtWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4kGLnEeePML3co2BKSQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4kWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.splitJoinVariableDeclaration.assist" commandName="Quick Assist - Split/Join variable declaration" description="Invokes quick assist and selects 'Split/Join variable declaration'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4kmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignParamToField.assist" commandName="Quick Assist - Assign parameter to var" description="Invokes quick assist and selects 'Assign parameter to var'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4k2LnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_Pvb5nGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4lGLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.importFromRepository" commandName="Import Plug-in from a Repository" description="Imports a plug-in from a source repository" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4lWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4lmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4l2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4mGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4mWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4mmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4m2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.renameInFile.assist" commandName="Quick Assist - Rename in file" description="Invokes quick assist and selects 'Rename in file'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4nGLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.commands.openPreferences" commandName="Open the preferences dialog" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvm4nWLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_Pvm4nmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.references.in.hierarchy" commandName="References in Hierarchy" description="Search for references of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4n2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4oGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4oWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.call.hierarchy" commandName="Open Call Hierarchy" description="Open a call hierarchy on the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4omLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.constant" commandName="Extract Constant" description="Extracts a constant into a new static field and uses the new static field" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4o2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4pGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.cleanup.document" commandName="Cleanup Document..." description="Cleanup document" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4pWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4pmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4p2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4qGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4qWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.qualifyField" commandName="Quick Fix - Qualify field access" description="Invokes quick assist and selects 'Qualify field access'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4qmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4q2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.format" commandName="Format" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4rGLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4rWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4rmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.externalize.strings" commandName="Externalize Strings" description="Finds all strings that are not externalized and moves them into a separate property file" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4r2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4sGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4sWLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.core.ui.command.addPlugin" commandName="Add Maven Plugin" description="Add Maven Plugin" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4smLnEeePML3co2BKSQ" elementId="sed.tabletree.expandAll" commandName="Expand All" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4s2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.modify.method.parameters" commandName="Change Method Signature" description="Change method signature includes parameter names and parameter order" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4tGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4tWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4tmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.extractLocal.assist" commandName="Quick Assist - Extract local variable (replace all occurrences)" description="Invokes quick assist and selects 'Extract local variable (replace all occurrences)'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4t2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4uGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4uWLnEeePML3co2BKSQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4umLnEeePML3co2BKSQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4u2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.open.external.javadoc" commandName="Open External JSDoc" description="Open the JSDoc of the selected element in an external browser" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4vGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4vWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4vmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4v2LnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.persistentTypeAddVirtualAttribute" commandName="Add Virtual Attribute..." category="_Pvb5f2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvm4wGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_Pvb5m2LnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvm4wWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_PvnfoGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_PvnfoWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newDynamicEntity" commandName="EclipseLink Dynamic Entity" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfomLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfo2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfpGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implement.occurrences" commandName="Search Implement Occurrences in File" description="Search for implement occurrences of a selected type" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfpWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.javaAppletShortcut.debug" commandName="Debug Java Applet" description="Debug Java Applet" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfpmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.gotoMatchingTag" commandName="Matching Tag" description="Go to Matching Tag" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfp2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfqGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.saveToDatabaseAction" commandName="Save to Database" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfqWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfqmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfq2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.convertAnonymousToLocal.assist" commandName="Quick Assist - Convert anonymous to local class" description="Invokes quick assist and selects 'Convert anonymous to local class'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfrGLnEeePML3co2BKSQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfrWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfrmLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfr2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfsGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addSuppressWarnings" commandName="Quick Fix - Add @SuppressWarnings" description="Invokes quick fix and selects 'Add @SuppressWarnings' " category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfsWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXml" commandName="Add Attribute to XML" category="_Pvb5f2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfsmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.push.down" commandName="Push Down" description="Move members to subclasses" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfs2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.write.access.in.project" commandName="Write Access in Project" description="Search for write references to the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnftGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnftWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnftmLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.persistentTypeMapAs" commandName="Map As" category="_Pvb5f2LnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvnft2LnEeePML3co2BKSQ" elementId="persistentTypeMappingKey" name="mapping key" optional="false"/>
  </commands>
  <commands xmi:id="_PvnfuGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.DMLDialogSelectionAction" commandName="Edit in SQL Query Builder..." category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfuWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfumLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.dbws.ui.generateDbws" commandName="Generate Database Web Services" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfu2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.debug" commandName="Debug JUnit Plug-in Test" description="Debug JUnit Plug-in Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfvGLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.ViewLogCommand" commandName="View Log File" description="View Log File" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfvWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.extractLocalNotReplaceOccurrences.assist" commandName="Quick Assist - Extract local variable" description="Invokes quick assist and selects 'Extract local variable'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfvmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.attachProfileAction" commandName="Set Connection Information" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfv2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfwGLnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_Pvb5nGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfwWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfwmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.AllReferences" commandName="All References" description="Inspect all references to the selected object" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfw2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.junit.junitShortcut.rerunLast" commandName="Rerun JUnit Test" description="Rerun JUnit Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfxGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.format.active.elements" commandName="Format Active Elements" description="Format active elements" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfxWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.connectivity.commands.import" commandName="Import Profiles Command" description="Command to import connection profiles" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfxmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfx2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.working.set" commandName="References in Working Set" description="Search for references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfyGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfyWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfymLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfy2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfzGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfzWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.infer.type.arguments" commandName="Infer Generic Type Arguments" description="Infer type arguments for references to generic classes and remove unnecessary casts" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvnfzmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnfz2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf0GLnEeePML3co2BKSQ" elementId="org.eclipse.m2e.actions.LifeCycleClean.run" commandName="Run Maven Clean" description="Run Maven Clean" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf0WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf0mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf02LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf1GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf1WLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf1mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.ForceReturn" commandName="Force Return" description="Forces return from method with value of selected expression " category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf12LnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf2GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf2WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.hierarchy" commandName="Declaration in Hierarchy" description="Search for declarations of the selected element in its hierarchy" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf2mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_Pvb5e2LnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvnf22LnEeePML3co2BKSQ" elementId="title" name="Title"/>
    <parameters xmi:id="_Pvnf3GLnEeePML3co2BKSQ" elementId="message" name="Message"/>
    <parameters xmi:id="_Pvnf3WLnEeePML3co2BKSQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Pvnf3mLnEeePML3co2BKSQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_Pvnf32LnEeePML3co2BKSQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_Pvnf4GLnEeePML3co2BKSQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_Pvnf4WLnEeePML3co2BKSQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_Pvnf4mLnEeePML3co2BKSQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_Pvnf42LnEeePML3co2BKSQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_Pvnf5GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf5WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf5mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.extract.method" commandName="Extract Function" description="Extract a set of statements or an expression into a new function and use the new function" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf52LnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.generateDDL" commandName="Generate Tables from Entities..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf6GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.referencedFileErrors" commandName="Show Details..." description="Show Details..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf6WLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.runAction" commandName="Run" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf6mLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvnf62LnEeePML3co2BKSQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGsGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.command.OpenFromClipboard" commandName="Open from Clipboard" description="Opens a Java element or a Java stack trace from clipboard" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGsWLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.internationalize" commandName="Internationalize Plug-ins" description="Sets up internationalization for a plug-in" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGsmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGs2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGtGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGtWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.read.access.in.workspace" commandName="Read Access in Workspace" description="Search for read references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGtmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.implementors.in.workspace" commandName="Implementors in Workspace" description="Search for implementors of the selected interface" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGt2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGuGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.JavaHierarchyPerspective" commandName="Java Type Hierarchy" description="Show the Java Type Hierarchy perspective" category="_Pvb5m2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGuWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.declarations.in.project" commandName="Declaration in Project" description="Search for declarations of the selected element in the enclosing project" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGumLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.surround.with.try.multicatch" commandName="Surround with try/multi-catch Block" description="Surround the selected text with a try/multi-catch block" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGu2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGvGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGvWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.JavadocView" commandName="Documentation" description="Show the JavaScript Documentation view" category="_Pvb5h2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGvmLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGv2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvoGwGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_PvoGwWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_PvoGwmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGw2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.introduce.parameter.object" commandName="Introduce Parameter Object" description="Introduce a parameter object to a selected method" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGxGLnEeePML3co2BKSQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_Pvb5nWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGxWLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.commands.openElementInEditor" commandName="Open Java Element" description="Open a Java element in its editor" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvoGxmLnEeePML3co2BKSQ" elementId="elementRef" name="Java element reference" typeId="org.eclipse.jdt.ui.commands.javaElementReference" optional="false"/>
  </commands>
  <commands xmi:id="_PvoGx2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected JavaScript comment lines" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGyGLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.result.removeInstance" commandName="Remove Result" category="_Pvb5eWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGyWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGymLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.discovery.ui.discoveryWizardCommand" commandName="Discovery Wizard" description="shows the connector discovery wizard" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGy2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.read.access.in.working.set" commandName="Read Access in Working Set" description="Search for read references to the selected element in a working set" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGzGLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.persistentAttributeAddToXmlAndMap" commandName="Add Attribute to XML and Map..." category="_Pvb5f2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGzWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.toggle.comment" commandName="Toggle Comment" description="Toggle Comment" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGzmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoGz2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.format" commandName="Format" description="Format the selected text" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG0GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG0WLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.select.previous" commandName="Select Previous Element" description="Expand selection to include previous sibling" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG0mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG02LnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.editor.synchronizePreferences" commandName="Synchronize Preferences" category="_Pvb5i2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG1GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.ui.cmnd.contentmodel.sych" commandName="Synch" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG1WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG1mLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG12LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG2GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xsd.ui.refactor.renameTargetNamespace" commandName="Rename Target Namespace" description="Changes the target namespace of the schema" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG2WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.folding.collapseComments" commandName="Collapse Comments" description="Collapse all comments" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG2mLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_Pvb5hmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG22LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.generate.javadoc" commandName="Generate Javadoc" description="Generates Javadoc for a selectable set of Java resources" category="_Pvb5lmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG3GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG3WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG3mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG32LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG4GLnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.design" commandName="Graphical Designer" category="_Pvb5iGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG4WLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_Pvb5l2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG4mLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG42LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG5GLnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.OpenServerHomeCommand" commandName="OpenServerHomeCommand" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG5WLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.openManifest" commandName="Open Manifest" description="Open the plug-in manifest" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG5mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG52LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.self.encapsulate.field" commandName="Encapsulate Var" description="Create getting and setting functions for the var and use only those to access the var" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG6GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.generate.tostring" commandName="Generate toString()" description="Generates the toString() method for the type" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG6WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.organize.imports" commandName="Organize Imports" description="Evaluate all required imports and replace the current imports" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG6mLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG62LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.promote.local.variable" commandName="Convert Local Variable to Var" description="Convert a local variable to a var" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG7GLnEeePML3co2BKSQ" elementId="org.eclipse.pde.api.tools.ui.compare.to.baseline" commandName="API Baseline..." description="Allows to compare the selected resource with the current baseline" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG7WLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.imagebrowser.saveToWorkspace" commandName="Save Image" description="Save the selected image into a project in the workspace" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG7mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.debug.ui.commands.Display" commandName="Display" description="Display result of evaluating selected text" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG72LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.exception.occurrences" commandName="Search Exception Occurrences in File" description="Search for exception occurrences of a selected exception type" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG8GLnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_Pvb5j2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG8WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG8mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.assignToField.assist" commandName="Quick Assist - Assign to field" description="Invokes quick assist and selects 'Assign to field'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG82LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG9GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.method" commandName="Extract Method" description="Extract a set of statements or an expression into a new method and use the new method" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG9WLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.eclipselink.ui.newEclipseLinkMappingFile" commandName="EclipseLink ORM Mapping File" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG9mLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG92LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG-GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG-WLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG-mLnEeePML3co2BKSQ" elementId="org.eclipse.jst.jsp.ui.refactor.rename" commandName="Rename" description="Rename a Java Element" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG-2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG_GLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.junitWorkbenchShortcut.run" commandName="Run JUnit Plug-in Test" description="Run JUnit Plug-in Test" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG_WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.navigate.gotopackage" commandName="Go to Package" description="Go to Package" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG_mLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_Pvb5dGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoG_2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHAGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.horizotal" commandName="Horizontal Layout" category="_Pvb5iGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHAWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHAmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHA2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHBGLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.navigate.java.open.structure" commandName="Open Structure" description="Show the structure of the selected element" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHBWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.assignToLocal.assist" commandName="Quick Assist - Assign to local variable" description="Invokes quick assist and selects 'Assign to local variable'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHBmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHB2LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.bugs.commands.newTaskFromMarker" commandName="New Task from Marker..." description="Report as Bug from Marker" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvoHCGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.refactor.apply.refactoring.script" commandName="Apply Script" description="Perform refactorings from a refactoring script on the local workspace" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotwGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.run" commandName="Run" description="Run server" category="_Pvb5dWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotwWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvotwmLnEeePML3co2BKSQ" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_Pvotw2LnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotxGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_Pvb5eGLnEeePML3co2BKSQ">
    <parameters xmi:id="_PvotxWLnEeePML3co2BKSQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_PvotxmLnEeePML3co2BKSQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_Pvotx2LnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.source" commandName="Source Code" category="_Pvb5iGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotyGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_Pvb5lWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotyWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.common.project.facet.ui.ConvertProjectToFacetedForm" commandName="Convert to Faceted Form..." category="_Pvb5d2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotymLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.annotate.classFile" commandName="Annotate Class File" description="Externally add Annotations to a Class File." category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvoty2LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotzGLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.replace.invocations" commandName="Replace Invocations" description="Replace invocations of the selected function" category="_Pvb5hWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotzWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_PvotzmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvotz2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.references.in.workspace" commandName="References in Workspace" description="Search for references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot0GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.format.document" commandName="Format" description="Format selection" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot0WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.addNonNLS" commandName="Quick Fix - Add non-NLS tag" description="Invokes quick assist and selects 'Add non-NLS tag'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot0mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.search.write.access.in.workspace" commandName="Write Access in Workspace" description="Search for write references to the selected element in the workspace" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot02LnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot1GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_Pvb5d2LnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvot1WLnEeePML3co2BKSQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_Pvot1mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot12LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.uncomment" commandName="Uncomment" description="Uncomment the selected Java comment lines" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot2GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot2WLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot2mLnEeePML3co2BKSQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot22LnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_Pvb5cmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot3GLnEeePML3co2BKSQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot3WLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_Pvb5mWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot3mLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.search.occurrences.in.file" commandName="Search All Occurrences in File" description="Search for all occurrences of the selected element in its declaring file" category="_Pvb5mGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot32LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot4GLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvot4WLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_Pvot4mLnEeePML3co2BKSQ" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_Pvot42LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.gotoBreadcrumb" commandName="Show In Breadcrumb" description="Shows the Java editor breadcrumb and sets the keyboard focus into it" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot5GLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.runtimeWorkbenchShortcut.debug" commandName="Debug Eclipse Application" description="Debug Eclipse Application" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot5WLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.makePersistent" commandName="Make Persistent..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot5mLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.ExecuteSelectionAction" commandName="Execute Selected Text" category="_Pvb5mmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot52LnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jaxb.ui.command.createPackageInfo" commandName="Create package-info.java" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot6GLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.edit.text.java.extract.class" commandName="Extract Class..." description="Extracts fields into a new class" category="_Pvb5k2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot6WLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.correction.extractConstant.assist" commandName="Quick Assist - Extract constant" description="Invokes quick assist and selects 'Extract constant'" category="_Pvb5nmLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot6mLnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.rcp.commands.extensionDiscovery" commandName="Discover New Extensions" category="_Pvb5hGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvot62LnEeePML3co2BKSQ" elementId="org.eclipse.recommenders.utils.rcp.linkContribution.href" name="URI" optional="false"/>
  </commands>
  <commands xmi:id="_Pvot7GLnEeePML3co2BKSQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_Pvb5kGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot7WLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_Pvb5emLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot7mLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.newMappingFile" commandName="JPA ORM Mapping File" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot72LnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot8GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.comment" commandName="Comment" description="Turn the selected lines into JavaScript comments" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot8WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot8mLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.xmlFileUpgradeToLatestVersion" commandName="Upgrade JPA Document Version" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot82LnEeePML3co2BKSQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_Pvb5cWLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot9GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.search.find.occurrences" commandName="Occurrences in File" description="Find occurrences of the selection in the file" category="_PvbSYGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot9WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_Pvb5eGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot9mLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot92LnEeePML3co2BKSQ" elementId="org.eclipse.wst.sse.ui.outline.customFilter" commandName="&amp;Filters" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot-GLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqlscrapbook.commands.openscrapbook" commandName="Open SQL Scrapboo&amp;k" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot-WLnEeePML3co2BKSQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_Pvb5jGLnEeePML3co2BKSQ">
    <parameters xmi:id="_Pvot-mLnEeePML3co2BKSQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_Pvot-2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.correction.addBlock.assist" commandName="Quick Assist - Replace statement with block" description="Invokes quick assist and selects 'Replace statement with block'" category="_Pvb5c2LnEeePML3co2BKSQ"/>
  <commands xmi:id="_Pvot_GLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.edit.text.java.goto.previous.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the JavaScript file" category="_Pvb5jGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_P5IA8GLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="org.eclipse.egit.ui.team.CompareWithCommit"/>
  <commands xmi:id="_P5JPEGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.CompareWithRevision" commandName="org.eclipse.egit.ui.team.CompareWithRevision"/>
  <commands xmi:id="_P5J2IGLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="org.eclipse.egit.ui.team.ReplaceWithPrevious"/>
  <commands xmi:id="_QMtEQGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ant.ui.actionSet.presentation/org.eclipse.ant.ui.toggleAutoReconcile" commandName="Toggle Ant Editor Auto Reconcile" description="Toggle Ant Editor Auto Reconcile" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMtrUGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.sqlscrapbook.actionSet/org.eclipse.datatools.sqltools.sqlscrapbook.actions.OpenScrapbookAction" commandName="Open SQL Scrapbook" description="Open scrapbook to edit SQL statements" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMwHkGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMwHkWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMwuoGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMwuoWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMxVsGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMxVsWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMxVsmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMxVs2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QMxVtGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM1AEGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New Java Class" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM1nIGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenPackageWizard" commandName="Package..." description="New Java Package" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM1nIWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.JavaElementCreationActionSet/org.eclipse.jdt.ui.actions.OpenProjectWizard" commandName="Java Project..." description="New Java Project" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM2OMGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.ui.SearchActionSet/org.eclipse.jdt.ui.actions.OpenJavaSearchPage" commandName="Java..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM21QGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEArtifact" commandName="Servlet" description="Create a new Servlet" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM21QWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jst.j2ee.J2eeMainActionSet/org.eclipse.jst.j2ee.internal.actions.NewJavaEEProject" commandName="Dynamic Web Project" description="Create a Dynamic Web project" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM3cUGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.java.actionSet.browsing/org.eclipse.mylyn.java.ui.actions.ApplyMylynToBrowsingPerspectiveAction" commandName="Focus Browsing Perspective" description="Focus Java Browsing Views on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM3cUWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.doc.actionSet/org.eclipse.mylyn.tasks.ui.bug.report" commandName="Report Bug or Enhancement..." description="Report Bug or Enhancement" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM4DYGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM4DYWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.pde.ui.SearchActionSet/org.eclipse.pde.ui.actions.OpenPluginSearchPage" commandName="Plug-in..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM4qcGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM4qcWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM4qcmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM5RgGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM5RgWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM54kGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM54kWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenFileWizard" commandName="JavaScript Source File" description="New JavaScript file" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM6foGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.JavaElementCreationActionSet/org.eclipse.wst.jsdt.ui.actions.OpenProjectWizard" commandName="JavaScript Project..." description="New JavaScript Project" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM6foWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.refactor.show.refactoring.history" commandName="History..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM7GsGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.ui.SearchActionSet/org.eclipse.wst.jsdt.ui.actions.OpenJavaSearchPage" commandName="JavaScript..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM7twGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.new.actionSet/org.eclipse.wst.server.ui.action.new.server" commandName="Create Server" description="Create Server" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM7twWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.open" commandName="Open Web Browser" description="Open Web Browser" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM7twmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.server.ui.internal.webbrowser.actionSet/org.eclipse.wst.server.ui.internal.webbrowser.action.switch" commandName="Web Browser" description="Web Browser" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM8U0GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newCSSFile" commandName="CSS" description="Create a new Cascading Style Sheet" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM8U0WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newJSFile" commandName="JavaScript" description="Create a new JavaScript file" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM8U0mLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.web.ui.wizardsActionSet/org.eclipse.wst.web.ui.actions.newHTMLFile" commandName="HTML" description="Create a new HTML page" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM874GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.ws.explorer.explorer/org.eclipse.wst.ws.internal.explorer.action.LaunchWSEAction" commandName="Launch the Web Services Explorer" description="Launch the Web Services Explorer" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM874WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ant.ui.BreakpointRulerActions/org.eclipse.ant.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM9i8GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.datatools.sqltools.rullerDoubleClick/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Add Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM9i8WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.exporter.genModelEditorContribution/org.eclipse.emf.exporter.ui.GenModelExportActionDelegate.Editor" commandName="Export Model..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM9i8mLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.importer.genModelEditorContribution/org.eclipse.emf.importer.ui.GenModelReloadActionDelegate.Editor" commandName="Reload..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-KAGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.RemoveMappingActionID" commandName="Remove Mapping" description="Remove the mapping associated with the selected objects." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-KAWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.TypeMatchMappingActionID" commandName="Match Mapping by Type" description="Create child mappings automatically by type." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-KAmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.NameMatchMappingActionID" commandName="Match Mapping by Name" description="Create child mappings automatically by name." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-xEGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateOneSidedMappingActionID" commandName="Create One-sided Mapping" description="Create a new mapping for the selected object." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-xEWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.action.CreateMappingActionID" commandName="Create Mapping" description="Create a new mapping between the selected objects." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM-xEmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddOuputRootActionID" commandName="Add Output Root..." description="Add new output root." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM_YIGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.emf.mapping.ecore2ecore.presentation.Ecore2EcoreContributionID/org.eclipse.emf.mapping.ecore2ecore.action.AddInputRootActionID" commandName="Add Input Root..." description="Add new input root." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM_YIWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.CompilationUnitEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM__MGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ClassFileEditor.BreakpointRulerActions/org.eclipse.jdt.debug.ui.actions.ManageBreakpointRulerAction" commandName="Toggle Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QM__MWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetExecute" commandName="Execute" description="Execute the Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNAmQGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetDisplay" commandName="Display" description="Display Result of Evaluating Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNAmQWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.JavaSnippetToolbarActions/org.eclipse.jdt.debug.ui.SnippetInspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNAmQmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNAmQ2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNBNUGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNBNUWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="Java Editor Bookmark Ruler Action" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNBNUmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.jdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="Java Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNB0YGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNB0YWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jst.jsp.core.jspsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNB0YmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution/org.eclipse.m2e.jdt.ui.downloadSourcesAction" commandName="label" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNB0Y2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.m2e.jdt.ui.downloadSourcesContribution_38/org.eclipse.m2e.jdt.ui.downloadSourcesAction_38" commandName="label" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNB0ZGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Text Editor Bookmark Ruler Action" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNCbcGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNCbcWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNCbcmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.css.core.csssource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNCbc2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNCbdGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.dtd.core.dtdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDCgGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDCgWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.html.core.htmlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDCgmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.debug.ui.togglebreakpoint/org.eclipse.wst.jsdt.debug.ui.RulerToggleBreakpoint" commandName="Toggle Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDCg2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDChGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.CompilationUnitEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDChWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.ClassFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.javaeditor.JavaSelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDChmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.BookmarkRulerAction" commandName="JavaScript Editor Bookmark Ruler Action" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDpkGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.jsdt.internal.ui.PropertiesFileEditor.ruler.actions/org.eclipse.wst.jsdt.internal.ui.propertiesfileeditor.SelectRulerAction" commandName="JavaScript Editor Ruler Single-Click" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDpkWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.editor/org.eclipse.wst.wsdl.ui.actions.ReloadDependenciesActionDelegate" commandName="Reload Dependencies" description="Reload Dependencies" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDpkmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDpk2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.wsdl.wsdlsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDplGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNDplWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.core.runtime.xml.source.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNEQoGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="Add Bookmark..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNEQoWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.wst.xsd.core.xsdsource.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Select Ruler" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNEQomLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNEQo2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNEQpGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNE3sGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNE3sWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNE3smLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNE3s2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNE3tGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNFewGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNFewWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNFewmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNFew2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNFexGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF0GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF0WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF0mLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF02LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF1GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGF1WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs4GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variablesViewActions.AllReferencesInView" commandName="Show References" description="Shows references to each object in the variables view as an array of objects." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs4WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs4mLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs42LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs5GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.VariableViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNGs5WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.Preferences" commandName="Java Preferences..." description="Opens preferences for Java variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT8GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.AllReferencesInView" commandName="Show References" description="Show &amp;References" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT8WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.variableViewActions.ShowNullEntries" commandName="Show Null Array Entries" description="Show Null Array Entries" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT8mLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT82LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowStatic" commandName="Show Static Variables" description="Show Static Variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT9GLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.ExpressionViewActions/org.eclipse.jdt.debug.ui.expressionViewActions.ShowConstants" commandName="Show Constants" description="Show Constants" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNHT9WLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.actions.AddException" commandName="Add Java Exception Breakpoint" description="Add Java Exception Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7AGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.BreakpointViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7AWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowThreadGroups" commandName="Show Thread Groups" description="Show Thread Groups" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7AmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowQualified" commandName="Show Qualified Names" description="Show Qualified Names" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7A2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowSystemThreads" commandName="Show System Threads" description="Show System Threads" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7BGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.LaunchViewActions/org.eclipse.jdt.debug.ui.launchViewActions.ShowMonitorThreadInfo" commandName="Show Monitors" description="Show the Thread &amp; Monitor Information" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNH7BWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Watch" commandName="Watch" description="Create a Watch Expression from the Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNIiEGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Execute" commandName="Execute" description="Execute the Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNIiEWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Display" commandName="Display" description="Display Result of Evaluating Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNIiEmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.jdt.debug.ui.DisplayViewActions/org.eclipse.jdt.debug.ui.displayViewToolbar.Inspect" commandName="Inspect" description="Inspect Result of Evaluating Selected Text" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNIiE2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJJIGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.java.ui.markers.breakpoints.contribution/org.eclipse.mylyn.java.ui.actions.focus.markers.breakpoints" commandName="Focus on Active Task" description="Focus on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJJIWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.ui.debug.view.contribution/org.eclipse.mylyn.ui.actions.FilterResourceNavigatorAction" commandName="Focus on Active Task (Experimental)" description="Focus on Active Task (Experimental)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJJImLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.ui.projectexplorer.filter/org.eclipse.mylyn.ide.ui.actions.focus.projectExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJJI2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.ui.resource.navigator.filter/org.eclipse.mylyn.ide.ui.actions.focus.resourceNavigator" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJJJGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.problems.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.problems" commandName="Focus on Active Task" description="Focus on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwMGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.markers.all.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.all" commandName="Focus on Active Task" description="Focus on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwMWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.markers.tasks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.tasks" commandName="Focus on Active Task" description="Focus on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwMmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.markers.bookmarks.contribution/org.eclipse.mylyn.ide.ui.actions.focus.markers.bookmarks" commandName="Focus on Active Task" description="Focus on Active Task" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwM2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.java.explorer.contribution/org.eclipse.mylyn.java.actions.focus.packageExplorer" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwNGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.search.open" commandName="Search Repository..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNJwNWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXQGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXQWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXQmLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXQ2LnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXRGLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.pde.ui.logViewActions/org.eclipse.jdt.debug.ui.LogViewActions.showStackTrace" commandName="Show Stack Trace in Console View" description="Show Stack Trace in Console View" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXRWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNKXRmLnEeePML3co2BKSQ" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.add.scriptload.breakpoint" commandName="Add Script Load Breakpoint" description="Add Script Load Breakpoint" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNK-UGLnEeePML3co2BKSQ" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.jdt.debug.ui.breakpointViewActions.ShowQualified" commandName="Suspend For All Script Loads" description="Suspends when any script is loaded" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNK-UWLnEeePML3co2BKSQ" elementId="AUTOGEN:::breakpointsViewActions/org.eclipse.wst.jsdt.debug.ui.suspend.on.exceptions" commandName="Suspend On JavaScript Exceptions" description="Suspend on all JavaScript exceptions" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNK-UmLnEeePML3co2BKSQ" elementId="AUTOGEN:::debugViewActions/org.eclipse.wst.jsdt.debug.ui.show.all.scripts" commandName="Show All Scripts" description="Shows or hides all scripts loaded in the visible targets" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNK-U2LnEeePML3co2BKSQ" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.functions" commandName="Show function variables" description="Show or hide function variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNK-VGLnEeePML3co2BKSQ" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.this" commandName="Show 'this' variable" description="Show or hide the this variable" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNLlYGLnEeePML3co2BKSQ" elementId="AUTOGEN:::variableViewActions/org.eclipse.wst.jsdt.debug.ui.variableview.show.prototypes" commandName="Show proto variables" description="Show or hide proto variables" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <commands xmi:id="_QNLlYWLnEeePML3co2BKSQ" elementId="AUTOGEN:::org.eclipse.ui.articles.action.contribution.view/org.eclipse.wst.wsi.ui.internal.actions.actionDelegates.ValidateWSIProfileActionDelegate" commandName="WS-I Profile Validator" description="Validate WS-I Message Log File" category="_Pvb5hGLnEeePML3co2BKSQ"/>
  <addons xmi:id="_PuEniGLnEeePML3co2BKSQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_PuEniWLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_PuEnimLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_PuEni2LnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_PuEnjGLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_PuEnjWLnEeePML3co2BKSQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_PuEnjmLnEeePML3co2BKSQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_PuEnj2LnEeePML3co2BKSQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_PuEnkGLnEeePML3co2BKSQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_PuEnkWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.platform" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_PuL8QGLnEeePML3co2BKSQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_PvbSYGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_Pvb5cGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_Pvb5cWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_Pvb5cmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_Pvb5c2LnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.category.source" name="Source" description="JavaScript Source Actions"/>
  <categories xmi:id="_Pvb5dGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_Pvb5dWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.server.ui" name="Server" description="Server"/>
  <categories xmi:id="_Pvb5dmLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="%commands.category.name" description="%commands.category.description"/>
  <categories xmi:id="_Pvb5d2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_Pvb5eGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_Pvb5eWLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.result.category" name="SQL Results View"/>
  <categories xmi:id="_Pvb5emLnEeePML3co2BKSQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_Pvb5e2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_Pvb5fGLnEeePML3co2BKSQ" elementId="org.eclipse.oomph" name="Oomph"/>
  <categories xmi:id="_Pvb5fWLnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.jpaMetadataConversionCommands" name="JPA Metadata Conversion"/>
  <categories xmi:id="_Pvb5fmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.xml.views.XPathView" name="XPath"/>
  <categories xmi:id="_Pvb5f2LnEeePML3co2BKSQ" elementId="org.eclipse.jpt.jpa.ui.jpaStructureViewCommands" name="JPA Structure View"/>
  <categories xmi:id="_Pvb5gGLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_Pvb5gWLnEeePML3co2BKSQ" elementId="org.eclipse.pde.ui.category.source" name="Manifest Editor Source" description="PDE Source Page actions"/>
  <categories xmi:id="_Pvb5gmLnEeePML3co2BKSQ" elementId="org.eclipse.oomph.commands" name="Oomph"/>
  <categories xmi:id="_Pvb5g2LnEeePML3co2BKSQ" elementId="oracle.eclipse.tools.glassfish.commands.category" name="glassfish-commands"/>
  <categories xmi:id="_Pvb5hGLnEeePML3co2BKSQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_Pvb5hWLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.ui.category.refactoring" name="Refactor - JavaScript" description="JavaScript Refactoring Actions"/>
  <categories xmi:id="_Pvb5hmLnEeePML3co2BKSQ" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_Pvb5h2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_Pvb5iGLnEeePML3co2BKSQ" elementId="org.eclipse.jst.pagedesigner.pagelayout" name="Web Page Editor Layout"/>
  <categories xmi:id="_Pvb5iWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_Pvb5imLnEeePML3co2BKSQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_Pvb5i2LnEeePML3co2BKSQ" elementId="org.eclipse.oomph.setup.category" name="Oomph Setup"/>
  <categories xmi:id="_Pvb5jGLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_Pvb5jWLnEeePML3co2BKSQ" elementId="org.eclipse.mylyn.java.ui.commands" name="Java Context" description="Java Task-Focused Interface Commands"/>
  <categories xmi:id="_Pvb5jmLnEeePML3co2BKSQ" elementId="org.eclipse.wst.jsdt.debug.ui.category" name="JavaScript Debug" description="Tooling for debugging JavaScript"/>
  <categories xmi:id="_Pvb5j2LnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_Pvb5kGLnEeePML3co2BKSQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_Pvb5kWLnEeePML3co2BKSQ" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_Pvb5kmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.enablement.sybase.asa.schemaobjecteditor.examples.tableschemaedtor.10x" name="ASA 9.x table schema editor"/>
  <categories xmi:id="_Pvb5k2LnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.category.refactoring" name="Refactor - Java" description="Java Refactoring Actions"/>
  <categories xmi:id="_Pvb5lGLnEeePML3co2BKSQ" elementId="org.eclipse.emf.codegen.ecore.ui.Commands" name="EMF Code Generation" description="Commands for the EMF code generation tools"/>
  <categories xmi:id="_Pvb5lWLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_Pvb5lmLnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_Pvb5l2LnEeePML3co2BKSQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_Pvb5mGLnEeePML3co2BKSQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_Pvb5mWLnEeePML3co2BKSQ" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_Pvb5mmLnEeePML3co2BKSQ" elementId="org.eclipse.datatools.sqltools.sqleditor.category" name="Database Tools" description="Database Development tools"/>
  <categories xmi:id="_Pvb5m2LnEeePML3co2BKSQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_Pvb5nGLnEeePML3co2BKSQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_Pvb5nWLnEeePML3co2BKSQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_Pvb5nmLnEeePML3co2BKSQ" elementId="org.eclipse.jdt.ui.category.source" name="Source" description="Java Source Actions"/>
  <categories xmi:id="_Pvb5n2LnEeePML3co2BKSQ" elementId="org.eclipse.pde.runtime.spy.commands.category" name="Spy"/>
</application:Application>
