{"version": "v1", "title": "Eclipse", "timestamp": 1499413113825, "ttl": 10080, "helpUrl": "https://dev.eclipse.org/recommenders/community/aeri/v2/help/", "feedbackUrl": "http://ctrlflow.com/automated-error-reporting", "aboutUrl": "https://wiki.eclipse.org/EPP/Logging", "submitUrl": "https://dev.eclipse.org/recommenders/community/confess/0.6/reports/", "maxReportSize": 262144, "problemsUrl": "https://www.eclipse.org/downloads/download.php?r=1&file=/technology/epp/logging/problems.zip", "problemsTtl": 20160, "connectTimeout": 10, "socketTimeout": 10, "acceptedProducts": ["org.eclipse.*", "org.fordiac.*"], "acceptedPlugins": ["org.apache.log4j.*", "org.eclipse.*", "org.fordiac.*"], "acceptedPackages": ["ch.qos.*", "com.cforcoding.*", "com.google.*", "com.gradleware.tooling.*", "com.mountainminds.eclemma.*", "com.naef.*", "com.sun.*", "java.*", "javafx.*", "javax.*", "org.apache.*", "org.eclipse.*", "org.fordiac.*", "org.gradle.*", "org.jacoco.*", "org.osgi.*", "org.slf4j.*", "sun.*"], "requiredPackages": ["com.cforcoding.*", "com.gradleware.tooling.*", "com.mountainminds.eclemma.*", "com.naef.*", "org.eclipse.*", "org.fordiac.*", "org.gradle.*", "org.jacoco.*"], "acceptOtherPackages": false, "acceptUiFreezes": true, "ignoredStatuses": [":java.io.IOException:There is not enough space on the disk", ":java.net.*:", "org.eclipse.core.filesystem::Could not delete*", "org.eclipse.core.filesystem::Could not move*", "org.eclipse.core.resources:org.eclipse.core.internal.resources.ResourceException:Resource is out of sync with the file system*", "org.eclipse.core.runtime::Invalid input url*", "org.eclipse.epp.mpc.ui:java.io.IOException:", "org.eclipse.equinox.p2.*::", "org.eclipse.jface:java.io.IOException:Unable to resolve plug-in*", "org.eclipse.oomph.setup.core:$org.apache.http.ConnectionClosedException:", "org.eclipse.pde.core::The current target platform contains errors*", "org.eclipse.ui::Conflicting handlers for*"], "problemsZipLastDownloadTimestamp": 0}