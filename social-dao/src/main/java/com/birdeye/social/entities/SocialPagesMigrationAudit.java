package com.birdeye.social.entities;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Entity
@Table(name = "social_pages_migration_audit")
public class SocialPagesMigrationAudit implements Serializable {

    private static final long serialVersionUID = -3577144936559185821L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "channel")
    @Size(max = 255)
    private String channel;

    @Column(name = "logs")
    private String logs;

    @Column(name = "error")
    private Boolean error;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getLogs() {
        return logs;
    }

    public void setLogs(String logs) {
        this.logs = logs;
    }

    public Boolean getError() {
        return error;
    }

    public void setError(Boolean error) {
        this.error = error;
    }

    @Override
    public String toString() {
        return "SocialPagesMigrationAudit{" +
                "id=" + id +
                ", channel='" + channel + '\'' +
                ", logs='" + logs + '\'' +
                ", error=" + error +
                '}';
    }
}
