package com.birdeye.social.entities;

import lombok.*;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "gmb_keyword_analytics")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GMBKeywordAnalytics {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "page_Id")
    private String pageId;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "response_data")
    private String responsData;

    @Column(name= "date")
    private Date date;

    @Column(name="metadata")
    private String metadata;

    @CreatedDate
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created")
    private Date created;
}
