package com.birdeye.social.entities.assetlibrary;

import com.birdeye.social.entities.AbstractSocialBaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

/**
 * <AUTHOR> on 09/10/23
 */
@Getter
@Setter
@Entity
@Table(name = "asset_business_tag")
@Deprecated
public class SocialAssetLibraryBusinessTag extends AbstractSocialBaseEntity {

    private static final long serialVersionUID = -2414852650601219931L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "updated_by")
    private Long updatedBy;
}
