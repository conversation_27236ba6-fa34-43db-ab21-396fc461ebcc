package com.birdeye.social.entities;

import org.apache.commons.lang3.builder.ToStringBuilder;

public class LocationMovementDto {

	private Long eventId;
	private String eventType;
	private Integer sourceBusinessId;
	private Long sourceBusinessNumber;
	private Integer sourceEnterpriseId;
	private Long sourceEnterpriseNumber;
	private Integer sourceResellerId;
	private String sourceBusinessType;
	private Integer targetBusinessId;
	private Long targetBusinessNumber;
	private Integer targetEnterpriseId;
	private Long targetEnterpriseNumber;
	private String targetBusinessType;
	private Integer targetResellerId;

	/**
	 * No args constructor for use in serialization
	 *
	 */
	public LocationMovementDto() {
	}

	/**
	 *
	 * @param eventId
	 * @param targetResellerId
	 * @param sourceBusinessId
	 * @param eventType
	 * @param targetEnterpriseId
	 * @param targetBusinessNumber
	 * @param sourceBusinessNumber
	 * @param sourceBusinessType
	 * @param sourceEnterpriseId
	 * @param targetBusinessType
	 * @param sourceResellerId
	 * @param targetBusinessId
	 * @param sourceEnterpriseNumber
	 * @param targetEnterpriseNumber
	 */
	public LocationMovementDto(Long eventId, String eventType, Integer sourceBusinessId, Long sourceBusinessNumber,
			Integer sourceEnterpriseId, Long sourceEnterpriseNumber, Integer sourceResellerId, String sourceBusinessType,
			Integer targetBusinessId, Long targetBusinessNumber, Integer targetEnterpriseId, Long targetEnterpriseNumber,
			String targetBusinessType, Integer targetResellerId) {
		super();
		this.eventId = eventId;
		this.eventType = eventType;
		this.sourceBusinessId = sourceBusinessId;
		this.sourceBusinessNumber = sourceBusinessNumber;
		this.sourceEnterpriseId = sourceEnterpriseId;
		this.sourceEnterpriseNumber = sourceEnterpriseNumber;
		this.sourceResellerId = sourceResellerId;
		this.sourceBusinessType = sourceBusinessType;
		this.targetBusinessId = targetBusinessId;
		this.targetBusinessNumber = targetBusinessNumber;
		this.targetEnterpriseId = targetEnterpriseId;
		this.targetEnterpriseNumber = targetEnterpriseNumber;
		this.targetBusinessType = targetBusinessType;
		this.targetResellerId = targetResellerId;
	}

	public Long getEventId() {
		return eventId;
	}

	public void setEventId(Long eventId) {
		this.eventId = eventId;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public Integer getSourceBusinessId() {
		return sourceBusinessId;
	}

	public void setSourceBusinessId(Integer sourceBusinessId) {
		this.sourceBusinessId = sourceBusinessId;
	}

	public Long getSourceBusinessNumber() {
		return sourceBusinessNumber;
	}

	public void setSourceBusinessNumber(Long sourceBusinessNumber) {
		this.sourceBusinessNumber = sourceBusinessNumber;
	}

	public Integer getSourceEnterpriseId() {
		return sourceEnterpriseId;
	}

	public void setSourceEnterpriseId(Integer sourceEnterpriseId) {
		this.sourceEnterpriseId = sourceEnterpriseId;
	}

	public Long getSourceEnterpriseNumber() {
		return sourceEnterpriseNumber;
	}

	public void setSourceEnterpriseNumber(Long sourceEnterpriseNumber) {
		this.sourceEnterpriseNumber = sourceEnterpriseNumber;
	}

	public Integer getSourceResellerId() {
		return sourceResellerId;
	}

	public void setSourceResellerId(Integer sourceResellerId) {
		this.sourceResellerId = sourceResellerId;
	}

	public String getSourceBusinessType() {
		return sourceBusinessType;
	}

	public void setSourceBusinessType(String sourceBusinessType) {
		this.sourceBusinessType = sourceBusinessType;
	}

	public Integer getTargetBusinessId() {
		return targetBusinessId;
	}

	public void setTargetBusinessId(Integer targetBusinessId) {
		this.targetBusinessId = targetBusinessId;
	}

	public Long getTargetBusinessNumber() {
		return targetBusinessNumber;
	}

	public void setTargetBusinessNumber(Long targetBusinessNumber) {
		this.targetBusinessNumber = targetBusinessNumber;
	}

	public Integer getTargetEnterpriseId() {
		return targetEnterpriseId;
	}

	public void setTargetEnterpriseId(Integer targetEnterpriseId) {
		this.targetEnterpriseId = targetEnterpriseId;
	}

	public Long getTargetEnterpriseNumber() {
		return targetEnterpriseNumber;
	}

	public void setTargetEnterpriseNumber(Long targetEnterpriseNumber) {
		this.targetEnterpriseNumber = targetEnterpriseNumber;
	}

	public String getTargetBusinessType() {
		return targetBusinessType;
	}

	public void setTargetBusinessType(String targetBusinessType) {
		this.targetBusinessType = targetBusinessType;
	}

	public Integer getTargetResellerId() {
		return targetResellerId;
	}

	public void setTargetResellerId(Integer targetResellerId) {
		this.targetResellerId = targetResellerId;
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this).append("eventId", eventId).append("eventType", eventType)
				.append("sourceBusinessId", sourceBusinessId).append("sourceBusinessNumber", sourceBusinessNumber)
				.append("sourceEnterpriseId", sourceEnterpriseId)
				.append("sourceEnterpriseNumber", sourceEnterpriseNumber).append("sourceResellerId", sourceResellerId)
				.append("sourceBusinessType", sourceBusinessType).append("targetBusinessId", targetBusinessId)
				.append("targetBusinessNumber", targetBusinessNumber).append("targetEnterpriseId", targetEnterpriseId)
				.append("targetEnterpriseNumber", targetEnterpriseNumber)
				.append("targetBusinessType", targetBusinessType).append("targetResellerId", targetResellerId)
				.toString();
	}

}
