package com.birdeye.social.entities;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "temp_business_fb_page")
public class TempBusinessFBPage implements Serializable
{

	private static final long serialVersionUID = -8567478059664421879L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "facebook_page_id")
	@Size(max = 20)
	private String facebookPageId;

	@Column(name = "link")
	private String link;

	@Column(name = "page_access_token")
	@Size(max = 255)
	private String pageAccessToken;

	@Column(name = "facebook_page_name")
	private String facebookPageName;

	@Column(name = "facebook_picture_url")
	private String facebookPagePictureUrl;

	@Column(name = "single_line_address")
	private String singleLineAddress;

	@Column(name = "user_id")
	private String userId;

	@Column(name = "picture_url")
	private String pictureUrl;

	@Column(name = "firstName")
	private String firstName;

	@Column(name = "lastName")
	private String lastName;

	@Column(name = "handle")
	private String	handle;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date createdAt;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date updatedAt;

	@Column(name = "enterprise_id")
	private Long enterpriseId;

	@Column(name = "business_id")
	private Integer	 businessId;

	@Column(name = "reseller_id")
	private Long resellerId;

	@Column(name = "is_selected")
	private Integer isSelected;

	@Column(name = "is_valid")
	private Integer isValid;

	@Column(name = "request_id")
	private String requestId;

	@Column(name = "is_managed")
	private Integer isManaged;

	@Column(name = "scope")
	private String scope;

	@Column(name = "created_by")
	private Integer createdBy;

	@Column(name = "updated_by")
	private Integer updatedBy;

	@Column(name="page_permissions")
	private String pagePermissions;

	@Column(name = "messenger_opted")
	private Integer messengerOpted = 0;

	@Column(name = "ratings_opted")
	private Integer ratingsOpted = 0;

	@Column(name="fb_error_subcode")
	private Integer fbErrorSubcode;

	@Column(name = "last_scanned_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastScannedOn;

	@Column(name = "error_log")
	private String errorLog;

	@Column(name = "min_rating")
	private Integer minRating = 4;

	@Column(name = "enabled")
	private Integer enabled = 0;

	@Column(name = "max_post_allowed")
	private Integer maxPostAllowed = 3;

	@Column(name = "validity_type")
	private Integer validType;

	@Column(name = "user_email_id")
	private String userEmailId;

	@Column(name = "primary_phone")
	private String	primaryPhone;

	@Column(name = "next_sync_date")
	private Date nextSyncDate;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}


	/**
	 * @return the facebookPageId
	 */
	public String getFacebookPageId() {
		return facebookPageId;
	}

	/**
	 * @param facebookPageId the facebookPageId to set
	 */
	public void setFacebookPageId(String facebookPageId) {
		this.facebookPageId = facebookPageId;
	}


	/**
	 * @return the link
	 */
	public String getLink() {
		return link;
	}

	/**
	 * @param link the link to set
	 */
	public void setLink(String link) {
		this.link = link;
	}


	/**
	 * @return the pageAccessToken
	 */
	public String getPageAccessToken() {
		return pageAccessToken;
	}

	/**
	 * @param pageAccessToken the pageAccessToken to set
	 */
	public void setPageAccessToken(String pageAccessToken) {
		this.pageAccessToken = pageAccessToken;
	}

	/**
	 * @return the facebookPageUrl
	 */
	public String getFacebookPagePictureUrl() {
		return facebookPagePictureUrl;
	}

	/**
	 * @param facebookPageUrl the facebookPageUrl to set
	 */
	public void setFacebookPagePictureUrl(String facebookPagePictureUrl) {
		this.facebookPagePictureUrl = facebookPagePictureUrl;
	}

	/**
	 * @return the singleLineAddress
	 */
	public String getSingleLineAddress() {
		return singleLineAddress;
	}

	/**
	 * @param singleLineAddress the singleLineAddress to set
	 */
	public void setSingleLineAddress(String singleLineAddress) {
		this.singleLineAddress = singleLineAddress;
	}

	/**
	 * @return the userId
	 */
	public String getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}

	/**
	 * @return the pictureUrl
	 */
	public String getPictureUrl() {
		return pictureUrl;
	}

	/**
	 * @param pictureUrl the pictureUrl to set
	 */
	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}

	/**
	 * @return the firstName
	 */
	public String getFirstName() {
		return firstName;
	}

	/**
	 * @param firstName the firstName to set
	 */
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	/**
	 * @return the lastName
	 */
	public String getLastName() {
		return lastName;
	}

	/**
	 * @param lastName the lastName to set
	 */
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	/**
	 * @return the createdAt
	 */
	public Date getCreatedAt() {
		return createdAt;
	}

	/**
	 * @param createdAt the createdAt to set
	 */
	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	/**
	 * @return the updatedAt
	 */
	public Date getUpdatedAt() {
		return updatedAt;
	}

	/**
	 * @param updatedAt the updatedAt to set
	 */
	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}
	public String getFacebookPageName() {
		return facebookPageName;
	}

	public void setFacebookPageName(String facebookPageName) {
		this.facebookPageName = facebookPageName;
	}


	@Override
	public int hashCode() {
		int hash = 0;
		hash += (id != null ? id.hashCode() : 0);
		return hash;
	}

	/**
	 * @return the enterpriseId
	 */
	public Long getEnterpriseId() {
		return enterpriseId;
	}

	/**
	 * @param enterpriseId the enterpriseId to set
	 */
	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the resellerId
	 */
	public Long getResellerId() {
		return resellerId;
	}

	/**
	 * @param resellerId the enterpriseId to set
	 */
	public void setResellerId(Long resellerId) {
		this.resellerId = resellerId;
	}

	/**
	 * @return the isSelected
	 */
	public Integer getIsSelected() {
		return isSelected;
	}

	/**
	 * @param isSelected the isSelected to set
	 */
	public void setIsSelected(Integer isSelected) {
		this.isSelected = isSelected;
	}

	/**
	 * @return the isValid
	 */
	public Integer getIsValid() {
		return isValid;
	}

	/**
	 * @param isValid the isValid to set
	 */
	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public String getHandle() {
		return handle;
	}

	public void setHandle(String handle) {
		this.handle = handle;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public TempBusinessFBPage() {

	}

	public TempBusinessFBPage(String facebookPageId, String link, String pageAccessToken, String facebookPageName, String facebookPagePictureUrl, String singleLineAddress, String userId, String pictureUrl,
                              String firstName, String lastName, Long enterpriseId, Integer isSelected, Integer isValid) {
		super();
		this.facebookPageId = facebookPageId;
		this.link = link;
		this.pageAccessToken = pageAccessToken;
		this.facebookPageName = facebookPageName;
		this.facebookPagePictureUrl = facebookPagePictureUrl;
		this.singleLineAddress = singleLineAddress;
		this.userId = userId;
		this.pictureUrl = pictureUrl;
		this.firstName = firstName;
		this.lastName = lastName;
		this.enterpriseId = enterpriseId;
		this.isSelected = isSelected;
		this.isValid = isValid;
	}

	public TempBusinessFBPage(Integer id, String facebookPageId, String link, String pageAccessToken, String facebookPageName, String facebookPagePictureUrl, String singleLineAddress, String userId,
                              String pictureUrl, String firstName, String lastName, String handle, Date createdAt, Date updatedAt, Long enterpriseId, Integer isSelected, Integer isValid) {
		this.id = id;
		this.facebookPageId = facebookPageId;
		this.link = link;
		this.pageAccessToken = pageAccessToken;
		this.facebookPageName = facebookPageName;
		this.facebookPagePictureUrl = facebookPagePictureUrl;
		this.singleLineAddress = singleLineAddress;
		this.userId = userId;
		this.pictureUrl = pictureUrl;
		this.firstName = firstName;
		this.lastName = lastName;
		this.handle = handle;
		this.createdAt = createdAt;
		this.updatedAt = updatedAt;
		this.enterpriseId = enterpriseId;
		this.isSelected = isSelected;
		this.isValid = isValid;
	}
	
	/**
	 * @return the isManaged
	 */
	public Integer getIsManaged() {
		return isManaged;
	}

	/**
	 * @param isManaged the isManaged to set
	 */
	public void setIsManaged(Integer isManaged) {
		this.isManaged = isManaged;
	}

	/**
	 * @return the scope
	 */
	public String getScope() {
		return scope;
	}

	/**
	 * @param scope the scope to set
	 */
	public void setScope(String scope) {
		this.scope = scope;
	}

	/**
	 * @return the createdBy
	 */
	public Integer getCreatedBy() {
		return createdBy;
	}

	/**
	 * @param createdBy the createdBy to set
	 */
	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	/**
	 * @return the updatedBy
	 */
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	/**
	 * @param updatedBy the updatedBy to set
	 */
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	/**
	 * @return the pagePermsssions
	 */
	public String getPagePermissions() {
		return pagePermissions;
	}

	/**
	 * @param pagePermsssions the pagePermsssions to set
	 */
	public void setPagePermissions(String pagePermsssions) {
		this.pagePermissions = pagePermsssions;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getMessengerOpted() {
		return messengerOpted;
	}

	public void setMessengerOpted(Integer messengerOpted) {
		this.messengerOpted = messengerOpted;
	}

	public Integer getRatingsOpted() {
		return ratingsOpted;
	}

	public void setRatingsOpted(Integer ratingsOpted) {
		this.ratingsOpted = ratingsOpted;
	}

	public Integer getFbErrorSubcode() {
		return fbErrorSubcode;
	}

	public void setFbErrorSubcode(Integer fbErrorSubcode) {
		this.fbErrorSubcode = fbErrorSubcode;
	}

	public Date getLastScannedOn() {
		return lastScannedOn;
	}

	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

	public Integer getMinRating() {
		return minRating;
	}

	public void setMinRating(Integer minRating) {
		this.minRating = minRating;
	}

	public Integer getEnabled() {
		return enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Integer getMaxPostAllowed() {
		return maxPostAllowed;
	}

	public void setMaxPostAllowed(Integer maxPostAllowed) {
		this.maxPostAllowed = maxPostAllowed;
	}

	public Integer getValidType() {
		return validType;
	}

	public void setValidType(Integer validType) {
		this.validType = validType;
	}

	public String getUserEmailId() {
		return userEmailId;
	}

	public void setUserEmailId(String userEmailId) {
		this.userEmailId = userEmailId;
	}

	public String getPrimaryPhone() {
		return primaryPhone;
	}

	public void setPrimaryPhone(String primaryPhone) {
		this.primaryPhone = primaryPhone;
	}


	public Date getNextSyncDate() {
		return nextSyncDate;
	}

	public void setNextSyncDate(Date nextSyncDate) {
		this.nextSyncDate = nextSyncDate;
	}

	@Override
	public String toString() {
		return "BusinessFBPage{" +
				"id=" + id +
				", facebookPageId='" + facebookPageId + '\'' +
				", link='" + link + '\'' +
				", pageAccessToken='" + pageAccessToken + '\'' +
				", facebookPageName='" + facebookPageName + '\'' +
				", facebookPagePictureUrl='" + facebookPagePictureUrl + '\'' +
				", singleLineAddress='" + singleLineAddress + '\'' +
				", userId='" + userId + '\'' +
				", pictureUrl='" + pictureUrl + '\'' +
				", firstName='" + firstName + '\'' +
				", lastName='" + lastName + '\'' +
				", handle='" + handle + '\'' +
				", createdAt=" + createdAt +
				", updatedAt=" + updatedAt +
				", enterpriseId=" + enterpriseId +
				", businessId=" + businessId +
				", resellerId=" + resellerId +
				", isSelected=" + isSelected +
				", isValid=" + isValid +
				", requestId='" + requestId + '\'' +
				", isManaged=" + isManaged +
				", scope='" + scope + '\'' +
				", createdBy=" + createdBy +
				", updatedBy=" + updatedBy +
				", pagePermissions='" + pagePermissions + '\'' +
				", messengerOpted=" + messengerOpted +
				", ratingsOpted=" + ratingsOpted +
				", fbErrorSubcode=" + fbErrorSubcode +
				", lastScannedOn=" + lastScannedOn +
				", errorLog='" + errorLog + '\'' +
				", minRating=" + minRating +
				", enabled=" + enabled +
				", maxPostAllowed=" + maxPostAllowed +
				", validType=" + validType +
				", userEmailId='" + userEmailId + '\'' +
				", primaryPhone='" + primaryPhone + '\'' +
				", nextSync=" + nextSyncDate +
				'}';
	}
}
