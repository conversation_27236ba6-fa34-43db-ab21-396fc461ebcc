package com.birdeye.social.entities;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

@Entity
@Table(name = "business_whatsapp_accounts")
public class BusinessWhatsappAccounts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "reseller_id")
    private Long resellerId;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "phone_number_id")
    private String phoneNumberId;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "verified_name")
    private String verifiedName;

    @Column(name = "quality_rating")
    private String qualityRating;

    @Column(name = "phone_number_status")
    private String phoneNumberStatus;

    @Column(name = "messaging_limit")
    private String messagingLimit;

    @Column(name = "country")
    private String country;

    @Column(name = "encrypted_pin")
    private String encryptedPin;

    @Column(name = "waba_id")
    private String wabaId;

    @Column(name = "waba_name")
    private String wabaName;

    @Column(name = "waba_status")
    private String wabaStatus;

    @Column(name = "access_token")
    private String accessToken;

    @Column(name = "meta_business_id")
    private String  metaBusinessId;

    @Column(name = "meta_business_name")
    private String metaBusinessName    ;

    @Column(name = "is_business_verified")
    private Integer isBusinessVerified ;

    @Column(name = "is_valid")
    private Integer isValid;

    @Column(name = "can_post")
    private Integer canPost ;

    @Column(name = "validity_type")
    private Integer validityType;

    @Column(name = "is_selected")
    private Integer isSelected ;

    @Column(name = "profile_image_url")
    private String profileImageUrl;

    @Column(name = "created_at")
    private Timestamp createdAt;

    @Column(name = "updated_at")
    private Timestamp updatedAt;

    @Column(name = "request_id")
    private String requestId;

    @Column(name = "created_by")
    private Integer createdBy;

    @Column(name = "updated_by")
    private Integer updatedBy;

    @Column(name = "scopes")
    private String scopes;

    @Column(name = "user_email_id")
    private String userEmailId;

    @Column(name = "expires_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiresOn;

    @Column(name = "last_scanned_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastScannedOn;

    @Column(name = "next_sync_date")
    private Date nextSyncDate;

    @Column(name = "page_permissions")
    private String pagePermission;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Long getResellerId() {
        return resellerId;
    }

    public void setResellerId(Long resellerId) {
        this.resellerId = resellerId;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getPhoneNumberId() {
        return phoneNumberId;
    }

    public void setPhoneNumberId(String phoneNumberId) {
        this.phoneNumberId = phoneNumberId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getVerifiedName() {
        return verifiedName;
    }

    public void setVerifiedName(String verifiedName) {
        this.verifiedName = verifiedName;
    }

    public String getQualityRating() {
        return qualityRating;
    }

    public void setQualityRating(String qualityRating) {
        this.qualityRating = qualityRating;
    }

    public String getPhoneNumberStatus() {
        return phoneNumberStatus;
    }

    public void setPhoneNumberStatus(String phoneNumberStatus) {
        this.phoneNumberStatus = phoneNumberStatus;
    }

    public String getMessagingLimit() {
        return messagingLimit;
    }

    public void setMessagingLimit(String messagingLimit) {
        this.messagingLimit = messagingLimit;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEncryptedPin() {
        return encryptedPin;
    }

    public void setEncryptedPin(String encryptedPin) {
        this.encryptedPin = encryptedPin;
    }

    public String getWabaId() {
        return wabaId;
    }

    public void setWabaId(String wabaId) {
        this.wabaId = wabaId;
    }

    public String getWabaName() {
        return wabaName;
    }

    public void setWabaName(String wabaName) {
        this.wabaName = wabaName;
    }

    public String getWabaStatus() {
        return wabaStatus;
    }

    public void setWabaStatus(String wabaStatus) {
        this.wabaStatus = wabaStatus;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getMetaBusinessId() {
        return metaBusinessId;
    }

    public void setMetaBusinessId(String metaBusinessId) {
        this.metaBusinessId = metaBusinessId;
    }

    public String getMetaBusinessName() {
        return metaBusinessName;
    }

    public void setMetaBusinessName(String metaBusinessName) {
        this.metaBusinessName = metaBusinessName;
    }

    public Integer getIsBusinessVerified() {
        return isBusinessVerified;
    }

    public void setIsBusinessVerified(Integer isBusinessVerified) {
        this.isBusinessVerified = isBusinessVerified;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public Integer getCanPost() {
        return canPost;
    }

    public void setCanPost(Integer canPost) {
        this.canPost = canPost;
    }

    public Integer getValidityType() {
        return validityType;
    }

    public void setValidityType(Integer validityType) {
        this.validityType = validityType;
    }

    public Integer getIsSelected() {
        return isSelected;
    }

    public void setIsSelected(Integer isSelected) {
        this.isSelected = isSelected;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getScopes() {
        return scopes;
    }

    public void setScopes(String scopes) {
        this.scopes = scopes;
    }

    public String getUserEmailId() {
        return userEmailId;
    }

    public void setUserEmailId(String userEmailId) {
        this.userEmailId = userEmailId;
    }

    public Date getExpiresOn() {
        return expiresOn;
    }

    public void setExpiresOn(Date expiresOn) {
        this.expiresOn = expiresOn;
    }

    public Date getLastScannedOn() {
        return lastScannedOn;
    }

    public void setLastScannedOn(Date lastScannedOn) {
        this.lastScannedOn = lastScannedOn;
    }

    public Date getNextSyncDate() {
        return nextSyncDate;
    }

    public void setNextSyncDate(Date nextSyncDate) {
        this.nextSyncDate = nextSyncDate;
    }

    public String getPagePermission() {
        return pagePermission;
    }

    public void setPagePermission(String pagePermission) {
        this.pagePermission = pagePermission;
    }
}