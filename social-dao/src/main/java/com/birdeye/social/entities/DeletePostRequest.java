package com.birdeye.social.entities;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Entity
@Table(name="delete_post_request")
public class DeletePostRequest {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer	id;

    @Column(name = "post_id")
    private Integer postId;

    @Column(name = "status")
    private String status;

    @Column(name = "delete_post_count")
    private Integer deletePostCount;
}
