package com.birdeye.social.entities;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "business_posts_assets")
@XmlRootElement
@Getter
@Setter
@ToString
public class BusinessPostsAssets implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "business_posts_id")
    private Integer businessPostsId;

    @Column(name = "media_url")
    private String mediaUrl;

    @Column(name = "cdn_url")
    private String cdnUrl;

    @Column(name = "is_image")
    private Integer isImage;

    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;

    @Column(name = "updated_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedDate;

}
