package com.birdeye.social.entities;

import com.birdeye.social.scheduler.dto.ExternalIntegration;
import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@ToString
@Table(name = "google_refresh_token")
public class GoogleRefreshToken implements Serializable, ExternalIntegration {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "refresh_token")
    private String refreshToken;

    @Column(name = "type")
    private String type;

    @Column(name = "channel")
    private String channel;

    @Column(name = "domain_id")
    private Integer domainId;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "client_cred_id")
    private Integer clientCredentialsId;

    @Column(name = "is_valid")
    private Integer isValid = 1;

    @Column(name = "google_user_id")
    private String googleUserId;

    @Column(name = "last_scanned_on")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastScannedOn;

    @Column(name = "google_user_name")
    private String googleUserName;

    @Column(name = "user_profile_url")
    private String userProfileUrl;

    @Column(name = "user_image_url")
    private String userImageUrl;

}