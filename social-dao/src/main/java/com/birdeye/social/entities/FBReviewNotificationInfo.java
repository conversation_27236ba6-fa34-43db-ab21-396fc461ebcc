package com.birdeye.social.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "fb_review_notification_info")
public class FBReviewNotificationInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "status")
    private String status;

    @Column(name = "comment")
    private String comment;

    @Column(name = "event_data")
    private String eventData;

    @Column(name = "notification_type")
    private String notificationType;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "fb_review_id")
    private String fbReviewId;

    @Column(name = "fb_reviewer_id")
    private String fbReviewerId;

    public String getFbReviewId() {
        return fbReviewId;
    }

    public void setFbReviewId(String fbReviewId) {
        this.fbReviewId = fbReviewId;
    }

    public String getFbReviewerId() {
        return fbReviewerId;
    }

    public void setFbReviewerId(String fbReviewerId) {
        this.fbReviewerId = fbReviewerId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getEventData() {
        return eventData;
    }

    public void setEventData(String eventData) {
        this.eventData = eventData;
    }

    public String getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }

    public String getPageId() {
        return pageId;
    }

    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
