package com.birdeye.social.entities;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Data
@Table(name = "gmb_qna_notification")
public class GMBQnaNotification implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;


    @Column(name = "business_id")
    private Integer business_id;

    @Column(name = "status")
    private String status;

    @Column(name = "comment")
    private String comment;

    @Column(name = "location_id")
    private String locationId;

    @Column(name = "notification_type")
    private String notificationType;

    @Column(name = "location_uri")
    private String locationUri;

    @Column(name = "event_data")
    private String eventData;

    @Column(name = "decoded_event_data")
    private String decodedEventData;
    @Column(name = "question_id")
    private String questionId;

    @Column(name = "answer_id")
    private String answerId;

    @Column(name = "question")
    private String question;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created")
    private Date created;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated")
    private Date updated;

    @Column(name = "error_code")
    private Integer errorCode;

    @Column(name = "place_id")
    private String placeId;

    @Override
    public String toString() {
        return "GMBReviewNotification [id=" + id + ", business_id=" + business_id + ", status=" + status + ", comment="
                + comment + ", locationId=" + locationId + ", notificationType="
                + notificationType + ", reviewUri=" + locationUri + ", eventData=" + eventData + ", decodedEventData="
                + decodedEventData + ", questionId=" + questionId + ", answerId=" + answerId + ", created=" + created
                + ", updated=" + updated + ", errorCode=" + errorCode + ", placeId=" + placeId + "]";
    }
}
