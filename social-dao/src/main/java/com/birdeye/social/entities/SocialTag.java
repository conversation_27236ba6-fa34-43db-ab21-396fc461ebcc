package com.birdeye.social.entities;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

/**
 * <AUTHOR> on 21/12/23
 */
@Getter
@Setter
@Entity
@ToString
@Table(name = "social_tag")
public class SocialTag extends AbstractSocialBaseEntity {

    private static final long serialVersionUID = -3841728892799580333L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "account_num")
    private Long accountNumber;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "updated_by")
    private Long updatedBy;
}
