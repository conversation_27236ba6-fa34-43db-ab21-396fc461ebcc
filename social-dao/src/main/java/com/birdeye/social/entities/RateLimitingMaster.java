package com.birdeye.social.entities;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "social_rate_limit_api_info")
public class RateLimitingMaster implements Serializable {

    /**
     *
     */
    private static final long	serialVersionUID	= 8447265039263897783L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer				id;

    @Column(name = "api_url")
    private String apiUrl;
    @Column(name = "api_type")
    private String apiType;
    @Column(name = "source")
    private String source;
    @Column(name = "rate_limit_indentifier")
    private String rateLimitIdentifier;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getRateLimitIdentifier() {
        return rateLimitIdentifier;
    }

    public void setRateLimitIdentifier(String rateLimitIdentifier) {
        this.rateLimitIdentifier = rateLimitIdentifier;
    }

}
