package com.birdeye.social.entities.report;

import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Table(name = "facebook_page_insight")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FacebookPageInsight implements Serializable {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "data")
    private String data;

    @Column(name = "created")
    private Date date;

    @Column(name = "last_sync_date")
    private Date lastSyncDate;

    @Column(name = "next_sync_date")
    private Date nextSyncDate;

}
