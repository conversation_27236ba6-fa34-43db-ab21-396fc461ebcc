package com.birdeye.social.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "business_apple_inactive_location")
public class BusinessInactiveAppleLocation {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "accountId")
    private Integer	 accountId;

    @Column(name = "business_id")
    private Integer	 businessId;

    @Column(name = "apple_company_id")
    private String appleCompanyId;

    @Column(name = "apple_business_id")
    private String appleBusinessId;

    @Column(name = "apple_location_id")
    private String appleLocationId;

    @Column(name = "apple_business_name")
    private String appleBusinessName;

    @Column(name = "link")
    private String link;

    @Column(name = "location_name")
    private String locationName;

    @Column(name = "single_line_address")
    private String singleLineAddress;

    @Column(name = "primary_phone")
    private String primaryPhone;

    @Column(name = "auto_mapped")
    private Integer autoMapped;

    @Column(name = "created_by")
    private Integer createdBy;

    @Column(name = "updated_by")
    private Integer updatedBy;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "next_sync_date")
    private Date nextSyncDate;

    @Column(name = "cta")
    private String cta;
    @Column(name = "logo_url")
    private String logoUrl;

    @Column(name = "request_id")
    private String requestId;


}
