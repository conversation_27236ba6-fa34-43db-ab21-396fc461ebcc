package com.birdeye.social.entities;

import com.birdeye.social.utils.ListToJsonConverter;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.*;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "social_post_draft_info")
@XmlRootElement
public class SocialPostDraftInfo implements Serializable {
	/**
	 *
	 */
	private static final long	serialVersionUID	= 3076393472924402382L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id")
	private Integer				id;

	@Column(name = "enterprise_id")
    private Integer enterpriseId;

    @Column(name = "source_id")
    private Integer sourceId;

    @Column(name = "level_id")
	private Integer	levelAliasId;

	@Column(name = "level_alias")
	private String		levelAlias;

	@Column(name = "level_names",columnDefinition = "json")
	@Convert(converter = ListToJsonConverter.class)
	private List<String> levelNames;

	@Column(name = "pageIds",columnDefinition = "json")
	@Convert(converter = ListToJsonConverter.class)
	private List<String> pageIds;

	@Column(name = "social_post_id")
    private Integer socialPostId;

	@Column(name = "master_post_id")
	private Integer masterPostId;

	@Column(name = "is_posted")
	private Integer isPosted;

	@Column(name = "is_valid_draft")
	private Integer isValidDraft;

	@Column(name = "created_at")
	private Date createdAt;

	@Column(name = "planned_for")
	@Temporal(TemporalType.TIMESTAMP)
	private Date plannedFor;

	@Column(name = "post_group_details")
	private String postGroupDetails;

	@Column(name = "post_method")
	private String postMethod;

	@Column(name = "client_source")
	private String clientSource;

}
