package com.birdeye.social.entities.report;


import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@ToString
@Entity
@Builder
@Table(name = "facebook_post_insight")
@AllArgsConstructor
@NoArgsConstructor
public class FacebookPostInsight implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "post_id")
    private String postId;

    @Column(name = "data")
    private String data;

    @Column(name = "created")
    private Date created;
}
