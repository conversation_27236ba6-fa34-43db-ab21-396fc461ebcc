package com.birdeye.social.entities;

import javax.persistence.*;

@Entity
@Table(name = "social_module_permission")
public class SocialModulePermission {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "source_id")
    private Integer sourceId;

    @Column(name = "permissions_needed")
    private String permissionsNeeded;

    @Column(name = "module")
    private String module;


    @Column(name = "updated_by")
    private String updatedBy;


	public Integer getId() {
		return id;
	}


	public void setId(Integer id) {
		this.id = id;
	}


	public Integer getSourceId() {
		return sourceId;
	}


	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}


	public String getPermissionsNeeded() {
		return permissionsNeeded;
	}


	public void setPermissionsNeeded(String permissionsNeeded) {
		this.permissionsNeeded = permissionsNeeded;
	}


	public String getModule() {
		return module;
	}


	public void setModule(String module) {
		this.module = module;
	}


	public String getUpdatedBy() {
		return updatedBy;
	}


	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}


	
    

   
}
