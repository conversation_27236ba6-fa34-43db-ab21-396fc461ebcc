package com.birdeye.social.entities.competitor;

import lombok.*;

import javax.persistence.*;

@Entity
@Builder
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "facebook_competitor_mapping")
public class FacebookCompetitorMapping {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "competitor_id")
    private String competitorId;

    @Column(name = "raw_competitor_id")
    private Integer rawCompetitorId;
}
