package com.birdeye.social.entities.linkinbio;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "social_link_in_bio_info")
public class SocialLinkInBioInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Column(name = "enterprise_id")
    private Long enterpriseId;
    @Column(name = "business_id")
    private Integer businessId;
    @Column(name = "url",unique = true)
    private String url;
    @Column(name = "page_name")
    private String pageName;
    @Column(name = "description")
    private String description;
    @Column(name = "view_count")
    private Integer viewCount = 0;
    @Column(name = "total_click_count")
    private Integer totalClickCount = 0;
    @Column(name = "meta_data")
    private String metaData;
    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;
    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;
}
