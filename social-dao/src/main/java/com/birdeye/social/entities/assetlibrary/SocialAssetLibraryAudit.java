package com.birdeye.social.entities.assetlibrary;

import com.birdeye.social.entities.AbstractSocialBaseEntity;
import com.birdeye.social.sro.assetlibrary.SocialAssetLibraryAuditPersistenceDetail;
import com.birdeye.social.utils.assetlibrary.SocialAssetLibraryAuditPersistenceDetailDBConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

/**
 * <AUTHOR> on 09/10/23
 */
@Getter
@Setter
@Entity
@Table(name = "asset_lib_audit")
public class SocialAssetLibraryAudit extends AbstractSocialBaseEntity {

    private static final long serialVersionUID = -550643396953851340L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "asset_id")
    private Long assetId;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "modified_by")
    private Long modifiedBy;

    @Column(name = "action")
    private String action;

    @Convert(converter = SocialAssetLibraryAuditPersistenceDetailDBConverter.class)
    @Column(name = "former_state", columnDefinition = "text")
    private SocialAssetLibraryAuditPersistenceDetail formerState;

    @Convert(converter = SocialAssetLibraryAuditPersistenceDetailDBConverter.class)
    @Column(name = "current_state", columnDefinition = "text")
    private SocialAssetLibraryAuditPersistenceDetail currentState;

}
