package com.birdeye.social.entities;


import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 */

@Entity
@Table(name = "social_error_message")
public class SocialErrorMessage implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "action")
    @Size(max = 255)
    private String action;

    @Column(name = "message")
    private String message;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    @Override
    public String toString() {
        return "SocialErrorMessage{" +
                "id=" + id +
                ", action='" + action + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
