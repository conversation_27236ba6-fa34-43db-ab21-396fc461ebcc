package com.birdeye.social.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "social_googleplus_page")
public class SocialGooglePlusPage implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5857434108168752437L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "page_name")
	private String pageName;

	@Column(name = "profile_image_url")
	private String profileImageUrl;

	@Column(name = "page_url")
	private String pageUrl;

	@Column(name = "page_id")
	private String pageId;

	@Column(name = "google_user_id")
	private String googleUserId;

	@Column(name = "about_me")
	private String aboutMe;

	@Column(name = "tagline")
	private String tagline;

	@Column(name = "cover_image_url")
	private String coverImageUrl;

	@Column(name = "circled_by_count")
	private Integer circledByCount;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "is_valid")
	private Integer isValid = 1;

	@Column(name = "error_log")
	private String errorLog;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "created_at")
	private Date createdAt;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "updated_at")
	private Date updatedAt;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPageName() {
		return pageName;
	}

	public void setPageName(String pageName) {
		this.pageName = pageName;
	}

	public String getProfileImageUrl() {
		return profileImageUrl;
	}

	public void setProfileImageUrl(String profileImageUrl) {
		this.profileImageUrl = profileImageUrl;
	}

	public String getPageUrl() {
		return pageUrl;
	}

	public void setPageUrl(String pageUrl) {
		this.pageUrl = pageUrl;
	}

	public String getPageId() {
		return pageId;
	}

	public void setPageId(String pageId) {
		this.pageId = pageId;
	}

	public String getGoogleUserId() {
		return googleUserId;
	}

	public void setGoogleUserId(String googleUserId) {
		this.googleUserId = googleUserId;
	}

	public String getAboutMe() {
		return aboutMe;
	}

	public void setAboutMe(String aboutMe) {
		this.aboutMe = aboutMe;
	}

	public String getTagline() {
		return tagline;
	}

	public void setTagline(String tagline) {
		this.tagline = tagline;
	}

	public String getCoverImageUrl() {
		return coverImageUrl;
	}

	public void setCoverImageUrl(String coverImageUrl) {
		this.coverImageUrl = coverImageUrl;
	}

	public Integer getCircledByCount() {
		return circledByCount;
	}

	public void setCircledByCount(Integer circledByCount) {
		this.circledByCount = circledByCount;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public String getErrorLog() {
		return errorLog;
	}

	public void setErrorLog(String errorLog) {
		this.errorLog = errorLog;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public Date getUpdatedAt() {
		return updatedAt;
	}

	public void setUpdatedAt(Date updatedAt) {
		this.updatedAt = updatedAt;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("SocialGooglePlusPage [id=");
		builder.append(id);
		builder.append(", pageName=");
		builder.append(pageName);
		builder.append(", profileImageUrl=");
		builder.append(profileImageUrl);
		builder.append(", pageUrl=");
		builder.append(pageUrl);
		builder.append(", pageId=");
		builder.append(pageId);
		builder.append(", googleUserId=");
		builder.append(googleUserId);
		builder.append(", aboutMe=");
		builder.append(aboutMe);
		builder.append(", tagline=");
		builder.append(tagline);
		builder.append(", coverImageUrl=");
		builder.append(coverImageUrl);
		builder.append(", circledByCount=");
		builder.append(circledByCount);
		builder.append(", businessId=");
		builder.append(businessId);
		builder.append(", isValid=");
		builder.append(isValid);
		builder.append(", errorLog=");
		builder.append(errorLog);
		builder.append(", createdAt=");
		builder.append(createdAt);
		builder.append(", updatedAt=");
		builder.append(updatedAt);
		builder.append("]");
		return builder.toString();
	}

}
