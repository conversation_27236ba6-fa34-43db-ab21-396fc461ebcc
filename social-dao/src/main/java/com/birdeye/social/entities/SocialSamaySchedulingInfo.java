package com.birdeye.social.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "social_samay_scheduling_info")
public class SocialSamaySchedulingInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    @Column(name = "process_identifier")
    private  String identifier;

    @Column(name = "scheduler_acknowledgement_id")
    private String	schedulerAcknowledgementId;

    @Column(name = "samay_payload")
    private String	samayPayload;

    @Column(name="retry_reason")
    private String retryReason;

    @Column(name = "social_post_id")
    private Integer	socialPostId;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getSchedulerAcknowledgementId() {
        return schedulerAcknowledgementId;
    }

    public void setSchedulerAcknowledgementId(String schedulerAcknowledgementId) {
        this.schedulerAcknowledgementId = schedulerAcknowledgementId;
    }

    public String getSamayPayload() {
        return samayPayload;
    }

    public void setSamayPayload(String samayPayload) {
        this.samayPayload = samayPayload;
    }

    public String getRetryReason() {
        return retryReason;
    }

    public void setRetryReason(String retryReason) {
        this.retryReason = retryReason;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public SocialSamaySchedulingInfo() {
    }

    public Integer getSocialPostId() {
        return socialPostId;
    }

    public void setSocialPostId(Integer socialPostId) {
        this.socialPostId = socialPostId;
    }

    public SocialSamaySchedulingInfo(String identifier, String samayPayload, String retryReason) {
        this.identifier = identifier;
        this.samayPayload = samayPayload;
        this.retryReason = retryReason;
    }

    public SocialSamaySchedulingInfo(String identifier, String samayPayload, String retryReason, Integer socialPostId) {
        this.identifier = identifier;
        this.samayPayload = samayPayload;
        this.retryReason = retryReason;
        this.socialPostId = socialPostId;
    }
}
