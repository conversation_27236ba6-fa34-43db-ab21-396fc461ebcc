package com.birdeye.social.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlRootElement;

@Entity
@Table(name = "social_posts_assets")
@XmlRootElement
public class SocialPostsAssets implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -4679621476788743910L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;
    
    @Column(name = "video_url")
    private String videoUrl;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @Column(name = "video_thumbnail_url")
    private String videoThumbnail;

    @Column(name = "default_video_thumbnail_url")
    private String defaultVideoThumbnail;
    
    @NotNull
    @Column(name = "created_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdDate;
    
    @Column(name = "last_used_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastUsedDate;

    @Column(name = "public_video_url")
    private String publicVideoUrl;

    @Column(name = "public_image_url")
    private String publicImageUrl;

    @Column(name = "asset_meta_data")
    private String assetMetaData;


    //this is the account id for which the asset was created
    //used because when an image post is created for reseller tab, the cdn url changes for enterprise tab (due to different business number)
    @Column(name = "bucket_id")
    private String bucketId;

    public String getBucketId() {
        return bucketId;
    }

    public void setBucketId(String bucketId) {
        this.bucketId = bucketId;
    }

    public String getAssetMetaData() {
        return assetMetaData;
    }

    public void setAssetMetaData(String assetMetaData) {
        this.assetMetaData = assetMetaData;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getVideoThumbnail() {
        return videoThumbnail;
    }

    public String getDefaultVideoThumbnail() {
        return defaultVideoThumbnail;
    }

    public void setVideoThumbnail(String videoThumbnail) {
        this.videoThumbnail = videoThumbnail;
    }

    public void setDefaultVideoThumbnail(String defaultVideoThumbnail) {
        this.defaultVideoThumbnail = defaultVideoThumbnail;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastUsedDate() {
        return lastUsedDate;
    }

    public void setLastUsedDate(Date lastUsedDate) {
        this.lastUsedDate = lastUsedDate;
    }

    public String getPublicVideoUrl() {
        return publicVideoUrl;
    }

    public void setPublicVideoUrl(String publicVideoUrl) {
        this.publicVideoUrl = publicVideoUrl;
    }

    public String getPublicImageUrl() {
        return publicImageUrl;
    }

    public void setPublicImageUrl(String publicImageUrl) {
        this.publicImageUrl = publicImageUrl;
    }

    public SocialPostsAssets() {}

    public SocialPostsAssets(String imageUrl, String bucketId) {
        this.imageUrl = imageUrl;
        this.bucketId = bucketId;
    }

}

