package com.birdeye.social.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LocationMovementPostDTO implements Serializable {

    private Integer sourceBusinessId;
    private Integer sourceEnterpriseId;
    private Integer targetEnterpriseId;
    private Map<Integer, List<String>> channelAndPageIdMap;
}
