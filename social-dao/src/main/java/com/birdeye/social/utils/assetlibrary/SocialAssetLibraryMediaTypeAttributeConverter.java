package com.birdeye.social.utils.assetlibrary;

import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryAssetType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

/**
 * <AUTHOR> on 09/10/23
 */
@Converter(autoApply = true)
public class SocialAssetLibraryMediaTypeAttributeConverter implements AttributeConverter<SocialAssetLibraryAssetType, Integer> {
    @Override
    public Integer convertToDatabaseColumn(SocialAssetLibraryAssetType assetType) {
        if (Objects.isNull(assetType)) {
            return null;
        }
        return assetType.getId();
    }

    @Override
    public SocialAssetLibraryAssetType convertToEntityAttribute(Integer id) {
        return SocialAssetLibraryAssetType.byId(id);
    }
}
