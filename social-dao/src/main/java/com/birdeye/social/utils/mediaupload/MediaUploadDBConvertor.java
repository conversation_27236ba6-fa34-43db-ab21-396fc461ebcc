package com.birdeye.social.utils.mediaupload;

import com.birdeye.social.sro.MediaUploadMetaData;
import com.birdeye.social.utils.GenericObjectToJsonDBConverter;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;

public class MediaUploadDBConvertor extends GenericObjectToJsonDBConverter<MediaUploadMetaData> {

    @SneakyThrows
    @Override
    public MediaUploadMetaData convertToEntityAttribute(String dbData) {
        if (StringUtils.isBlank(dbData)) {
            return new MediaUploadMetaData();
        }
        return getObjectMapper().readValue(dbData, MediaUploadMetaData.class);
    }
}
