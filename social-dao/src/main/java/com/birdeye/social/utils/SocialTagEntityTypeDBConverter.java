package com.birdeye.social.utils;

import com.birdeye.social.constant.SocialTagEntityType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

/**
 * <AUTHOR> on 21/12/23
 */
@Converter(autoApply = true)
public class SocialTagEntityTypeDBConverter implements AttributeConverter<SocialTagEntityType, Integer> {

    @Override
    public Integer convertToDatabaseColumn(SocialTagEntityType entityType) {
        if (Objects.isNull(entityType)) {
            return null;
        }
        return entityType.getId();
    }

    @Override
    public SocialTagEntityType convertToEntityAttribute(Integer entityTypeId) {
        return SocialTagEntityType.byId(entityTypeId);
    }
}