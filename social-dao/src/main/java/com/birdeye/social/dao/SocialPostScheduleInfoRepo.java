package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialPostScheduleInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface SocialPostScheduleInfoRepo extends JpaRepository<SocialPostScheduleInfo, Integer> {
	
	public SocialPostScheduleInfo findById(Integer id);

	List<SocialPostScheduleInfo> findByIdInAndIsPublished(Collection<Integer> scheduleInfoIds, Integer isPublished);

	List<SocialPostScheduleInfo> findBySocialPostId(Integer socialPostId);

	List<SocialPostScheduleInfo> findBySocialPostIdAndEnterpriseId(Integer socialPostId, Integer enterpriseId);

	@Query("Select distinct s.socialPostId from SocialPostScheduleInfo s where "
			+ "s.enterpriseId = :enterpriseId AND s.socialPostId < :lastSyncedPostId AND s.socialPostId >= :latestSyncedPostId ORDER BY s.socialPostId")
	Page<Integer> findByEnterpriseId(@Param("enterpriseId") Integer enterpriseId, @Param("lastSyncedPostId") Integer lastSyncedPostId,
													@Param("latestSyncedPostId") Integer latestSyncedPostId, Pageable pageable);

	SocialPostScheduleInfo findBySocialPostIdAndSourceId(Integer socialPostId, Integer sourceId);

	@Query("Select s from SocialPostScheduleInfo s where "
			+ "s.enterpriseId = :enterpriseId and s.publishDate >= :startDate and "
			+ "s.publishDate <= :endDate and s.isPublished in :isPublished and s.sourceId in :sourceId order by s.publishDate desc")
	List<SocialPostScheduleInfo> findByEnterpriseAndDate(@Param("enterpriseId") Integer enterpriseId, @Param("startDate") Date startDate,
														 @Param("endDate") Date endDate, @Param("isPublished") Collection<Integer> isPublished,
														 @Param("sourceId") Collection<Integer> sourceId);

	@Query("Select s from SocialPostScheduleInfo s where "
			+ "s.enterpriseId = :enterpriseId and "
			+ "s.applePublishEndDate between :startDate and :endDate and s.isPublished in :isPublished and s.sourceId in :sourceId order by s.publishDate desc")
	List<SocialPostScheduleInfo> findByEnterpriseAndSourceId(@Param("enterpriseId") Integer enterpriseId,@Param("startDate") Date startDate,
														 @Param("endDate") Date endDate, @Param("isPublished") Collection<Integer> isPublished,
														 @Param("sourceId") Collection<Integer> sourceId);

	@Query("Select s from SocialPostScheduleInfo s where "
			+ "s.enterpriseId = :enterpriseId and "
			+ "s.publishDate < :startDate and "
			+ "s.applePublishEndDate >= :startDate and "
			+ "s.applePublishEndDate <= :endDate and s.isPublished in :isPublished and s.sourceId in :sourceId order by s.publishDate desc")
	List<SocialPostScheduleInfo> findByEnterpriseAndSourceIdForApple(@Param("enterpriseId") Integer enterpriseId,
															 @Param("startDate") Date startDate,
															 @Param("endDate") Date endDate, @Param("isPublished") Collection<Integer> isPublished,
															 @Param("sourceId") Collection<Integer> sourceId);


	@Query("Select s from SocialPostScheduleInfo s where "
			+ "s.enterpriseId in :accountIds and s.publishDate >= :startDate and "
			+ "s.publishDate <= :endDate and s.isPublished in :isPublished and s.sourceId in :sourceId and "
			+ "s.postMethod in ('Business locations', 'Groups', 'BULK-RESELLER-POSTING')") //show only reseller made posts
	List<SocialPostScheduleInfo> findByAccountIdsAndDate(@Param("accountIds") List<Integer> accountIds, @Param("startDate") Date startDate,
														 @Param("endDate") Date endDate, @Param("isPublished") Collection<Integer> isPublished,
														 @Param("sourceId") Collection<Integer> sourceId);

	@Modifying
	@Transactional
	void deleteAllBySocialPostId(Integer postId);

	@Modifying
	@Transactional
	void deleteAllBySocialPostIdAndEnterpriseId(Integer postId, Integer enterpriseId);

	List<SocialPostScheduleInfo> findBySocialPostIdAndSourceIdAndIsPublished(Integer socialPostId,Integer sourceId,Integer isPublished);

	List<SocialPostScheduleInfo> findBySocialPostIdAndSourceIdAndEnterpriseIdAndIsPublished(Integer socialPostId,Integer sourceId, Integer enterpriseId, Integer isPublished);

	SocialPostScheduleInfo findBySocialPostIdAndSourceIdAndIsPublishedIn(Integer socialPostId,Integer sourceId,List<Integer> isPublished);

	SocialPostScheduleInfo findBySocialPostIdAndSourceIdAndEnterpriseIdAndIsPublishedIn(Integer socialPostId,Integer sourceId, Integer enterpriseId, List<Integer> isPublished);

	boolean existsAllByIdInAndIsPublished(List<Integer> socialPostId, Integer isPublished);
	
	@Modifying
	@Transactional
	@Query("UPDATE SocialPostScheduleInfo s SET s.isPublished = :isPublished, s.publishDate = :currDate where s.id in :ids and s.isPublished = :isPublishedCondition")
	public int updateisPublishedByIds(@Param("isPublished") Integer isPublished, @Param("ids") List<Integer> ids, @Param("currDate") Date currDate, @Param("isPublishedCondition") Integer isPublishedCondition);
	@Modifying
	@Transactional
	@Query("UPDATE SocialPostScheduleInfo s SET s.isPublished = :isPublished where s.id in :ids and s.isPublished = :isPublishedCondition")
	public int updateisPublishedByIdsForApple(@Param("isPublished") Integer isPublished, @Param("ids") List<Integer> ids, @Param("isPublishedCondition") Integer isPublishedCondition);

	@Query("select s.id from SocialPostScheduleInfo s where s.socialPostId = :socialPostId")
	List<Integer> findIdsBySocialPostId(@Param("socialPostId")  Integer socialPostId);

	@Modifying
	@Transactional
	@Query("UPDATE SocialPostScheduleInfo s SET s.isPublished = :isPublished where s.socialPostId = :postId")
	Integer updateisPublishedByWithId(@Param("isPublished") Integer isPublished, @Param("postId") Integer postId);

	@Modifying
	@Transactional
	@Query("UPDATE SocialPostScheduleInfo s SET s.isPublished = :isPublished where s.socialPostId = :postId and s.enterpriseId = :enterpriseId")
	Integer updateIsPublishedByWithPostIdAndEnterpriseId(@Param("isPublished") Integer isPublished, @Param("postId") Integer postId, @Param("enterpriseId") Integer enterpriseId);

	@Query("select s.isPublished from SocialPostScheduleInfo s where s.socialPostId = :postId and s.sourceId = :sourceId")
	Integer publishStateBySourceIdAndPostId(@Param("postId") Integer postId, @Param("sourceId") Integer sourceId);

	@Query("select s.isPublished from SocialPostScheduleInfo s where s.socialPostId = :postId and s.sourceId = :sourceId and s.postMethod in ('Business locations', 'Groups', 'BULK-RESELLER-POSTING')")
	List<Integer> publishStateBySourceIdAndPostIdForReseller(@Param("postId") Integer postId, @Param("sourceId") Integer sourceId);

	@Query("select s.isPublished from SocialPostScheduleInfo s where s.socialPostId = :postId and s.sourceId = :sourceId and s.enterpriseId = :enterpriseId")
	Integer publishStateBySourceIdAndPostIdAndEnterpriseId(@Param("postId") Integer postId, @Param("sourceId") Integer sourceId, @Param("enterpriseId") Integer enterpriseId);

	List<SocialPostScheduleInfo> findBySocialPostIdInAndSourceId(List<Integer>socialPostIds, Integer sourceId);

	List<SocialPostScheduleInfo> findBySocialPostIdAndSourceIdIn(Integer socialPost, List<Integer> sourceIds);

	@Query("select distinct s.socialPostId from SocialPostScheduleInfo s where s.isPublished = :isPublished")
	List<Integer> findDistinctPostIdForScheduledPosts(@Param("isPublished") Integer isPublished);

	@Query("select s.postGroupDetails from SocialPostScheduleInfo s where s.socialPostId = :postId")
	List<String> findGroupDetailsForPostId(@Param("postId") Integer postId);

	@Query("select distinct s.socialPostId from SocialPostScheduleInfo s where s.isPublished != :isPublished and s.id <= :limitId")
	Page<Integer> findDistinctSocialPostIdByIsPublishedNot(@Param("isPublished") Integer isPublished, @Param("limitId") Integer limitId,Pageable page);

	@Query("select distinct s.socialPostId from SocialPostScheduleInfo s where s.isPublished != :isPublished and publishDate >= :startDate and publishDate <= :endDate")
	Page<Integer> findDistinctSocialPostIdByIsPublishedNotAndPublishedDateBetween(@Param("isPublished") Integer isPublished, @Param("startDate") Date startDate, @Param("endDate") Date endDate, Pageable page);

	@Modifying
	@Transactional
	void deleteByIdIn(List<Integer> ids);

	@Transactional
	@Modifying
	@Query("update SocialPostScheduleInfo s set s.enterpriseId = :newEnterpriseId where s.enterpriseId = :oldEnterpriseId and s.isPublished = 0")
    void updateEnterpriseId(@Param("oldEnterpriseId") Integer sourceBusinessAccountId,@Param("newEnterpriseId") Integer targetBusinessAccountId);

	List<SocialPostScheduleInfo> findByEnterpriseIdAndIsPublished(Integer sourceBusinessAccountId,Integer isPublished);

	@Query("Select count(s) from SocialPostPublishInfo s where s.sourceId = :sourceId and s.publishDate > :publishDate and s.businessId in :businessIds")
	public Integer getCreatedShowcase(@Param("sourceId") Integer sourceId, @Param("publishDate") Date publishDate, @Param("businessIds") List<Integer> businessIds);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where stm.tag_id in :tagIds and sps.enterprise_id = :enterpriseId and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndTagIdsIn(@Param("enterpriseId") Integer enterpriseId,
																					  @Param("startDate") Date startDate,
																					  @Param("endDate") Date endDate,
																					  @Param("isPublished") Collection<Integer> isPublished,
																					  @Param("sourceId") Collection<Integer> sourceId,
																					  @Param("tagIds") Collection<Long> tagIds);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps left join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where sps.enterprise_id = :enterpriseId and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  and " +
			"stm.entity_id is null " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndUntagged(@Param("enterpriseId") Integer enterpriseId,
																					  @Param("startDate") Date startDate,
																					  @Param("endDate") Date endDate,
																					  @Param("isPublished") Collection<Integer> isPublished,
																					  @Param("sourceId") Collection<Integer> sourceId);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps left join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where sps.enterprise_id = :enterpriseId and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  and " +
			"(stm.entity_id is null or stm.tag_id in :tagIds) " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndUntaggedAndTagIds(@Param("enterpriseId") Integer enterpriseId,
																							  @Param("startDate") Date startDate,
																							  @Param("endDate") Date endDate,
																							  @Param("isPublished") Collection<Integer> isPublished,
																							  @Param("sourceId") Collection<Integer> sourceId,
																							   @Param("tagIds") Collection<Long> tagIds);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where stm.tag_id in :tagIds and sps.enterprise_id in :enterpriseIds and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  and " +
			"sps.postMethod in ('Business locations', 'Groups', 'BULK-RESELLER-POSTING') " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndTagIdsInForReseller(@Param("enterpriseIds") List<Integer> enterpriseIds,
																					  @Param("startDate") Date startDate,
																					  @Param("endDate") Date endDate,
																					  @Param("isPublished") Collection<Integer> isPublished,
																					  @Param("sourceId") Collection<Integer> sourceId,
																					  @Param("tagIds") Collection<Long> tagIds);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps left join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where sps.enterprise_id in :enterpriseIds and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  and " +
			"stm.entity_id is null and " +
			"sps.postMethod in ('Business locations', 'Groups', 'BULK-RESELLER-POSTING') " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndUntaggedForReseller(@Param("enterpriseIds") List<Integer> enterpriseIds,
																					  @Param("startDate") Date startDate,
																					  @Param("endDate") Date endDate,
																					  @Param("isPublished") Collection<Integer> isPublished,
																					  @Param("sourceId") Collection<Integer> sourceId);

	@Query(value = "Select sps.* from " +
			"social_post_schedule_info sps left join social_tag_mapping stm on stm.entity_id = sps.social_post_id " +
			"where sps.enterprise_id in :enterpriseIds and sps.publish_date >= :startDate and " +
			"sps.publish_date <= :endDate and sps.isPublished in :isPublished and sps.source_id in :sourceId  and " +
			"(stm.entity_id is null or stm.tag_id in :tagIds) and " +
			"sps.post_method in ('Business locations', 'Groups', 'BULK-RESELLER-POSTING') " +
			"order by sps.publish_date desc", nativeQuery = true)
	List<SocialPostScheduleInfo> findBySocialPostIdsInAndEnterpriseAndDateAndUntaggedAndTagIdsForReseller(@Param("enterpriseIds") List<Integer> enterpriseIds,
																							   @Param("startDate") Date startDate,
																							   @Param("endDate") Date endDate,
																							   @Param("isPublished") Collection<Integer> isPublished,
																							   @Param("sourceId") Collection<Integer> sourceId,
																							   @Param("tagIds") Collection<Long> tagIds);

	@Query(value = "select a.* from social_post_schedule_info a where a.isPublished = (:isPublished) and a.source_id = (:sourceId) and a.pageIds like %:pageId% ",nativeQuery = true)
    List<SocialPostScheduleInfo> findByIsPublishedAndSourceIdAndPageIdsIn(@Param("isPublished") Integer isPublished,@Param("sourceId") Integer sourceId,@Param("pageId") String pageId);

	@Modifying
	@Transactional
	@Query("update SocialPostScheduleInfo s set s.enterpriseId = :targetEnterpriseId where s.enterpriseId = :sourceBusinessId ")
	void updateEnterpriseIdWhereEnterpriseId(@Param("targetEnterpriseId") Integer targetEnterpriseId,@Param("sourceBusinessId") Integer sourceBusinessId);

	@Modifying
	@Transactional
	@Query(value = "Update social_post_schedule_info spi set spi.enterprise_id = :targetEnterpriseId where spi.enterprise_id = :sourceBusinessId " +
			"and EXISTS(select 1 from social_post sp where sp.id = spi.social_post_id and sp.approval_workflow_id is null)",nativeQuery = true)
	void updateEnterpriseIdWhereEnterpriseIdAndApprovalWorkflowIdIsNull(@Param("targetEnterpriseId") Integer targetEnterpriseId,
																		@Param("sourceBusinessId") Integer sourceBusinessId);

	@Query(value = "SELECT * FROM social_post_schedule_info spsi LEFT JOIN social_post sp ON spsi.social_post_id = sp.id " +
			"WHERE spsi.enterprise_id = :businessId AND sp.save_type != :saveType AND spsi.publish_date >= NOW() AND " +
			"spsi.publish_date <= :endDate", nativeQuery = true)
	List<SocialPostScheduleInfo> findByEnterpriseIdAndSaveTypeNotAndDate(@Param("businessId") Integer businessId,
																					   @Param("saveType") String saveType,
																					   @Param("endDate") Date endDate);

	@Query(value = "SELECT * FROM social_post_schedule_info spsi LEFT JOIN social_post sp ON spsi.social_post_id = sp.id " +
			"WHERE spsi.enterprise_id = :businessId AND sp.created_by = :userId AND sp.save_type != :saveType AND " +
			"spsi.publish_date >= NOW() AND spsi.publish_date <= :endDate", nativeQuery = true)
	List<SocialPostScheduleInfo> findByEnterpriseIdAndUserIdAndSaveTypeNotAndDate(@Param("businessId") Integer businessId,
																		 @Param("userId") Integer userId,
																		 @Param("saveType") String saveType,
																		 @Param("endDate") Date endDate);

	@Query("SELECT DISTINCT spi.socialPostId FROM SocialPostScheduleInfo spi WHERE spi.enterpriseId = :enterpriseId")
	List<Integer> findDistinctPostIdsByEnterpriseId(@Param("enterpriseId") Integer enterpriseId);

	@Query(value = "SELECT DISTINCT spi.social_post_id FROM social_post_schedule_info spi JOIN social_post sp ON spi.social_post_id = sp.id " +
			"WHERE spi.enterprise_id = :enterpriseId AND sp.approval_workflow_id IS NULL",nativeQuery = true)
	List<Integer> findDistinctByEnterpriseIdAndApprovalWorkflowIsNull(@Param("enterpriseId") Integer targetEnterpriseId);

	@Query(value = "SELECT spsi.publishDate FROM SocialPostScheduleInfo spsi WHERE spsi.socialPostId = :socialPostId")
	Date getPublishDateBySocialPostId(@Param("socialPostId") Integer socialPostId);

	List<SocialPostScheduleInfo> findBySocialPostIdIn(List<Integer> socialPostId);
}
