package com.birdeye.social.dao;

import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.social.entities.SocialStreams;

import javax.transaction.Transactional;

/**
 *
 * <AUTHOR>
 */
public interface SocialStreamsRepository extends JpaRepository<SocialStreams, Integer> {
    
    @Query("select s from SocialStreams s where s.businessId = :businessId and s.id = :streamId")
    public SocialStreams findByBusinessIdAndStreamId(@Param("businessId") Integer businessId, @Param("streamId") Integer streamId);

    @Query("select s from SocialStreams s where s.businessId in :businessIds")
    public List<SocialStreams> findByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
    
    @Query("select s.id from SocialStreams s where s.businessId in :businessIds and s.channelId != 2 order by id asc")
    public List<Integer> findIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
    
    @Query("select s from SocialStreams s where s.businessId in :businessIds and s.id = :streamId")
    public List<SocialStreams> findByIdAndBusinessIdIn(@Param("streamId") Integer streamId, @Param("businessIds") List<Integer> businessIds);
    
    @Query("Select s from SocialStreams s where s.channelId = :channelId and s.socialId in :socialIds")
    public List<SocialStreams> findByChannelIdAndSocialIdIn(@Param("channelId") Integer channelId, @Param("socialIds") List<Integer> socialIds);

    @Query("Select s from SocialStreams s where s.channelId = :channelId and s.socialId in :socialIds and s.streamType = :streamType")
    List<SocialStreams> findByChannelIdAndSocialIdInAndStreamType(@Param("channelId") Integer channelId, @Param("socialIds") List<Integer> socialIds,
                                                                          @Param("streamType") String streamType);

    List<SocialStreams> findByChannelIdAndBusinessIdAndStreamTypeIn(Integer channelId, Integer businessId, Collection<String> streamType);

    @Modifying
    @Transactional
    @Query("delete from SocialStreams s where s.id = :streamId")
    public void deleteByStreamId(@Param("streamId") Integer streamId);

    @Query("select s.socialId from SocialStreams s where s.channelId = :channelId and s.socialId is not null")
    public List<Integer> findSocialIdByChannelId(@Param("channelId") Integer channelId);
}
