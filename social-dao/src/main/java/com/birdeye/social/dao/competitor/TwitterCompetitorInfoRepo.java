package com.birdeye.social.dao.competitor;

import com.birdeye.social.entities.competitor.TwitterCompetitorInfo;
import com.birdeye.social.entities.competitor.TwitterCompetitorInfo;
import com.birdeye.social.entities.competitor.TwitterCompetitorInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;

@Repository

public interface Twitter<PERSON>ompetitorInfoRepo extends JpaRepository<TwitterCompetitorInfo, Integer> {
    TwitterCompetitorInfo findByPageId(String pageId);

    List<TwitterCompetitorInfo> findByPageIdIn(List<String> pageId);

    @Query(value = "SELECT tc.* from twitter_competitor_info as tc join twitter_competitor_mapping tm " +
            "on tc.id = tm.raw_competitor_id where tc.last_scan_date < :lastScanDate " +
            "ORDER by last_scan_date asc LIMIT :limit", nativeQuery = true)
    List<TwitterCompetitorInfo> findRecordsByLastScanDate(@Param("lastScanDate") Date lastScanDate, @Param("limit") int limit);

    Page<TwitterCompetitorInfo> findByLastScanDateIsLessThanOrderByLastScanDateAsc(Date lastScanDate, Pageable pageable);

    @Modifying
    @Transactional
    @Query("UPDATE TwitterCompetitorInfo b  SET b.lastScanDate = :lastScanDate where b.id in :ids")
    int updateLastScanDate(@Param("lastScanDate") Date lastScanDate, @Param("ids") List<Integer> ids);

    @Modifying
    @Transactional
    @Query("UPDATE TwitterCompetitorInfo b  SET b.scannedOnce = :scannedOnce where b.id = :id")
    int updateScannedOnce(@Param("scannedOnce") Integer scannedOnce, @Param("id") Integer id);
}
