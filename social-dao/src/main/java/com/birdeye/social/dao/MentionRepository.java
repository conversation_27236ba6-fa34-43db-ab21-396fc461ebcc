package com.birdeye.social.dao;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.social.dto.WebhoseMentionDTO;
import com.birdeye.social.entities.Mention;
import java.lang.String;
import java.util.List;
import java.util.Set;

/**
 * Repository interface for social mentions.
 * 
 * <AUTHOR>
 *
 */
public interface MentionRepository extends JpaRepository<Mention, Long> {
	
	List<Mention> findByBusinessId(Integer businessId);
	
	@Query("SELECT new com.birdeye.social.dto.WebhoseMentionDTO(m.title, m.externalId,m.nickName) " +
			"FROM Mention m WHERE m.businessId= :businessId AND m.externalId in :externalid")
	public List<WebhoseMentionDTO> findExistingByExternalIdInAndBusinessId(@Param("externalid" )List<String> externalid,@Param("businessId" )Integer businessId);
    

	List<Mention> findByExternalIdInAndBusinessId(List<String> externalid,Integer businessId);

	@Query(nativeQuery = true, value ="SELECT m.externalId FROM Mention m WHERE m.pageId= :pageId AND m.type= :type order by m.id desc limit :lim")
	List<String> findPostIdByPageIdAndType(@Param("pageId") String pageId, @Param("type") String type,@Param("lim") Integer lim);

	@Query("SELECT m.notificationId FROM Mention m WHERE m.pageId= :pageId AND m.type in  :type")
	List<String> findPostIdByPageIdAndTypeIn(@Param("pageId") String pageId, @Param("type") List<String> type);

	Mention findByNotificationId(String notificationId);

}
