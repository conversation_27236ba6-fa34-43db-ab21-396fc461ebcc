package com.birdeye.social.dao;

import com.birdeye.social.entities.PermissionMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

public interface PermissionMappingRepo extends JpaRepository<PermissionMapping, Integer> {

    List<PermissionMapping> findByChannel(String channel);
    PermissionMapping findByChannelAndPermissionCode(String channel, Integer permissionCode);
    List<PermissionMapping> findByChannelAndPermissionCodeAndModule(String channel, Integer permissionCode, String module);
    PermissionMapping findByChannelAndErrorCode(String channel, String errorCode);

    List<PermissionMapping> findAllByChannelAndErrorCode(String channel, String errorCode);


    @Query("Select p from PermissionMapping p where p.channel = :channel and p.permissionName is not null")
    List<PermissionMapping> findByChannelAndPermissionNameIsNotNull(@Param("channel") String channel);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.httpResponse = :httpResponse and p.errorParentCode =:errorCode and p.permissionCode = :errorSubCode")
    List<PermissionMapping> findAllByChannelAndHttpResAndErrorCodeAndErrorSubCode(@Param("channel") String channel,@Param("httpResponse") Integer httpResponse,@Param("errorCode") Integer errorCode,@Param("errorSubCode") Integer errorSubCode);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.httpResponse = :httpResponse and p.errorParentCode =:errorCode")
    List<PermissionMapping> findAllByChannelAndHttpResAndErrorCode(@Param("channel") String channel,@Param("httpResponse") Integer httpResponse,@Param("errorCode") Integer errorCode);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.httpResponse = :httpResponse")
    List<PermissionMapping> findAllByChannelAndHttpRes(@Param("channel") String channel,@Param("httpResponse") Integer httpResponse);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.errorParentCode =:errorCode and p.permissionCode =:errorSubCode")
    List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCode(@Param("channel") String channel, @Param("errorCode") Integer errorCode, @Param("errorSubCode") Integer errorSubCode);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.errorParentCode =:errorCode and p.permissionCode is null")
    List<PermissionMapping> getDataByChannelAndParentErrorCodeAndPermissionCodeNull(@Param("channel") String channel, @Param("errorCode") Integer errorCode);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.permissionCode = :subErrorCode")
    List<PermissionMapping> getDataByChannelAndPermissionCode(@Param("channel") String channel, @Param("subErrorCode") Integer subErrorCode);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.httpResponse = :httpResponse")
    List<PermissionMapping> findByChannelAndHttpErrorResponse(@Param("channel") String channel, @Param("httpResponse") Integer httpResponse);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.errorActualMessage = :primaryErr")
    PermissionMapping findByChannelAndErrorActualMessage(@Param("channel") String channel, @Param("primaryErr") String primaryErr);

    @Query("Select p from PermissionMapping p where p.channel = :channel and p.errorParentCode = :parentCode and p.errorCode = :errorCode")
    PermissionMapping findByChannelAndErrorParentCodeAndErrorCode(@Param("channel") String channel, @Param("parentCode") Integer parentCode, @Param("errorCode") String errorCode);

    @Modifying
    @Transactional
    @Query("update PermissionMapping p set p.errorMessage = :errorMessage where p.id = :id")
    public void updateErrorMessage(@Param("errorMessage") String errorMessage, @Param("id") Integer id);

    List<PermissionMapping> findByChannelAndModuleAndPermissionName(String channel, String module, String permissionName);

    List<PermissionMapping> findByChannelAndModuleAndPermissionCode(String channel, String module, Integer permissionCode);

    List<PermissionMapping> findAllByChannelAndModuleAndErrorCode(String channel, String module, String toLowerCase);

    List<PermissionMapping> findByChannelAndModuleAndPermissionNameNotNull(String channel, String module);

    List<PermissionMapping> findByChannelAndErrorParentCodeAndPermissionCodeAndModule( String channel, Integer errorCode, Integer errorSubCode, String module);

    List<PermissionMapping> findByChannelAndPermissionCodeInAndModule(String channel, Collection<Integer> permissionCode, String module);

    List<PermissionMapping> findByPermissionNameAndErrorParentCodeAndModule(String permissionName, Integer value, String name);
}
