package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.InstagramPageEngagementReset;
import com.birdeye.social.entities.report.InstagramPageInsight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface InstagramPageEngagementResetRepo extends JpaRepository<InstagramPageEngagementReset, Integer> {
    @Query("Select i from InstagramPageEngagementReset i where  i.pageId = :pageId")
    InstagramPageEngagementReset findByPageId(@Param("pageId") String pageId);

}
