package com.birdeye.social.dao.assetlibrary;

import com.birdeye.social.dto.assetlibrary.SocialAssetLibraryBizTagMappingInfo;
import com.birdeye.social.entities.assetlibrary.SocialAssetLibraryBusinessTagMapping;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> on 09/10/23
 */
public interface SocialAssetLibraryBusinessTagMappingRepository extends JpaRepository<SocialAssetLibraryBusinessTagMapping, Long> {

    @Query(value = "SELECT new com.birdeye.social.dto.assetlibrary.SocialAssetLibraryBizTagMappingInfo(btm.assetId, btm.tagId, bt.name) "
                   + "FROM SocialAssetLibraryBusinessTagMapping btm INNER JOIN btm.businessTag bt WHERE btm.assetId IN :assetIds")
    List<SocialAssetLibraryBizTagMappingInfo> findTagMappingInfoByAssetIds(@Param("assetIds") Collection<Long> assetIds);

    @Query("SELECT DISTINCT(btm.assetId) FROM SocialAssetLibraryBusinessTagMapping btm where btm.tagId IN :tagIds")
    List<Long> findDistinctAssetIdIdMappedToTagIdIn(@Param("tagIds") Collection<Long> tagIds);

    @Modifying
    @Transactional
    @Query("DELETE FROM SocialAssetLibraryBusinessTagMapping btm where btm.tagId IN :tagIds")
    void deleteByTagIdIn(@Param("tagIds") Collection<Long> tagIds);

    @Query("SELECT DISTINCT(btm.accountId) FROM SocialAssetLibraryBusinessTagMapping btm")
    List<Integer> findDistinctAccountsWithTagMapping(Pageable pageable);

    @Query(value = "SELECT new com.birdeye.social.dto.assetlibrary.SocialAssetLibraryBizTagMappingInfo(btm.assetId, btm.tagId, bt.name, bt.updatedBy, btm.updatedBy) "
                   + "FROM SocialAssetLibraryBusinessTagMapping btm INNER JOIN btm.businessTag bt WHERE btm.accountId = :accountId")
    List<SocialAssetLibraryBizTagMappingInfo> findTagAndMappingInfoByAccountId(@Param("accountId") Integer accountId);

}
