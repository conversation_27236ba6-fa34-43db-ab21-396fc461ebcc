package com.birdeye.social.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.social.entities.Mention;
import com.birdeye.social.entities.WebhoseRaw;

/**
 * Repository interface for social Webhose raw data.
 * 
 * <AUTHOR>
 *
 */
public interface WebhoseRepository extends JpaRepository<WebhoseRaw, Integer> {
	
	List<WebhoseRaw> findByRequestInfoId(Integer requestInfoId);

}
