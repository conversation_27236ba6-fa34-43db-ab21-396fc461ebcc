package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialPostDraftInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface SocialPostDraftInfoRepository extends JpaRepository<SocialPostDraftInfo, Integer> {


    @Modifying
    @Transactional
    void deleteAllBySocialPostId(Integer postId);

    @Modifying
    @Transactional
    void deleteAllBySocialPostIdIn(List<Integer> postIds);

    @Modifying
    @Transactional
    void deleteAllBySocialPostIdInAndEnterpriseId(List<Integer> postIds, Integer enterpriseId);

    @Modifying
    @Transactional
    void deleteByIdIn(Set<Integer> id);

    List<SocialPostDraftInfo> findAllBySocialPostIdAndIsPosted(Integer postId, Integer isPosted);

    List<SocialPostDraftInfo> findAllBySocialPostIdAndEnterpriseIdAndIsPosted(Integer postId, Integer enterpriseId, Integer isPosted);

    List<SocialPostDraftInfo> findAllBySocialPostIdInAndEnterpriseId(List<Integer> socialPostId, Integer enterpriseId);

    List<SocialPostDraftInfo> findAllBySocialPostId(Integer postId);

    List<SocialPostDraftInfo> findByEnterpriseIdAndIsPosted(Integer enterpriseId, Integer isPosted);

    List<SocialPostDraftInfo> findBySocialPostId(Integer socialPostId);

    List<SocialPostDraftInfo> findBySocialPostIdIn(List<Integer> socialPostId);

    @Query("select s.isPosted from SocialPostDraftInfo s where s.socialPostId in :postIds")
    List<Integer> findIsPostedbySocialPostIdIn(@Param("postIds") List<Integer> postIds);

    @Modifying
    @Transactional
    @Query("UPDATE SocialPostDraftInfo s SET s.isPosted = :isPosted where s.socialPostId = :postId")
    Integer updateDraftPostState(@Param("isPosted") Integer isPosted, @Param("postId") Integer postId);

    @Modifying
    @Transactional
    @Query("UPDATE SocialPostDraftInfo s SET s.isPosted = :isPosted where s.socialPostId = :postId and s.enterpriseId = :enterpriseId")
    Integer updateDraftPostStateForEnterprise(@Param("isPosted") Integer isPosted, @Param("postId") Integer postId, @Param("enterpriseId") Integer enterpriseId);

    @Query("SELECT count(distinct s.socialPostId) from SocialPostDraftInfo s where s.enterpriseId = :enterpriseId and s.isPosted = :isPosted")
    int countByEnterpriseId(@Param("enterpriseId") Integer enterpriseId, @Param("isPosted") Integer isPosted);

    @Modifying
    @Transactional
    @Query("UPDATE SocialPostDraftInfo s SET s.isPosted = :isPosted where s.socialPostId in :postIds")
    Integer updateDraftPostsState(@Param("isPosted") Integer isPosted, @Param("postIds") List<Integer> postIds);

    @Query("select distinct s.socialPostId from SocialPostDraftInfo s where s.isPosted= :isPosted")
    List<Integer> findDistinctPostIdForDraftPosts(@Param("isPosted") Integer isPosted);

    List<SocialPostDraftInfo> findByMasterPostIdIn(List<Integer> socialPostIds);

    boolean existsByMasterPostIdAndSourceId(Integer masterPostId, Integer sourceId);

    List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdIn(List<Integer> masterPostIds, List<Integer> sourceIds);

    List<SocialPostDraftInfo> findByMasterPostIdInAndSourceIdInAndEnterpriseId(List<Integer> masterPostIds, List<Integer> sourceIds, Integer enterpriseId);

    @Query("select s.postGroupDetails from SocialPostScheduleInfo s where s.socialPostId = :postId")
    String findGroupDetailsForPostId(@Param("postId") Integer postId);

    @Query("select s from SocialPostDraftInfo s where s.enterpriseId in :enterpriseIds and s.postMethod in ('Business locations', 'Groups')")
    List<SocialPostDraftInfo> findByEnterpriseIdInMadeByReseller(@Param("enterpriseIds") List<Integer> enterpriseIds);

    @Transactional
    @Modifying
    void deleteById(Integer id);

    Boolean existsByMasterPostIdAndEnterpriseId(Integer id, Integer businessId);

    List<SocialPostDraftInfo> findBySocialPostIdInAndEnterpriseId(List<Integer> postIds, Integer enterpriseId);

    @Query(value = "SELECT * FROM social_post_draft_info spdi LEFT JOIN social_post sp ON spdi.social_post_id = sp.id " +
            "WHERE spdi.enterprise_id = :businessId AND spdi.is_posted = :isPosted AND sp.created_date >= :startDate " +
            "AND sp.created_date <= :endDate AND sp.created_by != :createdBy ", nativeQuery = true)
    List<SocialPostDraftInfo> findByEnterpriseIdAndIsPostedAndDateAndCreatedByNot(@Param("businessId") Integer businessId,
                                                       @Param("createdBy") Integer createdBy,
                                                       @Param("isPosted") Integer isPosted,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    @Query(value = "SELECT * FROM social_post_draft_info spdi LEFT JOIN social_post sp ON spdi.social_post_id = sp.id " +
            "WHERE spdi.enterprise_id = :businessId AND sp.created_by = :userId AND spdi.is_posted = :isPosted " +
            "AND sp.created_date >= :startDate AND sp.created_date <= :endDate", nativeQuery = true)
    List<SocialPostDraftInfo> findByEnterpriseIdAndUserIdAndIsPostedAndDate(@Param("businessId") Integer businessId,
                                                       @Param("userId") Integer userId,
                                                       @Param("isPosted") Integer isPosted,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    @Query(value = "SELECT * FROM social_post_draft_info spdi LEFT JOIN social_post sp ON spdi.social_post_id = sp.id " +
            "WHERE spdi.enterprise_id = :businessId AND spdi.is_posted = :isPosted AND sp.created_date >= :startDate " +
            "AND sp.created_date <= :endDate AND sp.created_by = :createdBy AND sp.edited_by IS NOT NULL AND sp.edited_by != :editedBy", nativeQuery = true)
    List<SocialPostDraftInfo> findByEnterpriseIdAndIsPostedAndDateAndCreatedByAndEditedByNonNull(@Param("businessId") Integer businessId,
                                                                   @Param("isPosted") Integer isPosted,
                                                                   @Param("startDate") Date startDate,
                                                                   @Param("endDate") Date endDate,
                                                                    @Param("createdBy") Integer createdBy,
                                                                     @Param("editedBy") Integer editedBy);

    @Query(value = "SELECT * FROM social_post_draft_info spdi LEFT JOIN social_post sp ON spdi.social_post_id = sp.id " +
            "WHERE spdi.enterprise_id = :businessId AND sp.created_by = :userId AND spdi.is_posted = :isPosted " +
            "AND sp.created_date >= :startDate AND sp.created_date <= :endDate AND sp.edited_by = :editedBy ", nativeQuery = true)
    List<SocialPostDraftInfo> findByEnterpriseIdAndUserIdAndIsPostedAndDateAndEditedBy(@Param("businessId") Integer businessId,
                                                                            @Param("userId") Integer userId,
                                                                            @Param("editedBy") Integer editedBy,
                                                                            @Param("isPosted") Integer isPosted,
                                                                            @Param("startDate") Date startDate,
                                                                            @Param("endDate") Date endDate);

    @Query("SELECT s FROM SocialPostDraftInfo s WHERE s.id IN (SELECT MIN(s2.id) FROM SocialPostDraftInfo s2 WHERE s2.masterPostId IN :masterPostIds GROUP BY s2.masterPostId)")
    List<SocialPostDraftInfo> findOneByMasterPostIdIn(@Param("masterPostIds") List<Integer> masterPostIds);
}
