package com.birdeye.social.dao;

import com.birdeye.social.entities.GoogleAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GoogleAccountRepo extends JpaRepository<GoogleAccount,Integer>, PagingAndSortingRepository<GoogleAccount, Integer> {

    List<GoogleAccount> findByAccountIdIn(List<String> accountIds);

    List<GoogleAccount> findAllByBusinessGetPageReqId(String requestId);

    GoogleAccount findByAccountId(String accountId);

    List<GoogleAccount> findByUserEmail(String userEmail);

    List<GoogleAccount> findByUserEmailOrderByBusinessGetPageReqIdDesc(String userEmail);

    GoogleAccount findByAccountIdAndBusinessGetPageReqId(String accountId, String requestId);

    List<GoogleAccount> findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(String accountId, String requestId);
    List<GoogleAccount> findByUserEmailAndActive(String userEmail, Integer active);

    List<GoogleAccount> findByBusinessGetPageReqId(String requestId);
}
