package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.GMBPageInsight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface GMBPageInsightRepo extends JpaRepository<GMBPageInsight, Integer> {

    List<GMBPageInsight> findByLocationIdOrderByIdDesc(String externalId);
}
