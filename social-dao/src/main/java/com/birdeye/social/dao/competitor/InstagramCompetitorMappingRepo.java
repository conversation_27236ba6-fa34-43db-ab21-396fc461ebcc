package com.birdeye.social.dao.competitor;

import com.birdeye.social.entities.competitor.InstagramCompetitorMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface InstagramCompetitorMappingRepo extends JpaRepository<InstagramCompetitorMapping, Integer> {
    InstagramCompetitorMapping findByCompetitorIdAndEnterpriseId(String compId, Long enterpriseId);

    List<InstagramCompetitorMapping> findByEnterpriseId(Long enterpriseId);

    List<InstagramCompetitorMapping> findByRawCompetitorId(Integer rawCompId);

    List<InstagramCompetitorMapping> findByCompetitorIdInAndEnterpriseId(Collection<String> compId, Long enterpriseId);

}
