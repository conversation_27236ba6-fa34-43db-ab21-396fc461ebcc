package com.birdeye.social.dao;

import com.birdeye.social.entities.BusinessWhatsappAccounts;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;

@Repository
public interface BusinessWhatsappAccountsRepository extends JpaRepository<BusinessWhatsappAccounts, Integer> {

    BusinessWhatsappAccounts findByBusinessId(Integer businessIds);
    BusinessWhatsappAccounts findByPhoneNumberId(String phoneNumberId);
    boolean existsByBusinessId(Integer businessId);
    List<BusinessWhatsappAccounts> findByEnterpriseId(Long enterpriseId);
    List<BusinessWhatsappAccounts> findByPhoneNumberIdIn(List<String> phoneNumberIds);
    List<BusinessWhatsappAccounts> findAllByBusinessIdIn(List<Integer> businessIds);
    List<BusinessWhatsappAccounts> findByEnterpriseIdAndBusinessIdNotNull(Long enterpriseId);
    BusinessWhatsappAccounts findById(Integer id);
    List<BusinessWhatsappAccounts> findByRequestId(String id);
    Page<BusinessWhatsappAccounts> findByRequestId(String requestId, Pageable pageable);
    Page<BusinessWhatsappAccounts> findAll(Specification<BusinessWhatsappAccounts> specification, Pageable pageable);
    List<BusinessWhatsappAccounts> findByWabaId(String wabaId);
    List<BusinessWhatsappAccounts> findByResellerIdAndPhoneNumberIdIn(Long resellerId, List<String> phoneNumberId);
    List<BusinessWhatsappAccounts> findByEnterpriseIdAndPhoneNumberIdIn(Long enterpriseId, List<String> phoneNumberId);
    List<BusinessWhatsappAccounts> findByAccountId(Integer accountId);
    @Transactional
    void deleteByAccountId(Integer accountId);
    boolean existsByEnterpriseIdAndIsSelected(Long enterpriseId, Integer isSelected);





    @Query("select count(b) from BusinessWhatsappAccounts b where b.enterpriseId = :enterpriseId and b.validityType in (:validity)")
    Long findCountByEnterpriseIdAndValidityType(@Param("enterpriseId") Long enterpriseId, @Param("validity") List<Integer> asList);
    @Query("select count(b) from BusinessWhatsappAccounts b where b.resellerId = :resellerId and b.validityType in (:validity)")
    Long findCountByResellerIdAndValidityType(@Param("resellerId") Long resellerId, @Param("validity") List<Integer> asList);
    @Query("SELECT COUNT(b) > 0 FROM BusinessWhatsappAccounts b WHERE b.enterpriseId = :enterpriseId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByEnterpriseId(@Param("enterpriseId") Long enterpriseId, @Param("businessIds")List<Integer> businessIds);
    @Query("SELECT COUNT(b) > 0 FROM BusinessWhatsappAccounts b WHERE b.resellerId = :resellerId AND b.businessId IS NOT NULL AND b.businessId in :businessIds")
    boolean existsMappedPageByResellerId(@Param("resellerId") Long resellerId, @Param("businessIds")List<Integer> businessIds);
    @Query(value = "SELECT wa.businessId FROM BusinessWhatsappAccounts wa WHERE wa.businessId IN :businessIds")
    List<Integer> findAllIdByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);
    @Modifying
    @Transactional
    @Query(nativeQuery =true,value="DELETE from business_whatsapp_accounts  WHERE reseller_id = :reseller_id AND phone_number_id NOT IN (:pageIds)")
    void deleteByResellerIdAndPhoneNumberIdNotIn(@Param("reseller_id") Long parentId, @Param("pageIds") List<String> pageIds);

    @Modifying
    @Transactional
    @Query(nativeQuery =true,value="DELETE from business_whatsapp_accounts  WHERE enterprise_id = :enterpriseId AND phone_number_id NOT IN (:pageIds)")
    void deleteByEnterpriseIdAndPhoneNumberIdNotIn(@Param("enterpriseId") Long parentId, @Param("pageIds") List<String> pageIds);

}
