package com.birdeye.social.dao;

import com.birdeye.social.entities.GoogleClientCred;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface GoogleClientCredRepo extends JpaRepository<GoogleClientCred, Integer> {

    public GoogleClientCred findById(Integer id);

    @Query("select gc from GoogleClientCred gc where gc.domainId is null and gc.project = 'aggregation-project1'")
    public GoogleClientCred findGoogleCredDefault();

    @Query("select gc from GoogleClientCred gc where gc.domainId is null and gc.project = 'youtube-project'")
    public List<GoogleClientCred> findYoutubeCredDefault();

    @Query("select gc from GoogleClientCred gc where gc.domainId = :domainId and gc.project = 'aggregation-project1'")
    public List<GoogleClientCred> findGoogleCredByDomain(@Param("domainId") Long domainId);
}
