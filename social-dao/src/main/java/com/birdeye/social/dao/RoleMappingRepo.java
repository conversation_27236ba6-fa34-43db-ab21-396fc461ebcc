package com.birdeye.social.dao;

import com.birdeye.social.entities.RoleMapping;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Collection;
import java.util.List;

public interface RoleMappingRepo extends JpaRepository<RoleMapping, Integer> {

    List<RoleMapping> findByChannel(String channel);
    RoleMapping findByChannelAndRole(String channel, String role);

    List<RoleMapping> findByChannelAndRoleIn(String channel, Collection<String> role);
}
