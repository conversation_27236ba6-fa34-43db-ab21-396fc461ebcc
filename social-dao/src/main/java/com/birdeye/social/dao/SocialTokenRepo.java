package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface SocialTokenRepo extends JpaRepository<SocialToken, Integer> {

    @Query("Select g from SocialToken g where g.channel = :channel and g.sessionToken = :sessionToken")
    public List<SocialToken> findBySessionToken(@Param("channel") String channel, @Param("sessionToken") String sessionToken);

    @Query("Select g from SocialToken g where g.channel = :channel and g.enterpriseId = :enterpriseId and g.source = :source")
    public List<SocialToken> findByEnterpriseIdAndSource(@Param("channel") String channel, @Param("enterpriseId") Long enterpriseId, @Param("source") String source);

    @Query(value = "Select o from SocialToken o WHERE o.validTill < :validTill")
    public List<SocialToken> getExpiredToken(@Param("validTill") Date validTill);

}
