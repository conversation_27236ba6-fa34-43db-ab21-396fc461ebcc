package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.FacebookPageInsight;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface FacebookPageInsightRepo extends JpaRepository<FacebookPageInsight, Integer> {

    List<FacebookPageInsight> findByPageIdOrderByIdDesc(String externalId);

    @Modifying
    @Transactional
    @Query("update FacebookPageInsight f set f.businessId = :businessId, f.enterpriseId = :enterpriseId where f.pageId = :pageId")
    void updateBusinessIdWherePageId(@Param("pageId") String pageId,@Param("businessId") Integer businessId,@Param("enterpriseId") Long enterpriseId);

    List<FacebookPageInsight> findByPageIdAndDateBetween( String pageId,Date startDate, Date endDate);

}
