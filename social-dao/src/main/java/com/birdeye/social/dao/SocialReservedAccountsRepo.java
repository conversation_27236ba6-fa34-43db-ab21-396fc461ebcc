package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialReservedAccounts;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SocialReservedAccountsRepo extends JpaRepository<SocialReservedAccounts, Integer> {

    public List<SocialReservedAccounts> findBySourceId(Integer srcId);

    public List<SocialReservedAccounts> findBySourceIdAndName(Integer srcId, String name);
}
