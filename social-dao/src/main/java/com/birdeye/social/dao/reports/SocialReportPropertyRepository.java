package com.birdeye.social.dao.reports;

import com.birdeye.social.entities.report.SocialReportProperty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface SocialReportPropertyRepository extends JpaRepository<SocialReportProperty, Integer> {

    SocialReportProperty findById(Integer id);
    @Query("Select min(s.backfillTillDays) from SocialReportProperty s where s.sourceId = :sourceId and s.reportType = :reportType and s.matrixType != 'follower_count'")
    Integer findMinDaysForSourceIdAndReportType(@Param("sourceId") Integer sourceId,@Param("reportType")  String reportType);
    @Query("Select min(s.deltaSyncTillDays) from SocialReportProperty s where s.sourceId = :sourceId and s.reportType = :reportType and s.matrixType in :matrixTypes")
    Integer findMinDaysForDailySyncForSourceIdAndReportType(@Param("sourceId") Integer sourceId,@Param("reportType")  String reportType, @Param("matrixTypes") List<String> matrixType);
}
