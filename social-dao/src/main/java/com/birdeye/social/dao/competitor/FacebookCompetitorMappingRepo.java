package com.birdeye.social.dao.competitor;

import com.birdeye.social.entities.competitor.FacebookCompetitorMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FacebookCompetitorMappingRepo extends JpaRepository<FacebookCompetitorMapping, Integer> {
    FacebookCompetitorMapping findByCompetitorIdAndEnterpriseId(String compId, Long enterpriseId);
    List<FacebookCompetitorMapping> findByEnterpriseId(Long enterpriseId);

    List<FacebookCompetitorMapping> findByRawCompetitorId(Integer rawCompetitorId);

    List<FacebookCompetitorMapping> findByCompetitorIdInAndEnterpriseId(Collection<String> compId, Long enterpriseId);

}
