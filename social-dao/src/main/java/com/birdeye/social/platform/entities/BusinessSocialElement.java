package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;



@Entity
	@Table(name = "business_social_element")	
	public class BusinessSocialElement implements Serializable {
	    private static final long serialVersionUID = 1L;
	    @Id
	    @GeneratedValue(strategy = GenerationType.IDENTITY)
	    private Integer id;

	    @Column(name = "business_id")
	    private Integer businessId;
	    
	    @NotNull
	    @Size(min = 1, max = 1000)
	    @Column(name = "url")
	    private String url;

	    @Column(name = "source_id")
	    private Integer sourceId;

	    @Column(name = "updated_at")
	    @Temporal(TemporalType.TIMESTAMP)
	    private Date updatedAt;
	    
	    @Basic(optional = false)
	    @Column(name = "created_at")
	    @Temporal(TemporalType.TIMESTAMP)
	    private Date createdAt;

	    public BusinessSocialElement() {
	        createdAt = new Date();
	        updatedAt = new Date();
	    }

	    public Integer getId() {
	        return id;
	    }

	    public void setId(Integer id) {
	        this.id = id;
	    }

	    public Integer getBusinessId() {
	        return businessId;
	    }

	    public void setBusinessId(Integer businessId) {
	        this.businessId = businessId;
	    }

	    public String getUrl() {
	        return url;
	    }

	    public void setUrl(String url) {
	        this.url = url;
	    }

	    public Integer getSourceId() {
	        return sourceId;
	    }

	    public void setSourceId(Integer sourceId) {
	        this.sourceId = sourceId;
	    }

	    public Date getUpdatedAt() {
	        return updatedAt;
	    }

	    public void setUpdatedAt(Date updatedAt) {
	        this.updatedAt = updatedAt;
	    }

	    public Date getCreatedAt() {
	        return createdAt;
	    }

	    public void setCreatedAt(Date createdAt) {
	        this.createdAt = createdAt;
	    }
	    
	    @Override
	    public int hashCode() {
	        int hash = 0;
	        hash += (id != null ? id.hashCode() : 0);
	        return hash;
	    }

	    @Override
	    public boolean equals(Object object) {
	        // TODO: Warning - this method won't work in the case the id fields are not set
	        if (!(object instanceof BusinessSocialElement)) {
	            return false;
	        }
	        BusinessSocialElement other = (BusinessSocialElement) object;
	        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
	            return false;
	        }
	        return true;
	    }

	    @Override
	    public String toString() {
	        return "BusinessSocialProfile{" + "id=" + id + ", businessId=" + businessId + ", URL=" + url + ", sourceId=" + sourceId + '}';
	    }

}
