package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "aggregation_source")
public class AggregationSource implements Serializable {

	private static final long serialVersionUID = 1L;

	@Basic(optional = false)
	@Column(name = "updated")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updated;

	@Basic(optional = false)
	@Column(name = "created")
	@Temporal(TemporalType.TIMESTAMP)
	private Date created;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Basic(optional = false)
	@Column(name = "id", nullable = false)
	private Integer id;

	@Basic(optional = false)
	@NotNull
	@Size(min = 1, max = 4000)
	@Column(name = "name", nullable = false, length = 4000)
	private String name;

	@Size(max = 4000)
	@Column(name = "url", length = 4000)
	private String url;

	@Size(max = 8)
	@Column(name = "status", length = 8)
	private String status;

	@Column(name = "background_color")
	private String backgroundColor = "#01aed8";

	@Column(name = "text_color")
	private String textColor = "#ffffff";

	@Size(max = 255)
	@Column(name = "thumbnail_url", length = 255)
	private String thumbnailUrl;

	@Column(name = "is_tier1")
	private Integer isTier1 = 0;

	@Column(name = "priority")
	private Integer priority;

	@Column(name = "search_enabled")
	private Integer searchEnabled;

	@Column(name = "is_directory_source")
	private Integer isDirectorySource;

	@Column(name = "single_review_source")
	private Integer singleReviewSource = 0;

	@Column(name = "alias")
	private String sourceAlias;

	@Column(name = "type")
	private String type = "location";

	@Column(name = "yext_alias")
	private String yextAlias;

	@Column(name = "sweetiq_alias")
	private String sweetIQAlias;

	@Column(name = "is_yext_scan_enabled")
	private Integer isYextScanEnabled = 0;

	@Column(name = "require_model")
	private Integer requireModel;

	@Column(name = "vendor_id")
	private Integer vendorId;

	@Column(name = "timeout")
	private Integer timeout;

	@Column(name="social_enabled")
	private Integer socialEnabled;

	public AggregationSource() {
	}

	public AggregationSource(Integer id) {
		this.id = id;
	}

	public AggregationSource(Integer id, String name, Date created, Date updated) {
		this.id = id;
		this.name = name;
		this.created = created;
		this.updated = updated;
	}

	public AggregationSource(Integer id, String name, String textColor, String backgroundColor, String url) {
		this.id = id;
		this.name = name;
		this.url = url;
		this.textColor = textColor;
		this.backgroundColor = backgroundColor;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getUpdated() {
		return updated;
	}

	public void setUpdated(Date updated) {
		this.updated = updated;
	}

	public Date getCreated() {
		return created;
	}

	public void setCreated(Date created) {
		this.created = created;
	}

	public String getBackgroundColor() {
		return backgroundColor;
	}

	public void setBackgroundColor(String backgroundColor) {
		this.backgroundColor = backgroundColor;
	}

	public String getTextColor() {
		return textColor;
	}

	public void setTextColor(String textColor) {
		this.textColor = textColor;
	}

	public String getThumbnailUrl() {
		return thumbnailUrl;
	}

	public void setThumbnailUrl(String thumbnailUrl) {
		this.thumbnailUrl = thumbnailUrl;
	}

	public Integer getIsTier1() {
		return isTier1;
	}

	public void setIsTier1(Integer isTier1) {
		this.isTier1 = isTier1;
	}

	public Integer getPriority() {
		return priority;
	}

	public void setPriority(Integer priority) {
		this.priority = priority;
	}

	public Integer getSearchEnabled() {
		return searchEnabled;
	}

	public void setSearchEnabled(Integer searchEnabled) {
		this.searchEnabled = searchEnabled;
	}

	public Integer getIsDirectorySource() {
		return isDirectorySource;
	}

	public void setIsDirectorySource(Integer isDirectorySource) {
		this.isDirectorySource = isDirectorySource;
	}

	public Integer getSingleReviewSource() {
		return singleReviewSource;
	}

	public void setSingleReviewSource(Integer singleReviewSource) {
		this.singleReviewSource = singleReviewSource;
	}

	/**
	 * @return the sourceAlias
	 */
	public String getSourceAlias() {
		return sourceAlias;
	}

	/**
	 * @param sourceAlias
	 *            the sourceAlias to set
	 */
	public void setSourceAlias(String sourceAlias) {
		this.sourceAlias = sourceAlias;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Override
	public int hashCode() {
		int hash = 0;
		hash += (id != null ? id.hashCode() : 0);
		return hash;
	}

	@Override
	public boolean equals(Object object) {
		// TODO: Warning - this method won't work in the case the id fields are not set
		if (!(object instanceof AggregationSource)) {
			return false;
		}
		AggregationSource other = (AggregationSource) object;
		if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
			return false;
		}
		return true;
	}

	public String getYextAlias() {
		return yextAlias;
	}

	public void setYextAlias(String yextAlias) {
		this.yextAlias = yextAlias;
	}

	public Integer getIsYextScanEnabled() {
		return isYextScanEnabled;
	}

	public void setIsYextScanEnabled(Integer isYextScanEnabled) {
		this.isYextScanEnabled = isYextScanEnabled;
	}

	public Integer getVendorId() {
		return vendorId;
	}

	public void setVendorId(Integer vendorId) {
		this.vendorId = vendorId;
	}

	@Override
	public String toString() {
		ReflectionToStringBuilder b = new ReflectionToStringBuilder(this, ToStringStyle.DEFAULT_STYLE);
		return b.toString();
	}

	/**
	 * To String implemntation for logging the entities.
	 *
	 * @return the toString
	 */
	public String entityLog() {
		return "AggregationSource{" + "updated=" + updated + ", created=" + created + ", id=" + id + ", name=" + name
				+ ", url=" + url + ", status=" + status + '}';
	}

	/**
	 * @return the requireModel
	 */
	public Integer getRequireModel() {
		return requireModel;
	}

	/**
	 * @param requireModel
	 *            the requireModel to set
	 */
	public void setRequireModel(Integer requireModel) {
		this.requireModel = requireModel;
	}

	/**
	 * @return the sweetIQAlias
	 */
	public String getSweetIQAlias() {
		return sweetIQAlias;
	}

	/**
	 * @param sweetIQAlias
	 *            the sweetIQAlias to set
	 */
	public void setSweetIQAlias(String sweetIQAlias) {
		this.sweetIQAlias = sweetIQAlias;
	}

	public Integer getTimeout() {
		return timeout;
	}

	public void setTimeout(Integer timeout) {
		this.timeout = timeout;
	}

	public Integer getSocialEnabled() {
		return socialEnabled;
	}

	public void setSocialEnabled(Integer socialEnabled) {
		this.socialEnabled = socialEnabled;
	}
}
