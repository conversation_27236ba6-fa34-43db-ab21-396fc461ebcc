package com.birdeye.social.platform.entities;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "roi_setting")
public class ROISetting implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Column(name = "type", nullable = false)
    private int type;
    
    @Column(name = "name", nullable = false)
    private String name;
    
    @Column(name = "value_per_unit")
    private Float valuePerUnit = 0f;

    @Column(name ="min_threshold")
    private Float minThreshold;
    
    @JoinColumn(name = "category", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    private Category category;
    
    @JoinColumn(name = "business", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER)
    private Business business;

    @Column(name = "business", insertable = false, updatable = false)
    private Integer businessId;
    
    public ROISetting() {
    }

    public ROISetting(ROISetting setting) {
        this.type = setting.getType();
        this.name = setting.getName();
        this.valuePerUnit = setting.getValuePerUnit();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Float getValuePerUnit() {
        return valuePerUnit;
    }

    public void setValuePerUnit(Float valuePerUnit) {
        this.valuePerUnit = valuePerUnit;
    }

    public Float getMinThreshold() {
        return minThreshold;
    }

    public void setMinThreshold(Float minThreshold) {
        this.minThreshold = minThreshold;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Business getBusiness() {
        return business;
    }

    public void setBusiness(Business business) {
        this.business = business;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Override
    public String toString() {
        return "ROISetting{" + "id=" + id + ", type=" + type + ", name=" + name + ", valuePerUnit=" + valuePerUnit + ", minThreshold=" + minThreshold + ", category=" + category + ", business=" + business + ", businessId=" + businessId + '}';
    }

}
