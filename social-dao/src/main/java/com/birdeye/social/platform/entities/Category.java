package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Collection;

import javax.persistence.Basic;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "category")
public class Category implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id", nullable = false)
    private Integer id;
    
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 50)
    @Column(name = "name", nullable = false, length = 50)
    private String name;
 
    @JoinColumn(name = "parent_category", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.EAGER, cascade= CascadeType.MERGE)    
    private Category parentCategory;
    
    @Size(max = 300)
    @Column(name = "cover_image_url", length = 300)
    private String coverImageUrl;
    
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "parentCategory", fetch = FetchType.LAZY)
    private Collection<Category> childCategoryCollection;

    @Column(name = "rep_id")
    private Integer repId;
    
    @Column(name = "is_mapped")
    private Integer isMapped = 0;

    @Column(name = "search_count")
    private Integer searchCount = 1;

    @Column(name = "association_count")
    private long associationCount = 0L;
    
    @Column(name = "avg_rating")
    private double avgRating = 0.0;
    
    @Column(name = "is_business_mapped")
    private Integer isBusinessMapped = 0;
    
    @JoinColumn(name = "schema_type_id", referencedColumnName = "id")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private SchemaType schemaType;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Collection<Category> getChildCategoryCollection() {
        return childCategoryCollection;
    }

    public void setChildCategoryCollection(Collection<Category> childCategoryCollection) {
        this.childCategoryCollection = childCategoryCollection;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Category getParentCategory() {
        return parentCategory;
    }

    public void setParentCategory(Category parentCategory) {
        this.parentCategory = parentCategory;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
  
    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof Category)) {
            return false;
        }
        Category other = (Category) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    /**
	  * To String implemntation for logging the entities.
	  * @return the toString 
	*/
    public String entityLog() {
        return "Category{" + "id=" + id + ", name=" + name + ", parentCategory=" + parentCategory + ", coverImageUrl=" + coverImageUrl + ", childCategoryCollection=" + childCategoryCollection + '}';
    }

    @Override
    public String toString() {
        return "com.bazaarify.platform.entities.Category[ id=" + id + " ]";
    }

    public Integer getRepId() {
        return repId;
    }

    public void setRepId(Integer repId) {
        this.repId = repId;
    }

    public Integer getIsMapped() {
        return isMapped;
    }

    public void setIsMapped(Integer isMapped) {
        this.isMapped = isMapped;
    }

    public Integer getSearchCount() {
        return searchCount;
    }

    public void setSearchCount(Integer searchCount) {
        this.searchCount = searchCount;
    }

    public long getAssociationCount() {
        return associationCount;
    }

    public void setAssociationCount(long associationCount) {
        this.associationCount = associationCount;
    }

    public double getAvgRating() {
        return avgRating;
    }

    public void setAvgRating(double avgRating) {
        this.avgRating = avgRating;
    }

    public String getCoverImageUrlFromParent(){
        Category category = this;
        String coverImageURL = null;
        while(category != null && coverImageURL == null){
            coverImageURL = category.getCoverImageUrl();
            category = category.getParentCategory();
        }
        return coverImageURL;
    }

	/**
	 * @return the isBusinessMapped
	 */
	public Integer getIsBusinessMapped() {
		return isBusinessMapped;
	}

	/**
	 * @param isBusinessMapped the isBusinessMapped to set
	 */
	public void setIsBusinessMapped(Integer isBusinessMapped) {
		this.isBusinessMapped = isBusinessMapped;
	}

    public SchemaType getSchemaType() {
        return schemaType;
    }

    public void setSchemaType(SchemaType schemaType) {
        this.schemaType = schemaType;
    }
    
}
