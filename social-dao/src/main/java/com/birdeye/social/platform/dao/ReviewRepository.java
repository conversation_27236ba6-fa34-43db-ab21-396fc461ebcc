/**
 * 
 */
package com.birdeye.social.platform.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.social.entities.Mention;
import com.birdeye.social.platform.entities.Review;

public interface ReviewRepository extends JpaRepository<Review, Integer> {
	
	@Query("Select new com.birdeye.social.entities.Mention(r.id,r.comments,r.title,r.reviewUrl,r.reviewDate,r.reviewer.nickName, r.sourceId,r.businessId) FROM Review r  WHERE  r.businessId = :businessId AND r.sourceType = :sourceType")
	public List<Mention> findByBusinessIdAndSourceType(@Param("businessId") Integer businessId,
			@Param("sourceType") String sourceType);

	}
