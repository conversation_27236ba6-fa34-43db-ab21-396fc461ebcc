package com.birdeye.social.platform.dao;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.birdeye.social.dto.BusinessGMBLocationAggregation;
import com.birdeye.social.dto.GMBLocationEntity;
import com.birdeye.social.platform.entities.GMBLocation;

@Repository
public interface GMBLocationRepository extends JpaRepository<GMBLocation, Integer> {

	public List<GMBLocation> findByLocationId(String locationId);

	public Integer countByBusinessAggregationId(Integer businessAggregationId);
	
	@Query(value = "SELECT new com.birdeye.social.dto.GMBLocationEntity(gmb.locationId, gmb.locationUri,gmb.businessAggregationId,agg.businessId) FROM GMBLocation gmb inner join gmb.businessAggregation agg WHERE gmb.businessAggregationId=agg.id")
	public List<GMBLocationEntity> getAllGMBLocations();
	
	@Query(value = "SELECT new com.birdeye.social.dto.GMBLocationEntity(gmb.locationId, gmb.locationUri,gmb.businessAggregationId,agg.businessId) FROM GMBLocation gmb inner join gmb.businessAggregation agg WHERE gmb.businessAggregationId=agg.id and agg.id in :aggregationIds")
	public List<GMBLocationEntity> getAllGMBLocationsByAggregationIds(List<Integer> aggregationIds);
	
	@Query(value = "SELECT new com.birdeye.social.dto.BusinessGMBLocationAggregation(gmb.locationId, gmb.locationUri,gmb.refreshTokenId) FROM GMBLocation gmb WHERE gmb.businessAggregationId= :businessAggregationId")
	public List<BusinessGMBLocationAggregation> findByAggregationIds(@Param("businessAggregationId" ) Integer aggregationId);
}
