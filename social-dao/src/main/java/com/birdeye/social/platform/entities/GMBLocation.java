package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "gmb_location")
public class GMBLocation implements Serializable {

	private static final long serialVersionUID = 2194167597980546810L;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "location_id", nullable = false)
	private String locationId;

	@Column(name = "location_uri", nullable = false)
	private String locationUri;

	@Column(name = "business_aggregation_id")
	private Integer businessAggregationId;
	
	 @JoinColumn(name = "business_aggregation_id", referencedColumnName = "id", insertable=false, updatable=false)
	 @ManyToOne(fetch = FetchType.LAZY)
	 private BusinessAggregation businessAggregation;

	@Column(name = "custom_match", nullable = false)
	private Boolean customMacth = false;

	@Column(name = "created_date")
	@Temporal(TemporalType.TIMESTAMP)
	private Date createdDate;

	@Column(name = "updated_date")
	@Temporal(TemporalType.TIMESTAMP)
	private Date updatedDate;

	@Column(name = "refresh_token_id")
	private Integer refreshTokenId;

	@Column(name = "is_valid")
	private Integer isValid;

	public GMBLocation() {
		super();
	}

	public GMBLocation(String locationId, String locationUri, Integer refreshTokenId) {
		this.locationId = locationId;
		this.locationUri = locationUri;
		this.refreshTokenId = refreshTokenId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getLocationUri() {
		return locationUri;
	}

	public void setLocationUri(String locationUri) {
		this.locationUri = locationUri;
	}

	public Integer getBusinessAggregationId() {
		return businessAggregationId;
	}

	public void setBusinessAggregationId(Integer businessAggregationId) {
		this.businessAggregationId = businessAggregationId;
	}

	public Boolean getCustomMacth() {
		return customMacth;
	}

	public void setCustomMacth(Boolean customMacth) {
		this.customMacth = customMacth;
	}

	public Date getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(Date createdDate) {
		this.createdDate = createdDate;
	}

	public Date getUpdatedDate() {
		return updatedDate;
	}

	public void setUpdatedDate(Date updatedDate) {
		this.updatedDate = updatedDate;
	}

	public Integer getRefreshTokenId() {
		return refreshTokenId;
	}

	public void setRefreshTokenId(Integer refreshTokenId) {
		this.refreshTokenId = refreshTokenId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	/**
	 * @return the businessAggregation
	 */
	public BusinessAggregation getBusinessAggregation() {
		return businessAggregation;
	}

	/**
	 * @param businessAggregation the businessAggregation to set
	 */
	public void setBusinessAggregation(BusinessAggregation businessAggregation) {
		this.businessAggregation = businessAggregation;
	}

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("GMBLocation [id=");
		builder.append(id);
		builder.append(", locationId=");
		builder.append(locationId);
		builder.append(", locationUri=");
		builder.append(locationUri);
		builder.append(", businessAggregationId=");
		builder.append(businessAggregationId);
		builder.append(", customMacth=");
		builder.append(customMacth);
		builder.append(", createdDate=");
		builder.append(createdDate);
		builder.append(", updatedDate=");
		builder.append(updatedDate);
		builder.append(", refreshTokenId=");
		builder.append(refreshTokenId);
		builder.append(", isValid=");
		builder.append(isValid);
		builder.append("]");
		return builder.toString();
	}

}
