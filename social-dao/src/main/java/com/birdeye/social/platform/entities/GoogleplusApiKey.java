/**
 * 
 */
package com.birdeye.social.platform.entities;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "googleplus_api_key")
public class GoogleplusApiKey implements Serializable {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 7359258339777032550L;

	@Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Column(name = "api_key")
    private String apiKey;
   
    @Column(name = "project_name")
    private String projectName;
    
    @Column(name = "key_type")
    private String keyType;
    
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    @Override
    public String toString() {
        return "GoogleplusAPIKey{" + "id=" + id + ", apiKey=" + apiKey + ", projectName=" + projectName + '}';
    }

    /**
     * @return the keyType
     */
    public String getKeyType() {
        return keyType;
    }

    /**
     * @param keyType the keyType to set
     */
    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

}
