package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.birdeye.social.scheduler.dto.ExternalIntegration;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "google_refresh_token")
public class GoogleRefreshTokenSocial implements Serializable, ExternalIntegration {

	private static final long serialVersionUID = 1L;
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "refresh_token")
	private String refreshToken;

	@Column(name = "type")
	private String type;

	@Column(name = "channel")
	private String channel;

	@Column(name = "domain_id")
	private Integer domainId;

	@Column(name = "business_id")
	private Integer businessId;

	@Column(name = "client_cred_id")
	private Integer clientCredentialsId;

	@Column(name = "is_valid")
	private Integer isValid = 1;

	@Column(name = "google_user_id")
	private String googleUserId;

	@Column(name = "last_scanned_on")
	@Temporal(TemporalType.TIMESTAMP)
	private Date lastScannedOn;
	
	@Column(name = "google_user_name")
	private String googleUserName;
	
	@Column(name = "user_profile_url")
	private String userProfileUrl;
	
	@Column(name = "user_image_url")
	private String userImageUrl;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getRefreshToken() {
		return refreshToken;
	}

	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getBusinessId() {
		return businessId;
	}

	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	public Integer getDomainId() {
		return domainId;
	}

	public void setDomainId(Integer domainId) {
		this.domainId = domainId;
	}

	public Integer getClientCredentialsId() {
		return clientCredentialsId;
	}

	public void setClientCredentialsId(Integer clientCredentialsId) {
		this.clientCredentialsId = clientCredentialsId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public String getGoogleUserId() {
		return googleUserId;
	}

	public void setGoogleUserId(String googleUserId) {
		this.googleUserId = googleUserId;
	}

	public Date getLastScannedOn() {
		return lastScannedOn;
	}

	public void setLastScannedOn(Date lastScannedOn) {
		this.lastScannedOn = lastScannedOn;
	}

	/**
	 * @return the googleUserName
	 */
	public String getGoogleUserName() {
		return googleUserName;
	}

	/**
	 * @param googleUserName the googleUserName to set
	 */
	public void setGoogleUserName(String googleUserName) {
		this.googleUserName = googleUserName;
	}

	/**
	 * @return the userProfileUrl
	 */
	public String getUserProfileUrl() {
		return userProfileUrl;
	}

	/**
	 * @param userProfileUrl the userProfileUrl to set
	 */
	public void setUserProfileUrl(String userProfileUrl) {
		this.userProfileUrl = userProfileUrl;
	}

	/**
	 * @return the userImageUrl
	 */
	public String getUserImageUrl() {
		return userImageUrl;
	}

	/**
	 * @param userImageUrl the userImageUrl to set
	 */
	public void setUserImageUrl(String userImageUrl) {
		this.userImageUrl = userImageUrl;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	@Override
	public String toString() {
		return "GoogleRefreshTokenSocial{" +
				"id=" + id +
				", refreshToken='" + refreshToken + '\'' +
				", type='" + type + '\'' +
				", channel='" + channel + '\'' +
				", domainId=" + domainId +
				", businessId=" + businessId +
				", clientCredentialsId=" + clientCredentialsId +
				", isValid=" + isValid +
				", googleUserId='" + googleUserId + '\'' +
				", lastScannedOn=" + lastScannedOn +
				", googleUserName='" + googleUserName + '\'' +
				", userProfileUrl='" + userProfileUrl + '\'' +
				", userImageUrl='" + userImageUrl + '\'' +
				'}';
	}
}
