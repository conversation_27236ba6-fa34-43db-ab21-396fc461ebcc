package com.birdeye.social.platform.dao;

import java.util.Collection;
import java.util.List;

import com.birdeye.social.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.birdeye.social.platform.entities.Business;

public interface BusinessRepository extends JpaRepository<Business, Integer> {
	
	
	@Query("SELECT b from Business b where b.id =:businessId")
	public Business findById(@Param("businessId") Integer businessId);
	
	@Query("SELECT b from Business b where b.businessId =:businessId")
	public Business findByBusinessId(@Param("businessId") Long businessId);


	public Business findFirstById(Integer id);

    @Query("SELECT b.id from Business b where b.enterpriseId = :enterpriseId and b.closed = 0")
    public List<Integer> findEnterpriseLocations(@Param("enterpriseId") Integer enterpriseId);


	@Query("SELECT b.id, b.name , b.alias1 from Business b where b.id in :businessIds")
    public List<Object> findLocationInfo(@Param("businessIds") List<Integer> businessIds);
    
    @Query("SELECT b.id from Business b where b.enterpriseId = :enterpriseId and b.closed = 0 and b.id in :locations")
    public List<Integer> findFilteredEnterpriseLocations(@Param("enterpriseId") Integer enterpriseId, @Param("locations") List<Integer> businessIds);
    
    public List<Business> findByIdIn(List<Integer> ids);

	@Query(value = "SELECT new com.birdeye.social.dto.BusinessLocationEntity(b.id, b.name,b.alias1,b.phone,b.fax,b.emailId,b.bazaarifyEmailId,b.websiteUrl,"
			+ "b.enterpriseId,b.resellerId,b.businessId,b.accountType,b.type,b.businessParentId,b.mergeReview,b.resellerAsMailSender,b.teamImageUrl,b.logoUrl,"
			+ "l.address1,l.address2,l.city,l.state,l.zip,l.countryCode,"
			+ "l.countryName,l.latitude,l.longitude) FROM Business b left join b.location l WHERE b.id in :businessIds order by b.name")
	List<BusinessLocationEntity> getAllBusinessEnterpriseByBid(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "SELECT new com.birdeye.social.dto.BusinessLocationLiteEntity(b.id, b.name, b.alias1, l.address1, l.address2, l.city, l.state, l.zip, l.countryCode, l.countryName) "
			+ "FROM Business b left join b.location l WHERE b.id in :businessIds")
	List<BusinessLocationLiteEntity> getBusinessLocationsByBusinessIdIn(@Param("businessIds") Collection<Integer> businessIds);

	@Query(value = "SELECT new com.birdeye.social.dto.BusinesIdNumberDTO(b.id,b.businessId) FROM Business b " +
			"where b.businessId in :businessIds and (b.type = 'Business' OR b.type = 'Product') and b.enterpriseId is NULL")
	public List<BusinesIdNumberDTO> getAllSMBByBusinessId(@Param("businessIds") List<Long> businessIds);


	@Query(value = "SELECT new com.birdeye.social.dto.BusinessEntity(b.id, b.name,b.alias1,b.phone,b.fax,b.emailId,b.bazaarifyEmailId,b.websiteUrl,"
			+ "b.enterpriseId,b.resellerId,b.businessId,b.accountType,b.type,b.businessParentId,b.locationId) FROM Business b WHERE b.id in :businessIds")
	List<BusinessEntity> getAllBusinessByBid(@Param("businessIds") List<Integer> businessIds);

    @Query("SELECT new com.birdeye.social.dto.BusinessInfoForGnipRules (b.id, b.businessId, b.name, b.status, b.activationStatus) from Business b where b.id in :businessIds")
	public List<BusinessInfoForGnipRules> findForRulesByBusinessIdIn(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "SELECT new com.birdeye.social.dto.BusinessLocationLiteEntity(b.id, b.name, b.alias1, l.address1, l.address2, l.city, l.state, l.zip, l.countryCode, l.countryName)"
			+ " FROM Business b left join b.location l WHERE b.id in (:businessIds) and (b.name like %:search% or b.alias1 like %:search% ) ")
	List<BusinessLocationLiteEntity> getBusinessLocationsByBusinessIdInPaginatedSearch(@Param("businessIds") Collection<Integer> businessIds, @Param("search") String search);


	@Query("SELECT b.socialEnabled from Business b where b.businessId = :businessId")
	Integer socialEnabledByBusinessId(@Param("businessId") Long businessId);


	@Query(value = "select new com.birdeye.social.dto.BusinessLiteInterface(b.id, b.businessId, b.name, b.alias1, b.type, b.enterpriseId, b.socialEnabled) " +
			"from Business b where b.enterpriseId is null and (b.type = 'Enterprise-Location') and (b.activationStatus = 'paid' or b.activationStatus = 'demo') and b.name is not null and b.closed = 0 order by b.id desc ")
	Page<BusinessLiteInterface> findBySocialEnabledIn(Pageable pageRequest);

	@Query("SELECT new com.birdeye.social.dto.BusinessDataDTO(b.id,b.businessId,b.enterpriseId,b.activationStatus,b.type) from Business b where b.id in :businessIds and (b.activationStatus='suspended' or b.closed=1)")
	public List<BusinessDataDTO> findInactiveBusinessIds(@Param("businessIds") List<Integer> businessIds);

	@Query(value = "SELECT new com.birdeye.social.dto.BusinesIdNumberDTO(b.id,b.businessId) FROM Business b " +
			"where b.businessId in :businessIds and (b.type = 'Business' OR b.type = 'Product') and b.enterpriseId is NULL and b.status='inactive' and b.activationStatus='suspended'")
	public List<BusinesIdNumberDTO> getAllInactiveSMBByBusinessId(@Param("businessIds") List<Long> businessIds);

	@Query(value = "select b.enterpriseId from Business b where b.id in :locationIds and b.enterpriseId is not null and type = 'Business'")
    List<Integer> getEnterprisesForLocations(@Param("locationIds") List<Integer> locationIds);

	@Query(value = "select b.id from Business b where b.id in :locationIds) and b.enterpriseId is null and type = 'Business'")
	List<Integer> getSmbsForLocations(@Param("locationIds") List<Integer> locationIds);

	@Query("select b.id from Business b where b.closed = 0 and  b.enterpriseId = :businessId and b.type in ('Business', 'Product')")
	public List<Integer> getAllBusinessIdsUnderEnterprise(@Param("businessId") Integer businessId);

}
