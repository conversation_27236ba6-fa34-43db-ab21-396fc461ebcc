package com.birdeye.social.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import java.util.Date;

@Getter
@Setter
@ToString
public class InstagramListenInfo {

    private Integer id;

    private String instagramAccountId;

    private String instagramAccountName;

    private Long enterpriseId;

    private String type;

    private String mediaId;

    private String postUrl;

    private String caption;

    private String postUsername;

    private Date postDate;

    private String mediaUrl;

    private Integer postLikes;

    private Integer postComments;

    private String commentId;

    private Date commentDate;

    private String commentUsername;

    private String commentText;

    private Integer commentLikes;
}
