/**
 *
 *
 */
package com.birdeye.social.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class BusinessGMBNotificationDTO implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -9219714689523644138L;

	private Long	enterpriseId;

	private String	accountId;

	private String	locationId;

	private Integer	refreshTokenId;

	public BusinessGMBNotificationDTO(Long enterpriseId, String accountId, String locationId, Integer refreshTokenId) {
		this.enterpriseId = enterpriseId;
		this.accountId = accountId;
		this.locationId = locationId;
		this.refreshTokenId = refreshTokenId;
	}

	public Long getEnterpriseId() {
		return enterpriseId;
	}

	public void setEnterpriseId(Long enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public Integer getRefreshTokenId() {
		return refreshTokenId;
	}

	public void setRefreshTokenId(Integer refreshTokenId) {
		this.refreshTokenId = refreshTokenId;
	}
}
