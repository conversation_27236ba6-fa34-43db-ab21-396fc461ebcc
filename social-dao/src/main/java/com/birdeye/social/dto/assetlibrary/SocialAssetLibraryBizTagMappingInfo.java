package com.birdeye.social.dto.assetlibrary;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> on 11/10/23
 */
@Getter
@Setter
public class SocialAssetLibraryBizTagMappingInfo {

    private Long assetId;
    private Long tagId;
    private String tagName;
    private Long tagUpdatedBy;
    private Long tagMappingUpdatedBy;

    public SocialAssetLibraryBizTagMappingInfo(Long assetId, Long tagId, String tagName) {
        this.assetId = assetId;
        this.tagId = tagId;
        this.tagName = tagName;
    }

    public SocialAssetLibraryBizTagMappingInfo(Long assetId, Long tagId, String tagName, Long tagUpdatedBy, Long tagMappingUpdatedBy) {
        this.assetId = assetId;
        this.tagId = tagId;
        this.tagName = tagName;
        this.tagUpdatedBy = tagUpdatedBy;
        this.tagMappingUpdatedBy = tagMappingUpdatedBy;
    }
}
