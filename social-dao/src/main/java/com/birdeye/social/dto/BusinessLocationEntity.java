package com.birdeye.social.dto;

public class BusinessLocationEntity 
{

	private Integer id;
	private Long businessId;
	private String name;
	private String alias1;
	private String phone;
	private String fax;
	private String emailId;
	private String bazaarifyEmailId;
	private String websiteUrl;
	private Integer enterpriseId;
	private Integer resellerId;
	private String accountType = "Direct";
	private String type = "Business";
	private Integer businessParentId;
	private Integer mergeReview = 0;
	private Integer resellerAsMailSender = 1;
	private String teamImageUrl;
	private String logoUrl;
	private String address1;
	private String address2;
	private String city;
	private String state;
	private String zip;
	private String countryCode;
	private String countryName;
	private Long latitude;
	private Long longitude;

	public BusinessLocationEntity(Integer id, String name, String alias1, String phone, String fax, String emailId,
			String bazaarifyEmailId, String websiteUrl, Integer enterpriseId, Integer resellerId, Long businessId,
			String accountType, String type, Integer businessParentId, Integer mergeReview,
			Integer resellerAsMailSender, String teamImageUrl, String logoUrl, String address1, String address2,
			String city, String state, String zip, String countryCode, String countryName, Long latitude,
			Long longitude) {
		super();
		this.id = id;
		this.name = name;
		this.alias1 = alias1;
		this.phone = phone;
		this.fax = fax;
		this.emailId = emailId;
		this.bazaarifyEmailId = bazaarifyEmailId;
		this.websiteUrl = websiteUrl;
		this.enterpriseId = enterpriseId;
		this.resellerId = resellerId;
		this.businessId = businessId;
		this.logoUrl=logoUrl;
		this.accountType = accountType;
		this.type = type;
		this.businessParentId = businessParentId;
		this.mergeReview = mergeReview;
		this.resellerAsMailSender = resellerAsMailSender;
		this.address1 = address1;
		this.address2 = address2;
		this.city = city;
		this.state = state;
		this.zip = zip;
		this.countryCode = countryCode;
		this.countryName = countryName;
		this.latitude = latitude;
		this.longitude = longitude;
	}

	public BusinessLocationEntity(Integer id, String name, String alias1, String phone, String fax, String emailId,
			String bazaarifyEmailId, String websiteUrl, Integer enterpriseId, Integer resellerId, Long businessId,
			String accountType, String type, Integer businessParentId, Integer mergeReview,
			Integer resellerAsMailSender, String teamImageUrl, String logoUrl) {
		super();
		this.id = id;
		this.name = name;
		this.alias1 = alias1;
		this.phone = phone;
		this.fax = fax;
		this.emailId = emailId;
		this.bazaarifyEmailId = bazaarifyEmailId;
		this.websiteUrl = websiteUrl;
		this.enterpriseId = enterpriseId;
		this.resellerId = resellerId;
		this.businessId = businessId;
		this.accountType = accountType;
		this.type = type;
		this.businessParentId = businessParentId;
		this.mergeReview = mergeReview;
		this.resellerAsMailSender = resellerAsMailSender;
	}

	/**
	 * @return the teamImageUrl
	 */
	public String getTeamImageUrl() {
		return teamImageUrl;
	}

	/**
	 * @param teamImageUrl
	 *            the teamImageUrl to set
	 */
	public void setTeamImageUrl(String teamImageUrl) {
		this.teamImageUrl = teamImageUrl;
	}

	/**
	 * @return the logoUrl
	 */
	public String getLogoUrl() {
		return logoUrl;
	}

	/**
	 * @param logoUrl
	 *            the logoUrl to set
	 */
	public void setLogoUrl(String logoUrl) {
		this.logoUrl = logoUrl;
	}

	/**
	 * @return the resellerAsMailSender
	 */
	public Integer getResellerAsMailSender() {
		return resellerAsMailSender;
	}

	/**
	 * @param resellerAsMailSender
	 *            the resellerAsMailSender to set
	 */
	public void setResellerAsMailSender(Integer resellerAsMailSender) {
		this.resellerAsMailSender = resellerAsMailSender;
	}

	/**
	 * @return the address1
	 */
	public String getAddress1() {
		return address1;
	}

	/**
	 * @param address1
	 *            the address1 to set
	 */
	public void setAddress1(String address1) {
		this.address1 = address1;
	}

	/**
	 * @return the address2
	 */
	public String getAddress2() {
		return address2;
	}

	/**
	 * @param address2
	 *            the address2 to set
	 */
	public void setAddress2(String address2) {
		this.address2 = address2;
	}

	/**
	 * @return the city
	 */
	public String getCity() {
		return city;
	}

	/**
	 * @param city
	 *            the city to set
	 */
	public void setCity(String city) {
		this.city = city;
	}

	/**
	 * @return the state
	 */
	public String getState() {
		return state;
	}

	/**
	 * @param state
	 *            the state to set
	 */
	public void setState(String state) {
		this.state = state;
	}

	/**
	 * @return the zip
	 */
	public String getZip() {
		return zip;
	}

	/**
	 * @param zip
	 *            the zip to set
	 */
	public void setZip(String zip) {
		this.zip = zip;
	}

	/**
	 * @return the countryCode
	 */
	public String getCountryCode() {
		return countryCode;
	}

	/**
	 * @param countryCode
	 *            the countryCode to set
	 */
	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	/**
	 * @return the countryName
	 */
	public String getCountryName() {
		return countryName;
	}

	/**
	 * @param countryName
	 *            the countryName to set
	 */
	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	/**
	 * @return the latitude
	 */
	public Long getLatitude() {
		return latitude;
	}

	/**
	 * @param latitude
	 *            the latitude to set
	 */
	public void setLatitude(Long latitude) {
		this.latitude = latitude;
	}

	/**
	 * @return the longitude
	 */
	public Long getLongitude() {
		return longitude;
	}

	/**
	 * @param longitude
	 *            the longitude to set
	 */
	public void setLongitude(Long longitude) {
		this.longitude = longitude;
	}

	/**
	 * @return the mergeReview
	 */
	public Integer getMergeReview() {
		return mergeReview;
	}

	/**
	 * @param mergeReview
	 *            the mergeReview to set
	 */
	public void setMergeReview(Integer mergeReview) {
		this.mergeReview = mergeReview;
	}

	/**
	 * @return the businessParentId
	 */
	public Integer getBusinessParentId() {
		return businessParentId;
	}

	/**
	 * @param businessParentId
	 *            the businessParentId to set
	 */
	public void setBusinessParentId(Integer businessParentId) {
		this.businessParentId = businessParentId;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @return the accountType
	 */
	public String getAccountType() {
		return accountType;
	}

	/**
	 * @param accountType
	 *            the accountType to set
	 */
	public void setAccountType(String accountType) {
		this.accountType = accountType;
	}

	/**
	 * @return the businessId
	 */
	public Long getBusinessId() {
		return businessId;
	}

	/**
	 * @param businessId
	 *            the businessId to set
	 */
	public void setBusinessId(Long businessId) {
		this.businessId = businessId;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the alias1
	 */
	public String getAlias1() {
		return alias1;
	}

	/**
	 * @param alias1
	 *            the alias1 to set
	 */
	public void setAlias1(String alias1) {
		this.alias1 = alias1;
	}

	/**
	 * @return the phone
	 */
	public String getPhone() {
		return phone;
	}

	/**
	 * @param phone
	 *            the phone to set
	 */
	public void setPhone(String phone) {
		this.phone = phone;
	}

	/**
	 * @return the fax
	 */
	public String getFax() {
		return fax;
	}

	/**
	 * @param fax
	 *            the fax to set
	 */
	public void setFax(String fax) {
		this.fax = fax;
	}

	/**
	 * @return the emailId
	 */
	public String getEmailId() {
		return emailId;
	}

	/**
	 * @param emailId
	 *            the emailId to set
	 */
	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	/**
	 * @return the bazaarifyEmailId
	 */
	public String getBazaarifyEmailId() {
		return bazaarifyEmailId;
	}

	/**
	 * @param bazaarifyEmailId
	 *            the bazaarifyEmailId to set
	 */
	public void setBazaarifyEmailId(String bazaarifyEmailId) {
		this.bazaarifyEmailId = bazaarifyEmailId;
	}

	/**
	 * @return the websiteUrl
	 */
	public String getWebsiteUrl() {
		return websiteUrl;
	}

	/**
	 * @param websiteUrl
	 *            the websiteUrl to set
	 */
	public void setWebsiteUrl(String websiteUrl) {
		this.websiteUrl = websiteUrl;
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the enterpriseId
	 */
	public Integer getEnterpriseId() {
		return enterpriseId;
	}

	/**
	 * @param enterpriseId
	 *            the enterpriseId to set
	 */
	public void setEnterpriseId(Integer enterpriseId) {
		this.enterpriseId = enterpriseId;
	}

	/**
	 * @return the resellerId
	 */
	public Integer getResellerId() {
		return resellerId;
	}

	/**
	 * @param resellerId
	 *            the resellerId to set
	 */
	public void setResellerId(Integer resellerId) {
		this.resellerId = resellerId;
	}

}
