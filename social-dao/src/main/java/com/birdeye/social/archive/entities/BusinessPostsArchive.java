package com.birdeye.social.archive.entities;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@Builder
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "business_posts")
public class BusinessPostsArchive {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "business_id")
    private Integer businessId;

    @Column(name = "enterprise_id")
    private Long enterpriseId;

    @Column(name = "be_post_id")
    private Integer bePostId;

    @Column(name = "post_id")
    private String postId;

    @Column(name = "source_id")
    private Integer sourceId;

    @Column(name = "external_page_id")
    private String externalPageId;

    @Column(name = "page_name")
    private String pageName;

    @Column(name = "post_url")
    private String postUrl;

    @Column(name = "post_text")
    private String postText;

    @Column(name = "image_urls")
    private String imageUrls;

    @Column(name = "video_urls")
    private String videoUrls;

    @Column(name = "publish_date")
    private Date publishDate;

    @Column(name = "created")
    private Date created;

    @Column(name = "updated")
    private Date updated;

    @Column(name = "last_scan_date")
    private Date lastScanDate;

    @Column(name = "next_scan_date")
    private Date nextScanDate;

    @Column(name = "error")
    private String error;

    @Column(name = "response")
    private String response;

    @Column(name = "meta_data")
    private String metadata;

    @Column(name = "last_modified_date")
    private Date lastModifiedDate;

    @Column(name = "is_deleted")
    private Integer isDeleted = 0;

    @Column(name = "is_story")
    private Integer isStory = 0;
}
