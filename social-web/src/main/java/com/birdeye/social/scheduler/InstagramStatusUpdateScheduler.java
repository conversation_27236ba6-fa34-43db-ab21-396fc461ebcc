package com.birdeye.social.scheduler;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.external.service.KafkaProducerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class InstagramStatusUpdateScheduler {

    private static final Integer JOB_RESCDEULE_CYCLE_PERIOD_DEFAULT = -15;
    private static final Integer INSTAGRAM_PAGE_SIZE_DEFAULT = 200;

    @Autowired
    private BusinessInstagramAccountRepository businessInstagramAccountRepository;

    @Autowired
    private KafkaProducerService producer;


    public void updateIntegrationStatus() {
        Page<BusinessInstagramAccount> businessInstagramAccountPage =
                businessInstagramAccountRepository.findByIsValidAndBusinessIdNotNullAndLastScannedOnLessThan(1,
                        DateUtils.addDays(new Date(), getInstagramReconnnectRecyclePeriod()),
                        new PageRequest(0, getPageSize(), Sort.Direction.ASC, "lastScannedOn"));

        if(Objects.isNull(businessInstagramAccountPage)
                || CollectionUtils.isEmpty(businessInstagramAccountPage.getContent())) {
            return;
        }
        List<BusinessInstagramAccount> businessInstagramAccountList = businessInstagramAccountPage.getContent();
        businessInstagramAccountList.forEach(this::pushPayloadToKafka);
    }

    private void pushPayloadToKafka(BusinessInstagramAccount businessInstagramAccount) {
        SocialTokenValidationDTO data = new SocialTokenValidationDTO();
        data.setId(businessInstagramAccount.getId());
        data.setChannel(SocialChannel.INSTAGRAM.getName());
        producer.sendObjectV1("social-token-validate", data);
    }

    private int getInstagramReconnnectRecyclePeriod() {
        return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty("instagram.reconnect.job.recycle.period", JOB_RESCDEULE_CYCLE_PERIOD_DEFAULT);
    }

    private Integer getPageSize() {
        return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getIntegerProperty("instagram.scheduler.pagesize", INSTAGRAM_PAGE_SIZE_DEFAULT);
    }
}
