package com.birdeye.social.scheduler;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.BusinessYoutubeChannelRepository;
import com.birdeye.social.dao.GoogleRefreshTokenRepo;
import com.birdeye.social.dto.GMBPageDTO;
import com.birdeye.social.dto.SocialTokenValidationDTO;
import com.birdeye.social.entities.BusinessGoogleMyBusinessLocation;
import com.birdeye.social.entities.BusinessYoutubeChannel;
import com.birdeye.social.entities.GoogleRefreshToken;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.scheduler.dto.ExternalIntegration;
import com.birdeye.social.service.*;
import com.birdeye.social.utils.StringUtils;
import net.javacrumbs.shedlock.core.SchedulerLock;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * This scheduler runs jobs on google_refresh_token table.
 *
 * <AUTHOR>
 *
 */
@Component
@PropertySource("classpath:cron-scheduler.properties")
public class GooglePlusStatusUpdateScheduler extends SocialIntegrationCheckScheduler {

	@Autowired
	private GoogleRefreshTokenRepo googleRefreshTokenRepo;

	@Autowired
	private SocialPostGooglePlusService socialPostGooglePlusService;

	@Autowired
	private GoogleMyBusinessPageService googleMyBusinessPageService;

	@Autowired
	private GMBLocationDetailService gmbLocationDetailService;

	@Autowired
	private BusinessGMBLocationRawRepository businessGMBLocationRawRepository;

	@Autowired
	private BusinessYoutubeChannelRepository youtubeChannelRepository;

	@Autowired
	private KafkaProducerService producer;

 	@Value("${googleplus.reconnect.job.cron.pattern:0 0,30 * * * *}")
 	private static final String GOOGLEPLUS_CRON_PATTERN = "0 0,30 * * * *";

	private static final Integer JOB_RESCDEULE_CYCLE_PERIOD_DEFAULT = -15;
	private static final Integer GOOGLEPLUS_PAGE_SIZE_DEFAULT = 200;
	private static final long GOOGLEPLUS_LOCK_TIME_IN_MS = 1200000; // 20min

	private static final Logger LOGGER = LoggerFactory.getLogger(GooglePlusStatusUpdateScheduler.class);

	@Override
	@Scheduled(cron = GOOGLEPLUS_CRON_PATTERN)
	@SchedulerLock(name = "googlePlusIntegrationCheckShedLock", lockAtMostFor = GOOGLEPLUS_LOCK_TIME_IN_MS, lockAtLeastFor = GOOGLEPLUS_LOCK_TIME_IN_MS)
	public void updateIntegrationStatus() {
		super.updateIntegrationStatus();
	}

	public void updateIntegrationStatusThroughApi() {
		super.updateIntegrationStatusV2();
	}

	@Override
	public boolean isEnabled() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getBooleanProperty("googleplus.scheduler.enabled", true);
	}

	@Override
	public String getJobText() {
		return "googlePlusPagesStatusUpdateScheduler";
	}

	private Integer getPageSize() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getIntegerProperty("googleplus.scheduler.pagesize", GOOGLEPLUS_PAGE_SIZE_DEFAULT);
	}

	private Integer getGooglePlusReconnnectRecyclePeriod() {
		return CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getIntegerProperty("googleplus.reconnect.job.recycle.period", JOB_RESCDEULE_CYCLE_PERIOD_DEFAULT);
	}

	@Override
	public List<GoogleRefreshToken> getIntegrations() {
		List<GoogleRefreshToken> refreshTokens = googleRefreshTokenRepo.findExecutableRefreshTokens(
				DateUtils.addDays(new Date(), getGooglePlusReconnnectRecyclePeriod()),
				new PageRequest(0, getPageSize()));
		LOGGER.info("[GooglePlus Job] Validate google refresh tokens: {}",
				CollectionUtils.isEmpty(refreshTokens) ? 0 : refreshTokens.size());
		return refreshTokens;
	}

	@Override
	public List<GoogleRefreshToken> getIntegrationsV2() {
		List<GoogleRefreshToken> refreshTokens = googleRefreshTokenRepo.findExecutableRefreshTokensLastScanDateLessThan(
				DateUtils.addDays(new Date(), getGooglePlusReconnnectRecyclePeriod()),
				new PageRequest(0, getPageSize(), Sort.Direction.ASC, "lastScannedOn"));
		LOGGER.info("[GooglePlus Job] Validate google refresh tokens: {}",
				CollectionUtils.isEmpty(refreshTokens) ? 0 : refreshTokens.size());
		return refreshTokens;
	}

	@Override
	public void updateIntegrationStatusForAPage(ExternalIntegration integration) {
		if (integration instanceof GoogleRefreshToken) {
			GoogleRefreshToken refreshToken = (GoogleRefreshToken) integration;
			if (refreshToken == null || StringUtils.isEmpty(refreshToken.getRefreshToken())) {
				return;
			}
			LOGGER.info("[GooglePlus Job] Initiating GooglePlus valdation for: {}", refreshToken);
			refreshToken.setLastScannedOn(new Date());
			GMBPageDTO valid = socialPostGooglePlusService.validateRefreshToken(refreshToken);
			if (!valid.isValid()) {
				refreshToken.setIsValid(0);
				LOGGER.info("[GooglePlus Job] GooglePlus Refresh token is invalid: {}", refreshToken);
			}
			List<BusinessGoogleMyBusinessLocation> existingPages = googleMyBusinessPageService.getGMBPagesByRefreshTokenId(refreshToken.getId());
			for(BusinessGoogleMyBusinessLocation page: existingPages) {
				if (!valid.isValid()) {
					page.setIsValid(0);
				}
			}
			businessGMBLocationRawRepository.save(existingPages);
			googleRefreshTokenRepo.saveAndFlush(refreshToken);
		}
	}

	@Override
	public void updateIntegrationStatusForAPageV2(ExternalIntegration integration) {
		if (integration instanceof GoogleRefreshToken) {
			LOGGER.info("Google refresh token request received.");
			GoogleRefreshToken refreshToken = (GoogleRefreshToken) integration;
			SocialTokenValidationDTO data = new SocialTokenValidationDTO();
			data.setChannel(refreshToken.getChannel());
			data.setId(refreshToken.getId());

			producer.sendObjectV1("social-token-validate", data);
		}
	}
}
