package com.birdeye.social.config;

import com.birdeye.social.validation.PostInputValidator;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.Validator;

@Configuration
public class CustomValidatorConfig {
    @Qualifier("postInputValidator")
    @Bean
    public Validator validator() {
        return new PostInputValidator();
    }
}
