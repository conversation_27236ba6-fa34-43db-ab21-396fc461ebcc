package com.birdeye.social.config;

import com.birdeye.social.constant.LoggingConstants;
import net.logstash.logback.marker.LogstashMarker;
import net.logstash.logback.marker.Markers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.util.Objects;

public class CustomHeaderInterceptor implements ClientHttpRequestInterceptor {
    private final String headerName;
    private final String headerValue;
    private static final Logger logger = LoggerFactory.getLogger("jsonLogger");

    public CustomHeaderInterceptor(String headerName, String headerValue) {
        this.headerName = headerName;
        this.headerValue = headerValue;
    }

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        HttpHeaders headers = request.getHeaders();
        headers.add(headerName, headerValue);
        LogstashMarker marker = Markers.append(LoggingConstants.URI, request.getURI())
                .and(Markers.append(LoggingConstants.HTTP_METHOD, request.getMethod()))
                .and(Markers.append(LoggingConstants.HEADERS, request.getHeaders()))
                .and(Markers.append(LoggingConstants.REQUEST_BODY, new String(body)))
                .and(Markers.append(LoggingConstants.HOST, request.getURI().getHost()));
        Long timeBeforeExecution = System.currentTimeMillis();
        ClientHttpResponse response = execution.execute(request, body);
        if(Objects.isNull(response)) {
            logger.info(marker, "client interceptor log, response is null");
            return response;
        }
        Long timeAfterExecution = System.currentTimeMillis();
        Long responseTime = timeAfterExecution - timeBeforeExecution;
        marker.and(Markers.append(LoggingConstants.STATUS, response.getStatusCode()))
                .and(Markers.append(LoggingConstants.RAW_STATUS_CODE, response.getRawStatusCode()))
                .and(Markers.append(LoggingConstants.RESPONSE_BODY, response.getBody()))
                .and(Markers.append(LoggingConstants.RESPONSE_TIME, responseTime));
        logger.info(marker, "client interceptor log");
        return response;
    }
}
