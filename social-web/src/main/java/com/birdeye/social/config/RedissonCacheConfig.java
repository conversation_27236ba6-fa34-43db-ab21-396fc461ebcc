package com.birdeye.social.config;

import io.github.bucket4j.distributed.ExpirationAfterWriteStrategy;
import io.github.bucket4j.distributed.proxy.ProxyManager;
import io.github.bucket4j.redis.jedis.cas.JedisBasedProxyManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;

import java.time.Duration;



@Configuration
public class RedissonCacheConfig {
    private final JedisPool pool;

    public RedissonCacheConfig(JedisPool pool) {
        this.pool = pool;
    }

    @Bean
    public ProxyManager proxyManager() {
        final ProxyManager<byte[]> proxyManager  = JedisBasedProxyManager.builderFor(pool)
                .withExpirationStrategy(ExpirationAfterWriteStrategy.basedOnTimeForRefillingBucketUpToMax(Duration.ofMinutes(1)))
                .build();
        return proxyManager;
    }

}

