package com.birdeye.social.config;

import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR> on 17/10/23
 */
@Configuration
public class AWSCredentialsProviderConfig {

    @Value("${aws.accessKeyId}")
    private String awsAccessKeyId;

    @Value("${aws.secretKey}")
    private String awsSecretKey;

    @Bean
    @Profile({"demo", "qa", "production"})
    public AWSCredentialsProvider credentialsProvider() {
        return new DefaultAWSCredentialsProviderChain();
    }

    @Bean
    @Profile({"local"})
    public AWSCredentialsProvider localCredentialsProvider() {
        System.setProperty("aws.accessKeyId", awsAccessKeyId);
        System.setProperty("aws.secretKey", awsSecretKey);
        // This change has been done as the local ES query execution was failing with
        // the commented code
        return new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsAccessKeyId, awsSecretKey));
        //return new STSAssumeRoleSessionCredentialsProvider("arn:aws:iam::322807588216:role/demo-social-es-role");
    }
}