package com.birdeye.social.controller;


import com.birdeye.social.apple.AppleSendRequest;
import com.birdeye.social.apple.AttachmentPreDownloadRequest;
import com.birdeye.social.apple.AttachmentPreUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.AutoSuggesterPagesResponse;
import com.birdeye.social.service.SocialAPILogService;
import com.birdeye.social.service.applechat.AppleChatService;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.applechat.*;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.birdeye.social.apple.AttachmentPreUploadResponse;
import com.birdeye.social.sro.AppleDisconnectRequest;
import com.birdeye.social.sro.AppleLocationPageMappingRequest;
import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.sro.LocationMappingRequest;
import com.birdeye.social.sro.LocationPageMapping;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.applechat.AppleConnectPageRequest;
import com.birdeye.social.sro.applechat.ConnectPageResponse;

@RestController
@RequestMapping("/social/apple")
public class AppleChatController {

    private static final Logger LOG = LoggerFactory.getLogger(AppleChatController.class);
	private static final String INVALID_REQUEST = "Request can not be null";

    @Autowired
    private AppleChatService appleChatService;

	@Autowired
	private SocialAPILogService socialAPILogService;

    /**
     * This api will give all pages add by enterprise
     * @param enterpriseId
     * @return
     */
    @GetMapping(value = "/pages")
    public @ResponseBody ResponseEntity<ConnectPageResponse> getPages(@RequestHeader("userId") Integer userId, @RequestParam("enterpriseId") Long enterpriseId,
																	  @RequestParam(value = "type", defaultValue = "all") String type ) {
        return new ResponseEntity<>(appleChatService.getPages(enterpriseId,type), HttpStatus.OK);
    }

    /**
     * This api will delete  entities from apple location by page Ids in case of enterprise and by businessId in case of SMB
     * @param input
     * @return
     */
    @PutMapping(value = "/remove/page")
    public @ResponseBody ResponseEntity<Void> removePage(@RequestParam("businessId") Long businessId,@RequestParam("isSMB") boolean isSMB, @RequestBody List<LocationPageMappingRequest> input) {
       LOG.info("Request received to remove page from Social, Input {}", input);

    	if(!CollectionUtils.isEmpty(input)) {
    		appleChatService.removePage(input, isSMB, businessId);
        }else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"No Data to Delete" );		
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @PutMapping(value = "/disconnect/account")
    public @ResponseBody ResponseEntity<Void> disconnectAccount(@RequestBody AppleDisconnectRequest appleDisconnectRequest) {
        appleChatService.removeAccount(appleDisconnectRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    /**
	 * API to remove page-location mappings for a channel using a list. Called from
	 * UI.
	private static final Logger LOG = LoggerFactory.getLogger(AppleChatController.class);

	@Autowired
	private AppleChatService appleChatService;


	/**
	 * TBD
	 * 
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/connect")
	public @ResponseBody ResponseEntity<ChannelPageInfo> connectPage(@RequestBody AppleConnectPageRequest request) {
		if (request != null) {
			return new ResponseEntity<>(appleChatService.connectPage(request), HttpStatus.OK);
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Input/Channel can not be null");
		}
	}



	/**
	 * API to remove page-location mappings for a channel using a list. Called from
	 * UI.
	 * @param input   - AppleLocationPageMappingRequest
	 * @return - HTTP Status
	 * @throws Exception - If input is incorrect
	 */
	@PostMapping("/remove/mapping")
	public @ResponseBody ResponseEntity<Void> removePageMapping(@RequestBody AppleLocationPageMappingRequest input) {
		if(Objects.isNull(input)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"The request can not be null");
		}
		LOG.info("POST mapping request received for Apple with mapping data {}", input);
		appleChatService.removePageMappings(input);
		return new ResponseEntity<>(HttpStatus.OK);
	}


	/**
	 * Auto suggester page search API Fetch unmapped pages of an enterprise. Called
	 * from UI
	 * @param businessId
	 * @return
	 * @throws Exception
	 */

	@GetMapping(value = "/search/pages")
	public @ResponseBody ResponseEntity<AutoSuggesterPagesResponse> findUnmappedPages(@RequestParam("businessId") Long businessId){
		return new ResponseEntity<>(appleChatService.findPagesWithMappingStatus(businessId),HttpStatus.OK);
	}

	/**
	 * API to get location-mapping pages. Called from UI.
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/locations/mapping/get")
	public @ResponseBody ResponseEntity<LocationPageMapping> getLocationMappingPagesV2(@RequestBody LocationMappingRequest request) {
		if(Objects.isNull(request)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,INVALID_REQUEST);
		}
		return new ResponseEntity<>(appleChatService.getLocationMappingPages(request), HttpStatus.OK);
	}

	/**
	 * Api for doup service to get all locations
	 * @param data
	 * @return
	 * @throws Exception
	 */
	
	@PostMapping(value = "/doup/response")
	public @ResponseBody ResponseEntity<SocialLocationResponse> getAllMappedAndUnMappedData(@RequestBody AppleChatDoupRequest data) throws Exception {
		if(Objects.isNull(data)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INVALID_REQUEST);
		}
		return new ResponseEntity<>(appleChatService.getAllMappedAndUnMappedLocations(data.getBusinessNumber()),HttpStatus.OK);
	}

	/**
	 * API to map pages via doup service
	 * @param request
	 * @return
	 */
	@PutMapping(value = "/mapping")
	public @ResponseBody ResponseEntity<AppleResponseStatusDTO> addMappingForEnterprise(@RequestBody AppleBulkImport request){
		if(Objects.isNull(request)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INVALID_REQUEST);
		}
		return new ResponseEntity<>(appleChatService.addMappingForEnterprise(request),HttpStatus.OK);
	}

	@PostMapping(value = "/audit/event")
	public @ResponseBody ResponseEntity<Void> postAuditEvent(@RequestBody AuditObject auditObject){
		if(Objects.isNull(auditObject)){
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INVALID_REQUEST);
		}
		socialAPILogService.postAuditEvent(auditObject);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping(value = "/account/status")
	public @ResponseBody ResponseEntity<AppleChatAccountStatus> accountStatus(@RequestParam Long enterpriseId) {
		if (Objects.isNull(enterpriseId)) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INVALID_REQUEST);
		}
		return new ResponseEntity<>(appleChatService.getAccountStatus(enterpriseId), HttpStatus.OK);
	}

	@GetMapping(value = "/businessId")
	public @ResponseBody ResponseEntity<Integer> getBusinessIdForPageId(
			@RequestParam(value = "destinationId", required = false) String destinationId,
			@RequestParam(value = "intent", required = false) String intent, @RequestParam("isSMB") boolean isSMB)
			throws Exception {
		return new ResponseEntity<>(appleChatService.getBusinessIdForPageId(destinationId, intent, isSMB),
				HttpStatus.OK);
	}

	@GetMapping(value = "/integration/status")
	public @ResponseBody ResponseEntity<String> getAppleIntegrationStatus(
			@RequestParam("businessId") Integer businessId) throws Exception {
		return new ResponseEntity<>(appleChatService.getAppleIntegrationStatus(businessId), HttpStatus.OK);
	}

	@GetMapping(value = "/pageId")
	public @ResponseBody ResponseEntity<String> getApplePageIdByBusinessId(
			@RequestParam("businessId") Integer businessId, @RequestParam("enterpriseNumber") Long enterpriseNumber,
			@RequestParam("isSMB") boolean isSMB) throws Exception {
		return new ResponseEntity<>(appleChatService.getApplePageIdByBusinessId(businessId, enterpriseNumber, isSMB),
				HttpStatus.OK);
	}

	@PostMapping("/send")
	public @ResponseBody ResponseEntity<Void> sendMessage(@RequestBody AppleSendRequest request) throws Exception {
		LOG.info("POST mapping request received for Apple Send {}", request);
		appleChatService.sendMessage(request);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/preDownload")
	public @ResponseBody ResponseEntity<String> attachmentPreDownload(@RequestBody AttachmentPreDownloadRequest request)
			throws Exception {
		LOG.info("POST mapping request received for attachmentPreDownload {}", request);
		return new ResponseEntity<>(appleChatService.attachmentPreDownload(request), HttpStatus.OK);
	}

	@PostMapping("/preUpload")
	public @ResponseBody ResponseEntity<AttachmentPreUploadResponse> attachmentPreUpload(
			@RequestBody AttachmentPreUploadRequest request) throws Exception {
		LOG.info("POST mapping request received for attachmentPreUpload {}", request);
		return new ResponseEntity<>(appleChatService.attachmentPreUpload(request), HttpStatus.OK);
	}

}
