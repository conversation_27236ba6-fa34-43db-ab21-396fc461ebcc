package com.birdeye.social.controller;

import com.birdeye.social.model.*;
import com.birdeye.social.service.SocialAccountService;
import com.birdeye.social.sro.ChannelPageInfo;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/freemium")
public class SocialFreemiumSetupController {
    @Autowired
    private SocialAccountService socialAccountService;

    @PostMapping(value = "/setup")
    public ResponseEntity<?> setup(@RequestBody FreemiumSetupRequest authRequest) {
        socialAccountService.initiatePageFetchFreemium(authRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping(value="/fetch-pages")
    public ResponseEntity<FreemiumFetchPageResponse> fetch(@RequestParam("freemiumSessionId") Integer freemiumSessionId){
        return new ResponseEntity<>(socialAccountService.fetchFreemiumPages(freemiumSessionId),HttpStatus.OK);
    }

    @PutMapping(value="/mapping")
    public ResponseEntity<ChannelPageInfo> connectPage(@RequestBody FreemiumConnectRequest freemiumConnectRequest){
        return new ResponseEntity<>(socialAccountService.connectFreemiumpage(freemiumConnectRequest),HttpStatus.OK);
    }

    @GetMapping(value="/status")
    public ResponseEntity<FreemiumStatusResponse> fetchStatus(@RequestParam("sessionIdentifier") Integer sessionIdentifier){
        return new ResponseEntity<>(socialAccountService.fetchStatus(sessionIdentifier),HttpStatus.OK);
    }
}
