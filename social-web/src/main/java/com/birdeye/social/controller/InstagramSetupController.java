package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.dto.SocialEnabledStatusDto;
import com.birdeye.social.entities.SocialAudit;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.instagram.InstagramConversationResponse;
import com.birdeye.social.model.DpSyncRequest;
import com.birdeye.social.model.InboxStatusResponse;
import com.birdeye.social.model.instagram.InstagramAuthRequest;
import com.birdeye.social.service.IInstragramSetupService;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/social/instagram")
public class InstagramSetupController {

    private static final String INPUT_CHANNEL_CAN_NOT_BE_NULL = "Input/Channel can not be null";
    private static final String INPUT_BUSINESS_ID_CAN_NOT_BE_NULL = "BusinessId can not be null";

    @Autowired
    private IInstragramSetupService socialInstagramService;

    /**
     * API to initiate fetch pages request. If the request for the channel on the business is already created by one user, all other users on that
     * business will see a message "please wait, we are fetching pages".
     *
     * @throws Exception
     */
    @PostMapping(value = "/pages")
    public @ResponseBody
    ResponseEntity<Void> initiatePageRequest(@RequestBody InstagramAuthRequest instagramAuthRequest) throws Exception {
        if (instagramAuthRequest.getBusinessId() != null) {
            socialInstagramService.submitFetchPageRequest(instagramAuthRequest.getBusinessId(), instagramAuthRequest.getBirdeyeUserId(),
                    instagramAuthRequest.getAuthCode(), instagramAuthRequest.getRedirectUri(),
                    instagramAuthRequest.getTempAccessToken(), Constants.ENTERPRISE);
            return new ResponseEntity<>(HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }
    /**
     * API to get all integrated pages post login (to be called by UI after status changed to fetched)
     **/
    @GetMapping(value = "/integration-pages")
    public @ResponseBody ResponseEntity<FetchPageResponse> getIntegrationPages(@RequestParam("enterpriseId") Long enterpriseId){
        if (enterpriseId != null) {
            FetchPageResponse pageInfo = socialInstagramService.getIntegrationPage(enterpriseId);
            return new ResponseEntity<>(pageInfo, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_BUSINESS_ID_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to get all integrated pages using enterpriseId
     **/
    @GetMapping(value = "/pages")
    public @ResponseBody ResponseEntity<ConnectedPages> getPages(@RequestHeader("userId") Integer userId,
                                                                 @RequestParam("enterpriseId") Long enterpriseId,
                                                                 @RequestParam(value = "type", defaultValue = "all") String type) {
        return new ResponseEntity<>(socialInstagramService.getPages(userId,enterpriseId,type), HttpStatus.OK);
    }

    /**
     * API to check instagram page validity
     **/
    @GetMapping(value = "/conversation")
    public @ResponseBody ResponseEntity<InstagramConversationResponse> getConversationInfo(@RequestHeader("fbPageId") String fbPageId,
                                                                                           @RequestParam("enterpriseId") Long enterpriseId,
                                                                                           @RequestParam(value = "type", defaultValue = "all") String type) {
        return new ResponseEntity<>(socialInstagramService.fetchInstagramConversationDataWrapper(fbPageId), HttpStatus.OK);
    }
    
    /**
     * API will be used by Inbox to check the validity of Instagram Page
     **/
    @GetMapping(value = "/status/{businessId}")
	public @ResponseBody ResponseEntity<Map<String, String>> getInstagramIntegrationStatus(@PathVariable("businessId") Integer businessId) {
    	Map<String, String> map = socialInstagramService.getInstagramIntegrationStatus(businessId);
    	return new ResponseEntity<>(map, HttpStatus.OK);
	}
    
    /**
     * API will be used by Inbox to get Instagram account id for given business id
     **/
    @GetMapping(value = "/businessid/{businessId}")
	public @ResponseBody ResponseEntity<String> getInstagramIdByBusinessId(@PathVariable("businessId") Integer businessId) {
    	String instagramId = socialInstagramService.getInstagramIdByBusinessId(businessId);
    	return new ResponseEntity<>(instagramId, HttpStatus.OK);
	}
    
    /**
     * API will be used by Inbox to get Instagram account id for given business id
     **/
    @GetMapping(value = "/{instagramId}")
	public @ResponseBody ResponseEntity<Map<String, Object>> getByInstagramId(@PathVariable("instagramId") String instagramId,
			@RequestParam(value = "fields", required = false) List<String> fields) {
    	Map<String, Object> instagramInfo = socialInstagramService.getByBusinessId(instagramId, fields);
    	return new ResponseEntity<>(instagramInfo, HttpStatus.OK);
	}

    /**
     * API to add all pages selected into a particular account
     **/
    @PutMapping(value = "/connectpage")
    public ResponseEntity<ChannelPageInfo> connectPagesV1(@RequestBody InstagramConnectAccountRequest input, @RequestHeader("account-id") Integer accountId) throws Exception {
        if (CollectionUtils.isNotEmpty(input.getId()) && input.getBusinessId() != null) {
        	input.setType(Constants.ENTERPRISE);
            return new ResponseEntity<>(socialInstagramService.connectInstagramAccountV1(input,accountId), HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to get status of instagram setup (to be called by ui on refresh page)
     **/
    @GetMapping(value = "/status")
    public @ResponseBody ResponseEntity<CheckStatusResponse> getIntegrationStatus(@RequestParam("businessId") Long businessId,
                                                                                  @RequestParam(value = "reconnect", required = false, defaultValue = "false") Boolean reconnectFlag){
        if (businessId != null) {
            CheckStatusResponse pageInfo = socialInstagramService.getIntegrationRequestStatus(businessId, reconnectFlag);
            return new ResponseEntity<>(pageInfo, HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_BUSINESS_ID_CAN_NOT_BE_NULL);
        }
    }

    /**
     * API to get messenger status
     **/
    @GetMapping(value = "/messaging/status/{enterpriseId}")
    public @ResponseBody ResponseEntity<InboxStatusResponse> checkMessengerStatus(@PathVariable("enterpriseId") Long enterpriseId) throws Exception {
        return new ResponseEntity<>(socialInstagramService.checkMessengerStatus(enterpriseId), HttpStatus.OK);
    }

    /**
     * API to cancel the fetch page request. The option to cancel the request will be available on the screen when all the pages are fetched and shown on
     * the screen.
     * @param enterpriseId
     * @throws Exception
     */
    @PostMapping(value = "/request/cancel")
    public @ResponseBody ResponseEntity<?> cancelRequest(@RequestParam("enterpriseId") Long enterpriseId,
                                                         @RequestParam(value = "forceCancel", required = false, defaultValue = "false")
                                                         Boolean forceCancel) throws Exception {
        socialInstagramService.cancelRequest(enterpriseId, forceCancel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to map an instagram page with birdeye location (Called from UI)
     */
    @PutMapping(value = "/page/mapping")
    public @ResponseBody ResponseEntity<Map<String, Object>> saveLocationPageMapping(@RequestHeader("userId") Integer userId,
                                                                                     @RequestBody LocationPageMappingRequest mappingRequest) throws Exception {
        if ( userId != null && mappingRequest != null && StringUtils.isNotEmpty(mappingRequest.getPageId())
                && mappingRequest.getLocationId() != null ) {
            socialInstagramService.saveInstagramLocationMapping(mappingRequest.getLocationId(), mappingRequest.getPageId(), userId,Constants.ENTERPRISE, null);
            return new ResponseEntity<>(new HashMap<>(), HttpStatus.OK);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid input present for saveLocationPageMapping");
        }
    }

    /**
     * API to remove page-location mappings for a channel using a list. Called from UI.
     * @param input - List of LocationPageMappingRequest
     * @return - HTTP Status
     * @throws Exception - If input is incorrect
     */
    @PostMapping("/remove/mapping")
    public @ResponseBody ResponseEntity<Void> removePageMapping( @RequestBody List<LocationPageMappingRequest> input) throws Exception {
        if (CollectionUtils.isNotEmpty(input)) {
            socialInstagramService.removeInstagramLocationAccountMapping(input,Constants.ENTERPRISE, false);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * API to remove social page. Called from UI.
     * @param input
     * @param businessId
     * @return
     * @throws Exception
     */
    @PutMapping(value ="remove/page")
    public @ResponseBody ResponseEntity<Void> removePage(@RequestBody List<LocationPageMappingRequest> input,
                                                         @RequestParam("enterpriseId") Long enterpriseId) throws Exception{
        if (CollectionUtils.isNotEmpty(input)) {
            socialInstagramService.removeInstagramAccounts(input, enterpriseId);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /**
     * API to reconnect invalid pages for Social channel. Called from UI
     *
     * @return
     * @throws Exception
     */
    @PutMapping(value = "/reconnect/all")
    public @ResponseBody ResponseEntity<Void> reconnectAllPage(@RequestBody InstagramAuthRequest authRequest) throws Exception {
        if (authRequest != null) {
        	authRequest.setType(Constants.ENTERPRISE);
            socialInstagramService.reconnectInstagramAccounts(authRequest);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }
    /**
     * API to mark intagram page invalid
     *
     * @throws Exception
     */
    @PostMapping(value = "/page/invalid")
    public @ResponseBody
    ResponseEntity<Void> markPageInvalid(@RequestBody SocialAudit audit) throws Exception {
        if (audit.getExternalId() != null) {
            socialInstagramService.updateInvalidPage(audit);
        } else {
            throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, INPUT_CHANNEL_CAN_NOT_BE_NULL);
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

//    @PutMapping("/sync/social-enabled")
//    public @ResponseBody ResponseEntity<Void> syncSocialEnabledStatus() throws Exception {
//        socialInstagramService.syncSocialEnabledStatus();
//        return new ResponseEntity<>(HttpStatus.OK);
//    }


    @GetMapping(value = "/initiate/dp-sync")
    public @ResponseBody ResponseEntity<Void> initiateDPSync() throws Exception {
        socialInstagramService.initiateDPSync();
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("sync/dp")
    public ResponseEntity<Void> syncIGDP(@RequestBody DpSyncRequest igDpSyncRequest) {
        socialInstagramService.syncIGDP(igDpSyncRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PutMapping("/update/subscription")
    public @ResponseBody ResponseEntity<Void> updateSubscription(@RequestBody SocialEnabledStatusDto socialEnabledStatusDto) throws Exception {
        socialInstagramService.updateSubscription(socialEnabledStatusDto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
