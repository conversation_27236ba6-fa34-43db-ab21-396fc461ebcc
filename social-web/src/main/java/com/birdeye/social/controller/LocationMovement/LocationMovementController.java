package com.birdeye.social.controller.LocationMovement;

import com.birdeye.social.locationmovement.LocationMovementRequest;
import com.birdeye.social.service.locationmovement.LocationMovementService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/location/movement")
@Slf4j
public class LocationMovementController {

    @Autowired
    LocationMovementService locationMovementService;

    @PatchMapping("/smb/enterprise-location")
    public ResponseEntity<Void> smbToEnterpriseLocation(
            @RequestParam(value = "excludeApprovalPosts",defaultValue = "false") Boolean excludeApprovalPosts,
            @RequestBody LocationMovementRequest request){
        log.info("Request received to update social post enterprise id:{}",request);
        locationMovementService.smbToEnterpriseLocation(excludeApprovalPosts,request);
        return new ResponseEntity<>( HttpStatus.OK);
    }
}
