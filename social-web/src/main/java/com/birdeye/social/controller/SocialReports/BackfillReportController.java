package com.birdeye.social.controller.SocialReports;

import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.insights.PageInsightsRequest;
import com.birdeye.social.model.BackfillInsightReq;
import com.birdeye.social.model.BackfillRequest;
import com.birdeye.social.service.SocialReportService.BackfillReportService;
import com.birdeye.social.twitter.HistoricalTweetInsight;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/social/backfill")
public class BackfillReportController {
    @Autowired
    BackfillReportService backfillReportService;
    @PostMapping("/{channel}/profile/pages")
    public ResponseEntity<Boolean> backfillProfilePages(@RequestBody PageInsightsRequest pageInsights, @PathVariable("channel") String channel){
        return new ResponseEntity<>(backfillReportService.backfillProfilePages(pageInsights,channel),HttpStatus.OK);
    }
    @PostMapping("/ig/insight")
    public ResponseEntity<Void> backFillIgInsight(@RequestBody BackfillInsightReq igBackfillInsightReq) {
        backfillReportService.backfillIgInsight(igBackfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/fb/insight")
    public ResponseEntity<Void> backFillFbInsight(@RequestBody BackfillInsightReq fbBackfillInsightReq) {
        backfillReportService.backfillFbInsight(fbBackfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/yt/insight")
    public ResponseEntity<Void> backFillYtInsight(@RequestBody BackfillInsightReq ytBackfillInsightReq) {
        backfillReportService.backfillYtInsight(ytBackfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/x/insight")
    public ResponseEntity<Void> backFillXInsight(@RequestBody BackfillInsightReq xBackfillInsightReq) {
        backfillReportService.backfillXInsight(xBackfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/ln/insight")
    public ResponseEntity<Void> backFillLnInsight(@RequestBody BackfillInsightReq lnBackfillInsightReq) {
        backfillReportService.backfillLnInsight(lnBackfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/x/historical")
    public ResponseEntity<Void> backFillXHistorical(@RequestBody HistoricalTweetInsight historicalTweetInsight) {
        backfillReportService.backFillXHistorical(historicalTweetInsight);
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/{channel}/engagement/breakdown")
    public ResponseEntity<Void> engagementBreakdown(@PathVariable("channel") String channel,@RequestBody BackfillInsightReq backfillInsightReq){
        backfillReportService.engagementBreakdownBackfill(channel,backfillInsightReq);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/{channel}/engagement")
    public ResponseEntity<Void> engagementBreakdown(@PathVariable("channel") String channel,@RequestBody BackfillRequest backfillRequest){
        backfillReportService.engagementBreakdown(channel,backfillRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /*
        * API to backFill social posts assets thumbnails for video posts.
        * It will be a one-time operation.
     */
    @GetMapping("/assets/thumbnails")
    public ResponseEntity<Void> backfillSocialPostsAssetsThumbnails(@RequestParam Integer count,
                                                                    @RequestParam(required = false) String businessNumber) {
        backfillReportService.backfillSocialPostsAssetsThumbnails(count, businessNumber);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
