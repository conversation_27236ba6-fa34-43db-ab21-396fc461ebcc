/**
 *
 *
 */
package com.birdeye.social.controller;

import java.io.IOException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FbMessengerUserDetailsResponse;
import com.birdeye.social.model.FbMessageSendRequest;
import com.birdeye.social.model.FbMessengerUserDetailsRequest;
import com.birdeye.social.service.FacebookMessengerService;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/social/messenger")
public class SocialMessengerController {
	
	@Autowired
	FacebookMessengerService fbMsgService;
	
	/**
	 * API to Subscribe pages for Messenger : opt - in
	 * 
	 * @param pageId
	 * @return
	 * @throws IOException 
	 */
	@PostMapping("facebook/subscription/{businessId}")
	public @ResponseBody ResponseEntity<FacebookBaseResponse> pageSubscribedApps(@PathVariable Integer businessId) throws IOException {
		FacebookBaseResponse response = fbMsgService.fbPageSubscribeApps(businessId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * API to Unsubscribe pages from Messenger : opt - out
	 * 
	 * @param pageId
	 * @return
	 */
	@PostMapping("facebook/subscription/{businessId}/remove")
	public @ResponseBody ResponseEntity<FacebookBaseResponse> pageUnsubscribedApps(@PathVariable Integer businessId) {
		FacebookBaseResponse response = fbMsgService.fbPageUnsubscribeApps(businessId);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * API to get message sender details
	 * 
	 * @param request
	 * @return
	 */
	@PostMapping("facebook/userdetails")
	public @ResponseBody ResponseEntity<FbMessengerUserDetailsResponse> fbMessengerUserDetails(@RequestBody FbMessengerUserDetailsRequest request) throws IOException {
		FbMessengerUserDetailsResponse response = fbMsgService.getFbMessengerUserDetails(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * API to send message to facebook messenger with text and attachment support
	 * 
	 * @param request
	 * @return
	 * @throws IOException 
	 */
	@PostMapping("facebook/send")
	public @ResponseBody ResponseEntity<Map<String, Object>> sendFbMessenage(@RequestBody FbMessageSendRequest request) throws IOException {
		if (request.getMessage() != null && request.getUrl() != null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_SEND_FB_MESSAGE_REQUEST, "Only one of the text or attachment fields can be specified");
		} else {
			Map<String, Object> response = fbMsgService.sendFbMessage(request);
			return new ResponseEntity<>(response, HttpStatus.OK);
		}
	}
}
