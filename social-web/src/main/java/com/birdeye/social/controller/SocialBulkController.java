package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.external.request.picturesque.SocialScheduledPostValidationRequest;
import com.birdeye.social.model.bulk.posting.SocialPostUploadRequest;
import com.birdeye.social.service.SocialBulkScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> on 12/12/23
 */

@RestController
@Slf4j
@RequestMapping(value = "/social/bulk")
public class SocialBulkController {

    @Autowired
    private SocialBulkScheduleService socialBulkScheduleService;

    @PostMapping(value = "/schedule")
    public ResponseEntity<Void> bulkXlsSchedule(@RequestBody SocialPostUploadRequest socialPostUploadRequest,
                                                @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String requestSource) {
        socialBulkScheduleService.scheduleBulkXlsRecords(socialPostUploadRequest, requestSource);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/schedule/upload/media")
    public ResponseEntity<Void> uploadBulkMedia(@RequestBody SocialScheduledPostValidationRequest request) {
        socialBulkScheduleService.uploadMediaToPicturesque(request);
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
