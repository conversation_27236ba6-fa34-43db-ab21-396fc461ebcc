/**
 *
 *
 */
package com.birdeye.social.controller;

import java.io.IOException;
import java.util.Map;

import com.birdeye.social.facebook.response.InstagramUserInfoResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.response.FacebookBaseResponse;
import com.birdeye.social.facebook.response.FbMessengerUserDetailsResponse;
import com.birdeye.social.facebook.response.InstagramUserDetailsResponse;
import com.birdeye.social.model.FbMessageSendRequest;
import com.birdeye.social.model.FbMessengerUserDetailsRequest;
import com.birdeye.social.model.InstagramMessageRequest;
import com.birdeye.social.model.InstagramUserDetailsRequest;
import com.birdeye.social.service.FacebookMessengerService;
import com.birdeye.social.service.InstagramMessengeService;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/instagram/message")
public class InstagramMessageController {
	
	@Autowired
	InstagramMessengeService instagramMsgService;
	
//	/**
//	 * API to Subscribe pages for Messenger : opt - in
//	 * 
//	 * @param pageId
//	 * @return
//	 * @throws IOException 
//	 */
//	@PostMapping("facebook/subscription/{businessId}")
//	public @ResponseBody ResponseEntity<FacebookBaseResponse> pageSubscribedApps(@PathVariable Integer businessId) throws IOException {
//		FacebookBaseResponse response = fbMsgService.fbPageSubscribeApps(businessId);
//		return new ResponseEntity<>(response, HttpStatus.OK);
//	}
//	
//	/**
//	 * API to Unsubscribe pages from Messenger : opt - out
//	 * 
//	 * @param pageId
//	 * @return
//	 */
//	@PostMapping("facebook/subscription/{businessId}/remove")
//	public @ResponseBody ResponseEntity<FacebookBaseResponse> pageUnsubscribedApps(@PathVariable Integer businessId) {
//		FacebookBaseResponse response = fbMsgService.fbPageUnsubscribeApps(businessId);
//		return new ResponseEntity<>(response, HttpStatus.OK);
//	}
	
	/**
	 * API to get message sender details
	 * 
	 * @param request
	 * @return
	 */
	@PostMapping("/user")
	public @ResponseBody ResponseEntity<InstagramUserInfoResponse> instagramUserDetails(@RequestBody InstagramUserDetailsRequest request) throws IOException {
		InstagramUserInfoResponse response = instagramMsgService.getUserDetails(request);
		return new ResponseEntity<>(response, HttpStatus.OK);
	}
	
	/**
	 * API to send message to instagram messenge with text and attachment support
	 * 
	 * @param request
	 * @return
	 * @throws IOException 
	 */
	@PostMapping("/send")
	public @ResponseBody ResponseEntity<Map<String, Object>> sendMessenage(@RequestBody InstagramMessageRequest request) throws IOException {
		if (request.getMessage() != null && request.getUrl() != null) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_SEND_FB_MESSAGE_REQUEST, "Only one of the text or attachment fields can be specified");
		}
		if(request.getMessage() != null && request.getMessage().length() > 1000) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_SEND_FB_MESSAGE_REQUEST, "Maximum 1000 characters limit exceeded");
		}else {
			Map<String, Object> response = instagramMsgService.sendMessage(request);
			return new ResponseEntity<>(response, HttpStatus.OK);
		}
	}
}
