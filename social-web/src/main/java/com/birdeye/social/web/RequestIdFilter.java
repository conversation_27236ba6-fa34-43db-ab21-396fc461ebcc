package com.birdeye.social.web;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SocialAPIRateLimitMaster;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.RateLimitingConstants;
import com.birdeye.social.model.RateLimitingMasterDTO;
import com.birdeye.social.service.IRedisExternalService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.UUID;



@Component
@Order(1)
public class RequestIdFilter implements Filter {

	private static Logger logger = LoggerFactory.getLogger(RequestIdFilter.class);

	@Autowired
	private IRedisExternalService redisExternalService;

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		long startTime = System.currentTimeMillis();
		String url = "UNKNOWN";
		try {
			MDC.put("requestId", UUID.randomUUID().toString());
			if (request instanceof HttpServletRequest) {
				url = ((HttpServletRequest) request).getRequestURI();
				MDC.put("uri", url);
				MDC.put("uriType",  ((HttpServletRequest) request).getMethod());

				//TODO: ask services for service name in header.
				Boolean rateLimitingEnabled = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getRateLimitingCheckEnabled();
				MDC.put("rateLimit", String.valueOf(rateLimitingEnabled));
				if (rateLimitingEnabled) {
						updateSourceIdentifierInRequest(url, ((HttpServletRequest) request).getMethod(), ((HttpServletRequest) request).getQueryString());
				}

				String armorRequestId = ((HttpServletRequest) request).getHeader("armor-request-id");

				if (StringUtils.isNotEmpty(armorRequestId)) {
					MDC.put("armor-request-id", armorRequestId);
				}
			}
			// continue down the chain
			chain.doFilter(request, response);
		} finally {
			HttpServletResponse httpResponse = (HttpServletResponse) response;
			int statusCode = httpResponse.getStatus();
			String clientIp = request.getRemoteAddr();
			logger.info("Time taken for request {} is {} ms. Status code is {}. Client IP is {}", url,
					(System.currentTimeMillis() - startTime), statusCode, clientIp);
			// Clear MDC post each request.
			MDC.clear();
		}
	}

	private String removeNumericIdFromSubmitUrl(String url) {
		if (StringUtils.isNotEmpty(url) && url.matches(".*/social/post/submit/\\d+$")) {
			url = url.replaceAll("/social/post/submit/\\d+$", "/social/post/submit");
		}
		logger.info("URL :: {}", url);
		return url;
	}

	public void updateSourceIdentifierInRequest(String url, String apiType, String queryString) {
		url = removeNumericIdFromSubmitUrl(url);
		String identifier=url+"##"+apiType;

		RateLimitingMasterDTO data= CacheManager.getInstance().getCache(SocialAPIRateLimitMaster.class).getMasterDataByType(identifier);

		//RateLimitingMasterDTO data= redisExternalService.getRateLimitingSocialApiDto(identifier);
		if (!ObjectUtils.isEmpty(data)) {
			if (StringUtils.isNotEmpty(data.getRateLimitIdentifier())) {
				MDC.put("source", data.getRateLimitIdentifier());
			}

			//Below code is done to find the channel for which rate limiting will be done.
			//If the channel is present in query string, use it. Otherwise, find the channel from table(social_rate_limit_api_info)
			//This is done to make sure that API for diff channels can have same path, just need to have different query string
			if (StringUtils.isNotEmpty(queryString) && queryString.contains(RateLimitingConstants.CHANNEL)) {
				queryString = queryString.substring(queryString.indexOf("channel=") + 8);
				if (queryString.contains(",")) {
					queryString = queryString.substring(0, queryString.indexOf(","));
				}
				if (StringUtils.isNotEmpty(queryString)) {
					MDC.put(RateLimitingConstants.CHANNEL, queryString);
				} else {
					MDC.put(RateLimitingConstants.CHANNEL, Constants.GMB);
				}
			} else if (StringUtils.isNotEmpty(data.getSource())) {
				MDC.put(RateLimitingConstants.CHANNEL, data.getSource());
			}
		}
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		logger.info("=================== RequestId filter initialized ===================");
	}

	@Override
	public void destroy() {
		// no op
	}

}
