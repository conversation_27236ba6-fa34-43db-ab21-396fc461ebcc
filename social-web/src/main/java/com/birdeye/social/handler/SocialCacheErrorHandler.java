package com.birdeye.social.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.Cache;
import org.springframework.cache.interceptor.CacheErrorHandler;

public class SocialCacheErrorHandler implements CacheErrorHandler {

	private final static Logger LOGGER = LoggerFactory.getLogger(SocialCacheErrorHandler.class);

	@Override
	public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
		LOGGER.error("Exception while getting key {} from cache {}, Proceeding with method call ::", key, null!= cache? cache.getName() : "", exception);

	}

	@Override
	public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
		LOGGER.error("Error while putting key {} from cache {} , Proceeding with method call ::", key, null!= cache? cache.getName() : "", exception);

	}

	@Override
	public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
		LOGGER.error("Error while evicting key {} from cache {}, Proceeding with method call ::", key, null!= cache? cache.getName() : "", exception);

	}

	@Override
	public void handleCacheClearError(RuntimeException exception, Cache cache) {
		LOGGER.error("Error while clearing  cache {} , Proceeding with method call ::",  null!= cache? cache.getName() : "", exception);

	}


} 