{
  "size": ${size},
  "from": ${from},
  "sort": [
  <#if sortByReviewDate >
    {
      "r_date": {
        "order" : "${sortOrder}"
      }
    }
  </#if>
  <#if sortByCreatedDate > ,
    {
      "created": {
        "order" : "${sortOrder}"
      }
    }
  </#if>
  <#if sortByFollowerCount > ,
    {
      "rwr.foll_cnt": {
        "order" : "${sortOrder}"
      }
    }
  </#if>
  ],
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "e_id": [
              ${businessIds}
            ]
          }
        }
        <#if reviewStartDate ?? && reviewEndDate ?? > ,
        {
          "range": {
            "r_date": {
              "gte": "${reviewStartDate}",
              "lte": "${reviewEndDate}"
            }
          }
        }
        </#if>
        <#if createdStartDate ?? && createdEndDate ?? > ,
        {
          "range": {
            "created": {
              "gte": "${createdStartDate}",
              "lte": "${createdEndDate}"
            }
          }
        }
        </#if>
      ]
    }
  }
}