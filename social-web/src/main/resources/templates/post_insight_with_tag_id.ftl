{
    "query": {
        "bool": {
          "must": [
            {
               "terms": {
                 "source_id": [
                    ${sourceIds}
                 ]
               }
            }
            <#if publisherIds??>
            ,{
                "terms": {
                  "publisher_id": [
                      ${publisherIds}
                  ]
                }
            }
            </#if>,
            {
               "terms": {
                 "tagIds": [
                     ${tagIds}
                 ]
               }
            },
            {
              "terms": {
                "page_id": [
                  ${pageIds}
                ]
              }
            },
            {
              "range": {
                "posted_date": {
                  "gte": "${startDate}",
                  "lte": "${endDate}"
                }
              }
            }
            <#if isDeleted?? && isDeleted == true>
            ,{
                "term": {
                    "is_deleted": true
                }
            }
            </#if>
          ]
        }
      },
      "size": 0,
      "aggs":{
          "be_post_id":{
            "terms": {
              "field": "be_post_id",
              "size": ${totalSize},
              "order": [
                {
                  "${sortParam}": "${sortOrder}"
                },
                {
                   "posted_date": "desc"
                }
              ]
            },
            "aggs": {
             "source_id": {
                  "terms": {
                    "field": "source_id"
                  },
                  "aggs": {
                    "reach": {
                      "sum": {
                        "field": "reach"
                      }
                    },
                    "engagement": {
                      "sum": {
                        "field": "engagement"
                      }
                    },
                    "impression": {
                      "sum": {
                        "field": "impression"
                      }
                    },
                     "likeCount": {
                       "sum": {
                         "field": "like_count"
                       }
                     },
                      "video_views": {
                        "sum": {
                          "field": "video_views"
                        }
                      },
                     "commentCount": {
                       "sum": {
                         "field": "comment_count"
                       }
                     },
                     "shareCount": {
                       "sum": {
                         "field": "share_count"
                       }
                     }
                  }
                },
              "reach": {
                "sum": {
                  "field": "reach"
                }
              },
              "engagement": {
                "sum": {
                  "field": "engagement"
                }
              },
              "impression": {
                "sum": {
                  "field": "impression"
                }
              },
              "likeCount": {
                  "sum": {
                    "field": "like_count"
                  }
                },
                "video_views": {
                  "sum": {
                    "field": "video_views"
                  }
                }
                "commentCount": {
                  "sum": {
                    "field": "comment_count"
                  }
                },
                "shareCount": {
                  "sum": {
                    "field": "share_count"
                  }
                },
              "posted_date": {
                 "max": {
                   "field": "posted_date"
                }
              }
            }
          }
        }
}