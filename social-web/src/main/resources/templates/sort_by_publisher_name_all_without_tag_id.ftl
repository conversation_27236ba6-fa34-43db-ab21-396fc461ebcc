{
    "query": {
        "bool": {
          "must": [
            {
               "terms": {
                 "page_id": [
                     ${pageIds}
                 ]
               }
            }
            <#if publisherIds??>
            ,{
                "terms": {
                  "publisher_id": [
                      ${publisherIds}
                  ]
                }
            }
            </#if>,
            {
              "range": {
                "posted_date": {
                  "gte": "${startDate}",
                  "lte": "${endDate}"
                }
              }
            }
          ],
          "must_not": [
            {
              "exists": {
                "field": "tagIds"
              }
            }
          ]
        }
      },
      "sort": [
        {
          "publisher_name.keyword": {
            "order": "${sortOrder}"
          }
        }
      ]
           <#if !excelDownload>
            ,"collapse": {
                "field": "be_post_id"
            }
            </#if>
            ,"size": ${size},
      "from": ${from}
}