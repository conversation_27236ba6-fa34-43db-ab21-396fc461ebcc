{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "page_id": [
              ${pageIds}
            ]
          }
        }
      ]
    }
  },
  "aggs": {
    "data": {
      "filter": {
        "bool": {
          "must": [{
            "range": {
              "day": {
              "gte": "${startDate}",
              "lte": "${endDate}"
              }
            }
          }]
        }
      },
      "aggs": {
        "histogram": {
          "aggs": {
            "published_story_count": {
              "sum": {
                "field": "published_story_count"
              }
            },
            "story_impressions": {
              "sum": {
                "field": "story_impressions"
              }
            },
            "story_engagements": {
              "sum": {
                "field": "story_engagements"
              }
            },
            "story_reach": {
              "sum": {
                "field": "story_reach"
              }
            },
            "story_likes": {
              "sum": {
                "field": "story_likes"
              }
            },
            "story_comments": {
              "sum": {
                "field": "story_comments"
              }
            },
            "story_shares": {
              "sum": {
                "field": "story_shares"
              }
            }
          },
          "date_histogram": {
            "field": "day",
            "interval": "${type}",
            "format": "yyyy-MM-dd HH:mm:ss",
            "keyed": true
            <#if includeExtendedBounds>,
            "extended_bounds" : {
              "min" : "${startDate}",
              "max" : "${endDate}"
            }
            </#if>
          }
        }
      }
    },
    "prev_data":{
      "filter": {
        "bool": {
          "must": [{
            "range": {
              "day": {
              "gte": "${prevDate}",
              "lt": "${startDate}"
              }
            }
          }]
        }
      },
      "aggs": {
        "published_story_count": {
          "sum": {
            "field": "published_story_count"
          }
        },
        "story_impressions": {
          "sum": {
            "field": "story_impressions"
          }
        },
        "story_engagements": {
          "sum": {
            "field": "story_engagements"
          }
        },
        "story_reach": {
          "sum": {
            "field": "story_reach"
          }
        },
        "story_likes": {
          "sum": {
            "field": "story_likes"
          }
        },
        "story_comments": {
          "sum": {
            "field": "story_comments"
          }
        },
        "story_shares": {
          "sum": {
            "field": "story_shares"
          }
        }
      }
    },
    "current_data":{
      "filter": {
        "bool": {
          "must": [{
            "range": {
              "day": {
              "gte": "${startDate}",
              "lte": "${endDate}"
              }
            }
          }]
        }
      },
      "aggs": {
        "published_story_count": {
          "sum": {
            "field": "published_story_count"
          }
        },
        "story_impressions": {
          "sum": {
            "field": "story_impressions"
          }
        },
        "story_engagements": {
          "sum": {
            "field": "story_engagements"
          }
        },
        "story_reach": {
          "sum": {
            "field": "story_reach"
          }
        },
        "story_likes": {
          "sum": {
            "field": "story_likes"
          }
        },
        "story_comments": {
          "sum": {
            "field": "story_comments"
          }
        },
        "story_shares": {
          "sum": {
            "field": "story_shares"
          }
        }
      }
    }
  }
}