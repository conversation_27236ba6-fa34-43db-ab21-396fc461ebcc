{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
             "be_post_id": [
                  ${bePostIds}
            ]
          }
        },
        {
          "terms": {
             "page_id": [
                ${pageIds}
            ]
          }
        }
      ]
    }
  },
  "aggs": {
    "be_post_id": {
      "terms": {
        "field": "be_post_id",
        "size": ${size}
      },
      "aggs": {
          "source_id": {
            "terms": {
              "field": "source_id"
            },
            "aggs": {
              "reach": {
                "sum": {
                  "field": "reach"
                }
              },
              "engagement": {
                "sum": {
                  "field": "engagement"
                }
              },
              "impression": {
                "sum": {
                  "field": "impression"
                }
              },
               "likeCount": {
                 "sum": {
                   "field": "like_count"
                 }
               },
               "commentCount": {
                 "sum": {
                   "field": "comment_count"
                 }
               },
               "shareCount": {
                 "sum": {
                   "field": "share_count"
                 }
               },
               "video_views": {
                                "sum": {
                                  "field": "video_views"
                                }
                              }
            }
          },
        "reach": {
          "sum": {
            "field": "reach"
          }
        },
         "video_views": {
          "sum": {
            "field": "video_views"
          }
        },
        "engagement": {
          "sum": {
            "field": "engagement"
          }
        },
        "impression": {
          "sum": {
            "field": "impression"
          }
        },
        "likeCount": {
          "sum": {
            "field": "like_count"
          }
        },
        "commentCount": {
          "sum": {
            "field": "comment_count"
          }
        },
        "shareCount": {
          "sum": {
            "field": "share_count"
          }
        }
      }
    }
  }
}