{
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "source_id": [
              ${sourceIds}
            ]
          }
        },
        {
          "terms": {
            "page_id": [
              ${pageIds}
            ]
          }
        },
        {
          "range": {
            "posted_date": {
              "gte": "${startDate}",
              "lte": "${endDate}"
            }
          }
        },
        {
          "bool": {
            "should": [
              {
                "bool": {
                  "must_not": [
                    {
                      "exists": {
                        "field": "tagIds"
                      }
                    }
                  ]
                }
              },
              {
                "terms": {
                  "tagIds": [
                    ${tagIds}
                  ]
                }
              }
            ]
          }
        }
        <#if isDeleted?? && isDeleted == true>
        , {
          "term": {
            "is_deleted": true
          }
        }
        </#if>
      ]
    }
  },
  "size": ${size},
  "from": ${from},
  "sort": [
    {
      "${sortParam}": "${sortOrder}"
    },
    {
      "posted_date": "desc"
    }
  ]
}
