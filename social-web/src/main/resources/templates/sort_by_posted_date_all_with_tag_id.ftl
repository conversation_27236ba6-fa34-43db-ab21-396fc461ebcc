{
    "query": {
        "bool": {
          "must": [
            {
               "terms": {
                 "page_id": [
                     ${pageIds}
                 ]
               }
            }
            <#if publisherIds??>
            ,{
                "terms": {
                  "publisher_id": [
                      ${publisherIds}
                  ]
                }
            }
            </#if>,
            {
               "terms": {
                 "tagIds": [
                     ${tagIds}
                 ]
               }
            },
            {
              "range": {
                "posted_date": {
                  "gte": "${startDate}",
                  "lte": "${endDate}"
                }
              }
            }
            <#if isDeleted?? && isDeleted == true>
            ,{
                "term": {
                    "is_deleted": true
                }
            }
            </#if>
          ]
        }
      },
      "sort": [
        {
         "${sortParam}": {
            "order": "${sortOrder}"
          }
        }
      ]
     <#if !excelDownload>
      ,"collapse": {
          "field": "be_post_id"
      }
      </#if>
      ,"size": ${size},
      "from": ${from}
}