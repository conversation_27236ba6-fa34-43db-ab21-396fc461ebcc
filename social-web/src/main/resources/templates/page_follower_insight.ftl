{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "page_id": [
              ${pageIds}
            ]
          }
        }
      ]
    }
  },
  "aggs": {
    "data": {
      "filter": {
          "bool": {
            "must": [{
            "range": {
              "day": {
              "gte": "${startDate}",
              "lte": "${endDate}"
              }
            }
          }]
         }
        },
        "aggs": {
          "histogram": {
            "aggs": {
              "follow_gain": {
                "sum": {
                  "field": "follower_gain"
                }
              },
              "follow_lost": {
                "sum": {
                      "field": "follower_lost"
                }
              },
              "total_follower": {
                "sum": {
                  "field": "total_follower_count"
                }
              },
              "likes_gain": {
                "sum": {
                  "field": "like_gain"
                }
              },
              "likes_lost": {
                "sum": {
                  "field": "like_lost"
                }
              },
              "total_likes": {
                "sum": {
                  "field": "total_like"
                }
              }
            },
            "date_histogram": {
              "field": "day",
              "interval": "${type}",
              "format": "yyyy-MM-dd HH:mm:ss",
              "keyed": true
              <#if includeExtendedBounds>,
              "extended_bounds" : {
                "min" : "${startDate}",
                "max" : "${endDate}"
              }
              </#if >
            }
          }
        }
    },
    "prev_data":{
      "filter": {
          "bool": {
            "must": [{
            "range": {
              "day": {
              "gte": "${prevDate}",
              "lt": "${startDate}"
              }
            }
          }]
         }
        },
      "aggs": {
        "follow_gain": {
          "sum": {
            "field": "follower_gain"
          }
        },
        "follow_lost":{
          "sum": {
            "field": "follower_lost"
          }
        },
        "total_follower": {
          "sum": {
            "field": "total_follower_count"
          }
        },
        "likes_gain": {
          "sum": {
            "field": "like_gain"
          }
        },
        "likes_lost": {
          "sum": {
            "field": "like_lost"
          }
        },
        "total_likes": {
          "sum": {
            "field": "total_like"
          }
        }
      }
    },
    "current_data":{
      "filter": {
          "bool": {
            "must": [{
            "range": {
              "day": {
              "gte": "${startDate}",
              "lte": "${endDate}"
              }
            }
          }]
         }
        },
      "aggs": {
        "follow_gain": {
          "sum": {
            "field": "follower_gain"
          }
        },
        "follow_lost":{
          "sum": {
            "field": "follower_lost"
          }
        },
        "total_follower": {
          "sum": {
            "field": "total_follower_count"
          }
        },
        "likes_gain": {
          "sum": {
            "field": "like_gain"
          }
        },
        "likes_lost": {
          "sum": {
            "field": "like_lost"
          }
        },
        "total_likes": {
          "sum": {
            "field": "total_like"
          }
        }
      }
    }
  }
}