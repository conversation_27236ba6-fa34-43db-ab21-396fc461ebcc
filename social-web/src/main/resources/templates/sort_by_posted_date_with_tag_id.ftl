{
    "query": {
        "bool": {
          "must": [
            {
               "terms": {
                 "source_id": [
                    ${sourceIds}
                 ]
               }
            }
            <#if publisherIds??>
            ,{
                "terms": {
                  "publisher_id": [
                      ${publisherIds}
                  ]
                }
            }
            </#if>,
            {
               "terms": {
                 "tagIds": [
                     ${tagIds}
                 ]
               }
            },
            {
              "terms": {
                "page_id": [
                  ${pageIds}
                ]
              }
            },
            {
              "range": {
                "posted_date": {
                  "gte": "${startDate}",
                  "lte": "${endDate}"
                }
              }
            }
            <#if isDeleted?? && isDeleted == true>
            ,{
                "term": {
                    "is_deleted": true
                }
            }
            </#if>
          ]
        }
      },
      "sort": [
        {
          "${sortParam}": {
            "order": "${sortOrder}"
          }
        }
      ]
           <#if !excelDownload>
            ,"collapse": {
                "field": "be_post_id"
            }
            </#if>
            ,"size": ${size},
      "from": ${from}
}