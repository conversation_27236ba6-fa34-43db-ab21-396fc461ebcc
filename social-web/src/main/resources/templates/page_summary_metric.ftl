{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "page_id": [
              ${pageIds}
            ]
          }
        }
      ]
    }
  },
  "aggs": {
    "data": {
      "filter": {
        "bool": {
          "must": [
            {
              "range": {
                "day": {
                  "gte": "${startDate}",
                  "lte": "${endDate}"
                }
              }
            }
          ]
        }
      },
      "aggs": {
        "engagement": {
          "sum": {
            "field": "post_engagement"
          }
        },
        "clickCount": {
          "sum": {
            "field": "click_count"
          }
        },
        "impression": {
          "sum": {
            "field": "post_impressions"
          }
        },
        "likeCount":{
          "sum": {
            "field": "like_count"
          }
        },
        "commentCount":{
          "sum": {
            "field": "comment_count"
          }
        },
        "shareCount":{
          "sum": {
            "field": "share_count"
          }
        },
                 "followers": {
                            "top_hits": {
                              "_source": "total_follower_count",
                              "size": 1,
                              "sort": [
                                {
                                  "day": {
                                    "order": "desc"
                                  }
                                }
                              ]
                            }
                          },
                "followerGain":{
                                  "sum": {
                                    "field": "follower_gain"
                                  }
                                },
                                "followerLost":{
                                                  "sum": {
                                                    "field": "follower_lost"
                                                  }
                                                }
      }
    }
  }
}