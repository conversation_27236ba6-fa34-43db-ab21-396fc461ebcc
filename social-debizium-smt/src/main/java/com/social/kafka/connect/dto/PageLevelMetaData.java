package com.social.kafka.connect.dto;

import java.util.Date;
import java.util.List;

public class PageLevelMetaData {
    private Date date;
    private Integer followerGainCount;
    private Integer followerLostCount;
    private Integer followerChange;
    private Integer totalFollower;
    private Integer likesGainCount;
    private Integer likesLostCount;
    private Integer totalLikes;
    private Integer postEngagements;
    private Integer postTotalCount;
    private Integer postReach;
    private Integer postImpressions;
    private Integer postImpressionTotal;
    private Double postEngagementRate;
    private Integer postEngagementTotal;
    private Integer shareCount;
    private Integer clickCount;
    private Integer postCount;
    private Integer commentCount;
    private Integer profileVideoViews;
    private Integer messageSent;
    private Integer totalProfileVideoViews;
    private Integer totalPostShareCount; 
    private Integer totalPostCommentCount; 
    private Integer totalPostLikeCount; 
    private Integer pagePostLikeCount;
    private String dateString;
    private List<TikTokPageData.PageMetrics.AudienceActivity> audienceActivity;
    private List<DemographicsInsightDataPoint.Data> audienceCountries;
    private List<DemographicsInsightDataPoint.Data> audienceGenders;
    private List<DemographicsInsightDataPoint.Data> audienceCities;
    private List<DemographicsInsightDataPoint.Data> audienceAges;
    private Boolean isBusinessAccount;

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Integer getFollowerGainCount() {
        return followerGainCount;
    }

    public void setFollowerGainCount(Integer followerGainCount) {
        this.followerGainCount = followerGainCount;
    }

    public Integer getFollowerLostCount() {
        return followerLostCount;
    }

    public void setFollowerLostCount(Integer followerLostCount) {
        this.followerLostCount = followerLostCount;
    }

    public Integer getFollowerChange() {
        return followerChange;
    }

    public void setFollowerChange(Integer followerChange) {
        this.followerChange = followerChange;
    }

    public Integer getTotalFollower() {
        return totalFollower;
    }

    public void setTotalFollower(Integer totalFollower) {
        this.totalFollower = totalFollower;
    }

    public Integer getLikesGainCount() {
        return likesGainCount;
    }

    public void setLikesGainCount(Integer likesGainCount) {
        this.likesGainCount = likesGainCount;
    }

    public Integer getLikesLostCount() {
        return likesLostCount;
    }

    public void setLikesLostCount(Integer likesLostCount) {
        this.likesLostCount = likesLostCount;
    }

    public Integer getTotalLikes() {
        return totalLikes;
    }

    public void setTotalLikes(Integer totalLikes) {
        this.totalLikes = totalLikes;
    }

    public Integer getPostEngagements() {
        return postEngagements;
    }

    public void setPostEngagements(Integer postEngagements) {
        this.postEngagements = postEngagements;
    }

    public Integer getPostTotalCount() {
        return postTotalCount;
    }

    public void setPostTotalCount(Integer postTotalCount) {
        this.postTotalCount = postTotalCount;
    }

    public Integer getPostReach() {
        return postReach;
    }

    public void setPostReach(Integer postReach) {
        this.postReach = postReach;
    }

    public Integer getPostImpressions() {
        return postImpressions;
    }

    public void setPostImpressions(Integer postImpressions) {
        this.postImpressions = postImpressions;
    }

    public Integer getPostImpressionTotal() {
        return postImpressionTotal;
    }

    public void setPostImpressionTotal(Integer postImpressionTotal) {
        this.postImpressionTotal = postImpressionTotal;
    }

    public Double getPostEngagementRate() {
        return postEngagementRate;
    }

    public void setPostEngagementRate(Double postEngagementRate) {
        this.postEngagementRate = postEngagementRate;
    }

    public Integer getPostEngagementTotal() {
        return postEngagementTotal;
    }

    public void setPostEngagementTotal(Integer postEngagementTotal) {
        this.postEngagementTotal = postEngagementTotal;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public Integer getClickCount() {
        return clickCount;
    }

    public void setClickCount(Integer clickCount) {
        this.clickCount = clickCount;
    }

    public Integer getPostCount() {
        return postCount;
    }

    public void setPostCount(Integer postCount) {
        this.postCount = postCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getProfileVideoViews() {
        return profileVideoViews;
    }

    public void setProfileVideoViews(Integer profileVideoViews) {
        this.profileVideoViews = profileVideoViews;
    }

    public Integer getMessageSent() {
        return messageSent;
    }

    public void setMessageSent(Integer messageSent) {
        this.messageSent = messageSent;
    }

    public Integer getTotalProfileVideoViews() {
        return totalProfileVideoViews;
    }

    public void setTotalProfileVideoViews(Integer totalProfileVideoViews) {
        this.totalProfileVideoViews = totalProfileVideoViews;
    }

    public Integer getTotalPostShareCount() {
        return totalPostShareCount;
    }

    public void setTotalPostShareCount(Integer totalPostShareCount) {
        this.totalPostShareCount = totalPostShareCount;
    }

    public Integer getTotalPostCommentCount() {
        return totalPostCommentCount;
    }

    public void setTotalPostCommentCount(Integer totalPostCommentCount) {
        this.totalPostCommentCount = totalPostCommentCount;
    }

    public Integer getTotalPostLikeCount() {
        return totalPostLikeCount;
    }

    public void setTotalPostLikeCount(Integer totalPostLikeCount) {
        this.totalPostLikeCount = totalPostLikeCount;
    }

    public Integer getPagePostLikeCount() {
        return pagePostLikeCount;
    }

    public void setPagePostLikeCount(Integer pagePostLikeCount) {
        this.pagePostLikeCount = pagePostLikeCount;
    }

    public String getDateString() {
        return dateString;
    }

    public void setDateString(String dateString) {
        this.dateString = dateString;
    }

    public List<TikTokPageData.PageMetrics.AudienceActivity> getAudienceActivity() {
        return audienceActivity;
    }

    public void setAudienceActivity(List<TikTokPageData.PageMetrics.AudienceActivity> audienceActivity) {
        this.audienceActivity = audienceActivity;
    }

    public List<DemographicsInsightDataPoint.Data> getAudienceCountries() {
        return audienceCountries;
    }

    public void setAudienceCountries(List<DemographicsInsightDataPoint.Data> audienceCountries) {
        this.audienceCountries = audienceCountries;
    }

    public List<DemographicsInsightDataPoint.Data> getAudienceGenders() {
        return audienceGenders;
    }

    public void setAudienceGenders(List<DemographicsInsightDataPoint.Data> audienceGenders) {
        this.audienceGenders = audienceGenders;
    }

    public List<DemographicsInsightDataPoint.Data> getAudienceCities() {
        return audienceCities;
    }

    public void setAudienceCities(List<DemographicsInsightDataPoint.Data> audienceCities) {
        this.audienceCities = audienceCities;
    }

    public List<DemographicsInsightDataPoint.Data> getAudienceAges() {
        return audienceAges;
    }

    public void setAudienceAges(List<DemographicsInsightDataPoint.Data> audienceAges) {
        this.audienceAges = audienceAges;
    }

    public Boolean getIsBusinessAccount() {
        return isBusinessAccount;
    }

    public void setIsBusinessAccount(Boolean businessAccount) {
        this.isBusinessAccount = businessAccount;
    }

    @Override
    public String toString() {
        return "PageLevelMetaData{" +
                "date=" + date +
                ", followerGainCount=" + followerGainCount +
                ", followerLostCount=" + followerLostCount +
                ", followerChange=" + followerChange +
                ", totalFollower=" + totalFollower +
                ", likesGainCount=" + likesGainCount +
                ", likesLostCount=" + likesLostCount +
                ", totalLikes=" + totalLikes +
                ", postEngagements=" + postEngagements +
                ", postTotalCount=" + postTotalCount +
                ", postReach=" + postReach +
                ", postImpressions=" + postImpressions +
                ", postImpressionTotal=" + postImpressionTotal +
                ", postEngagementRate=" + postEngagementRate +
                ", postEngagementTotal=" + postEngagementTotal +
                ", shareCount=" + shareCount +
                ", clickCount=" + clickCount +
                ", postCount=" + postCount +
                ", commentCount=" + commentCount +
                ", profileVideoViews=" + profileVideoViews +
                ", messageSent=" + messageSent +
                ", totalProfileVideoViews=" + totalProfileVideoViews +
                ", totalPostShareCount=" + totalPostShareCount +
                ", totalPostCommentCount=" + totalPostCommentCount +
                ", totalPostLikeCount=" + totalPostLikeCount +
                ", pagePostLikeCount=" + pagePostLikeCount +
                ", dateString='" + dateString + '\'' +
                ", audienceActivity=" + audienceActivity +
                ", audienceCountries=" + audienceCountries +
                ", audienceGenders=" + audienceGenders +
                ", audienceCities=" + audienceCities +
                ", audienceAges=" + audienceAges +
                ", isBusinessAccount=" + isBusinessAccount +
                '}';
    }
}
