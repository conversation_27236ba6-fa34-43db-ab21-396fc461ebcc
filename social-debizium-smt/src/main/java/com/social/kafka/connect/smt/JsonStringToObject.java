package com.social.kafka.connect.smt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.social.kafka.connect.dto.PageInsights;
import org.apache.kafka.common.config.ConfigDef;
import org.apache.kafka.common.config.ConfigException;
import org.apache.kafka.connect.connector.ConnectRecord;
import org.apache.kafka.connect.data.*;
import org.apache.kafka.connect.transforms.Transformation;
import org.apache.kafka.connect.errors.DataException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Map;

public class JsonStringToObject<R extends ConnectRecord<R>> implements Transformation<R> {
    public static final String FIELD_CONFIG = "field";
    private String fieldName;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final Logger log = LoggerFactory.getLogger(JsonStringToObject.class);
    public static void main(String[] args) {
        log.info("Called from JsonStringToObject main");
    }
    @Override
    public R apply(R record) {
        log.trace("Called from JsonStringToObject apply with record {}",record);
        if (record.valueSchema() == null) {
            throw new DataException("Record schema is missing");
        }
        try {
            // Retrieve the value and schema
            Struct value = (Struct) record.value();
            log.trace("Called from JsonStringToObject apply with value {}", value);
            Field afterField = value.schema().field("after");
            Struct afterFieldValue = (Struct) value.get(afterField);

            log.trace("Called from JsonStringToObject apply with afterField {}", afterField);
            Field field = afterField.schema().field(fieldName);
            log.trace("Called from JsonStringToObject apply with field {}", field);

            if (field == null) {
                throw new DataException("Field " + fieldName + " not found in the record");
            }

            Object fieldValue = afterFieldValue.get(field);
            if (fieldValue == null) {
                return record; // If the field is null, return the record as is
            }
            log.trace("Called from JsonStringToObject apply with fieldValue {}", fieldValue);

            // Try to convert the string to a JSON object
            try {
                PageInsights jsonData = objectMapper.readValue(fieldValue.toString(), PageInsights.class);
                log.trace("Called from JsonStringToObject apply with jsonData {}", jsonData);
                // Build a new schema and struct
                SchemaBuilder updatedSchemaBuilder = SchemaBuilder.struct().name(record.valueSchema().name());
                updatedSchemaBuilder.field("business_id", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("click_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("date", Timestamp.SCHEMA);
                updatedSchemaBuilder.field("day", Schema.OPTIONAL_STRING_SCHEMA);
                updatedSchemaBuilder.field("ent_id", Schema.OPTIONAL_INT64_SCHEMA);
                updatedSchemaBuilder.field("page_id", Schema.OPTIONAL_STRING_SCHEMA);
                updatedSchemaBuilder.field("follower_gain", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("follower_lost", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_follower_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("like_gain", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("like_lost", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_like", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_engagement", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_reach", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_impressions", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_impression_total", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("video_views", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_total_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_eng_rate", Schema.OPTIONAL_FLOAT64_SCHEMA);
                updatedSchemaBuilder.field("post_engagement_total", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_video_views", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("share_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("comment_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_post_like_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_post_share_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("total_post_comment_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_like_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_comment_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("post_share_count", Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("date_string",Schema.OPTIONAL_STRING_SCHEMA);
                SchemaBuilder audienceActivitySchema = SchemaBuilder.struct().name("audience_activity");
                audienceActivitySchema.field("hour",Schema.OPTIONAL_STRING_SCHEMA);
                audienceActivitySchema.field("count",Schema.OPTIONAL_INT32_SCHEMA);
                updatedSchemaBuilder.field("audience_activity", audienceActivitySchema.optional().build());
                updatedSchemaBuilder.field("audience_countries", getSchemaBuilder("audience_countries"));
                updatedSchemaBuilder.field("audience_genders",  getSchemaBuilder("audience_genders"));
                updatedSchemaBuilder.field("audience_cities", getSchemaBuilder("audience_cities"));
                updatedSchemaBuilder.field("audience_ages", getSchemaBuilder("audience_ages"));
                updatedSchemaBuilder.field("is_business_account",Schema.OPTIONAL_BOOLEAN_SCHEMA);
                updatedSchemaBuilder.field("ts_ms", Schema.INT64_SCHEMA);
                Schema updatedSchema = updatedSchemaBuilder.build();
                Struct updatedValue = new Struct(updatedSchema);
                updatedValue.put("business_id", jsonData.getBusinessId());
                updatedValue.put("click_count", jsonData.getPageInsights().get(0).getClickCount());
                updatedValue.put("date", jsonData.getPageInsights().get(0).getDate());
                updatedValue.put("day", new SimpleDateFormat("yyyy-MM-dd").format(jsonData.getPageInsights().get(0).getDate()) + " 00:00:00");
                updatedValue.put("ent_id", jsonData.getEnterpriseId());
                updatedValue.put("page_id", jsonData.getPageId());
                updatedValue.put("follower_gain", jsonData.getPageInsights().get(0).getFollowerGainCount());
                updatedValue.put("follower_lost", jsonData.getPageInsights().get(0).getFollowerLostCount());
                updatedValue.put("total_follower_count", jsonData.getPageInsights().get(0).getTotalFollower());
                updatedValue.put("like_gain", jsonData.getPageInsights().get(0).getLikesGainCount());
                updatedValue.put("like_lost", jsonData.getPageInsights().get(0).getLikesLostCount());
                updatedValue.put("total_like", jsonData.getPageInsights().get(0).getTotalLikes());
                updatedValue.put("post_engagement", jsonData.getPageInsights().get(0).getPostEngagements());
                updatedValue.put("post_reach", jsonData.getPageInsights().get(0).getPostReach());
                updatedValue.put("post_impressions", jsonData.getPageInsights().get(0).getPostImpressions());
                updatedValue.put("post_impression_total", jsonData.getPageInsights().get(0).getPostImpressionTotal());
                updatedValue.put("post_count", jsonData.getPageInsights().get(0).getPostCount());
                updatedValue.put("video_views", jsonData.getPageInsights().get(0).getProfileVideoViews());
                updatedValue.put("post_total_count", jsonData.getPageInsights().get(0).getPostTotalCount());
                updatedValue.put("post_eng_rate", jsonData.getPageInsights().get(0).getPostEngagementRate());
                updatedValue.put("post_engagement_total", jsonData.getPageInsights().get(0).getPostEngagementTotal());
                updatedValue.put("total_video_views", jsonData.getPageInsights().get(0).getTotalProfileVideoViews());
                updatedValue.put("share_count", jsonData.getPageInsights().get(0).getShareCount());
                updatedValue.put("comment_count", jsonData.getPageInsights().get(0).getCommentCount());
                updatedValue.put("total_post_like_count", jsonData.getPageInsights().get(0).getTotalPostLikeCount());
                updatedValue.put("total_post_share_count", jsonData.getPageInsights().get(0).getTotalPostShareCount());
                updatedValue.put("total_post_comment_count", jsonData.getPageInsights().get(0).getTotalPostCommentCount());
                updatedValue.put("post_like_count", jsonData.getPageInsights().get(0).getPagePostLikeCount());
                updatedValue.put("post_comment_count", jsonData.getPageInsights().get(0).getTotalPostCommentCount());
                updatedValue.put("post_share_count", jsonData.getPageInsights().get(0).getTotalPostShareCount());
                updatedValue.put("date_string",jsonData.getPageInsights().get(0).getDateString());
                updatedValue.put("audience_activity",jsonData.getPageInsights().get(0).getAudienceActivity());
                updatedValue.put("audience_countries",jsonData.getPageInsights().get(0).getAudienceCountries());
                updatedValue.put("audience_genders",jsonData.getPageInsights().get(0).getAudienceGenders());
                updatedValue.put("audience_cities",jsonData.getPageInsights().get(0).getAudienceCities());
                updatedValue.put("audience_ages",jsonData.getPageInsights().get(0).getAudienceAges());
                updatedValue.put("is_business_account",jsonData.getPageInsights().get(0).getIsBusinessAccount());
                updatedValue.put("ts_ms", value.get("ts_ms"));
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                String keyValue = dateFormat.parse(updatedValue.getString("day")).getTime() + "_" + jsonData.getPageId();
                //            String keyValue = jsonData.getPageInsights().get(0).getDate().getTime()+"_"+jsonData.getPageId();
                // Return the new record
                return record.newRecord(
                        record.topic(),
                        record.kafkaPartition(),
                        Schema.STRING_SCHEMA,
                        keyValue,
                        updatedSchema,
                        updatedValue,
                        record.timestamp()
                );

            } catch (Exception e) {
                log.error("Failed to parse field '{}' as JSON: {}", fieldName, e.getMessage());
                throw new DataException("Failed to parse JSON string", e);
            }
        }catch (Exception e){
            log.error("Failed to process record {}",record,e);
            return record;
        }
    }

    private static Schema getSchemaBuilder(String name) {
        SchemaBuilder audienceCountriesSchema = SchemaBuilder.struct().name(name);
        audienceCountriesSchema.field("label", Schema.OPTIONAL_STRING_SCHEMA);
        audienceCountriesSchema.field("name", Schema.OPTIONAL_STRING_SCHEMA);
        audienceCountriesSchema.field("percentage", Schema.OPTIONAL_FLOAT64_SCHEMA);
        audienceCountriesSchema.field("total", Schema.OPTIONAL_INT32_SCHEMA);
        audienceCountriesSchema.field("code", Schema.OPTIONAL_STRING_SCHEMA);
        audienceCountriesSchema.field("Men", Schema.OPTIONAL_INT32_SCHEMA);
        audienceCountriesSchema.field("Women", Schema.OPTIONAL_INT32_SCHEMA);
        audienceCountriesSchema.field("Other", Schema.OPTIONAL_INT32_SCHEMA);
        return audienceCountriesSchema.optional().build();
    }

    @Override
    public ConfigDef config() {
        log.info("Called from JsonStringToObject config");
        return new ConfigDef()
                .define(FIELD_CONFIG, ConfigDef.Type.STRING, ConfigDef.NO_DEFAULT_VALUE, ConfigDef.Importance.HIGH,
                        "The field to convert from JSON string to object.");
    }

    @Override
    public void configure(Map<String, ?> configs) {
        log.info("Called from JsonStringToObject configure");
        fieldName = (String) configs.get(FIELD_CONFIG);
        if (fieldName == null || fieldName.isEmpty()) {
            throw new ConfigException(FIELD_CONFIG, "Field must be provided");
        }
    }

    @Override
    public void close() {
        // Nothing to do
    }
}
